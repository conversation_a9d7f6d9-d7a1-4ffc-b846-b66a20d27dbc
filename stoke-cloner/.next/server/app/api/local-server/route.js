var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/local-server/route.js")
R.c("server/chunks/node_modules_0a04cafb._.js")
R.c("server/chunks/[root-of-the-server]__463d1424._.js")
R.m("[project]/.next-internal/server/app/api/local-server/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/local-server/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/local-server/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
