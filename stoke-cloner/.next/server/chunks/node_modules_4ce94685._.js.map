{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/internal/streams/stream.js"], "sourcesContent": ["module.exports = require('stream');\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/internal/streams/BufferList.js"], "sourcesContent": ["'use strict';\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Buffer = require('safe-buffer').Buffer;\nvar util = require('util');\n\nfunction copyBuffer(src, target, offset) {\n  src.copy(target, offset);\n}\n\nmodule.exports = function () {\n  function BufferList() {\n    _classCallCheck(this, BufferList);\n\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  BufferList.prototype.push = function push(v) {\n    var entry = { data: v, next: null };\n    if (this.length > 0) this.tail.next = entry;else this.head = entry;\n    this.tail = entry;\n    ++this.length;\n  };\n\n  BufferList.prototype.unshift = function unshift(v) {\n    var entry = { data: v, next: this.head };\n    if (this.length === 0) this.tail = entry;\n    this.head = entry;\n    ++this.length;\n  };\n\n  BufferList.prototype.shift = function shift() {\n    if (this.length === 0) return;\n    var ret = this.head.data;\n    if (this.length === 1) this.head = this.tail = null;else this.head = this.head.next;\n    --this.length;\n    return ret;\n  };\n\n  BufferList.prototype.clear = function clear() {\n    this.head = this.tail = null;\n    this.length = 0;\n  };\n\n  BufferList.prototype.join = function join(s) {\n    if (this.length === 0) return '';\n    var p = this.head;\n    var ret = '' + p.data;\n    while (p = p.next) {\n      ret += s + p.data;\n    }return ret;\n  };\n\n  BufferList.prototype.concat = function concat(n) {\n    if (this.length === 0) return Buffer.alloc(0);\n    var ret = Buffer.allocUnsafe(n >>> 0);\n    var p = this.head;\n    var i = 0;\n    while (p) {\n      copyBuffer(p.data, ret, i);\n      i += p.data.length;\n      p = p.next;\n    }\n    return ret;\n  };\n\n  return BufferList;\n}();\n\nif (util && util.inspect && util.inspect.custom) {\n  module.exports.prototype[util.inspect.custom] = function () {\n    var obj = util.inspect({ length: this.length });\n    return this.constructor.name + ' ' + obj;\n  };\n}"], "names": [], "mappings": "AAEA,SAAS,gBAAgB,QAAQ,EAAE,WAAW;IAAI,IAAI,CAAC,CAAC,oBAAoB,WAAW,GAAG;QAAE,MAAM,IAAI,UAAU;IAAsC;AAAE;AAExJ,IAAI,SAAS,wHAAuB,MAAM;AAC1C,IAAI;AAEJ,SAAS,WAAW,GAAG,EAAE,MAAM,EAAE,MAAM;IACrC,IAAI,IAAI,CAAC,QAAQ;AACnB;AAEA,OAAO,OAAO,GAAG;IACf,SAAS;QACP,gBAAgB,IAAI,EAAE;QAEtB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,CAAC;QACzC,IAAI,QAAQ;YAAE,MAAM;YAAG,MAAM;QAAK;QAClC,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;aAAW,IAAI,CAAC,IAAI,GAAG;QAC7D,IAAI,CAAC,IAAI,GAAG;QACZ,EAAE,IAAI,CAAC,MAAM;IACf;IAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,CAAC;QAC/C,IAAI,QAAQ;YAAE,MAAM;YAAG,MAAM,IAAI,CAAC,IAAI;QAAC;QACvC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG;QACnC,IAAI,CAAC,IAAI,GAAG;QACZ,EAAE,IAAI,CAAC,MAAM;IACf;IAEA,WAAW,SAAS,CAAC,KAAK,GAAG,SAAS;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG;QACvB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;QACxB,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;aAAU,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QACnF,EAAE,IAAI,CAAC,MAAM;QACb,OAAO;IACT;IAEA,WAAW,SAAS,CAAC,KAAK,GAAG,SAAS;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,WAAW,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,CAAC;QACzC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;QAC9B,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,MAAM,KAAK,EAAE,IAAI;QACrB,MAAO,IAAI,EAAE,IAAI,CAAE;YACjB,OAAO,IAAI,EAAE,IAAI;QACnB;QAAC,OAAO;IACV;IAEA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAS,OAAO,CAAC;QAC7C,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,CAAC;QAC3C,IAAI,MAAM,OAAO,WAAW,CAAC,MAAM;QACnC,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,IAAI;QACR,MAAO,EAAG;YACR,WAAW,EAAE,IAAI,EAAE,KAAK;YACxB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,IAAI,EAAE,IAAI;QACZ;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,IAAI,QAAQ,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,EAAE;IAC/C,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,MAAM,CAAC,GAAG;QAC9C,IAAI,MAAM,KAAK,OAAO,CAAC;YAAE,QAAQ,IAAI,CAAC,MAAM;QAAC;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,MAAM;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/internal/streams/destroy.js"], "sourcesContent": ["'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\n// undocumented cb() API, needed for core, not for public API\nfunction destroy(err, cb) {\n  var _this = this;\n\n  var readableDestroyed = this._readableState && this._readableState.destroyed;\n  var writableDestroyed = this._writableState && this._writableState.destroyed;\n\n  if (readableDestroyed || writableDestroyed) {\n    if (cb) {\n      cb(err);\n    } else if (err) {\n      if (!this._writableState) {\n        pna.nextTick(emitErrorNT, this, err);\n      } else if (!this._writableState.errorEmitted) {\n        this._writableState.errorEmitted = true;\n        pna.nextTick(emitErrorNT, this, err);\n      }\n    }\n\n    return this;\n  }\n\n  // we set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n\n  if (this._readableState) {\n    this._readableState.destroyed = true;\n  }\n\n  // if this is a duplex stream mark the writable part as destroyed as well\n  if (this._writableState) {\n    this._writableState.destroyed = true;\n  }\n\n  this._destroy(err || null, function (err) {\n    if (!cb && err) {\n      if (!_this._writableState) {\n        pna.nextTick(emitErrorNT, _this, err);\n      } else if (!_this._writableState.errorEmitted) {\n        _this._writableState.errorEmitted = true;\n        pna.nextTick(emitErrorNT, _this, err);\n      }\n    } else if (cb) {\n      cb(err);\n    }\n  });\n\n  return this;\n}\n\nfunction undestroy() {\n  if (this._readableState) {\n    this._readableState.destroyed = false;\n    this._readableState.reading = false;\n    this._readableState.ended = false;\n    this._readableState.endEmitted = false;\n  }\n\n  if (this._writableState) {\n    this._writableState.destroyed = false;\n    this._writableState.ended = false;\n    this._writableState.ending = false;\n    this._writableState.finalCalled = false;\n    this._writableState.prefinished = false;\n    this._writableState.finished = false;\n    this._writableState.errorEmitted = false;\n  }\n}\n\nfunction emitErrorNT(self, err) {\n  self.emit('error', err);\n}\n\nmodule.exports = {\n  destroy: destroy,\n  undestroy: undestroy\n};"], "names": [], "mappings": "AAEA,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,6DAA6D;AAC7D,SAAS,QAAQ,GAAG,EAAE,EAAE;IACtB,IAAI,QAAQ,IAAI;IAEhB,IAAI,oBAAoB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAC5E,IAAI,oBAAoB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IAE5E,IAAI,qBAAqB,mBAAmB;QAC1C,IAAI,IAAI;YACN,GAAG;QACL,OAAO,IAAI,KAAK;YACd,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,IAAI,QAAQ,CAAC,aAAa,IAAI,EAAE;YAClC,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;gBAC5C,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG;gBACnC,IAAI,QAAQ,CAAC,aAAa,IAAI,EAAE;YAClC;QACF;QAEA,OAAO,IAAI;IACb;IAEA,kEAAkE;IAClE,2EAA2E;IAE3E,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;IAEA,yEAAyE;IACzE,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;IAEA,IAAI,CAAC,QAAQ,CAAC,OAAO,MAAM,SAAU,GAAG;QACtC,IAAI,CAAC,MAAM,KAAK;YACd,IAAI,CAAC,MAAM,cAAc,EAAE;gBACzB,IAAI,QAAQ,CAAC,aAAa,OAAO;YACnC,OAAO,IAAI,CAAC,MAAM,cAAc,CAAC,YAAY,EAAE;gBAC7C,MAAM,cAAc,CAAC,YAAY,GAAG;gBACpC,IAAI,QAAQ,CAAC,aAAa,OAAO;YACnC;QACF,OAAO,IAAI,IAAI;YACb,GAAG;QACL;IACF;IAEA,OAAO,IAAI;AACb;AAEA,SAAS;IACP,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAChC,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC9B,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;QAC5B,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;IACnC;IAEA,IAAI,IAAI,CAAC,cAAc,EAAE;QACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAChC,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;QAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;QAC7B,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG;QAClC,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG;QAClC,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;QAC/B,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG;IACrC;AACF;AAEA,SAAS,YAAY,IAAI,EAAE,GAAG;IAC5B,KAAK,IAAI,CAAC,SAAS;AACrB;AAEA,OAAO,OAAO,GAAG;IACf,SAAS;IACT,WAAW;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/_stream_writable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n\n'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\nmodule.exports = Writable;\n\n/* <replacement> */\nfunction WriteReq(chunk, encoding, cb) {\n  this.chunk = chunk;\n  this.encoding = encoding;\n  this.callback = cb;\n  this.next = null;\n}\n\n// It seems a linked list but it is not\n// there will be only 2 of these for each stream\nfunction CorkedRequest(state) {\n  var _this = this;\n\n  this.next = null;\n  this.entry = null;\n  this.finish = function () {\n    onCorkedFinish(_this, state);\n  };\n}\n/* </replacement> */\n\n/*<replacement>*/\nvar asyncWrite = !process.browser && ['v0.10', 'v0.9.'].indexOf(process.version.slice(0, 5)) > -1 ? setImmediate : pna.nextTick;\n/*</replacement>*/\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nWritable.WritableState = WritableState;\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\n/*<replacement>*/\nvar internalUtil = {\n  deprecate: require('util-deprecate')\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\n/*</replacement>*/\n\nvar destroyImpl = require('./internal/streams/destroy');\n\nutil.inherits(Writable, Stream);\n\nfunction nop() {}\n\nfunction WritableState(options, stream) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  var isDuplex = stream instanceof Duplex;\n\n  // object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n  this.objectMode = !!options.objectMode;\n\n  if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode;\n\n  // the point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write()\n  var hwm = options.highWaterMark;\n  var writableHwm = options.writableHighWaterMark;\n  var defaultHwm = this.objectMode ? 16 : 16 * 1024;\n\n  if (hwm || hwm === 0) this.highWaterMark = hwm;else if (isDuplex && (writableHwm || writableHwm === 0)) this.highWaterMark = writableHwm;else this.highWaterMark = defaultHwm;\n\n  // cast to ints.\n  this.highWaterMark = Math.floor(this.highWaterMark);\n\n  // if _final has been called\n  this.finalCalled = false;\n\n  // drain event flag.\n  this.needDrain = false;\n  // at the start of calling end()\n  this.ending = false;\n  // when end() has been called, and returned\n  this.ended = false;\n  // when 'finish' is emitted\n  this.finished = false;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n  var noDecode = options.decodeStrings === false;\n  this.decodeStrings = !noDecode;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n  this.length = 0;\n\n  // a flag to see when we're in the middle of a write.\n  this.writing = false;\n\n  // when true all writes will be buffered until .uncork() call\n  this.corked = 0;\n\n  // a flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n  this.sync = true;\n\n  // a flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n  this.bufferProcessing = false;\n\n  // the callback that's passed to _write(chunk,cb)\n  this.onwrite = function (er) {\n    onwrite(stream, er);\n  };\n\n  // the callback that the user supplies to write(chunk,encoding,cb)\n  this.writecb = null;\n\n  // the amount that is being written when _write is called.\n  this.writelen = 0;\n\n  this.bufferedRequest = null;\n  this.lastBufferedRequest = null;\n\n  // number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted\n  this.pendingcb = 0;\n\n  // emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams\n  this.prefinished = false;\n\n  // True if the error was already emitted and should not be thrown again\n  this.errorEmitted = false;\n\n  // count buffered requests\n  this.bufferedRequestCount = 0;\n\n  // allocate the first CorkedRequest, there is always\n  // one allocated and free to use, and we maintain at most two\n  this.corkedRequestsFree = new CorkedRequest(this);\n}\n\nWritableState.prototype.getBuffer = function getBuffer() {\n  var current = this.bufferedRequest;\n  var out = [];\n  while (current) {\n    out.push(current);\n    current = current.next;\n  }\n  return out;\n};\n\n(function () {\n  try {\n    Object.defineProperty(WritableState.prototype, 'buffer', {\n      get: internalUtil.deprecate(function () {\n        return this.getBuffer();\n      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')\n    });\n  } catch (_) {}\n})();\n\n// Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\nvar realHasInstance;\nif (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {\n  realHasInstance = Function.prototype[Symbol.hasInstance];\n  Object.defineProperty(Writable, Symbol.hasInstance, {\n    value: function (object) {\n      if (realHasInstance.call(this, object)) return true;\n      if (this !== Writable) return false;\n\n      return object && object._writableState instanceof WritableState;\n    }\n  });\n} else {\n  realHasInstance = function (object) {\n    return object instanceof this;\n  };\n}\n\nfunction Writable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n  if (!realHasInstance.call(Writable, this) && !(this instanceof Duplex)) {\n    return new Writable(options);\n  }\n\n  this._writableState = new WritableState(options, this);\n\n  // legacy.\n  this.writable = true;\n\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write;\n\n    if (typeof options.writev === 'function') this._writev = options.writev;\n\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n\n    if (typeof options.final === 'function') this._final = options.final;\n  }\n\n  Stream.call(this);\n}\n\n// Otherwise people can pipe Writable streams, which is just wrong.\nWritable.prototype.pipe = function () {\n  this.emit('error', new Error('Cannot pipe, not readable'));\n};\n\nfunction writeAfterEnd(stream, cb) {\n  var er = new Error('write after end');\n  // TODO: defer error events consistently everywhere, not just the cb\n  stream.emit('error', er);\n  pna.nextTick(cb, er);\n}\n\n// Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\nfunction validChunk(stream, state, chunk, cb) {\n  var valid = true;\n  var er = false;\n\n  if (chunk === null) {\n    er = new TypeError('May not write null values to stream');\n  } else if (typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new TypeError('Invalid non-string/buffer chunk');\n  }\n  if (er) {\n    stream.emit('error', er);\n    pna.nextTick(cb, er);\n    valid = false;\n  }\n  return valid;\n}\n\nWritable.prototype.write = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  var ret = false;\n  var isBuf = !state.objectMode && _isUint8Array(chunk);\n\n  if (isBuf && !Buffer.isBuffer(chunk)) {\n    chunk = _uint8ArrayToBuffer(chunk);\n  }\n\n  if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;\n\n  if (typeof cb !== 'function') cb = nop;\n\n  if (state.ended) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {\n    state.pendingcb++;\n    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n  }\n\n  return ret;\n};\n\nWritable.prototype.cork = function () {\n  var state = this._writableState;\n\n  state.corked++;\n};\n\nWritable.prototype.uncork = function () {\n  var state = this._writableState;\n\n  if (state.corked) {\n    state.corked--;\n\n    if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n  }\n};\n\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = encoding.toLowerCase();\n  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new TypeError('Unknown encoding: ' + encoding);\n  this._writableState.defaultEncoding = encoding;\n  return this;\n};\n\nfunction decodeChunk(state, chunk, encoding) {\n  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {\n    chunk = Buffer.from(chunk, encoding);\n  }\n  return chunk;\n}\n\nObject.defineProperty(Writable.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function () {\n    return this._writableState.highWaterMark;\n  }\n});\n\n// if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n  if (!isBuf) {\n    var newChunk = decodeChunk(state, chunk, encoding);\n    if (chunk !== newChunk) {\n      isBuf = true;\n      encoding = 'buffer';\n      chunk = newChunk;\n    }\n  }\n  var len = state.objectMode ? 1 : chunk.length;\n\n  state.length += len;\n\n  var ret = state.length < state.highWaterMark;\n  // we must ensure that previous needDrain will not be reset to false.\n  if (!ret) state.needDrain = true;\n\n  if (state.writing || state.corked) {\n    var last = state.lastBufferedRequest;\n    state.lastBufferedRequest = {\n      chunk: chunk,\n      encoding: encoding,\n      isBuf: isBuf,\n      callback: cb,\n      next: null\n    };\n    if (last) {\n      last.next = state.lastBufferedRequest;\n    } else {\n      state.bufferedRequest = state.lastBufferedRequest;\n    }\n    state.bufferedRequestCount += 1;\n  } else {\n    doWrite(stream, state, false, len, chunk, encoding, cb);\n  }\n\n  return ret;\n}\n\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len;\n  state.writecb = cb;\n  state.writing = true;\n  state.sync = true;\n  if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);\n  state.sync = false;\n}\n\nfunction onwriteError(stream, state, sync, er, cb) {\n  --state.pendingcb;\n\n  if (sync) {\n    // defer the callback if we are being called synchronously\n    // to avoid piling up things on the stack\n    pna.nextTick(cb, er);\n    // this can emit finish, and it will always happen\n    // after error\n    pna.nextTick(finishMaybe, stream, state);\n    stream._writableState.errorEmitted = true;\n    stream.emit('error', er);\n  } else {\n    // the caller expect this to happen before if\n    // it is async\n    cb(er);\n    stream._writableState.errorEmitted = true;\n    stream.emit('error', er);\n    // this can emit finish, but finish must\n    // always follow error\n    finishMaybe(stream, state);\n  }\n}\n\nfunction onwriteStateUpdate(state) {\n  state.writing = false;\n  state.writecb = null;\n  state.length -= state.writelen;\n  state.writelen = 0;\n}\n\nfunction onwrite(stream, er) {\n  var state = stream._writableState;\n  var sync = state.sync;\n  var cb = state.writecb;\n\n  onwriteStateUpdate(state);\n\n  if (er) onwriteError(stream, state, sync, er, cb);else {\n    // Check if we're actually ready to finish, but don't emit yet\n    var finished = needFinish(state);\n\n    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n      clearBuffer(stream, state);\n    }\n\n    if (sync) {\n      /*<replacement>*/\n      asyncWrite(afterWrite, stream, state, finished, cb);\n      /*</replacement>*/\n    } else {\n      afterWrite(stream, state, finished, cb);\n    }\n  }\n}\n\nfunction afterWrite(stream, state, finished, cb) {\n  if (!finished) onwriteDrain(stream, state);\n  state.pendingcb--;\n  cb();\n  finishMaybe(stream, state);\n}\n\n// Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\nfunction onwriteDrain(stream, state) {\n  if (state.length === 0 && state.needDrain) {\n    state.needDrain = false;\n    stream.emit('drain');\n  }\n}\n\n// if there's something in the buffer waiting, then process it\nfunction clearBuffer(stream, state) {\n  state.bufferProcessing = true;\n  var entry = state.bufferedRequest;\n\n  if (stream._writev && entry && entry.next) {\n    // Fast case, write everything using _writev()\n    var l = state.bufferedRequestCount;\n    var buffer = new Array(l);\n    var holder = state.corkedRequestsFree;\n    holder.entry = entry;\n\n    var count = 0;\n    var allBuffers = true;\n    while (entry) {\n      buffer[count] = entry;\n      if (!entry.isBuf) allBuffers = false;\n      entry = entry.next;\n      count += 1;\n    }\n    buffer.allBuffers = allBuffers;\n\n    doWrite(stream, state, true, state.length, buffer, '', holder.finish);\n\n    // doWrite is almost always async, defer these to save a bit of time\n    // as the hot path ends with doWrite\n    state.pendingcb++;\n    state.lastBufferedRequest = null;\n    if (holder.next) {\n      state.corkedRequestsFree = holder.next;\n      holder.next = null;\n    } else {\n      state.corkedRequestsFree = new CorkedRequest(state);\n    }\n    state.bufferedRequestCount = 0;\n  } else {\n    // Slow case, write chunks one-by-one\n    while (entry) {\n      var chunk = entry.chunk;\n      var encoding = entry.encoding;\n      var cb = entry.callback;\n      var len = state.objectMode ? 1 : chunk.length;\n\n      doWrite(stream, state, false, len, chunk, encoding, cb);\n      entry = entry.next;\n      state.bufferedRequestCount--;\n      // if we didn't call the onwrite immediately, then\n      // it means that we need to wait until it does.\n      // also, that means that the chunk and cb are currently\n      // being processed, so move the buffer counter past them.\n      if (state.writing) {\n        break;\n      }\n    }\n\n    if (entry === null) state.lastBufferedRequest = null;\n  }\n\n  state.bufferedRequest = entry;\n  state.bufferProcessing = false;\n}\n\nWritable.prototype._write = function (chunk, encoding, cb) {\n  cb(new Error('_write() is not implemented'));\n};\n\nWritable.prototype._writev = null;\n\nWritable.prototype.end = function (chunk, encoding, cb) {\n  var state = this._writableState;\n\n  if (typeof chunk === 'function') {\n    cb = chunk;\n    chunk = null;\n    encoding = null;\n  } else if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n\n  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);\n\n  // .end() fully uncorks\n  if (state.corked) {\n    state.corked = 1;\n    this.uncork();\n  }\n\n  // ignore unnecessary end() calls.\n  if (!state.ending) endWritable(this, state, cb);\n};\n\nfunction needFinish(state) {\n  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\nfunction callFinal(stream, state) {\n  stream._final(function (err) {\n    state.pendingcb--;\n    if (err) {\n      stream.emit('error', err);\n    }\n    state.prefinished = true;\n    stream.emit('prefinish');\n    finishMaybe(stream, state);\n  });\n}\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function') {\n      state.pendingcb++;\n      state.finalCalled = true;\n      pna.nextTick(callFinal, stream, state);\n    } else {\n      state.prefinished = true;\n      stream.emit('prefinish');\n    }\n  }\n}\n\nfunction finishMaybe(stream, state) {\n  var need = needFinish(state);\n  if (need) {\n    prefinish(stream, state);\n    if (state.pendingcb === 0) {\n      state.finished = true;\n      stream.emit('finish');\n    }\n  }\n  return need;\n}\n\nfunction endWritable(stream, state, cb) {\n  state.ending = true;\n  finishMaybe(stream, state);\n  if (cb) {\n    if (state.finished) pna.nextTick(cb);else stream.once('finish', cb);\n  }\n  state.ended = true;\n  stream.writable = false;\n}\n\nfunction onCorkedFinish(corkReq, state, err) {\n  var entry = corkReq.entry;\n  corkReq.entry = null;\n  while (entry) {\n    var cb = entry.callback;\n    state.pendingcb--;\n    cb(err);\n    entry = entry.next;\n  }\n\n  // reuse the free corkReq.\n  state.corkedRequestsFree.next = corkReq;\n}\n\nObject.defineProperty(Writable.prototype, 'destroyed', {\n  get: function () {\n    if (this._writableState === undefined) {\n      return false;\n    }\n    return this._writableState.destroyed;\n  },\n  set: function (value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._writableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._writableState.destroyed = value;\n  }\n});\n\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\nWritable.prototype._destroy = function (err, cb) {\n  this.end();\n  cb(err);\n};"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,uCAAuC;AACvC,wEAAwE;AACxE,0CAA0C;AAI1C,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,OAAO,OAAO,GAAG;AAEjB,iBAAiB,GACjB,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,EAAE;IACnC,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,uCAAuC;AACvC,gDAAgD;AAChD,SAAS,cAAc,KAAK;IAC1B,IAAI,QAAQ,IAAI;IAEhB,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,MAAM,GAAG;QACZ,eAAe,OAAO;IACxB;AACF;AACA,kBAAkB,GAElB,eAAe,GACf,IAAI,aAAa,4CAAoB;IAAC;IAAS;CAAQ,CAAC,OAAO,CAAC,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,eAAe,IAAI,QAAQ;AAC/H,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,SAAS,aAAa,GAAG;AAEzB,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,eAAe,GACf,IAAI,eAAe;IACjB,SAAS;AACX;AACA,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GAEf,IAAI,SAAS,wHAAuB,MAAM;AAC1C,IAAI,gBAAgB,CAAC,kGAAyC,uBAAgF,EAAE,UAAU,IAAI,YAAa;AAC3K,SAAS,oBAAoB,KAAK;IAChC,OAAO,OAAO,IAAI,CAAC;AACrB;AACA,SAAS,cAAc,GAAG;IACxB,OAAO,OAAO,QAAQ,CAAC,QAAQ,eAAe;AAChD;AAEA,gBAAgB,GAEhB,IAAI;AAEJ,KAAK,QAAQ,CAAC,UAAU;AAExB,SAAS,OAAO;AAEhB,SAAS,cAAc,OAAO,EAAE,MAAM;IACpC,SAAS;IAET,UAAU,WAAW,CAAC;IAEtB,2DAA2D;IAC3D,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,2EAA2E;IAC3E,IAAI,WAAW,kBAAkB;IAEjC,4DAA4D;IAC5D,+BAA+B;IAC/B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;IAEtC,IAAI,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,QAAQ,kBAAkB;IAE/E,oDAAoD;IACpD,iEAAiE;IACjE,0DAA0D;IAC1D,IAAI,MAAM,QAAQ,aAAa;IAC/B,IAAI,cAAc,QAAQ,qBAAqB;IAC/C,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG,KAAK,KAAK;IAE7C,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG;SAAS,IAAI,YAAY,CAAC,eAAe,gBAAgB,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG;SAAiB,IAAI,CAAC,aAAa,GAAG;IAEnK,gBAAgB;IAChB,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,aAAa;IAElD,4BAA4B;IAC5B,IAAI,CAAC,WAAW,GAAG;IAEnB,oBAAoB;IACpB,IAAI,CAAC,SAAS,GAAG;IACjB,gCAAgC;IAChC,IAAI,CAAC,MAAM,GAAG;IACd,2CAA2C;IAC3C,IAAI,CAAC,KAAK,GAAG;IACb,2BAA2B;IAC3B,IAAI,CAAC,QAAQ,GAAG;IAEhB,wBAAwB;IACxB,IAAI,CAAC,SAAS,GAAG;IAEjB,kEAAkE;IAClE,kEAAkE;IAClE,6BAA6B;IAC7B,IAAI,WAAW,QAAQ,aAAa,KAAK;IACzC,IAAI,CAAC,aAAa,GAAG,CAAC;IAEtB,sEAAsE;IACtE,6DAA6D;IAC7D,uDAAuD;IACvD,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI;IAElD,2DAA2D;IAC3D,6DAA6D;IAC7D,kBAAkB;IAClB,IAAI,CAAC,MAAM,GAAG;IAEd,qDAAqD;IACrD,IAAI,CAAC,OAAO,GAAG;IAEf,6DAA6D;IAC7D,IAAI,CAAC,MAAM,GAAG;IAEd,qEAAqE;IACrE,iEAAiE;IACjE,oEAAoE;IACpE,0CAA0C;IAC1C,IAAI,CAAC,IAAI,GAAG;IAEZ,sEAAsE;IACtE,oEAAoE;IACpE,6CAA6C;IAC7C,IAAI,CAAC,gBAAgB,GAAG;IAExB,iDAAiD;IACjD,IAAI,CAAC,OAAO,GAAG,SAAU,EAAE;QACzB,QAAQ,QAAQ;IAClB;IAEA,kEAAkE;IAClE,IAAI,CAAC,OAAO,GAAG;IAEf,0DAA0D;IAC1D,IAAI,CAAC,QAAQ,GAAG;IAEhB,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,mBAAmB,GAAG;IAE3B,kDAAkD;IAClD,gDAAgD;IAChD,IAAI,CAAC,SAAS,GAAG;IAEjB,mEAAmE;IACnE,qDAAqD;IACrD,IAAI,CAAC,WAAW,GAAG;IAEnB,uEAAuE;IACvE,IAAI,CAAC,YAAY,GAAG;IAEpB,0BAA0B;IAC1B,IAAI,CAAC,oBAAoB,GAAG;IAE5B,oDAAoD;IACpD,6DAA6D;IAC7D,IAAI,CAAC,kBAAkB,GAAG,IAAI,cAAc,IAAI;AAClD;AAEA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAS;IAC3C,IAAI,UAAU,IAAI,CAAC,eAAe;IAClC,IAAI,MAAM,EAAE;IACZ,MAAO,QAAS;QACd,IAAI,IAAI,CAAC;QACT,UAAU,QAAQ,IAAI;IACxB;IACA,OAAO;AACT;AAEA,CAAC;IACC,IAAI;QACF,OAAO,cAAc,CAAC,cAAc,SAAS,EAAE,UAAU;YACvD,KAAK,aAAa,SAAS,CAAC;gBAC1B,OAAO,IAAI,CAAC,SAAS;YACvB,GAAG,uEAAuE,YAAY;QACxF;IACF,EAAE,OAAO,GAAG,CAAC;AACf,CAAC;AAED,qEAAqE;AACrE,iDAAiD;AACjD,IAAI;AACJ,IAAI,OAAO,WAAW,cAAc,OAAO,WAAW,IAAI,OAAO,SAAS,SAAS,CAAC,OAAO,WAAW,CAAC,KAAK,YAAY;IACtH,kBAAkB,SAAS,SAAS,CAAC,OAAO,WAAW,CAAC;IACxD,OAAO,cAAc,CAAC,UAAU,OAAO,WAAW,EAAE;QAClD,OAAO,SAAU,MAAM;YACrB,IAAI,gBAAgB,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO;YAC/C,IAAI,IAAI,KAAK,UAAU,OAAO;YAE9B,OAAO,UAAU,OAAO,cAAc,YAAY;QACpD;IACF;AACF,OAAO;IACL,kBAAkB,SAAU,MAAM;QAChC,OAAO,kBAAkB,IAAI;IAC/B;AACF;AAEA,SAAS,SAAS,OAAO;IACvB,SAAS;IAET,6CAA6C;IAC7C,kEAAkE;IAClE,mEAAmE;IAEnE,8EAA8E;IAC9E,2EAA2E;IAC3E,0DAA0D;IAC1D,IAAI,CAAC,gBAAgB,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC,IAAI,YAAY,MAAM,GAAG;QACtE,OAAO,IAAI,SAAS;IACtB;IAEA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,SAAS,IAAI;IAErD,UAAU;IACV,IAAI,CAAC,QAAQ,GAAG;IAEhB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QAEpE,IAAI,OAAO,QAAQ,MAAM,KAAK,YAAY,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;QAEvE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;QAE1E,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IACtE;IAEA,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,mEAAmE;AACnE,SAAS,SAAS,CAAC,IAAI,GAAG;IACxB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM;AAC/B;AAEA,SAAS,cAAc,MAAM,EAAE,EAAE;IAC/B,IAAI,KAAK,IAAI,MAAM;IACnB,oEAAoE;IACpE,OAAO,IAAI,CAAC,SAAS;IACrB,IAAI,QAAQ,CAAC,IAAI;AACnB;AAEA,4EAA4E;AAC5E,4EAA4E;AAC5E,mEAAmE;AACnE,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;IAC1C,IAAI,QAAQ;IACZ,IAAI,KAAK;IAET,IAAI,UAAU,MAAM;QAClB,KAAK,IAAI,UAAU;IACrB,OAAO,IAAI,OAAO,UAAU,YAAY,UAAU,aAAa,CAAC,MAAM,UAAU,EAAE;QAChF,KAAK,IAAI,UAAU;IACrB;IACA,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,SAAS;QACrB,IAAI,QAAQ,CAAC,IAAI;QACjB,QAAQ;IACV;IACA,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,KAAK,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACtD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,MAAM;IACV,IAAI,QAAQ,CAAC,MAAM,UAAU,IAAI,cAAc;IAE/C,IAAI,SAAS,CAAC,OAAO,QAAQ,CAAC,QAAQ;QACpC,QAAQ,oBAAoB;IAC9B;IAEA,IAAI,OAAO,aAAa,YAAY;QAClC,KAAK;QACL,WAAW;IACb;IAEA,IAAI,OAAO,WAAW;SAAc,IAAI,CAAC,UAAU,WAAW,MAAM,eAAe;IAEnF,IAAI,OAAO,OAAO,YAAY,KAAK;IAEnC,IAAI,MAAM,KAAK,EAAE,cAAc,IAAI,EAAE;SAAS,IAAI,SAAS,WAAW,IAAI,EAAE,OAAO,OAAO,KAAK;QAC7F,MAAM,SAAS;QACf,MAAM,cAAc,IAAI,EAAE,OAAO,OAAO,OAAO,UAAU;IAC3D;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,CAAC,IAAI,GAAG;IACxB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,MAAM,MAAM;AACd;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG;IAC1B,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM;QAEZ,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,gBAAgB,IAAI,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE;IAC7G;AACF;AAEA,SAAS,SAAS,CAAC,kBAAkB,GAAG,SAAS,mBAAmB,QAAQ;IAC1E,6CAA6C;IAC7C,IAAI,OAAO,aAAa,UAAU,WAAW,SAAS,WAAW;IACjE,IAAI,CAAC,CAAC;QAAC;QAAO;QAAQ;QAAS;QAAS;QAAU;QAAU;QAAQ;QAAS;QAAW;QAAY;KAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,WAAW,MAAM,CAAC,CAAC,GAAG,MAAM,IAAI,UAAU,uBAAuB;IACpM,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG;IACtC,OAAO,IAAI;AACb;AAEA,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,QAAQ;IACzC,IAAI,CAAC,MAAM,UAAU,IAAI,MAAM,aAAa,KAAK,SAAS,OAAO,UAAU,UAAU;QACnF,QAAQ,OAAO,IAAI,CAAC,OAAO;IAC7B;IACA,OAAO;AACT;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK;QACH,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AAEA,yDAAyD;AACzD,2DAA2D;AAC3D,oEAAoE;AACpE,SAAS,cAAc,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,IAAI,CAAC,OAAO;QACV,IAAI,WAAW,YAAY,OAAO,OAAO;QACzC,IAAI,UAAU,UAAU;YACtB,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;IACF;IACA,IAAI,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;IAE7C,MAAM,MAAM,IAAI;IAEhB,IAAI,MAAM,MAAM,MAAM,GAAG,MAAM,aAAa;IAC5C,qEAAqE;IACrE,IAAI,CAAC,KAAK,MAAM,SAAS,GAAG;IAE5B,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE;QACjC,IAAI,OAAO,MAAM,mBAAmB;QACpC,MAAM,mBAAmB,GAAG;YAC1B,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;QACR;QACA,IAAI,MAAM;YACR,KAAK,IAAI,GAAG,MAAM,mBAAmB;QACvC,OAAO;YACL,MAAM,eAAe,GAAG,MAAM,mBAAmB;QACnD;QACA,MAAM,oBAAoB,IAAI;IAChC,OAAO;QACL,QAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU;IACtD;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,MAAM,QAAQ,GAAG;IACjB,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG;IAChB,MAAM,IAAI,GAAG;IACb,IAAI,QAAQ,OAAO,OAAO,CAAC,OAAO,MAAM,OAAO;SAAO,OAAO,MAAM,CAAC,OAAO,UAAU,MAAM,OAAO;IAClG,MAAM,IAAI,GAAG;AACf;AAEA,SAAS,aAAa,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;IAC/C,EAAE,MAAM,SAAS;IAEjB,IAAI,MAAM;QACR,0DAA0D;QAC1D,yCAAyC;QACzC,IAAI,QAAQ,CAAC,IAAI;QACjB,kDAAkD;QAClD,cAAc;QACd,IAAI,QAAQ,CAAC,aAAa,QAAQ;QAClC,OAAO,cAAc,CAAC,YAAY,GAAG;QACrC,OAAO,IAAI,CAAC,SAAS;IACvB,OAAO;QACL,6CAA6C;QAC7C,cAAc;QACd,GAAG;QACH,OAAO,cAAc,CAAC,YAAY,GAAG;QACrC,OAAO,IAAI,CAAC,SAAS;QACrB,wCAAwC;QACxC,sBAAsB;QACtB,YAAY,QAAQ;IACtB;AACF;AAEA,SAAS,mBAAmB,KAAK;IAC/B,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG;IAChB,MAAM,MAAM,IAAI,MAAM,QAAQ;IAC9B,MAAM,QAAQ,GAAG;AACnB;AAEA,SAAS,QAAQ,MAAM,EAAE,EAAE;IACzB,IAAI,QAAQ,OAAO,cAAc;IACjC,IAAI,OAAO,MAAM,IAAI;IACrB,IAAI,KAAK,MAAM,OAAO;IAEtB,mBAAmB;IAEnB,IAAI,IAAI,aAAa,QAAQ,OAAO,MAAM,IAAI;SAAS;QACrD,8DAA8D;QAC9D,IAAI,WAAW,WAAW;QAE1B,IAAI,CAAC,YAAY,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,gBAAgB,IAAI,MAAM,eAAe,EAAE;YAClF,YAAY,QAAQ;QACtB;QAEA,IAAI,MAAM;YACR,eAAe,GACf,WAAW,YAAY,QAAQ,OAAO,UAAU;QAChD,gBAAgB,GAClB,OAAO;YACL,WAAW,QAAQ,OAAO,UAAU;QACtC;IACF;AACF;AAEA,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC7C,IAAI,CAAC,UAAU,aAAa,QAAQ;IACpC,MAAM,SAAS;IACf;IACA,YAAY,QAAQ;AACtB;AAEA,iEAAiE;AACjE,mEAAmE;AACnE,wDAAwD;AACxD,SAAS,aAAa,MAAM,EAAE,KAAK;IACjC,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,SAAS,EAAE;QACzC,MAAM,SAAS,GAAG;QAClB,OAAO,IAAI,CAAC;IACd;AACF;AAEA,8DAA8D;AAC9D,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,MAAM,gBAAgB,GAAG;IACzB,IAAI,QAAQ,MAAM,eAAe;IAEjC,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,IAAI,EAAE;QACzC,8CAA8C;QAC9C,IAAI,IAAI,MAAM,oBAAoB;QAClC,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,SAAS,MAAM,kBAAkB;QACrC,OAAO,KAAK,GAAG;QAEf,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,MAAO,MAAO;YACZ,MAAM,CAAC,MAAM,GAAG;YAChB,IAAI,CAAC,MAAM,KAAK,EAAE,aAAa;YAC/B,QAAQ,MAAM,IAAI;YAClB,SAAS;QACX;QACA,OAAO,UAAU,GAAG;QAEpB,QAAQ,QAAQ,OAAO,MAAM,MAAM,MAAM,EAAE,QAAQ,IAAI,OAAO,MAAM;QAEpE,oEAAoE;QACpE,oCAAoC;QACpC,MAAM,SAAS;QACf,MAAM,mBAAmB,GAAG;QAC5B,IAAI,OAAO,IAAI,EAAE;YACf,MAAM,kBAAkB,GAAG,OAAO,IAAI;YACtC,OAAO,IAAI,GAAG;QAChB,OAAO;YACL,MAAM,kBAAkB,GAAG,IAAI,cAAc;QAC/C;QACA,MAAM,oBAAoB,GAAG;IAC/B,OAAO;QACL,qCAAqC;QACrC,MAAO,MAAO;YACZ,IAAI,QAAQ,MAAM,KAAK;YACvB,IAAI,WAAW,MAAM,QAAQ;YAC7B,IAAI,KAAK,MAAM,QAAQ;YACvB,IAAI,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;YAE7C,QAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU;YACpD,QAAQ,MAAM,IAAI;YAClB,MAAM,oBAAoB;YAC1B,kDAAkD;YAClD,+CAA+C;YAC/C,uDAAuD;YACvD,yDAAyD;YACzD,IAAI,MAAM,OAAO,EAAE;gBACjB;YACF;QACF;QAEA,IAAI,UAAU,MAAM,MAAM,mBAAmB,GAAG;IAClD;IAEA,MAAM,eAAe,GAAG;IACxB,MAAM,gBAAgB,GAAG;AAC3B;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACvD,GAAG,IAAI,MAAM;AACf;AAEA,SAAS,SAAS,CAAC,OAAO,GAAG;AAE7B,SAAS,SAAS,CAAC,GAAG,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACpD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,IAAI,OAAO,UAAU,YAAY;QAC/B,KAAK;QACL,QAAQ;QACR,WAAW;IACb,OAAO,IAAI,OAAO,aAAa,YAAY;QACzC,KAAK;QACL,WAAW;IACb;IAEA,IAAI,UAAU,QAAQ,UAAU,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO;IAE7D,uBAAuB;IACvB,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM,GAAG;QACf,IAAI,CAAC,MAAM;IACb;IAEA,kCAAkC;IAClC,IAAI,CAAC,MAAM,MAAM,EAAE,YAAY,IAAI,EAAE,OAAO;AAC9C;AAEA,SAAS,WAAW,KAAK;IACvB,OAAO,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,eAAe,KAAK,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,OAAO;AAClH;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,OAAO,MAAM,CAAC,SAAU,GAAG;QACzB,MAAM,SAAS;QACf,IAAI,KAAK;YACP,OAAO,IAAI,CAAC,SAAS;QACvB;QACA,MAAM,WAAW,GAAG;QACpB,OAAO,IAAI,CAAC;QACZ,YAAY,QAAQ;IACtB;AACF;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,WAAW,EAAE;QAC5C,IAAI,OAAO,OAAO,MAAM,KAAK,YAAY;YACvC,MAAM,SAAS;YACf,MAAM,WAAW,GAAG;YACpB,IAAI,QAAQ,CAAC,WAAW,QAAQ;QAClC,OAAO;YACL,MAAM,WAAW,GAAG;YACpB,OAAO,IAAI,CAAC;QACd;IACF;AACF;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,OAAO,WAAW;IACtB,IAAI,MAAM;QACR,UAAU,QAAQ;QAClB,IAAI,MAAM,SAAS,KAAK,GAAG;YACzB,MAAM,QAAQ,GAAG;YACjB,OAAO,IAAI,CAAC;QACd;IACF;IACA,OAAO;AACT;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK,EAAE,EAAE;IACpC,MAAM,MAAM,GAAG;IACf,YAAY,QAAQ;IACpB,IAAI,IAAI;QACN,IAAI,MAAM,QAAQ,EAAE,IAAI,QAAQ,CAAC;aAAS,OAAO,IAAI,CAAC,UAAU;IAClE;IACA,MAAM,KAAK,GAAG;IACd,OAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,eAAe,OAAO,EAAE,KAAK,EAAE,GAAG;IACzC,IAAI,QAAQ,QAAQ,KAAK;IACzB,QAAQ,KAAK,GAAG;IAChB,MAAO,MAAO;QACZ,IAAI,KAAK,MAAM,QAAQ;QACvB,MAAM,SAAS;QACf,GAAG;QACH,QAAQ,MAAM,IAAI;IACpB;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC,IAAI,GAAG;AAClC;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,aAAa;IACrD,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,KAAK,WAAW;YACrC,OAAO;QACT;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;IACtC;IACA,KAAK,SAAU,KAAK;QAClB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;QACF;QAEA,iDAAiD;QACjD,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AAEA,SAAS,SAAS,CAAC,OAAO,GAAG,YAAY,OAAO;AAChD,SAAS,SAAS,CAAC,UAAU,GAAG,YAAY,SAAS;AACrD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC7C,IAAI,CAAC,GAAG;IACR,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/_stream_duplex.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n\n'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\n/*<replacement>*/\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n  for (var key in obj) {\n    keys.push(key);\n  }return keys;\n};\n/*</replacement>*/\n\nmodule.exports = Duplex;\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\nvar Readable = require('./_stream_readable');\nvar Writable = require('./_stream_writable');\n\nutil.inherits(Duplex, Readable);\n\n{\n  // avoid scope creep, the keys array can then be collected\n  var keys = objectKeys(Writable.prototype);\n  for (var v = 0; v < keys.length; v++) {\n    var method = keys[v];\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n  }\n}\n\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options);\n\n  Readable.call(this, options);\n  Writable.call(this, options);\n\n  if (options && options.readable === false) this.readable = false;\n\n  if (options && options.writable === false) this.writable = false;\n\n  this.allowHalfOpen = true;\n  if (options && options.allowHalfOpen === false) this.allowHalfOpen = false;\n\n  this.once('end', onend);\n}\n\nObject.defineProperty(Duplex.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function () {\n    return this._writableState.highWaterMark;\n  }\n});\n\n// the no-half-open enforcer\nfunction onend() {\n  // if we allow half-open state, or if the writable side ended,\n  // then we're ok.\n  if (this.allowHalfOpen || this._writableState.ended) return;\n\n  // no more data can be written.\n  // But allow more writes to happen in this tick.\n  pna.nextTick(onEndNT, this);\n}\n\nfunction onEndNT(self) {\n  self.end();\n}\n\nObject.defineProperty(Duplex.prototype, 'destroyed', {\n  get: function () {\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed && this._writableState.destroyed;\n  },\n  set: function (value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n    this._writableState.destroyed = value;\n  }\n});\n\nDuplex.prototype._destroy = function (err, cb) {\n  this.push(null);\n  this.end();\n\n  pna.nextTick(cb, err);\n};"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,uEAAuE;AACvE,oEAAoE;AACpE,mEAAmE;AACnE,YAAY;AAIZ,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GACf,IAAI,aAAa,OAAO,IAAI,IAAI,SAAU,GAAG;IAC3C,IAAI,OAAO,EAAE;IACb,IAAK,IAAI,OAAO,IAAK;QACnB,KAAK,IAAI,CAAC;IACZ;IAAC,OAAO;AACV;AACA,gBAAgB,GAEhB,OAAO,OAAO,GAAG;AAEjB,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,IAAI;AACJ,IAAI;AAEJ,KAAK,QAAQ,CAAC,QAAQ;AAEtB;IACE,0DAA0D;IAC1D,IAAI,OAAO,WAAW,SAAS,SAAS;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,SAAS,IAAI,CAAC,EAAE;QACpB,IAAI,CAAC,OAAO,SAAS,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,SAAS,CAAC,OAAO;IACtF;AACF,CAEA,SAAS,OAAO,OAAO;IACrB,IAAI,CAAC,CAAC,IAAI,YAAY,MAAM,GAAG,OAAO,IAAI,OAAO;IAEjD,SAAS,IAAI,CAAC,IAAI,EAAE;IACpB,SAAS,IAAI,CAAC,IAAI,EAAE;IAEpB,IAAI,WAAW,QAAQ,QAAQ,KAAK,OAAO,IAAI,CAAC,QAAQ,GAAG;IAE3D,IAAI,WAAW,QAAQ,QAAQ,KAAK,OAAO,IAAI,CAAC,QAAQ,GAAG;IAE3D,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,WAAW,QAAQ,aAAa,KAAK,OAAO,IAAI,CAAC,aAAa,GAAG;IAErE,IAAI,CAAC,IAAI,CAAC,OAAO;AACnB;AAEA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,yBAAyB;IAC/D,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK;QACH,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AAEA,4BAA4B;AAC5B,SAAS;IACP,8DAA8D;IAC9D,iBAAiB;IACjB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;IAErD,+BAA+B;IAC/B,gDAAgD;IAChD,IAAI,QAAQ,CAAC,SAAS,IAAI;AAC5B;AAEA,SAAS,QAAQ,IAAI;IACnB,KAAK,GAAG;AACV;AAEA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,aAAa;IACnD,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,IAAI,CAAC,cAAc,KAAK,WAAW;YAC1E,OAAO;QACT;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;IACvE;IACA,KAAK,SAAU,KAAK;QAClB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,IAAI,CAAC,cAAc,KAAK,WAAW;YAC1E;QACF;QAEA,iDAAiD;QACjD,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAChC,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AAEA,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC3C,IAAI,CAAC,IAAI,CAAC;IACV,IAAI,CAAC,GAAG;IAER,IAAI,QAAQ,CAAC,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/_stream_readable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\n\nvar pna = require('process-nextick-args');\n/*</replacement>*/\n\nmodule.exports = Readable;\n\n/*<replacement>*/\nvar isArray = require('isarray');\n/*</replacement>*/\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nReadable.ReadableState = ReadableState;\n\n/*<replacement>*/\nvar EE = require('events').EventEmitter;\n\nvar EElistenerCount = function (emitter, type) {\n  return emitter.listeners(type).length;\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\n/*</replacement>*/\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\n/*<replacement>*/\nvar debugUtil = require('util');\nvar debug = void 0;\nif (debugUtil && debugUtil.debuglog) {\n  debug = debugUtil.debuglog('stream');\n} else {\n  debug = function () {};\n}\n/*</replacement>*/\n\nvar BufferList = require('./internal/streams/BufferList');\nvar destroyImpl = require('./internal/streams/destroy');\nvar StringDecoder;\n\nutil.inherits(Readable, Stream);\n\nvar kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume'];\n\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn);\n\n  // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];\n}\n\nfunction ReadableState(options, stream) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  var isDuplex = stream instanceof Duplex;\n\n  // object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away\n  this.objectMode = !!options.objectMode;\n\n  if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode;\n\n  // the point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n  var hwm = options.highWaterMark;\n  var readableHwm = options.readableHighWaterMark;\n  var defaultHwm = this.objectMode ? 16 : 16 * 1024;\n\n  if (hwm || hwm === 0) this.highWaterMark = hwm;else if (isDuplex && (readableHwm || readableHwm === 0)) this.highWaterMark = readableHwm;else this.highWaterMark = defaultHwm;\n\n  // cast to ints.\n  this.highWaterMark = Math.floor(this.highWaterMark);\n\n  // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift()\n  this.buffer = new BufferList();\n  this.length = 0;\n  this.pipes = null;\n  this.pipesCount = 0;\n  this.flowing = null;\n  this.ended = false;\n  this.endEmitted = false;\n  this.reading = false;\n\n  // a flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n  this.sync = true;\n\n  // whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n  this.needReadable = false;\n  this.emittedReadable = false;\n  this.readableListening = false;\n  this.resumeScheduled = false;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // the number of writers that are awaiting a drain event in .pipe()s\n  this.awaitDrain = 0;\n\n  // if true, a maybeReadMore has been scheduled\n  this.readingMore = false;\n\n  this.decoder = null;\n  this.encoding = null;\n  if (options.encoding) {\n    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n    this.decoder = new StringDecoder(options.encoding);\n    this.encoding = options.encoding;\n  }\n}\n\nfunction Readable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  if (!(this instanceof Readable)) return new Readable(options);\n\n  this._readableState = new ReadableState(options, this);\n\n  // legacy\n  this.readable = true;\n\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read;\n\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n  }\n\n  Stream.call(this);\n}\n\nObject.defineProperty(Readable.prototype, 'destroyed', {\n  get: function () {\n    if (this._readableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed;\n  },\n  set: function (value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._readableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n  }\n});\n\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\nReadable.prototype._destroy = function (err, cb) {\n  this.push(null);\n  cb(err);\n};\n\n// Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\nReadable.prototype.push = function (chunk, encoding) {\n  var state = this._readableState;\n  var skipChunkCheck;\n\n  if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding;\n      if (encoding !== state.encoding) {\n        chunk = Buffer.from(chunk, encoding);\n        encoding = '';\n      }\n      skipChunkCheck = true;\n    }\n  } else {\n    skipChunkCheck = true;\n  }\n\n  return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n};\n\n// Unshift should *always* be something directly out of read()\nReadable.prototype.unshift = function (chunk) {\n  return readableAddChunk(this, chunk, null, true, false);\n};\n\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n  var state = stream._readableState;\n  if (chunk === null) {\n    state.reading = false;\n    onEofChunk(stream, state);\n  } else {\n    var er;\n    if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n    if (er) {\n      stream.emit('error', er);\n    } else if (state.objectMode || chunk && chunk.length > 0) {\n      if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n        chunk = _uint8ArrayToBuffer(chunk);\n      }\n\n      if (addToFront) {\n        if (state.endEmitted) stream.emit('error', new Error('stream.unshift() after end event'));else addChunk(stream, state, chunk, true);\n      } else if (state.ended) {\n        stream.emit('error', new Error('stream.push() after EOF'));\n      } else {\n        state.reading = false;\n        if (state.decoder && !encoding) {\n          chunk = state.decoder.write(chunk);\n          if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);else maybeReadMore(stream, state);\n        } else {\n          addChunk(stream, state, chunk, false);\n        }\n      }\n    } else if (!addToFront) {\n      state.reading = false;\n    }\n  }\n\n  return needMoreData(state);\n}\n\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync) {\n    stream.emit('data', chunk);\n    stream.read(0);\n  } else {\n    // update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length;\n    if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);\n\n    if (state.needReadable) emitReadable(stream);\n  }\n  maybeReadMore(stream, state);\n}\n\nfunction chunkInvalid(state, chunk) {\n  var er;\n  if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new TypeError('Invalid non-string/buffer chunk');\n  }\n  return er;\n}\n\n// if it's past the high water mark, we can push in some more.\n// Also, if we have no data yet, we can stand some\n// more bytes.  This is to work around cases where hwm=0,\n// such as the repl.  Also, if the push() triggered a\n// readable event, and the user called read(largeNumber) such that\n// needReadable was set, then we ought to push more, so that another\n// 'readable' event will be triggered.\nfunction needMoreData(state) {\n  return !state.ended && (state.needReadable || state.length < state.highWaterMark || state.length === 0);\n}\n\nReadable.prototype.isPaused = function () {\n  return this._readableState.flowing === false;\n};\n\n// backwards compatibility.\nReadable.prototype.setEncoding = function (enc) {\n  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n  this._readableState.decoder = new StringDecoder(enc);\n  this._readableState.encoding = enc;\n  return this;\n};\n\n// Don't raise the hwm > 8MB\nvar MAX_HWM = 0x800000;\nfunction computeNewHighWaterMark(n) {\n  if (n >= MAX_HWM) {\n    n = MAX_HWM;\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts\n    n--;\n    n |= n >>> 1;\n    n |= n >>> 2;\n    n |= n >>> 4;\n    n |= n >>> 8;\n    n |= n >>> 16;\n    n++;\n  }\n  return n;\n}\n\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || state.length === 0 && state.ended) return 0;\n  if (state.objectMode) return 1;\n  if (n !== n) {\n    // Only flow one buffer at a time\n    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;\n  }\n  // If we're asking for more than the current hwm, then raise the hwm.\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n  if (n <= state.length) return n;\n  // Don't have enough\n  if (!state.ended) {\n    state.needReadable = true;\n    return 0;\n  }\n  return state.length;\n}\n\n// you can override either this method, or the async _read(n) below.\nReadable.prototype.read = function (n) {\n  debug('read', n);\n  n = parseInt(n, 10);\n  var state = this._readableState;\n  var nOrig = n;\n\n  if (n !== 0) state.emittedReadable = false;\n\n  // if we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n  if (n === 0 && state.needReadable && (state.length >= state.highWaterMark || state.ended)) {\n    debug('read: emitReadable', state.length, state.ended);\n    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);\n    return null;\n  }\n\n  n = howMuchToRead(n, state);\n\n  // if we've ended, and we're now clear, then finish it up.\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this);\n    return null;\n  }\n\n  // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n\n  // if we need a readable event, then we need to do some reading.\n  var doRead = state.needReadable;\n  debug('need readable', doRead);\n\n  // if we currently have less than the highWaterMark, then also read some\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true;\n    debug('length less than watermark', doRead);\n  }\n\n  // however, if we've ended, then there's no point, and if we're already\n  // reading, then it's unnecessary.\n  if (state.ended || state.reading) {\n    doRead = false;\n    debug('reading or ended', doRead);\n  } else if (doRead) {\n    debug('do read');\n    state.reading = true;\n    state.sync = true;\n    // if the length is currently zero, then we *need* a readable event.\n    if (state.length === 0) state.needReadable = true;\n    // call internal read method\n    this._read(state.highWaterMark);\n    state.sync = false;\n    // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n    if (!state.reading) n = howMuchToRead(nOrig, state);\n  }\n\n  var ret;\n  if (n > 0) ret = fromList(n, state);else ret = null;\n\n  if (ret === null) {\n    state.needReadable = true;\n    n = 0;\n  } else {\n    state.length -= n;\n  }\n\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true;\n\n    // If we tried to read() past the EOF, then emit end on the next tick.\n    if (nOrig !== n && state.ended) endReadable(this);\n  }\n\n  if (ret !== null) this.emit('data', ret);\n\n  return ret;\n};\n\nfunction onEofChunk(stream, state) {\n  if (state.ended) return;\n  if (state.decoder) {\n    var chunk = state.decoder.end();\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk);\n      state.length += state.objectMode ? 1 : chunk.length;\n    }\n  }\n  state.ended = true;\n\n  // emit 'readable' now to make sure it gets picked up.\n  emitReadable(stream);\n}\n\n// Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\nfunction emitReadable(stream) {\n  var state = stream._readableState;\n  state.needReadable = false;\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing);\n    state.emittedReadable = true;\n    if (state.sync) pna.nextTick(emitReadable_, stream);else emitReadable_(stream);\n  }\n}\n\nfunction emitReadable_(stream) {\n  debug('emit readable');\n  stream.emit('readable');\n  flow(stream);\n}\n\n// at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore) {\n    state.readingMore = true;\n    pna.nextTick(maybeReadMore_, stream, state);\n  }\n}\n\nfunction maybeReadMore_(stream, state) {\n  var len = state.length;\n  while (!state.reading && !state.flowing && !state.ended && state.length < state.highWaterMark) {\n    debug('maybeReadMore read 0');\n    stream.read(0);\n    if (len === state.length)\n      // didn't get any data, stop spinning.\n      break;else len = state.length;\n  }\n  state.readingMore = false;\n}\n\n// abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\nReadable.prototype._read = function (n) {\n  this.emit('error', new Error('_read() is not implemented'));\n};\n\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  var src = this;\n  var state = this._readableState;\n\n  switch (state.pipesCount) {\n    case 0:\n      state.pipes = dest;\n      break;\n    case 1:\n      state.pipes = [state.pipes, dest];\n      break;\n    default:\n      state.pipes.push(dest);\n      break;\n  }\n  state.pipesCount += 1;\n  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);\n\n  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n\n  var endFn = doEnd ? onend : unpipe;\n  if (state.endEmitted) pna.nextTick(endFn);else src.once('end', endFn);\n\n  dest.on('unpipe', onunpipe);\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe');\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true;\n        cleanup();\n      }\n    }\n  }\n\n  function onend() {\n    debug('onend');\n    dest.end();\n  }\n\n  // when the dest drains, it reduces the awaitDrain counter\n  // on the source.  This would be more elegant with a .once()\n  // handler in flow(), but adding and removing repeatedly is\n  // too slow.\n  var ondrain = pipeOnDrain(src);\n  dest.on('drain', ondrain);\n\n  var cleanedUp = false;\n  function cleanup() {\n    debug('cleanup');\n    // cleanup event handlers once the pipe is broken\n    dest.removeListener('close', onclose);\n    dest.removeListener('finish', onfinish);\n    dest.removeListener('drain', ondrain);\n    dest.removeListener('error', onerror);\n    dest.removeListener('unpipe', onunpipe);\n    src.removeListener('end', onend);\n    src.removeListener('end', unpipe);\n    src.removeListener('data', ondata);\n\n    cleanedUp = true;\n\n    // if the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n  }\n\n  // If the user pushes more data while we're writing to dest then we'll end up\n  // in ondata again. However, we only want to increase awaitDrain once because\n  // dest will only emit one 'drain' event for the multiple writes.\n  // => Introduce a guard on increasing awaitDrain.\n  var increasedAwaitDrain = false;\n  src.on('data', ondata);\n  function ondata(chunk) {\n    debug('ondata');\n    increasedAwaitDrain = false;\n    var ret = dest.write(chunk);\n    if (false === ret && !increasedAwaitDrain) {\n      // If the user unpiped during `dest.write()`, it is possible\n      // to get stuck in a permanently paused state if that write\n      // also returned false.\n      // => Check whether `dest` is still a piping destination.\n      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n        debug('false write response, pause', state.awaitDrain);\n        state.awaitDrain++;\n        increasedAwaitDrain = true;\n      }\n      src.pause();\n    }\n  }\n\n  // if the dest has an error, then stop piping into it.\n  // however, don't suppress the throwing behavior for this.\n  function onerror(er) {\n    debug('onerror', er);\n    unpipe();\n    dest.removeListener('error', onerror);\n    if (EElistenerCount(dest, 'error') === 0) dest.emit('error', er);\n  }\n\n  // Make sure our error handler is attached before userland ones.\n  prependListener(dest, 'error', onerror);\n\n  // Both close and finish should trigger unpipe, but only once.\n  function onclose() {\n    dest.removeListener('finish', onfinish);\n    unpipe();\n  }\n  dest.once('close', onclose);\n  function onfinish() {\n    debug('onfinish');\n    dest.removeListener('close', onclose);\n    unpipe();\n  }\n  dest.once('finish', onfinish);\n\n  function unpipe() {\n    debug('unpipe');\n    src.unpipe(dest);\n  }\n\n  // tell the dest that it's being piped to\n  dest.emit('pipe', src);\n\n  // start the flow if it hasn't been started already.\n  if (!state.flowing) {\n    debug('pipe resume');\n    src.resume();\n  }\n\n  return dest;\n};\n\nfunction pipeOnDrain(src) {\n  return function () {\n    var state = src._readableState;\n    debug('pipeOnDrain', state.awaitDrain);\n    if (state.awaitDrain) state.awaitDrain--;\n    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {\n      state.flowing = true;\n      flow(src);\n    }\n  };\n}\n\nReadable.prototype.unpipe = function (dest) {\n  var state = this._readableState;\n  var unpipeInfo = { hasUnpiped: false };\n\n  // if we're not piping anywhere, then do nothing.\n  if (state.pipesCount === 0) return this;\n\n  // just one destination.  most common case.\n  if (state.pipesCount === 1) {\n    // passed in one, but it's not the right one.\n    if (dest && dest !== state.pipes) return this;\n\n    if (!dest) dest = state.pipes;\n\n    // got a match.\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    if (dest) dest.emit('unpipe', this, unpipeInfo);\n    return this;\n  }\n\n  // slow case. multiple pipe destinations.\n\n  if (!dest) {\n    // remove all.\n    var dests = state.pipes;\n    var len = state.pipesCount;\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n\n    for (var i = 0; i < len; i++) {\n      dests[i].emit('unpipe', this, { hasUnpiped: false });\n    }return this;\n  }\n\n  // try to find the right one.\n  var index = indexOf(state.pipes, dest);\n  if (index === -1) return this;\n\n  state.pipes.splice(index, 1);\n  state.pipesCount -= 1;\n  if (state.pipesCount === 1) state.pipes = state.pipes[0];\n\n  dest.emit('unpipe', this, unpipeInfo);\n\n  return this;\n};\n\n// set up data events if they are asked for\n// Ensure readable listeners eventually get something\nReadable.prototype.on = function (ev, fn) {\n  var res = Stream.prototype.on.call(this, ev, fn);\n\n  if (ev === 'data') {\n    // Start flowing on next tick if stream isn't explicitly paused\n    if (this._readableState.flowing !== false) this.resume();\n  } else if (ev === 'readable') {\n    var state = this._readableState;\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true;\n      state.emittedReadable = false;\n      if (!state.reading) {\n        pna.nextTick(nReadingNextTick, this);\n      } else if (state.length) {\n        emitReadable(this);\n      }\n    }\n  }\n\n  return res;\n};\nReadable.prototype.addListener = Readable.prototype.on;\n\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0');\n  self.read(0);\n}\n\n// pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\nReadable.prototype.resume = function () {\n  var state = this._readableState;\n  if (!state.flowing) {\n    debug('resume');\n    state.flowing = true;\n    resume(this, state);\n  }\n  return this;\n};\n\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true;\n    pna.nextTick(resume_, stream, state);\n  }\n}\n\nfunction resume_(stream, state) {\n  if (!state.reading) {\n    debug('resume read 0');\n    stream.read(0);\n  }\n\n  state.resumeScheduled = false;\n  state.awaitDrain = 0;\n  stream.emit('resume');\n  flow(stream);\n  if (state.flowing && !state.reading) stream.read(0);\n}\n\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing);\n  if (false !== this._readableState.flowing) {\n    debug('pause');\n    this._readableState.flowing = false;\n    this.emit('pause');\n  }\n  return this;\n};\n\nfunction flow(stream) {\n  var state = stream._readableState;\n  debug('flow', state.flowing);\n  while (state.flowing && stream.read() !== null) {}\n}\n\n// wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\nReadable.prototype.wrap = function (stream) {\n  var _this = this;\n\n  var state = this._readableState;\n  var paused = false;\n\n  stream.on('end', function () {\n    debug('wrapped end');\n    if (state.decoder && !state.ended) {\n      var chunk = state.decoder.end();\n      if (chunk && chunk.length) _this.push(chunk);\n    }\n\n    _this.push(null);\n  });\n\n  stream.on('data', function (chunk) {\n    debug('wrapped data');\n    if (state.decoder) chunk = state.decoder.write(chunk);\n\n    // don't skip over falsy values in objectMode\n    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;\n\n    var ret = _this.push(chunk);\n    if (!ret) {\n      paused = true;\n      stream.pause();\n    }\n  });\n\n  // proxy all the other methods.\n  // important when wrapping filters and duplexes.\n  for (var i in stream) {\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = function (method) {\n        return function () {\n          return stream[method].apply(stream, arguments);\n        };\n      }(i);\n    }\n  }\n\n  // proxy certain important events.\n  for (var n = 0; n < kProxyEvents.length; n++) {\n    stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n  }\n\n  // when we try to consume some more bytes, simply unpause the\n  // underlying stream.\n  this._read = function (n) {\n    debug('wrapped _read', n);\n    if (paused) {\n      paused = false;\n      stream.resume();\n    }\n  };\n\n  return this;\n};\n\nObject.defineProperty(Readable.prototype, 'readableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function () {\n    return this._readableState.highWaterMark;\n  }\n});\n\n// exposed for testing purposes only.\nReadable._fromList = fromList;\n\n// Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromList(n, state) {\n  // nothing buffered\n  if (state.length === 0) return null;\n\n  var ret;\n  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {\n    // read it all, truncate the list\n    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.head.data;else ret = state.buffer.concat(state.length);\n    state.buffer.clear();\n  } else {\n    // read part of list\n    ret = fromListPartial(n, state.buffer, state.decoder);\n  }\n\n  return ret;\n}\n\n// Extracts only enough buffered data to satisfy the amount requested.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromListPartial(n, list, hasStrings) {\n  var ret;\n  if (n < list.head.data.length) {\n    // slice is the same for buffers and strings\n    ret = list.head.data.slice(0, n);\n    list.head.data = list.head.data.slice(n);\n  } else if (n === list.head.data.length) {\n    // first chunk is a perfect match\n    ret = list.shift();\n  } else {\n    // result spans more than one buffer\n    ret = hasStrings ? copyFromBufferString(n, list) : copyFromBuffer(n, list);\n  }\n  return ret;\n}\n\n// Copies a specified amount of characters from the list of buffered data\n// chunks.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction copyFromBufferString(n, list) {\n  var p = list.head;\n  var c = 1;\n  var ret = p.data;\n  n -= ret.length;\n  while (p = p.next) {\n    var str = p.data;\n    var nb = n > str.length ? str.length : n;\n    if (nb === str.length) ret += str;else ret += str.slice(0, n);\n    n -= nb;\n    if (n === 0) {\n      if (nb === str.length) {\n        ++c;\n        if (p.next) list.head = p.next;else list.head = list.tail = null;\n      } else {\n        list.head = p;\n        p.data = str.slice(nb);\n      }\n      break;\n    }\n    ++c;\n  }\n  list.length -= c;\n  return ret;\n}\n\n// Copies a specified amount of bytes from the list of buffered data chunks.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction copyFromBuffer(n, list) {\n  var ret = Buffer.allocUnsafe(n);\n  var p = list.head;\n  var c = 1;\n  p.data.copy(ret);\n  n -= p.data.length;\n  while (p = p.next) {\n    var buf = p.data;\n    var nb = n > buf.length ? buf.length : n;\n    buf.copy(ret, ret.length - n, 0, nb);\n    n -= nb;\n    if (n === 0) {\n      if (nb === buf.length) {\n        ++c;\n        if (p.next) list.head = p.next;else list.head = list.tail = null;\n      } else {\n        list.head = p;\n        p.data = buf.slice(nb);\n      }\n      break;\n    }\n    ++c;\n  }\n  list.length -= c;\n  return ret;\n}\n\nfunction endReadable(stream) {\n  var state = stream._readableState;\n\n  // If we get here before consuming all the bytes, then that is a\n  // bug in node.  Should never happen.\n  if (state.length > 0) throw new Error('\"endReadable()\" called on non-empty stream');\n\n  if (!state.endEmitted) {\n    state.ended = true;\n    pna.nextTick(endReadableNT, state, stream);\n  }\n}\n\nfunction endReadableNT(state, stream) {\n  // Check that we didn't get one last unshift.\n  if (!state.endEmitted && state.length === 0) {\n    state.endEmitted = true;\n    stream.readable = false;\n    stream.emit('end');\n  }\n}\n\nfunction indexOf(xs, x) {\n  for (var i = 0, l = xs.length; i < l; i++) {\n    if (xs[i] === x) return i;\n  }\n  return -1;\n}"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAIzC,eAAe,GAEf,IAAI;AACJ,gBAAgB,GAEhB,OAAO,OAAO,GAAG;AAEjB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,SAAS,aAAa,GAAG;AAEzB,eAAe,GACf,IAAI,KAAK,uEAAkB,YAAY;AAEvC,IAAI,kBAAkB,SAAU,OAAO,EAAE,IAAI;IAC3C,OAAO,QAAQ,SAAS,CAAC,MAAM,MAAM;AACvC;AACA,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,gBAAgB,GAEhB,eAAe,GAEf,IAAI,SAAS,wHAAuB,MAAM;AAC1C,IAAI,gBAAgB,CAAC,kGAAyC,uBAAgF,EAAE,UAAU,IAAI,YAAa;AAC3K,SAAS,oBAAoB,KAAK;IAChC,OAAO,OAAO,IAAI,CAAC;AACrB;AACA,SAAS,cAAc,GAAG;IACxB,OAAO,OAAO,QAAQ,CAAC,QAAQ,eAAe;AAChD;AAEA,gBAAgB,GAEhB,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,eAAe,GACf,IAAI;AACJ,IAAI,QAAQ,KAAK;AACjB,IAAI,aAAa,UAAU,QAAQ,EAAE;IACnC,QAAQ,UAAU,QAAQ,CAAC;AAC7B,OAAO;IACL,QAAQ,YAAa;AACvB;AACA,gBAAgB,GAEhB,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,KAAK,QAAQ,CAAC,UAAU;AAExB,IAAI,eAAe;IAAC;IAAS;IAAS;IAAW;IAAS;CAAS;AAEnE,SAAS,gBAAgB,OAAO,EAAE,KAAK,EAAE,EAAE;IACzC,iEAAiE;IACjE,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,eAAe,KAAK,YAAY,OAAO,QAAQ,eAAe,CAAC,OAAO;IAEzF,4EAA4E;IAC5E,2EAA2E;IAC3E,yEAAyE;IACzE,4EAA4E;IAC5E,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,OAAO;SAAS,IAAI,QAAQ,QAAQ,OAAO,CAAC,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG;QAAC;QAAI,QAAQ,OAAO,CAAC,MAAM;KAAC;AAChN;AAEA,SAAS,cAAc,OAAO,EAAE,MAAM;IACpC,SAAS;IAET,UAAU,WAAW,CAAC;IAEtB,2DAA2D;IAC3D,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,2EAA2E;IAC3E,IAAI,WAAW,kBAAkB;IAEjC,2DAA2D;IAC3D,wDAAwD;IACxD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;IAEtC,IAAI,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,QAAQ,kBAAkB;IAE/E,iEAAiE;IACjE,uEAAuE;IACvE,IAAI,MAAM,QAAQ,aAAa;IAC/B,IAAI,cAAc,QAAQ,qBAAqB;IAC/C,IAAI,aAAa,IAAI,CAAC,UAAU,GAAG,KAAK,KAAK;IAE7C,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG;SAAS,IAAI,YAAY,CAAC,eAAe,gBAAgB,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG;SAAiB,IAAI,CAAC,aAAa,GAAG;IAEnK,gBAAgB;IAChB,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,aAAa;IAElD,6EAA6E;IAC7E,iEAAiE;IACjE,gBAAgB;IAChB,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,OAAO,GAAG;IAEf,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,yCAAyC;IACzC,IAAI,CAAC,IAAI,GAAG;IAEZ,qDAAqD;IACrD,mDAAmD;IACnD,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,iBAAiB,GAAG;IACzB,IAAI,CAAC,eAAe,GAAG;IAEvB,wBAAwB;IACxB,IAAI,CAAC,SAAS,GAAG;IAEjB,sEAAsE;IACtE,6DAA6D;IAC7D,uDAAuD;IACvD,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI;IAElD,oEAAoE;IACpE,IAAI,CAAC,UAAU,GAAG;IAElB,8CAA8C;IAC9C,IAAI,CAAC,WAAW,GAAG;IAEnB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,QAAQ,QAAQ,EAAE;QACpB,IAAI,CAAC,eAAe,gBAAgB;;;;;;;;;WAAQ,mBAAmB,aAAa;QAC5E,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,QAAQ,QAAQ;QACjD,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;IAClC;AACF;AAEA,SAAS,SAAS,OAAO;IACvB,SAAS;IAET,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,GAAG,OAAO,IAAI,SAAS;IAErD,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,SAAS,IAAI;IAErD,SAAS;IACT,IAAI,CAAC,QAAQ,GAAG;IAEhB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI;QAEjE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;IAC5E;IAEA,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,aAAa;IACrD,KAAK;QACH,IAAI,IAAI,CAAC,cAAc,KAAK,WAAW;YACrC,OAAO;QACT;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS;IACtC;IACA,KAAK,SAAU,KAAK;QAClB,oCAAoC;QACpC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;QACF;QAEA,iDAAiD;QACjD,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;IAClC;AACF;AAEA,SAAS,SAAS,CAAC,OAAO,GAAG,YAAY,OAAO;AAChD,SAAS,SAAS,CAAC,UAAU,GAAG,YAAY,SAAS;AACrD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC7C,IAAI,CAAC,IAAI,CAAC;IACV,GAAG;AACL;AAEA,mDAAmD;AACnD,+DAA+D;AAC/D,6DAA6D;AAC7D,qBAAqB;AACrB,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,QAAQ;IACjD,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI;IAEJ,IAAI,CAAC,MAAM,UAAU,EAAE;QACrB,IAAI,OAAO,UAAU,UAAU;YAC7B,WAAW,YAAY,MAAM,eAAe;YAC5C,IAAI,aAAa,MAAM,QAAQ,EAAE;gBAC/B,QAAQ,OAAO,IAAI,CAAC,OAAO;gBAC3B,WAAW;YACb;YACA,iBAAiB;QACnB;IACF,OAAO;QACL,iBAAiB;IACnB;IAEA,OAAO,iBAAiB,IAAI,EAAE,OAAO,UAAU,OAAO;AACxD;AAEA,8DAA8D;AAC9D,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK;IAC1C,OAAO,iBAAiB,IAAI,EAAE,OAAO,MAAM,MAAM;AACnD;AAEA,SAAS,iBAAiB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc;IAC3E,IAAI,QAAQ,OAAO,cAAc;IACjC,IAAI,UAAU,MAAM;QAClB,MAAM,OAAO,GAAG;QAChB,WAAW,QAAQ;IACrB,OAAO;QACL,IAAI;QACJ,IAAI,CAAC,gBAAgB,KAAK,aAAa,OAAO;QAC9C,IAAI,IAAI;YACN,OAAO,IAAI,CAAC,SAAS;QACvB,OAAO,IAAI,MAAM,UAAU,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YACxD,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,UAAU,IAAI,OAAO,cAAc,CAAC,WAAW,OAAO,SAAS,EAAE;gBACvG,QAAQ,oBAAoB;YAC9B;YAEA,IAAI,YAAY;gBACd,IAAI,MAAM,UAAU,EAAE,OAAO,IAAI,CAAC,SAAS,IAAI,MAAM;qBAA0C,SAAS,QAAQ,OAAO,OAAO;YAChI,OAAO,IAAI,MAAM,KAAK,EAAE;gBACtB,OAAO,IAAI,CAAC,SAAS,IAAI,MAAM;YACjC,OAAO;gBACL,MAAM,OAAO,GAAG;gBAChB,IAAI,MAAM,OAAO,IAAI,CAAC,UAAU;oBAC9B,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;oBAC5B,IAAI,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG,SAAS,QAAQ,OAAO,OAAO;yBAAY,cAAc,QAAQ;gBAC/G,OAAO;oBACL,SAAS,QAAQ,OAAO,OAAO;gBACjC;YACF;QACF,OAAO,IAAI,CAAC,YAAY;YACtB,MAAM,OAAO,GAAG;QAClB;IACF;IAEA,OAAO,aAAa;AACtB;AAEA,SAAS,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;IAChD,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,EAAE;QACtD,OAAO,IAAI,CAAC,QAAQ;QACpB,OAAO,IAAI,CAAC;IACd,OAAO;QACL,0BAA0B;QAC1B,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACnD,IAAI,YAAY,MAAM,MAAM,CAAC,OAAO,CAAC;aAAY,MAAM,MAAM,CAAC,IAAI,CAAC;QAEnE,IAAI,MAAM,YAAY,EAAE,aAAa;IACvC;IACA,cAAc,QAAQ;AACxB;AAEA,SAAS,aAAa,KAAK,EAAE,KAAK;IAChC,IAAI;IACJ,IAAI,CAAC,cAAc,UAAU,OAAO,UAAU,YAAY,UAAU,aAAa,CAAC,MAAM,UAAU,EAAE;QAClG,KAAK,IAAI,UAAU;IACrB;IACA,OAAO;AACT;AAEA,8DAA8D;AAC9D,kDAAkD;AAClD,yDAAyD;AACzD,qDAAqD;AACrD,kEAAkE;AAClE,oEAAoE;AACpE,sCAAsC;AACtC,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,YAAY,IAAI,MAAM,MAAM,GAAG,MAAM,aAAa,IAAI,MAAM,MAAM,KAAK,CAAC;AACxG;AAEA,SAAS,SAAS,CAAC,QAAQ,GAAG;IAC5B,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK;AACzC;AAEA,2BAA2B;AAC3B,SAAS,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG;IAC5C,IAAI,CAAC,eAAe,gBAAgB;;;;;;;;;OAAQ,mBAAmB,aAAa;IAC5E,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,cAAc;IAChD,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;IAC/B,OAAO,IAAI;AACb;AAEA,4BAA4B;AAC5B,IAAI,UAAU;AACd,SAAS,wBAAwB,CAAC;IAChC,IAAI,KAAK,SAAS;QAChB,IAAI;IACN,OAAO;QACL,2EAA2E;QAC3E,eAAe;QACf;QACA,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX;IACF;IACA,OAAO;AACT;AAEA,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,cAAc,CAAC,EAAE,KAAK;IAC7B,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,OAAO;IACxD,IAAI,MAAM,UAAU,EAAE,OAAO;IAC7B,IAAI,MAAM,GAAG;QACX,iCAAiC;QACjC,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;aAAM,OAAO,MAAM,MAAM;IAClG;IACA,qEAAqE;IACrE,IAAI,IAAI,MAAM,aAAa,EAAE,MAAM,aAAa,GAAG,wBAAwB;IAC3E,IAAI,KAAK,MAAM,MAAM,EAAE,OAAO;IAC9B,oBAAoB;IACpB,IAAI,CAAC,MAAM,KAAK,EAAE;QAChB,MAAM,YAAY,GAAG;QACrB,OAAO;IACT;IACA,OAAO,MAAM,MAAM;AACrB;AAEA,oEAAoE;AACpE,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,CAAC;IACnC,MAAM,QAAQ;IACd,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,QAAQ;IAEZ,IAAI,MAAM,GAAG,MAAM,eAAe,GAAG;IAErC,6DAA6D;IAC7D,gEAAgE;IAChE,oCAAoC;IACpC,IAAI,MAAM,KAAK,MAAM,YAAY,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,aAAa,IAAI,MAAM,KAAK,GAAG;QACzF,MAAM,sBAAsB,MAAM,MAAM,EAAE,MAAM,KAAK;QACrD,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,YAAY,IAAI;aAAO,aAAa,IAAI;QAC/E,OAAO;IACT;IAEA,IAAI,cAAc,GAAG;IAErB,0DAA0D;IAC1D,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,YAAY,IAAI;QACxC,OAAO;IACT;IAEA,oDAAoD;IACpD,4DAA4D;IAC5D,6DAA6D;IAC7D,6DAA6D;IAC7D,2DAA2D;IAC3D,iCAAiC;IACjC,EAAE;IACF,qBAAqB;IACrB,6DAA6D;IAC7D,0BAA0B;IAC1B,EAAE;IACF,oEAAoE;IACpE,kEAAkE;IAClE,kEAAkE;IAClE,mEAAmE;IACnE,sCAAsC;IACtC,qEAAqE;IACrE,sEAAsE;IACtE,kBAAkB;IAClB,EAAE;IACF,sEAAsE;IAEtE,gEAAgE;IAChE,IAAI,SAAS,MAAM,YAAY;IAC/B,MAAM,iBAAiB;IAEvB,wEAAwE;IACxE,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,IAAI,MAAM,aAAa,EAAE;QAChE,SAAS;QACT,MAAM,8BAA8B;IACtC;IAEA,uEAAuE;IACvE,kCAAkC;IAClC,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,EAAE;QAChC,SAAS;QACT,MAAM,oBAAoB;IAC5B,OAAO,IAAI,QAAQ;QACjB,MAAM;QACN,MAAM,OAAO,GAAG;QAChB,MAAM,IAAI,GAAG;QACb,oEAAoE;QACpE,IAAI,MAAM,MAAM,KAAK,GAAG,MAAM,YAAY,GAAG;QAC7C,4BAA4B;QAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,aAAa;QAC9B,MAAM,IAAI,GAAG;QACb,oEAAoE;QACpE,sEAAsE;QACtE,IAAI,CAAC,MAAM,OAAO,EAAE,IAAI,cAAc,OAAO;IAC/C;IAEA,IAAI;IACJ,IAAI,IAAI,GAAG,MAAM,SAAS,GAAG;SAAY,MAAM;IAE/C,IAAI,QAAQ,MAAM;QAChB,MAAM,YAAY,GAAG;QACrB,IAAI;IACN,OAAO;QACL,MAAM,MAAM,IAAI;IAClB;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,yDAAyD;QACzD,oDAAoD;QACpD,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAY,GAAG;QAEvC,sEAAsE;QACtE,IAAI,UAAU,KAAK,MAAM,KAAK,EAAE,YAAY,IAAI;IAClD;IAEA,IAAI,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ;IAEpC,OAAO;AACT;AAEA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,IAAI,MAAM,KAAK,EAAE;IACjB,IAAI,MAAM,OAAO,EAAE;QACjB,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG;QAC7B,IAAI,SAAS,MAAM,MAAM,EAAE;YACzB,MAAM,MAAM,CAAC,IAAI,CAAC;YAClB,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACrD;IACF;IACA,MAAM,KAAK,GAAG;IAEd,sDAAsD;IACtD,aAAa;AACf;AAEA,wEAAwE;AACxE,qEAAqE;AACrE,uDAAuD;AACvD,SAAS,aAAa,MAAM;IAC1B,IAAI,QAAQ,OAAO,cAAc;IACjC,MAAM,YAAY,GAAG;IACrB,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,MAAM,gBAAgB,MAAM,OAAO;QACnC,MAAM,eAAe,GAAG;QACxB,IAAI,MAAM,IAAI,EAAE,IAAI,QAAQ,CAAC,eAAe;aAAa,cAAc;IACzE;AACF;AAEA,SAAS,cAAc,MAAM;IAC3B,MAAM;IACN,OAAO,IAAI,CAAC;IACZ,KAAK;AACP;AAEA,oEAAoE;AACpE,mEAAmE;AACnE,iEAAiE;AACjE,oBAAoB;AACpB,iEAAiE;AACjE,wDAAwD;AACxD,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,CAAC,MAAM,WAAW,EAAE;QACtB,MAAM,WAAW,GAAG;QACpB,IAAI,QAAQ,CAAC,gBAAgB,QAAQ;IACvC;AACF;AAEA,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,IAAI,MAAM,MAAM,MAAM;IACtB,MAAO,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,MAAM,GAAG,MAAM,aAAa,CAAE;QAC7F,MAAM;QACN,OAAO,IAAI,CAAC;QACZ,IAAI,QAAQ,MAAM,MAAM,EAEtB;aAAW,MAAM,MAAM,MAAM;IACjC;IACA,MAAM,WAAW,GAAG;AACtB;AAEA,yEAAyE;AACzE,kDAAkD;AAClD,qEAAqE;AACrE,8CAA8C;AAC9C,SAAS,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC;IACpC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM;AAC/B;AAEA,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,QAAQ;IAChD,IAAI,MAAM,IAAI;IACd,IAAI,QAAQ,IAAI,CAAC,cAAc;IAE/B,OAAQ,MAAM,UAAU;QACtB,KAAK;YACH,MAAM,KAAK,GAAG;YACd;QACF,KAAK;YACH,MAAM,KAAK,GAAG;gBAAC,MAAM,KAAK;gBAAE;aAAK;YACjC;QACF;YACE,MAAM,KAAK,CAAC,IAAI,CAAC;YACjB;IACJ;IACA,MAAM,UAAU,IAAI;IACpB,MAAM,yBAAyB,MAAM,UAAU,EAAE;IAEjD,IAAI,QAAQ,CAAC,CAAC,YAAY,SAAS,GAAG,KAAK,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,SAAS,QAAQ,MAAM;IAEvG,IAAI,QAAQ,QAAQ,QAAQ;IAC5B,IAAI,MAAM,UAAU,EAAE,IAAI,QAAQ,CAAC;SAAY,IAAI,IAAI,CAAC,OAAO;IAE/D,KAAK,EAAE,CAAC,UAAU;IAClB,SAAS,SAAS,QAAQ,EAAE,UAAU;QACpC,MAAM;QACN,IAAI,aAAa,KAAK;YACpB,IAAI,cAAc,WAAW,UAAU,KAAK,OAAO;gBACjD,WAAW,UAAU,GAAG;gBACxB;YACF;QACF;IACF;IAEA,SAAS;QACP,MAAM;QACN,KAAK,GAAG;IACV;IAEA,0DAA0D;IAC1D,4DAA4D;IAC5D,2DAA2D;IAC3D,YAAY;IACZ,IAAI,UAAU,YAAY;IAC1B,KAAK,EAAE,CAAC,SAAS;IAEjB,IAAI,YAAY;IAChB,SAAS;QACP,MAAM;QACN,iDAAiD;QACjD,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,QAAQ;QAE3B,YAAY;QAEZ,uDAAuD;QACvD,yDAAyD;QACzD,iBAAiB;QACjB,6DAA6D;QAC7D,6DAA6D;QAC7D,IAAI,MAAM,UAAU,IAAI,CAAC,CAAC,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,SAAS,GAAG;IACnF;IAEA,6EAA6E;IAC7E,6EAA6E;IAC7E,iEAAiE;IACjE,iDAAiD;IACjD,IAAI,sBAAsB;IAC1B,IAAI,EAAE,CAAC,QAAQ;IACf,SAAS,OAAO,KAAK;QACnB,MAAM;QACN,sBAAsB;QACtB,IAAI,MAAM,KAAK,KAAK,CAAC;QACrB,IAAI,UAAU,OAAO,CAAC,qBAAqB;YACzC,4DAA4D;YAC5D,2DAA2D;YAC3D,uBAAuB;YACvB,yDAAyD;YACzD,IAAI,CAAC,MAAM,UAAU,KAAK,KAAK,MAAM,KAAK,KAAK,QAAQ,MAAM,UAAU,GAAG,KAAK,QAAQ,MAAM,KAAK,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,WAAW;gBAC/H,MAAM,+BAA+B,MAAM,UAAU;gBACrD,MAAM,UAAU;gBAChB,sBAAsB;YACxB;YACA,IAAI,KAAK;QACX;IACF;IAEA,sDAAsD;IACtD,0DAA0D;IAC1D,SAAS,QAAQ,EAAE;QACjB,MAAM,WAAW;QACjB;QACA,KAAK,cAAc,CAAC,SAAS;QAC7B,IAAI,gBAAgB,MAAM,aAAa,GAAG,KAAK,IAAI,CAAC,SAAS;IAC/D;IAEA,gEAAgE;IAChE,gBAAgB,MAAM,SAAS;IAE/B,8DAA8D;IAC9D,SAAS;QACP,KAAK,cAAc,CAAC,UAAU;QAC9B;IACF;IACA,KAAK,IAAI,CAAC,SAAS;IACnB,SAAS;QACP,MAAM;QACN,KAAK,cAAc,CAAC,SAAS;QAC7B;IACF;IACA,KAAK,IAAI,CAAC,UAAU;IAEpB,SAAS;QACP,MAAM;QACN,IAAI,MAAM,CAAC;IACb;IAEA,yCAAyC;IACzC,KAAK,IAAI,CAAC,QAAQ;IAElB,oDAAoD;IACpD,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;QACN,IAAI,MAAM;IACZ;IAEA,OAAO;AACT;AAEA,SAAS,YAAY,GAAG;IACtB,OAAO;QACL,IAAI,QAAQ,IAAI,cAAc;QAC9B,MAAM,eAAe,MAAM,UAAU;QACrC,IAAI,MAAM,UAAU,EAAE,MAAM,UAAU;QACtC,IAAI,MAAM,UAAU,KAAK,KAAK,gBAAgB,KAAK,SAAS;YAC1D,MAAM,OAAO,GAAG;YAChB,KAAK;QACP;IACF;AACF;AAEA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI;IACxC,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,aAAa;QAAE,YAAY;IAAM;IAErC,iDAAiD;IACjD,IAAI,MAAM,UAAU,KAAK,GAAG,OAAO,IAAI;IAEvC,2CAA2C;IAC3C,IAAI,MAAM,UAAU,KAAK,GAAG;QAC1B,6CAA6C;QAC7C,IAAI,QAAQ,SAAS,MAAM,KAAK,EAAE,OAAO,IAAI;QAE7C,IAAI,CAAC,MAAM,OAAO,MAAM,KAAK;QAE7B,eAAe;QACf,MAAM,KAAK,GAAG;QACd,MAAM,UAAU,GAAG;QACnB,MAAM,OAAO,GAAG;QAChB,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;QACpC,OAAO,IAAI;IACb;IAEA,yCAAyC;IAEzC,IAAI,CAAC,MAAM;QACT,cAAc;QACd,IAAI,QAAQ,MAAM,KAAK;QACvB,IAAI,MAAM,MAAM,UAAU;QAC1B,MAAM,KAAK,GAAG;QACd,MAAM,UAAU,GAAG;QACnB,MAAM,OAAO,GAAG;QAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;gBAAE,YAAY;YAAM;QACpD;QAAC,OAAO,IAAI;IACd;IAEA,6BAA6B;IAC7B,IAAI,QAAQ,QAAQ,MAAM,KAAK,EAAE;IACjC,IAAI,UAAU,CAAC,GAAG,OAAO,IAAI;IAE7B,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO;IAC1B,MAAM,UAAU,IAAI;IACpB,IAAI,MAAM,UAAU,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE;IAExD,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;IAE1B,OAAO,IAAI;AACb;AAEA,2CAA2C;AAC3C,qDAAqD;AACrD,SAAS,SAAS,CAAC,EAAE,GAAG,SAAU,EAAE,EAAE,EAAE;IACtC,IAAI,MAAM,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;IAE7C,IAAI,OAAO,QAAQ;QACjB,+DAA+D;QAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,MAAM;IACxD,OAAO,IAAI,OAAO,YAAY;QAC5B,IAAI,QAAQ,IAAI,CAAC,cAAc;QAC/B,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,iBAAiB,EAAE;YACjD,MAAM,iBAAiB,GAAG,MAAM,YAAY,GAAG;YAC/C,MAAM,eAAe,GAAG;YACxB,IAAI,CAAC,MAAM,OAAO,EAAE;gBAClB,IAAI,QAAQ,CAAC,kBAAkB,IAAI;YACrC,OAAO,IAAI,MAAM,MAAM,EAAE;gBACvB,aAAa,IAAI;YACnB;QACF;IACF;IAEA,OAAO;AACT;AACA,SAAS,SAAS,CAAC,WAAW,GAAG,SAAS,SAAS,CAAC,EAAE;AAEtD,SAAS,iBAAiB,IAAI;IAC5B,MAAM;IACN,KAAK,IAAI,CAAC;AACZ;AAEA,sEAAsE;AACtE,oDAAoD;AACpD,SAAS,SAAS,CAAC,MAAM,GAAG;IAC1B,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;QACN,MAAM,OAAO,GAAG;QAChB,OAAO,IAAI,EAAE;IACf;IACA,OAAO,IAAI;AACb;AAEA,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,MAAM,eAAe,GAAG;QACxB,IAAI,QAAQ,CAAC,SAAS,QAAQ;IAChC;AACF;AAEA,SAAS,QAAQ,MAAM,EAAE,KAAK;IAC5B,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe,GAAG;IACxB,MAAM,UAAU,GAAG;IACnB,OAAO,IAAI,CAAC;IACZ,KAAK;IACL,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE,OAAO,IAAI,CAAC;AACnD;AAEA,SAAS,SAAS,CAAC,KAAK,GAAG;IACzB,MAAM,yBAAyB,IAAI,CAAC,cAAc,CAAC,OAAO;IAC1D,IAAI,UAAU,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;QACzC,MAAM;QACN,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC9B,IAAI,CAAC,IAAI,CAAC;IACZ;IACA,OAAO,IAAI;AACb;AAEA,SAAS,KAAK,MAAM;IAClB,IAAI,QAAQ,OAAO,cAAc;IACjC,MAAM,QAAQ,MAAM,OAAO;IAC3B,MAAO,MAAM,OAAO,IAAI,OAAO,IAAI,OAAO,KAAM,CAAC;AACnD;AAEA,qDAAqD;AACrD,uDAAuD;AACvD,6CAA6C;AAC7C,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM;IACxC,IAAI,QAAQ,IAAI;IAEhB,IAAI,QAAQ,IAAI,CAAC,cAAc;IAC/B,IAAI,SAAS;IAEb,OAAO,EAAE,CAAC,OAAO;QACf,MAAM;QACN,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE;YACjC,IAAI,QAAQ,MAAM,OAAO,CAAC,GAAG;YAC7B,IAAI,SAAS,MAAM,MAAM,EAAE,MAAM,IAAI,CAAC;QACxC;QAEA,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,EAAE,CAAC,QAAQ,SAAU,KAAK;QAC/B,MAAM;QACN,IAAI,MAAM,OAAO,EAAE,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;QAE/C,6CAA6C;QAC7C,IAAI,MAAM,UAAU,IAAI,CAAC,UAAU,QAAQ,UAAU,SAAS,GAAG;aAAY,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,MAAM,GAAG;QAEjI,IAAI,MAAM,MAAM,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK;YACR,SAAS;YACT,OAAO,KAAK;QACd;IACF;IAEA,+BAA+B;IAC/B,gDAAgD;IAChD,IAAK,IAAI,KAAK,OAAQ;QACpB,IAAI,IAAI,CAAC,EAAE,KAAK,aAAa,OAAO,MAAM,CAAC,EAAE,KAAK,YAAY;YAC5D,IAAI,CAAC,EAAE,GAAG,SAAU,MAAM;gBACxB,OAAO;oBACL,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ;gBACtC;YACF,EAAE;QACJ;IACF;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE;IACjE;IAEA,6DAA6D;IAC7D,qBAAqB;IACrB,IAAI,CAAC,KAAK,GAAG,SAAU,CAAC;QACtB,MAAM,iBAAiB;QACvB,IAAI,QAAQ;YACV,SAAS;YACT,OAAO,MAAM;QACf;IACF;IAEA,OAAO,IAAI;AACb;AAEA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,yBAAyB;IACjE,qDAAqD;IACrD,mDAAmD;IACnD,qBAAqB;IACrB,YAAY;IACZ,KAAK;QACH,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;IAC1C;AACF;AAEA,qCAAqC;AACrC,SAAS,SAAS,GAAG;AAErB,8CAA8C;AAC9C,iEAAiE;AACjE,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,SAAS,CAAC,EAAE,KAAK;IACxB,mBAAmB;IACnB,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,IAAI;IACJ,IAAI,MAAM,UAAU,EAAE,MAAM,MAAM,MAAM,CAAC,KAAK;SAAQ,IAAI,CAAC,KAAK,KAAK,MAAM,MAAM,EAAE;QACjF,iCAAiC;QACjC,IAAI,MAAM,OAAO,EAAE,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC;aAAS,IAAI,MAAM,MAAM,CAAC,MAAM,KAAK,GAAG,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI;aAAM,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,MAAM;QAC3J,MAAM,MAAM,CAAC,KAAK;IACpB,OAAO;QACL,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,MAAM,MAAM,EAAE,MAAM,OAAO;IACtD;IAEA,OAAO;AACT;AAEA,sEAAsE;AACtE,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,gBAAgB,CAAC,EAAE,IAAI,EAAE,UAAU;IAC1C,IAAI;IACJ,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAC7B,4CAA4C;QAC5C,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IACxC,OAAO,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QACtC,iCAAiC;QACjC,MAAM,KAAK,KAAK;IAClB,OAAO;QACL,oCAAoC;QACpC,MAAM,aAAa,qBAAqB,GAAG,QAAQ,eAAe,GAAG;IACvE;IACA,OAAO;AACT;AAEA,yEAAyE;AACzE,UAAU;AACV,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,qBAAqB,CAAC,EAAE,IAAI;IACnC,IAAI,IAAI,KAAK,IAAI;IACjB,IAAI,IAAI;IACR,IAAI,MAAM,EAAE,IAAI;IAChB,KAAK,IAAI,MAAM;IACf,MAAO,IAAI,EAAE,IAAI,CAAE;QACjB,IAAI,MAAM,EAAE,IAAI;QAChB,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG;QACvC,IAAI,OAAO,IAAI,MAAM,EAAE,OAAO;aAAS,OAAO,IAAI,KAAK,CAAC,GAAG;QAC3D,KAAK;QACL,IAAI,MAAM,GAAG;YACX,IAAI,OAAO,IAAI,MAAM,EAAE;gBACrB,EAAE;gBACF,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,GAAG,EAAE,IAAI;qBAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;YAC9D,OAAO;gBACL,KAAK,IAAI,GAAG;gBACZ,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC;YACrB;YACA;QACF;QACA,EAAE;IACJ;IACA,KAAK,MAAM,IAAI;IACf,OAAO;AACT;AAEA,4EAA4E;AAC5E,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,eAAe,CAAC,EAAE,IAAI;IAC7B,IAAI,MAAM,OAAO,WAAW,CAAC;IAC7B,IAAI,IAAI,KAAK,IAAI;IACjB,IAAI,IAAI;IACR,EAAE,IAAI,CAAC,IAAI,CAAC;IACZ,KAAK,EAAE,IAAI,CAAC,MAAM;IAClB,MAAO,IAAI,EAAE,IAAI,CAAE;QACjB,IAAI,MAAM,EAAE,IAAI;QAChB,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,GAAG;QACvC,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,GAAG,GAAG,GAAG;QACjC,KAAK;QACL,IAAI,MAAM,GAAG;YACX,IAAI,OAAO,IAAI,MAAM,EAAE;gBACrB,EAAE;gBACF,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,GAAG,EAAE,IAAI;qBAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;YAC9D,OAAO;gBACL,KAAK,IAAI,GAAG;gBACZ,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC;YACrB;YACA;QACF;QACA,EAAE;IACJ;IACA,KAAK,MAAM,IAAI;IACf,OAAO;AACT;AAEA,SAAS,YAAY,MAAM;IACzB,IAAI,QAAQ,OAAO,cAAc;IAEjC,gEAAgE;IAChE,qCAAqC;IACrC,IAAI,MAAM,MAAM,GAAG,GAAG,MAAM,IAAI,MAAM;IAEtC,IAAI,CAAC,MAAM,UAAU,EAAE;QACrB,MAAM,KAAK,GAAG;QACd,IAAI,QAAQ,CAAC,eAAe,OAAO;IACrC;AACF;AAEA,SAAS,cAAc,KAAK,EAAE,MAAM;IAClC,6CAA6C;IAC7C,IAAI,CAAC,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG;QAC3C,MAAM,UAAU,GAAG;QACnB,OAAO,QAAQ,GAAG;QAClB,OAAO,IAAI,CAAC;IACd;AACF;AAEA,SAAS,QAAQ,EAAE,EAAE,CAAC;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAI,GAAG,IAAK;QACzC,IAAI,EAAE,CAAC,EAAE,KAAK,GAAG,OAAO;IAC1B;IACA,OAAO,CAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/_stream_transform.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n\n'use strict';\n\nmodule.exports = Transform;\n\nvar Duplex = require('./_stream_duplex');\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\nutil.inherits(Transform, Duplex);\n\nfunction afterTransform(er, data) {\n  var ts = this._transformState;\n  ts.transforming = false;\n\n  var cb = ts.writecb;\n\n  if (!cb) {\n    return this.emit('error', new Error('write callback called multiple times'));\n  }\n\n  ts.writechunk = null;\n  ts.writecb = null;\n\n  if (data != null) // single equals check for both `null` and `undefined`\n    this.push(data);\n\n  cb(er);\n\n  var rs = this._readableState;\n  rs.reading = false;\n  if (rs.needReadable || rs.length < rs.highWaterMark) {\n    this._read(rs.highWaterMark);\n  }\n}\n\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options);\n\n  Duplex.call(this, options);\n\n  this._transformState = {\n    afterTransform: afterTransform.bind(this),\n    needTransform: false,\n    transforming: false,\n    writecb: null,\n    writechunk: null,\n    writeencoding: null\n  };\n\n  // start out asking for a readable event once data is transformed.\n  this._readableState.needReadable = true;\n\n  // we have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n  this._readableState.sync = false;\n\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform;\n\n    if (typeof options.flush === 'function') this._flush = options.flush;\n  }\n\n  // When the writable side finishes, then flush out anything remaining.\n  this.on('prefinish', prefinish);\n}\n\nfunction prefinish() {\n  var _this = this;\n\n  if (typeof this._flush === 'function') {\n    this._flush(function (er, data) {\n      done(_this, er, data);\n    });\n  } else {\n    done(this, null, null);\n  }\n}\n\nTransform.prototype.push = function (chunk, encoding) {\n  this._transformState.needTransform = false;\n  return Duplex.prototype.push.call(this, chunk, encoding);\n};\n\n// This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\nTransform.prototype._transform = function (chunk, encoding, cb) {\n  throw new Error('_transform() is not implemented');\n};\n\nTransform.prototype._write = function (chunk, encoding, cb) {\n  var ts = this._transformState;\n  ts.writecb = cb;\n  ts.writechunk = chunk;\n  ts.writeencoding = encoding;\n  if (!ts.transforming) {\n    var rs = this._readableState;\n    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n  }\n};\n\n// Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\nTransform.prototype._read = function (n) {\n  var ts = this._transformState;\n\n  if (ts.writechunk !== null && ts.writecb && !ts.transforming) {\n    ts.transforming = true;\n    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n  } else {\n    // mark that we need a transform, so that any data that comes in\n    // will get processed, now that we've asked for it.\n    ts.needTransform = true;\n  }\n};\n\nTransform.prototype._destroy = function (err, cb) {\n  var _this2 = this;\n\n  Duplex.prototype._destroy.call(this, err, function (err2) {\n    cb(err2);\n    _this2.emit('close');\n  });\n};\n\nfunction done(stream, er, data) {\n  if (er) return stream.emit('error', er);\n\n  if (data != null) // single equals check for both `null` and `undefined`\n    stream.push(data);\n\n  // if there's nothing in the write buffer, then that means\n  // that nothing more will ever be provided\n  if (stream._writableState.length) throw new Error('Calling transform done when ws.length != 0');\n\n  if (stream._transformState.transforming) throw new Error('Calling transform done when still transforming');\n\n  return stream.push(null);\n}"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,gEAAgE;AAChE,8DAA8D;AAC9D,uEAAuE;AACvE,sEAAsE;AACtE,iDAAiD;AACjD,EAAE;AACF,gEAAgE;AAChE,qEAAqE;AACrE,kEAAkE;AAClE,0DAA0D;AAC1D,EAAE;AACF,yBAAyB;AACzB,EAAE;AACF,wEAAwE;AACxE,sEAAsE;AACtE,mEAAmE;AACnE,gEAAgE;AAChE,oDAAoD;AACpD,EAAE;AACF,uEAAuE;AACvE,oEAAoE;AACpE,qEAAqE;AACrE,uEAAuE;AACvE,qEAAqE;AACrE,sEAAsE;AACtE,EAAE;AACF,sEAAsE;AACtE,0EAA0E;AAC1E,yEAAyE;AACzE,oEAAoE;AACpE,sEAAsE;AACtE,sEAAsE;AACtE,wEAAwE;AACxE,sEAAsE;AACtE,qEAAqE;AACrE,oEAAoE;AACpE,yCAAyC;AACzC,EAAE;AACF,yEAAyE;AACzE,yEAAyE;AACzE,+DAA+D;AAI/D,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,KAAK,QAAQ,CAAC,WAAW;AAEzB,SAAS,eAAe,EAAE,EAAE,IAAI;IAC9B,IAAI,KAAK,IAAI,CAAC,eAAe;IAC7B,GAAG,YAAY,GAAG;IAElB,IAAI,KAAK,GAAG,OAAO;IAEnB,IAAI,CAAC,IAAI;QACP,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM;IACtC;IAEA,GAAG,UAAU,GAAG;IAChB,GAAG,OAAO,GAAG;IAEb,IAAI,QAAQ,MACV,IAAI,CAAC,IAAI,CAAC;IAEZ,GAAG;IAEH,IAAI,KAAK,IAAI,CAAC,cAAc;IAC5B,GAAG,OAAO,GAAG;IACb,IAAI,GAAG,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG,aAAa,EAAE;QACnD,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa;IAC7B;AACF;AAEA,SAAS,UAAU,OAAO;IACxB,IAAI,CAAC,CAAC,IAAI,YAAY,SAAS,GAAG,OAAO,IAAI,UAAU;IAEvD,OAAO,IAAI,CAAC,IAAI,EAAE;IAElB,IAAI,CAAC,eAAe,GAAG;QACrB,gBAAgB,eAAe,IAAI,CAAC,IAAI;QACxC,eAAe;QACf,cAAc;QACd,SAAS;QACT,YAAY;QACZ,eAAe;IACjB;IAEA,kEAAkE;IAClE,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG;IAEnC,kEAAkE;IAClE,gEAAgE;IAChE,mBAAmB;IACnB,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG;IAE3B,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,SAAS,KAAK,YAAY,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;QAEhF,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IACtE;IAEA,sEAAsE;IACtE,IAAI,CAAC,EAAE,CAAC,aAAa;AACvB;AAEA,SAAS;IACP,IAAI,QAAQ,IAAI;IAEhB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY;QACrC,IAAI,CAAC,MAAM,CAAC,SAAU,EAAE,EAAE,IAAI;YAC5B,KAAK,OAAO,IAAI;QAClB;IACF,OAAO;QACL,KAAK,IAAI,EAAE,MAAM;IACnB;AACF;AAEA,UAAU,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,QAAQ;IAClD,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG;IACrC,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;AACjD;AAEA,uCAAuC;AACvC,oDAAoD;AACpD,6BAA6B;AAC7B,EAAE;AACF,yDAAyD;AACzD,iEAAiE;AACjE,EAAE;AACF,iEAAiE;AACjE,sEAAsE;AACtE,wDAAwD;AACxD,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC5D,MAAM,IAAI,MAAM;AAClB;AAEA,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACxD,IAAI,KAAK,IAAI,CAAC,eAAe;IAC7B,GAAG,OAAO,GAAG;IACb,GAAG,UAAU,GAAG;IAChB,GAAG,aAAa,GAAG;IACnB,IAAI,CAAC,GAAG,YAAY,EAAE;QACpB,IAAI,KAAK,IAAI,CAAC,cAAc;QAC5B,IAAI,GAAG,aAAa,IAAI,GAAG,YAAY,IAAI,GAAG,MAAM,GAAG,GAAG,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa;IACtG;AACF;AAEA,yCAAyC;AACzC,gCAAgC;AAChC,iEAAiE;AACjE,UAAU,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC;IACrC,IAAI,KAAK,IAAI,CAAC,eAAe;IAE7B,IAAI,GAAG,UAAU,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC,GAAG,YAAY,EAAE;QAC5D,GAAG,YAAY,GAAG;QAClB,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,EAAE,GAAG,aAAa,EAAE,GAAG,cAAc;IACpE,OAAO;QACL,gEAAgE;QAChE,mDAAmD;QACnD,GAAG,aAAa,GAAG;IACrB;AACF;AAEA,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC9C,IAAI,SAAS,IAAI;IAEjB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,SAAU,IAAI;QACtD,GAAG;QACH,OAAO,IAAI,CAAC;IACd;AACF;AAEA,SAAS,KAAK,MAAM,EAAE,EAAE,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,SAAS;IAEpC,IAAI,QAAQ,MACV,OAAO,IAAI,CAAC;IAEd,0DAA0D;IAC1D,0CAA0C;IAC1C,IAAI,OAAO,cAAc,CAAC,MAAM,EAAE,MAAM,IAAI,MAAM;IAElD,IAAI,OAAO,eAAe,CAAC,YAAY,EAAE,MAAM,IAAI,MAAM;IAEzD,OAAO,OAAO,IAAI,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1927, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/lib/_stream_passthrough.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a passthrough stream.\n// basically just the most minimal sort of Transform stream.\n// Every written chunk gets output as-is.\n\n'use strict';\n\nmodule.exports = PassThrough;\n\nvar Transform = require('./_stream_transform');\n\n/*<replacement>*/\nvar util = Object.create(require('core-util-is'));\nutil.inherits = require('inherits');\n/*</replacement>*/\n\nutil.inherits(PassThrough, Transform);\n\nfunction PassThrough(options) {\n  if (!(this instanceof PassThrough)) return new PassThrough(options);\n\n  Transform.call(this, options);\n}\n\nPassThrough.prototype._transform = function (chunk, encoding, cb) {\n  cb(null, chunk);\n};"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,wBAAwB;AACxB,4DAA4D;AAC5D,yCAAyC;AAIzC,OAAO,OAAO,GAAG;AAEjB,IAAI;AAEJ,eAAe,GACf,IAAI,OAAO,OAAO,MAAM;AACxB,KAAK,QAAQ;AACb,gBAAgB,GAEhB,KAAK,QAAQ,CAAC,aAAa;AAE3B,SAAS,YAAY,OAAO;IAC1B,IAAI,CAAC,CAAC,IAAI,YAAY,WAAW,GAAG,OAAO,IAAI,YAAY;IAE3D,UAAU,IAAI,CAAC,IAAI,EAAE;AACvB;AAEA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,GAAG,MAAM;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/readable.js"], "sourcesContent": ["var Stream = require('stream');\nif (process.env.READABLE_STREAM === 'disable' && Stream) {\n  module.exports = Stream;\n  exports = module.exports = Stream.Readable;\n  exports.Readable = Stream.Readable;\n  exports.Writable = Stream.Writable;\n  exports.Duplex = Stream.Duplex;\n  exports.Transform = Stream.Transform;\n  exports.PassThrough = Stream.PassThrough;\n  exports.Stream = Stream;\n} else {\n  exports = module.exports = require('./lib/_stream_readable.js');\n  exports.Stream = Stream || exports;\n  exports.Readable = exports;\n  exports.Writable = require('./lib/_stream_writable.js');\n  exports.Duplex = require('./lib/_stream_duplex.js');\n  exports.Transform = require('./lib/_stream_transform.js');\n  exports.PassThrough = require('./lib/_stream_passthrough.js');\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI,QAAQ,GAAG,CAAC,eAAe,KAAK,aAAa,QAAQ;IACvD,OAAO,OAAO,GAAG;IACjB,UAAU,OAAO,OAAO,GAAG,OAAO,QAAQ;IAC1C,QAAQ,QAAQ,GAAG,OAAO,QAAQ;IAClC,QAAQ,QAAQ,GAAG,OAAO,QAAQ;IAClC,QAAQ,MAAM,GAAG,OAAO,MAAM;IAC9B,QAAQ,SAAS,GAAG,OAAO,SAAS;IACpC,QAAQ,WAAW,GAAG,OAAO,WAAW;IACxC,QAAQ,MAAM,GAAG;AACnB,OAAO;IACL,UAAU,OAAO,OAAO;IACxB,QAAQ,MAAM,GAAG,UAAU;IAC3B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,QAAQ;IAChB,QAAQ,MAAM;IACd,QAAQ,SAAS;IACjB,QAAQ,WAAW;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1987, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/lazystream/node_modules/readable-stream/passthrough.js"], "sourcesContent": ["module.exports = require('./readable').PassThrough\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,+HAAsB,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1992, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/ours/primordials.js"], "sourcesContent": ["'use strict'\n\n/*\n  This file is a reduced and adapted version of the main lib/internal/per_context/primordials.js file defined at\n\n  https://github.com/nodejs/node/blob/main/lib/internal/per_context/primordials.js\n\n  Don't try to replace with the original file and keep it up to date with the upstream file.\n*/\n\n// This is a simplified version of AggregateError\nclass AggregateError extends Error {\n  constructor(errors) {\n    if (!Array.isArray(errors)) {\n      throw new TypeError(`Expected input to be an Array, got ${typeof errors}`)\n    }\n    let message = ''\n    for (let i = 0; i < errors.length; i++) {\n      message += `    ${errors[i].stack}\\n`\n    }\n    super(message)\n    this.name = 'AggregateError'\n    this.errors = errors\n  }\n}\nmodule.exports = {\n  AggregateError,\n  ArrayIsArray(self) {\n    return Array.isArray(self)\n  },\n  ArrayPrototypeIncludes(self, el) {\n    return self.includes(el)\n  },\n  ArrayPrototypeIndexOf(self, el) {\n    return self.indexOf(el)\n  },\n  ArrayPrototypeJoin(self, sep) {\n    return self.join(sep)\n  },\n  ArrayPrototypeMap(self, fn) {\n    return self.map(fn)\n  },\n  ArrayPrototypePop(self, el) {\n    return self.pop(el)\n  },\n  ArrayPrototypePush(self, el) {\n    return self.push(el)\n  },\n  ArrayPrototypeSlice(self, start, end) {\n    return self.slice(start, end)\n  },\n  Error,\n  FunctionPrototypeCall(fn, thisArgs, ...args) {\n    return fn.call(thisArgs, ...args)\n  },\n  FunctionPrototypeSymbolHasInstance(self, instance) {\n    return Function.prototype[Symbol.hasInstance].call(self, instance)\n  },\n  MathFloor: Math.floor,\n  Number,\n  NumberIsInteger: Number.isInteger,\n  NumberIsNaN: Number.isNaN,\n  NumberMAX_SAFE_INTEGER: Number.MAX_SAFE_INTEGER,\n  NumberMIN_SAFE_INTEGER: Number.MIN_SAFE_INTEGER,\n  NumberParseInt: Number.parseInt,\n  ObjectDefineProperties(self, props) {\n    return Object.defineProperties(self, props)\n  },\n  ObjectDefineProperty(self, name, prop) {\n    return Object.defineProperty(self, name, prop)\n  },\n  ObjectGetOwnPropertyDescriptor(self, name) {\n    return Object.getOwnPropertyDescriptor(self, name)\n  },\n  ObjectKeys(obj) {\n    return Object.keys(obj)\n  },\n  ObjectSetPrototypeOf(target, proto) {\n    return Object.setPrototypeOf(target, proto)\n  },\n  Promise,\n  PromisePrototypeCatch(self, fn) {\n    return self.catch(fn)\n  },\n  PromisePrototypeThen(self, thenFn, catchFn) {\n    return self.then(thenFn, catchFn)\n  },\n  PromiseReject(err) {\n    return Promise.reject(err)\n  },\n  PromiseResolve(val) {\n    return Promise.resolve(val)\n  },\n  ReflectApply: Reflect.apply,\n  RegExpPrototypeTest(self, value) {\n    return self.test(value)\n  },\n  SafeSet: Set,\n  String,\n  StringPrototypeSlice(self, start, end) {\n    return self.slice(start, end)\n  },\n  StringPrototypeToLowerCase(self) {\n    return self.toLowerCase()\n  },\n  StringPrototypeToUpperCase(self) {\n    return self.toUpperCase()\n  },\n  StringPrototypeTrim(self) {\n    return self.trim()\n  },\n  Symbol,\n  SymbolFor: Symbol.for,\n  SymbolAsyncIterator: Symbol.asyncIterator,\n  SymbolHasInstance: Symbol.hasInstance,\n  SymbolIterator: Symbol.iterator,\n  SymbolDispose: Symbol.dispose || Symbol('Symbol.dispose'),\n  SymbolAsyncDispose: Symbol.asyncDispose || Symbol('Symbol.asyncDispose'),\n  TypedArrayPrototypeSet(self, buf, len) {\n    return self.set(buf, len)\n  },\n  Boolean,\n  Uint8Array\n}\n"], "names": [], "mappings": "AAEA;;;;;;AAMA,GAEA,iDAAiD;AACjD,MAAM,uBAAuB;IAC3B,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;YAC1B,MAAM,IAAI,UAAU,CAAC,mCAAmC,EAAE,OAAO,QAAQ;QAC3E;QACA,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;QACvC;QACA,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;AACA,OAAO,OAAO,GAAG;IACf;IACA,cAAa,IAAI;QACf,OAAO,MAAM,OAAO,CAAC;IACvB;IACA,wBAAuB,IAAI,EAAE,EAAE;QAC7B,OAAO,KAAK,QAAQ,CAAC;IACvB;IACA,uBAAsB,IAAI,EAAE,EAAE;QAC5B,OAAO,KAAK,OAAO,CAAC;IACtB;IACA,oBAAmB,IAAI,EAAE,GAAG;QAC1B,OAAO,KAAK,IAAI,CAAC;IACnB;IACA,mBAAkB,IAAI,EAAE,EAAE;QACxB,OAAO,KAAK,GAAG,CAAC;IAClB;IACA,mBAAkB,IAAI,EAAE,EAAE;QACxB,OAAO,KAAK,GAAG,CAAC;IAClB;IACA,oBAAmB,IAAI,EAAE,EAAE;QACzB,OAAO,KAAK,IAAI,CAAC;IACnB;IACA,qBAAoB,IAAI,EAAE,KAAK,EAAE,GAAG;QAClC,OAAO,KAAK,KAAK,CAAC,OAAO;IAC3B;IACA;IACA,uBAAsB,EAAE,EAAE,QAAQ,EAAE,GAAG,IAAI;QACzC,OAAO,GAAG,IAAI,CAAC,aAAa;IAC9B;IACA,oCAAmC,IAAI,EAAE,QAAQ;QAC/C,OAAO,SAAS,SAAS,CAAC,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM;IAC3D;IACA,WAAW,KAAK,KAAK;IACrB;IACA,iBAAiB,OAAO,SAAS;IACjC,aAAa,OAAO,KAAK;IACzB,wBAAwB,OAAO,gBAAgB;IAC/C,wBAAwB,OAAO,gBAAgB;IAC/C,gBAAgB,OAAO,QAAQ;IAC/B,wBAAuB,IAAI,EAAE,KAAK;QAChC,OAAO,OAAO,gBAAgB,CAAC,MAAM;IACvC;IACA,sBAAqB,IAAI,EAAE,IAAI,EAAE,IAAI;QACnC,OAAO,OAAO,cAAc,CAAC,MAAM,MAAM;IAC3C;IACA,gCAA+B,IAAI,EAAE,IAAI;QACvC,OAAO,OAAO,wBAAwB,CAAC,MAAM;IAC/C;IACA,YAAW,GAAG;QACZ,OAAO,OAAO,IAAI,CAAC;IACrB;IACA,sBAAqB,MAAM,EAAE,KAAK;QAChC,OAAO,OAAO,cAAc,CAAC,QAAQ;IACvC;IACA;IACA,uBAAsB,IAAI,EAAE,EAAE;QAC5B,OAAO,KAAK,KAAK,CAAC;IACpB;IACA,sBAAqB,IAAI,EAAE,MAAM,EAAE,OAAO;QACxC,OAAO,KAAK,IAAI,CAAC,QAAQ;IAC3B;IACA,eAAc,GAAG;QACf,OAAO,QAAQ,MAAM,CAAC;IACxB;IACA,gBAAe,GAAG;QAChB,OAAO,QAAQ,OAAO,CAAC;IACzB;IACA,cAAc,QAAQ,KAAK;IAC3B,qBAAoB,IAAI,EAAE,KAAK;QAC7B,OAAO,KAAK,IAAI,CAAC;IACnB;IACA,SAAS;IACT;IACA,sBAAqB,IAAI,EAAE,KAAK,EAAE,GAAG;QACnC,OAAO,KAAK,KAAK,CAAC,OAAO;IAC3B;IACA,4BAA2B,IAAI;QAC7B,OAAO,KAAK,WAAW;IACzB;IACA,4BAA2B,IAAI;QAC7B,OAAO,KAAK,WAAW;IACzB;IACA,qBAAoB,IAAI;QACtB,OAAO,KAAK,IAAI;IAClB;IACA;IACA,WAAW,OAAO,GAAG;IACrB,qBAAqB,OAAO,aAAa;IACzC,mBAAmB,OAAO,WAAW;IACrC,gBAAgB,OAAO,QAAQ;IAC/B,eAAe,OAAO,OAAO,IAAI,OAAO;IACxC,oBAAoB,OAAO,YAAY,IAAI,OAAO;IAClD,wBAAuB,IAAI,EAAE,GAAG,EAAE,GAAG;QACnC,OAAO,KAAK,GAAG,CAAC,KAAK;IACvB;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/legacy.js"], "sourcesContent": ["'use strict'\n\nconst { ArrayIsArray, ObjectSetPrototypeOf } = require('../../ours/primordials')\nconst { EventEmitter: EE } = require('events')\nfunction Stream(opts) {\n  EE.call(this, opts)\n}\nObjectSetPrototypeOf(Stream.prototype, EE.prototype)\nObjectSetPrototypeOf(Stream, EE)\nStream.prototype.pipe = function (dest, options) {\n  const source = this\n  function ondata(chunk) {\n    if (dest.writable && dest.write(chunk) === false && source.pause) {\n      source.pause()\n    }\n  }\n  source.on('data', ondata)\n  function ondrain() {\n    if (source.readable && source.resume) {\n      source.resume()\n    }\n  }\n  dest.on('drain', ondrain)\n\n  // If the 'end' option is not supplied, dest.end() will be called when\n  // source gets the 'end' or 'close' events.  Only dest.end() once.\n  if (!dest._isStdio && (!options || options.end !== false)) {\n    source.on('end', onend)\n    source.on('close', onclose)\n  }\n  let didOnEnd = false\n  function onend() {\n    if (didOnEnd) return\n    didOnEnd = true\n    dest.end()\n  }\n  function onclose() {\n    if (didOnEnd) return\n    didOnEnd = true\n    if (typeof dest.destroy === 'function') dest.destroy()\n  }\n\n  // Don't leave dangling pipes when there are errors.\n  function onerror(er) {\n    cleanup()\n    if (EE.listenerCount(this, 'error') === 0) {\n      this.emit('error', er)\n    }\n  }\n  prependListener(source, 'error', onerror)\n  prependListener(dest, 'error', onerror)\n\n  // Remove all the event listeners that were added.\n  function cleanup() {\n    source.removeListener('data', ondata)\n    dest.removeListener('drain', ondrain)\n    source.removeListener('end', onend)\n    source.removeListener('close', onclose)\n    source.removeListener('error', onerror)\n    dest.removeListener('error', onerror)\n    source.removeListener('end', cleanup)\n    source.removeListener('close', cleanup)\n    dest.removeListener('close', cleanup)\n  }\n  source.on('end', cleanup)\n  source.on('close', cleanup)\n  dest.on('close', cleanup)\n  dest.emit('pipe', source)\n\n  // Allow for unix-like usage: A.pipe(B).pipe(C)\n  return dest\n}\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn)\n\n  // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn)\n  else if (ArrayIsArray(emitter._events[event])) emitter._events[event].unshift(fn)\n  else emitter._events[event] = [fn, emitter._events[event]]\n}\nmodule.exports = {\n  Stream,\n  prependListener\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,YAAY,EAAE,oBAAoB,EAAE;AAC5C,MAAM,EAAE,cAAc,EAAE,EAAE;AAC1B,SAAS,OAAO,IAAI;IAClB,GAAG,IAAI,CAAC,IAAI,EAAE;AAChB;AACA,qBAAqB,OAAO,SAAS,EAAE,GAAG,SAAS;AACnD,qBAAqB,QAAQ;AAC7B,OAAO,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,OAAO;IAC7C,MAAM,SAAS,IAAI;IACnB,SAAS,OAAO,KAAK;QACnB,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,CAAC,WAAW,SAAS,OAAO,KAAK,EAAE;YAChE,OAAO,KAAK;QACd;IACF;IACA,OAAO,EAAE,CAAC,QAAQ;IAClB,SAAS;QACP,IAAI,OAAO,QAAQ,IAAI,OAAO,MAAM,EAAE;YACpC,OAAO,MAAM;QACf;IACF;IACA,KAAK,EAAE,CAAC,SAAS;IAEjB,sEAAsE;IACtE,kEAAkE;IAClE,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAW,QAAQ,GAAG,KAAK,KAAK,GAAG;QACzD,OAAO,EAAE,CAAC,OAAO;QACjB,OAAO,EAAE,CAAC,SAAS;IACrB;IACA,IAAI,WAAW;IACf,SAAS;QACP,IAAI,UAAU;QACd,WAAW;QACX,KAAK,GAAG;IACV;IACA,SAAS;QACP,IAAI,UAAU;QACd,WAAW;QACX,IAAI,OAAO,KAAK,OAAO,KAAK,YAAY,KAAK,OAAO;IACtD;IAEA,oDAAoD;IACpD,SAAS,QAAQ,EAAE;QACjB;QACA,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,aAAa,GAAG;YACzC,IAAI,CAAC,IAAI,CAAC,SAAS;QACrB;IACF;IACA,gBAAgB,QAAQ,SAAS;IACjC,gBAAgB,MAAM,SAAS;IAE/B,kDAAkD;IAClD,SAAS;QACP,OAAO,cAAc,CAAC,QAAQ;QAC9B,KAAK,cAAc,CAAC,SAAS;QAC7B,OAAO,cAAc,CAAC,OAAO;QAC7B,OAAO,cAAc,CAAC,SAAS;QAC/B,OAAO,cAAc,CAAC,SAAS;QAC/B,KAAK,cAAc,CAAC,SAAS;QAC7B,OAAO,cAAc,CAAC,OAAO;QAC7B,OAAO,cAAc,CAAC,SAAS;QAC/B,KAAK,cAAc,CAAC,SAAS;IAC/B;IACA,OAAO,EAAE,CAAC,OAAO;IACjB,OAAO,EAAE,CAAC,SAAS;IACnB,KAAK,EAAE,CAAC,SAAS;IACjB,KAAK,IAAI,CAAC,QAAQ;IAElB,+CAA+C;IAC/C,OAAO;AACT;AACA,SAAS,gBAAgB,OAAO,EAAE,KAAK,EAAE,EAAE;IACzC,iEAAiE;IACjE,0CAA0C;IAC1C,IAAI,OAAO,QAAQ,eAAe,KAAK,YAAY,OAAO,QAAQ,eAAe,CAAC,OAAO;IAEzF,4EAA4E;IAC5E,2EAA2E;IAC3E,yEAAyE;IACzE,4EAA4E;IAC5E,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,OAAO;SAC9D,IAAI,aAAa,QAAQ,OAAO,CAAC,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;SACzE,QAAQ,OAAO,CAAC,MAAM,GAAG;QAAC;QAAI,QAAQ,OAAO,CAAC,MAAM;KAAC;AAC5D;AACA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2205, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/ours/util/inspect.js"], "sourcesContent": ["'use strict'\n\n/*\n  This file is a reduced and adapted version of the main lib/internal/util/inspect.js file defined at\n\n  https://github.com/nodejs/node/blob/main/lib/internal/util/inspect.js\n\n  Don't try to replace with the original file and keep it up to date with the upstream file.\n*/\nmodule.exports = {\n  format(format, ...args) {\n    // Simplified version of https://nodejs.org/api/util.html#utilformatformat-args\n    return format.replace(/%([sdifj])/g, function (...[_unused, type]) {\n      const replacement = args.shift()\n      if (type === 'f') {\n        return replacement.toFixed(6)\n      } else if (type === 'j') {\n        return JSON.stringify(replacement)\n      } else if (type === 's' && typeof replacement === 'object') {\n        const ctor = replacement.constructor !== Object ? replacement.constructor.name : ''\n        return `${ctor} {}`.trim()\n      } else {\n        return replacement.toString()\n      }\n    })\n  },\n  inspect(value) {\n    // Vastly simplified version of https://nodejs.org/api/util.html#utilinspectobject-options\n    switch (typeof value) {\n      case 'string':\n        if (value.includes(\"'\")) {\n          if (!value.includes('\"')) {\n            return `\"${value}\"`\n          } else if (!value.includes('`') && !value.includes('${')) {\n            return `\\`${value}\\``\n          }\n        }\n        return `'${value}'`\n      case 'number':\n        if (isNaN(value)) {\n          return 'NaN'\n        } else if (Object.is(value, -0)) {\n          return String(value)\n        }\n        return value\n      case 'bigint':\n        return `${String(value)}n`\n      case 'boolean':\n      case 'undefined':\n        return String(value)\n      case 'object':\n        return '{}'\n    }\n  }\n}\n"], "names": [], "mappings": "AAEA;;;;;;AAMA,GACA,OAAO,OAAO,GAAG;IACf,QAAO,MAAM,EAAE,GAAG,IAAI;QACpB,+EAA+E;QAC/E,OAAO,OAAO,OAAO,CAAC,eAAe,SAAU,GAAG,CAAC,SAAS,KAAK;YAC/D,MAAM,cAAc,KAAK,KAAK;YAC9B,IAAI,SAAS,KAAK;gBAChB,OAAO,YAAY,OAAO,CAAC;YAC7B,OAAO,IAAI,SAAS,KAAK;gBACvB,OAAO,KAAK,SAAS,CAAC;YACxB,OAAO,IAAI,SAAS,OAAO,OAAO,gBAAgB,UAAU;gBAC1D,MAAM,OAAO,YAAY,WAAW,KAAK,SAAS,YAAY,WAAW,CAAC,IAAI,GAAG;gBACjF,OAAO,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI;YAC1B,OAAO;gBACL,OAAO,YAAY,QAAQ;YAC7B;QACF;IACF;IACA,SAAQ,KAAK;QACX,0FAA0F;QAC1F,OAAQ,OAAO;YACb,KAAK;gBACH,IAAI,MAAM,QAAQ,CAAC,MAAM;oBACvB,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM;wBACxB,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;oBACrB,OAAO,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ,CAAC,MAAM,QAAQ,CAAC,OAAO;wBACxD,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC;oBACvB;gBACF;gBACA,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YACrB,KAAK;gBACH,IAAI,MAAM,QAAQ;oBAChB,OAAO;gBACT,OAAO,IAAI,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI;oBAC/B,OAAO,OAAO;gBAChB;gBACA,OAAO;YACT,KAAK;gBACH,OAAO,GAAG,OAAO,OAAO,CAAC,CAAC;YAC5B,KAAK;YACL,KAAK;gBACH,OAAO,OAAO;YAChB,KAAK;gBACH,OAAO;QACX;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2261, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/ours/errors.js"], "sourcesContent": ["'use strict'\n\nconst { format, inspect } = require('./util/inspect')\nconst { AggregateError: CustomAggregateError } = require('./primordials')\n\n/*\n  This file is a reduced and adapted version of the main lib/internal/errors.js file defined at\n\n  https://github.com/nodejs/node/blob/main/lib/internal/errors.js\n\n  Don't try to replace with the original file and keep it up to date (starting from E(...) definitions)\n  with the upstream file.\n*/\n\nconst AggregateError = globalThis.AggregateError || CustomAggregateError\nconst kIsNodeError = Symbol('kIsNodeError')\nconst kTypes = [\n  'string',\n  'function',\n  'number',\n  'object',\n  // Accept 'Function' and 'Object' as alternative to the lower cased version.\n  'Function',\n  'Object',\n  'boolean',\n  'bigint',\n  'symbol'\n]\nconst classRegExp = /^([A-Z][a-z0-9]*)+$/\nconst nodeInternalPrefix = '__node_internal_'\nconst codes = {}\nfunction assert(value, message) {\n  if (!value) {\n    throw new codes.ERR_INTERNAL_ASSERTION(message)\n  }\n}\n\n// Only use this for integers! Decimal numbers do not work with this function.\nfunction addNumericalSeparator(val) {\n  let res = ''\n  let i = val.length\n  const start = val[0] === '-' ? 1 : 0\n  for (; i >= start + 4; i -= 3) {\n    res = `_${val.slice(i - 3, i)}${res}`\n  }\n  return `${val.slice(0, i)}${res}`\n}\nfunction getMessage(key, msg, args) {\n  if (typeof msg === 'function') {\n    assert(\n      msg.length <= args.length,\n      // Default options do not count.\n      `Code: ${key}; The provided arguments length (${args.length}) does not match the required ones (${msg.length}).`\n    )\n    return msg(...args)\n  }\n  const expectedLength = (msg.match(/%[dfijoOs]/g) || []).length\n  assert(\n    expectedLength === args.length,\n    `Code: ${key}; The provided arguments length (${args.length}) does not match the required ones (${expectedLength}).`\n  )\n  if (args.length === 0) {\n    return msg\n  }\n  return format(msg, ...args)\n}\nfunction E(code, message, Base) {\n  if (!Base) {\n    Base = Error\n  }\n  class NodeError extends Base {\n    constructor(...args) {\n      super(getMessage(code, message, args))\n    }\n    toString() {\n      return `${this.name} [${code}]: ${this.message}`\n    }\n  }\n  Object.defineProperties(NodeError.prototype, {\n    name: {\n      value: Base.name,\n      writable: true,\n      enumerable: false,\n      configurable: true\n    },\n    toString: {\n      value() {\n        return `${this.name} [${code}]: ${this.message}`\n      },\n      writable: true,\n      enumerable: false,\n      configurable: true\n    }\n  })\n  NodeError.prototype.code = code\n  NodeError.prototype[kIsNodeError] = true\n  codes[code] = NodeError\n}\nfunction hideStackFrames(fn) {\n  // We rename the functions that will be hidden to cut off the stacktrace\n  // at the outermost one\n  const hidden = nodeInternalPrefix + fn.name\n  Object.defineProperty(fn, 'name', {\n    value: hidden\n  })\n  return fn\n}\nfunction aggregateTwoErrors(innerError, outerError) {\n  if (innerError && outerError && innerError !== outerError) {\n    if (Array.isArray(outerError.errors)) {\n      // If `outerError` is already an `AggregateError`.\n      outerError.errors.push(innerError)\n      return outerError\n    }\n    const err = new AggregateError([outerError, innerError], outerError.message)\n    err.code = outerError.code\n    return err\n  }\n  return innerError || outerError\n}\nclass AbortError extends Error {\n  constructor(message = 'The operation was aborted', options = undefined) {\n    if (options !== undefined && typeof options !== 'object') {\n      throw new codes.ERR_INVALID_ARG_TYPE('options', 'Object', options)\n    }\n    super(message, options)\n    this.code = 'ABORT_ERR'\n    this.name = 'AbortError'\n  }\n}\nE('ERR_ASSERTION', '%s', Error)\nE(\n  'ERR_INVALID_ARG_TYPE',\n  (name, expected, actual) => {\n    assert(typeof name === 'string', \"'name' must be a string\")\n    if (!Array.isArray(expected)) {\n      expected = [expected]\n    }\n    let msg = 'The '\n    if (name.endsWith(' argument')) {\n      // For cases like 'first argument'\n      msg += `${name} `\n    } else {\n      msg += `\"${name}\" ${name.includes('.') ? 'property' : 'argument'} `\n    }\n    msg += 'must be '\n    const types = []\n    const instances = []\n    const other = []\n    for (const value of expected) {\n      assert(typeof value === 'string', 'All expected entries have to be of type string')\n      if (kTypes.includes(value)) {\n        types.push(value.toLowerCase())\n      } else if (classRegExp.test(value)) {\n        instances.push(value)\n      } else {\n        assert(value !== 'object', 'The value \"object\" should be written as \"Object\"')\n        other.push(value)\n      }\n    }\n\n    // Special handle `object` in case other instances are allowed to outline\n    // the differences between each other.\n    if (instances.length > 0) {\n      const pos = types.indexOf('object')\n      if (pos !== -1) {\n        types.splice(types, pos, 1)\n        instances.push('Object')\n      }\n    }\n    if (types.length > 0) {\n      switch (types.length) {\n        case 1:\n          msg += `of type ${types[0]}`\n          break\n        case 2:\n          msg += `one of type ${types[0]} or ${types[1]}`\n          break\n        default: {\n          const last = types.pop()\n          msg += `one of type ${types.join(', ')}, or ${last}`\n        }\n      }\n      if (instances.length > 0 || other.length > 0) {\n        msg += ' or '\n      }\n    }\n    if (instances.length > 0) {\n      switch (instances.length) {\n        case 1:\n          msg += `an instance of ${instances[0]}`\n          break\n        case 2:\n          msg += `an instance of ${instances[0]} or ${instances[1]}`\n          break\n        default: {\n          const last = instances.pop()\n          msg += `an instance of ${instances.join(', ')}, or ${last}`\n        }\n      }\n      if (other.length > 0) {\n        msg += ' or '\n      }\n    }\n    switch (other.length) {\n      case 0:\n        break\n      case 1:\n        if (other[0].toLowerCase() !== other[0]) {\n          msg += 'an '\n        }\n        msg += `${other[0]}`\n        break\n      case 2:\n        msg += `one of ${other[0]} or ${other[1]}`\n        break\n      default: {\n        const last = other.pop()\n        msg += `one of ${other.join(', ')}, or ${last}`\n      }\n    }\n    if (actual == null) {\n      msg += `. Received ${actual}`\n    } else if (typeof actual === 'function' && actual.name) {\n      msg += `. Received function ${actual.name}`\n    } else if (typeof actual === 'object') {\n      var _actual$constructor\n      if (\n        (_actual$constructor = actual.constructor) !== null &&\n        _actual$constructor !== undefined &&\n        _actual$constructor.name\n      ) {\n        msg += `. Received an instance of ${actual.constructor.name}`\n      } else {\n        const inspected = inspect(actual, {\n          depth: -1\n        })\n        msg += `. Received ${inspected}`\n      }\n    } else {\n      let inspected = inspect(actual, {\n        colors: false\n      })\n      if (inspected.length > 25) {\n        inspected = `${inspected.slice(0, 25)}...`\n      }\n      msg += `. Received type ${typeof actual} (${inspected})`\n    }\n    return msg\n  },\n  TypeError\n)\nE(\n  'ERR_INVALID_ARG_VALUE',\n  (name, value, reason = 'is invalid') => {\n    let inspected = inspect(value)\n    if (inspected.length > 128) {\n      inspected = inspected.slice(0, 128) + '...'\n    }\n    const type = name.includes('.') ? 'property' : 'argument'\n    return `The ${type} '${name}' ${reason}. Received ${inspected}`\n  },\n  TypeError\n)\nE(\n  'ERR_INVALID_RETURN_VALUE',\n  (input, name, value) => {\n    var _value$constructor\n    const type =\n      value !== null &&\n      value !== undefined &&\n      (_value$constructor = value.constructor) !== null &&\n      _value$constructor !== undefined &&\n      _value$constructor.name\n        ? `instance of ${value.constructor.name}`\n        : `type ${typeof value}`\n    return `Expected ${input} to be returned from the \"${name}\"` + ` function but got ${type}.`\n  },\n  TypeError\n)\nE(\n  'ERR_MISSING_ARGS',\n  (...args) => {\n    assert(args.length > 0, 'At least one arg needs to be specified')\n    let msg\n    const len = args.length\n    args = (Array.isArray(args) ? args : [args]).map((a) => `\"${a}\"`).join(' or ')\n    switch (len) {\n      case 1:\n        msg += `The ${args[0]} argument`\n        break\n      case 2:\n        msg += `The ${args[0]} and ${args[1]} arguments`\n        break\n      default:\n        {\n          const last = args.pop()\n          msg += `The ${args.join(', ')}, and ${last} arguments`\n        }\n        break\n    }\n    return `${msg} must be specified`\n  },\n  TypeError\n)\nE(\n  'ERR_OUT_OF_RANGE',\n  (str, range, input) => {\n    assert(range, 'Missing \"range\" argument')\n    let received\n    if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {\n      received = addNumericalSeparator(String(input))\n    } else if (typeof input === 'bigint') {\n      received = String(input)\n      const limit = BigInt(2) ** BigInt(32)\n      if (input > limit || input < -limit) {\n        received = addNumericalSeparator(received)\n      }\n      received += 'n'\n    } else {\n      received = inspect(input)\n    }\n    return `The value of \"${str}\" is out of range. It must be ${range}. Received ${received}`\n  },\n  RangeError\n)\nE('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times', Error)\nE('ERR_METHOD_NOT_IMPLEMENTED', 'The %s method is not implemented', Error)\nE('ERR_STREAM_ALREADY_FINISHED', 'Cannot call %s after a stream was finished', Error)\nE('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable', Error)\nE('ERR_STREAM_DESTROYED', 'Cannot call %s after a stream was destroyed', Error)\nE('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError)\nE('ERR_STREAM_PREMATURE_CLOSE', 'Premature close', Error)\nE('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF', Error)\nE('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event', Error)\nE('ERR_STREAM_WRITE_AFTER_END', 'write after end', Error)\nE('ERR_UNKNOWN_ENCODING', 'Unknown encoding: %s', TypeError)\nmodule.exports = {\n  AbortError,\n  aggregateTwoErrors: hideStackFrames(aggregateTwoErrors),\n  hideStackFrames,\n  codes\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;AACzB,MAAM,EAAE,gBAAgB,oBAAoB,EAAE;AAE9C;;;;;;;AAOA,GAEA,MAAM,iBAAiB,WAAW,cAAc,IAAI;AACpD,MAAM,eAAe,OAAO;AAC5B,MAAM,SAAS;IACb;IACA;IACA;IACA;IACA,4EAA4E;IAC5E;IACA;IACA;IACA;IACA;CACD;AACD,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAC3B,MAAM,QAAQ,CAAC;AACf,SAAS,OAAO,KAAK,EAAE,OAAO;IAC5B,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM,sBAAsB,CAAC;IACzC;AACF;AAEA,8EAA8E;AAC9E,SAAS,sBAAsB,GAAG;IAChC,IAAI,MAAM;IACV,IAAI,IAAI,IAAI,MAAM;IAClB,MAAM,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI;IACnC,MAAO,KAAK,QAAQ,GAAG,KAAK,EAAG;QAC7B,MAAM,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,GAAG,KAAK,KAAK;IACvC;IACA,OAAO,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK;AACnC;AACA,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,IAAI;IAChC,IAAI,OAAO,QAAQ,YAAY;QAC7B,OACE,IAAI,MAAM,IAAI,KAAK,MAAM,EACzB,gCAAgC;QAChC,CAAC,MAAM,EAAE,IAAI,iCAAiC,EAAE,KAAK,MAAM,CAAC,oCAAoC,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC;QAElH,OAAO,OAAO;IAChB;IACA,MAAM,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,EAAE,EAAE,MAAM;IAC9D,OACE,mBAAmB,KAAK,MAAM,EAC9B,CAAC,MAAM,EAAE,IAAI,iCAAiC,EAAE,KAAK,MAAM,CAAC,oCAAoC,EAAE,eAAe,EAAE,CAAC;IAEtH,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IACA,OAAO,OAAO,QAAQ;AACxB;AACA,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;IAC5B,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,MAAM,kBAAkB;QACtB,YAAY,GAAG,IAAI,CAAE;YACnB,KAAK,CAAC,WAAW,MAAM,SAAS;QAClC;QACA,WAAW;YACT,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;QAClD;IACF;IACA,OAAO,gBAAgB,CAAC,UAAU,SAAS,EAAE;QAC3C,MAAM;YACJ,OAAO,KAAK,IAAI;YAChB,UAAU;YACV,YAAY;YACZ,cAAc;QAChB;QACA,UAAU;YACR;gBACE,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;YAClD;YACA,UAAU;YACV,YAAY;YACZ,cAAc;QAChB;IACF;IACA,UAAU,SAAS,CAAC,IAAI,GAAG;IAC3B,UAAU,SAAS,CAAC,aAAa,GAAG;IACpC,KAAK,CAAC,KAAK,GAAG;AAChB;AACA,SAAS,gBAAgB,EAAE;IACzB,wEAAwE;IACxE,uBAAuB;IACvB,MAAM,SAAS,qBAAqB,GAAG,IAAI;IAC3C,OAAO,cAAc,CAAC,IAAI,QAAQ;QAChC,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,mBAAmB,UAAU,EAAE,UAAU;IAChD,IAAI,cAAc,cAAc,eAAe,YAAY;QACzD,IAAI,MAAM,OAAO,CAAC,WAAW,MAAM,GAAG;YACpC,kDAAkD;YAClD,WAAW,MAAM,CAAC,IAAI,CAAC;YACvB,OAAO;QACT;QACA,MAAM,MAAM,IAAI,eAAe;YAAC;YAAY;SAAW,EAAE,WAAW,OAAO;QAC3E,IAAI,IAAI,GAAG,WAAW,IAAI;QAC1B,OAAO;IACT;IACA,OAAO,cAAc;AACvB;AACA,MAAM,mBAAmB;IACvB,YAAY,UAAU,2BAA2B,EAAE,UAAU,SAAS,CAAE;QACtE,IAAI,YAAY,aAAa,OAAO,YAAY,UAAU;YACxD,MAAM,IAAI,MAAM,oBAAoB,CAAC,WAAW,UAAU;QAC5D;QACA,KAAK,CAAC,SAAS;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AACA,EAAE,iBAAiB,MAAM;AACzB,EACE,wBACA,CAAC,MAAM,UAAU;IACf,OAAO,OAAO,SAAS,UAAU;IACjC,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW;QAC5B,WAAW;YAAC;SAAS;IACvB;IACA,IAAI,MAAM;IACV,IAAI,KAAK,QAAQ,CAAC,cAAc;QAC9B,kCAAkC;QAClC,OAAO,GAAG,KAAK,CAAC,CAAC;IACnB,OAAO;QACL,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,QAAQ,CAAC,OAAO,aAAa,WAAW,CAAC,CAAC;IACrE;IACA,OAAO;IACP,MAAM,QAAQ,EAAE;IAChB,MAAM,YAAY,EAAE;IACpB,MAAM,QAAQ,EAAE;IAChB,KAAK,MAAM,SAAS,SAAU;QAC5B,OAAO,OAAO,UAAU,UAAU;QAClC,IAAI,OAAO,QAAQ,CAAC,QAAQ;YAC1B,MAAM,IAAI,CAAC,MAAM,WAAW;QAC9B,OAAO,IAAI,YAAY,IAAI,CAAC,QAAQ;YAClC,UAAU,IAAI,CAAC;QACjB,OAAO;YACL,OAAO,UAAU,UAAU;YAC3B,MAAM,IAAI,CAAC;QACb;IACF;IAEA,yEAAyE;IACzE,sCAAsC;IACtC,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,MAAM,MAAM,MAAM,OAAO,CAAC;QAC1B,IAAI,QAAQ,CAAC,GAAG;YACd,MAAM,MAAM,CAAC,OAAO,KAAK;YACzB,UAAU,IAAI,CAAC;QACjB;IACF;IACA,IAAI,MAAM,MAAM,GAAG,GAAG;QACpB,OAAQ,MAAM,MAAM;YAClB,KAAK;gBACH,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC5B;YACF,KAAK;gBACH,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC/C;YACF;gBAAS;oBACP,MAAM,OAAO,MAAM,GAAG;oBACtB,OAAO,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM;gBACtD;QACF;QACA,IAAI,UAAU,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,GAAG;YAC5C,OAAO;QACT;IACF;IACA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAQ,UAAU,MAAM;YACtB,KAAK;gBACH,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC,EAAE,EAAE;gBACvC;YACF,KAAK;gBACH,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE;gBAC1D;YACF;gBAAS;oBACP,MAAM,OAAO,UAAU,GAAG;oBAC1B,OAAO,CAAC,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM;gBAC7D;QACF;QACA,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,OAAO;QACT;IACF;IACA,OAAQ,MAAM,MAAM;QAClB,KAAK;YACH;QACF,KAAK;YACH,IAAI,KAAK,CAAC,EAAE,CAAC,WAAW,OAAO,KAAK,CAAC,EAAE,EAAE;gBACvC,OAAO;YACT;YACA,OAAO,GAAG,KAAK,CAAC,EAAE,EAAE;YACpB;QACF,KAAK;YACH,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAC1C;QACF;YAAS;gBACP,MAAM,OAAO,MAAM,GAAG;gBACtB,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM;YACjD;IACF;IACA,IAAI,UAAU,MAAM;QAClB,OAAO,CAAC,WAAW,EAAE,QAAQ;IAC/B,OAAO,IAAI,OAAO,WAAW,cAAc,OAAO,IAAI,EAAE;QACtD,OAAO,CAAC,oBAAoB,EAAE,OAAO,IAAI,EAAE;IAC7C,OAAO,IAAI,OAAO,WAAW,UAAU;QACrC,IAAI;QACJ,IACE,CAAC,sBAAsB,OAAO,WAAW,MAAM,QAC/C,wBAAwB,aACxB,oBAAoB,IAAI,EACxB;YACA,OAAO,CAAC,0BAA0B,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE;QAC/D,OAAO;YACL,MAAM,YAAY,QAAQ,QAAQ;gBAChC,OAAO,CAAC;YACV;YACA,OAAO,CAAC,WAAW,EAAE,WAAW;QAClC;IACF,OAAO;QACL,IAAI,YAAY,QAAQ,QAAQ;YAC9B,QAAQ;QACV;QACA,IAAI,UAAU,MAAM,GAAG,IAAI;YACzB,YAAY,GAAG,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;QAC5C;QACA,OAAO,CAAC,gBAAgB,EAAE,OAAO,OAAO,EAAE,EAAE,UAAU,CAAC,CAAC;IAC1D;IACA,OAAO;AACT,GACA;AAEF,EACE,yBACA,CAAC,MAAM,OAAO,SAAS,YAAY;IACjC,IAAI,YAAY,QAAQ;IACxB,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,YAAY,UAAU,KAAK,CAAC,GAAG,OAAO;IACxC;IACA,MAAM,OAAO,KAAK,QAAQ,CAAC,OAAO,aAAa;IAC/C,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO,WAAW,EAAE,WAAW;AACjE,GACA;AAEF,EACE,4BACA,CAAC,OAAO,MAAM;IACZ,IAAI;IACJ,MAAM,OACJ,UAAU,QACV,UAAU,aACV,CAAC,qBAAqB,MAAM,WAAW,MAAM,QAC7C,uBAAuB,aACvB,mBAAmB,IAAI,GACnB,CAAC,YAAY,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE,GACvC,CAAC,KAAK,EAAE,OAAO,OAAO;IAC5B,OAAO,CAAC,SAAS,EAAE,MAAM,0BAA0B,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAC7F,GACA;AAEF,EACE,oBACA,CAAC,GAAG;IACF,OAAO,KAAK,MAAM,GAAG,GAAG;IACxB,IAAI;IACJ,MAAM,MAAM,KAAK,MAAM;IACvB,OAAO,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO;QAAC;KAAK,EAAE,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IACvE,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;YAChC;QACF,KAAK;YACH,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC;YAChD;QACF;YACE;gBACE,MAAM,OAAO,KAAK,GAAG;gBACrB,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,MAAM,MAAM,EAAE,KAAK,UAAU,CAAC;YACxD;YACA;IACJ;IACA,OAAO,GAAG,IAAI,kBAAkB,CAAC;AACnC,GACA;AAEF,EACE,oBACA,CAAC,KAAK,OAAO;IACX,OAAO,OAAO;IACd,IAAI;IACJ,IAAI,OAAO,SAAS,CAAC,UAAU,KAAK,GAAG,CAAC,SAAS,KAAK,IAAI;QACxD,WAAW,sBAAsB,OAAO;IAC1C,OAAO,IAAI,OAAO,UAAU,UAAU;QACpC,WAAW,OAAO;QAClB,MAAM,QAAQ,OAAO,MAAM,OAAO;QAClC,IAAI,QAAQ,SAAS,QAAQ,CAAC,OAAO;YACnC,WAAW,sBAAsB;QACnC;QACA,YAAY;IACd,OAAO;QACL,WAAW,QAAQ;IACrB;IACA,OAAO,CAAC,cAAc,EAAE,IAAI,8BAA8B,EAAE,MAAM,WAAW,EAAE,UAAU;AAC3F,GACA;AAEF,EAAE,yBAAyB,kCAAkC;AAC7D,EAAE,8BAA8B,oCAAoC;AACpE,EAAE,+BAA+B,8CAA8C;AAC/E,EAAE,0BAA0B,6BAA6B;AACzD,EAAE,wBAAwB,+CAA+C;AACzE,EAAE,0BAA0B,uCAAuC;AACnE,EAAE,8BAA8B,mBAAmB;AACnD,EAAE,6BAA6B,2BAA2B;AAC1D,EAAE,sCAAsC,oCAAoC;AAC5E,EAAE,8BAA8B,mBAAmB;AACnD,EAAE,wBAAwB,wBAAwB;AAClD,OAAO,OAAO,GAAG;IACf;IACA,oBAAoB,gBAAgB;IACpC;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/utils.js"], "sourcesContent": ["'use strict'\n\nconst { Symbol<PERSON>yncIterator, SymbolIterator, SymbolFor } = require('../../ours/primordials')\n\n// We need to use SymbolFor to make these globally available\n// for interopt with readable-stream, i.e. readable-stream\n// and node core needs to be able to read/write private state\n// from each other for proper interoperability.\nconst kIsDestroyed = SymbolFor('nodejs.stream.destroyed')\nconst kIsErrored = SymbolFor('nodejs.stream.errored')\nconst kIsReadable = SymbolFor('nodejs.stream.readable')\nconst kIsWritable = SymbolFor('nodejs.stream.writable')\nconst kIsDisturbed = SymbolFor('nodejs.stream.disturbed')\nconst kIsClosedPromise = SymbolFor('nodejs.webstream.isClosedPromise')\nconst kControllerErrorFunction = SymbolFor('nodejs.webstream.controllerErrorFunction')\nfunction isReadableNodeStream(obj, strict = false) {\n  var _obj$_readableState\n  return !!(\n    (\n      obj &&\n      typeof obj.pipe === 'function' &&\n      typeof obj.on === 'function' &&\n      (!strict || (typeof obj.pause === 'function' && typeof obj.resume === 'function')) &&\n      (!obj._writableState ||\n        ((_obj$_readableState = obj._readableState) === null || _obj$_readableState === undefined\n          ? undefined\n          : _obj$_readableState.readable) !== false) &&\n      // Duplex\n      (!obj._writableState || obj._readableState)\n    ) // Writable has .pipe.\n  )\n}\nfunction isWritableNodeStream(obj) {\n  var _obj$_writableState\n  return !!(\n    (\n      obj &&\n      typeof obj.write === 'function' &&\n      typeof obj.on === 'function' &&\n      (!obj._readableState ||\n        ((_obj$_writableState = obj._writableState) === null || _obj$_writableState === undefined\n          ? undefined\n          : _obj$_writableState.writable) !== false)\n    ) // Duplex\n  )\n}\nfunction isDuplexNodeStream(obj) {\n  return !!(\n    obj &&\n    typeof obj.pipe === 'function' &&\n    obj._readableState &&\n    typeof obj.on === 'function' &&\n    typeof obj.write === 'function'\n  )\n}\nfunction isNodeStream(obj) {\n  return (\n    obj &&\n    (obj._readableState ||\n      obj._writableState ||\n      (typeof obj.write === 'function' && typeof obj.on === 'function') ||\n      (typeof obj.pipe === 'function' && typeof obj.on === 'function'))\n  )\n}\nfunction isReadableStream(obj) {\n  return !!(\n    obj &&\n    !isNodeStream(obj) &&\n    typeof obj.pipeThrough === 'function' &&\n    typeof obj.getReader === 'function' &&\n    typeof obj.cancel === 'function'\n  )\n}\nfunction isWritableStream(obj) {\n  return !!(obj && !isNodeStream(obj) && typeof obj.getWriter === 'function' && typeof obj.abort === 'function')\n}\nfunction isTransformStream(obj) {\n  return !!(obj && !isNodeStream(obj) && typeof obj.readable === 'object' && typeof obj.writable === 'object')\n}\nfunction isWebStream(obj) {\n  return isReadableStream(obj) || isWritableStream(obj) || isTransformStream(obj)\n}\nfunction isIterable(obj, isAsync) {\n  if (obj == null) return false\n  if (isAsync === true) return typeof obj[SymbolAsyncIterator] === 'function'\n  if (isAsync === false) return typeof obj[SymbolIterator] === 'function'\n  return typeof obj[SymbolAsyncIterator] === 'function' || typeof obj[SymbolIterator] === 'function'\n}\nfunction isDestroyed(stream) {\n  if (!isNodeStream(stream)) return null\n  const wState = stream._writableState\n  const rState = stream._readableState\n  const state = wState || rState\n  return !!(stream.destroyed || stream[kIsDestroyed] || (state !== null && state !== undefined && state.destroyed))\n}\n\n// Have been end():d.\nfunction isWritableEnded(stream) {\n  if (!isWritableNodeStream(stream)) return null\n  if (stream.writableEnded === true) return true\n  const wState = stream._writableState\n  if (wState !== null && wState !== undefined && wState.errored) return false\n  if (typeof (wState === null || wState === undefined ? undefined : wState.ended) !== 'boolean') return null\n  return wState.ended\n}\n\n// Have emitted 'finish'.\nfunction isWritableFinished(stream, strict) {\n  if (!isWritableNodeStream(stream)) return null\n  if (stream.writableFinished === true) return true\n  const wState = stream._writableState\n  if (wState !== null && wState !== undefined && wState.errored) return false\n  if (typeof (wState === null || wState === undefined ? undefined : wState.finished) !== 'boolean') return null\n  return !!(wState.finished || (strict === false && wState.ended === true && wState.length === 0))\n}\n\n// Have been push(null):d.\nfunction isReadableEnded(stream) {\n  if (!isReadableNodeStream(stream)) return null\n  if (stream.readableEnded === true) return true\n  const rState = stream._readableState\n  if (!rState || rState.errored) return false\n  if (typeof (rState === null || rState === undefined ? undefined : rState.ended) !== 'boolean') return null\n  return rState.ended\n}\n\n// Have emitted 'end'.\nfunction isReadableFinished(stream, strict) {\n  if (!isReadableNodeStream(stream)) return null\n  const rState = stream._readableState\n  if (rState !== null && rState !== undefined && rState.errored) return false\n  if (typeof (rState === null || rState === undefined ? undefined : rState.endEmitted) !== 'boolean') return null\n  return !!(rState.endEmitted || (strict === false && rState.ended === true && rState.length === 0))\n}\nfunction isReadable(stream) {\n  if (stream && stream[kIsReadable] != null) return stream[kIsReadable]\n  if (typeof (stream === null || stream === undefined ? undefined : stream.readable) !== 'boolean') return null\n  if (isDestroyed(stream)) return false\n  return isReadableNodeStream(stream) && stream.readable && !isReadableFinished(stream)\n}\nfunction isWritable(stream) {\n  if (stream && stream[kIsWritable] != null) return stream[kIsWritable]\n  if (typeof (stream === null || stream === undefined ? undefined : stream.writable) !== 'boolean') return null\n  if (isDestroyed(stream)) return false\n  return isWritableNodeStream(stream) && stream.writable && !isWritableEnded(stream)\n}\nfunction isFinished(stream, opts) {\n  if (!isNodeStream(stream)) {\n    return null\n  }\n  if (isDestroyed(stream)) {\n    return true\n  }\n  if ((opts === null || opts === undefined ? undefined : opts.readable) !== false && isReadable(stream)) {\n    return false\n  }\n  if ((opts === null || opts === undefined ? undefined : opts.writable) !== false && isWritable(stream)) {\n    return false\n  }\n  return true\n}\nfunction isWritableErrored(stream) {\n  var _stream$_writableStat, _stream$_writableStat2\n  if (!isNodeStream(stream)) {\n    return null\n  }\n  if (stream.writableErrored) {\n    return stream.writableErrored\n  }\n  return (_stream$_writableStat =\n    (_stream$_writableStat2 = stream._writableState) === null || _stream$_writableStat2 === undefined\n      ? undefined\n      : _stream$_writableStat2.errored) !== null && _stream$_writableStat !== undefined\n    ? _stream$_writableStat\n    : null\n}\nfunction isReadableErrored(stream) {\n  var _stream$_readableStat, _stream$_readableStat2\n  if (!isNodeStream(stream)) {\n    return null\n  }\n  if (stream.readableErrored) {\n    return stream.readableErrored\n  }\n  return (_stream$_readableStat =\n    (_stream$_readableStat2 = stream._readableState) === null || _stream$_readableStat2 === undefined\n      ? undefined\n      : _stream$_readableStat2.errored) !== null && _stream$_readableStat !== undefined\n    ? _stream$_readableStat\n    : null\n}\nfunction isClosed(stream) {\n  if (!isNodeStream(stream)) {\n    return null\n  }\n  if (typeof stream.closed === 'boolean') {\n    return stream.closed\n  }\n  const wState = stream._writableState\n  const rState = stream._readableState\n  if (\n    typeof (wState === null || wState === undefined ? undefined : wState.closed) === 'boolean' ||\n    typeof (rState === null || rState === undefined ? undefined : rState.closed) === 'boolean'\n  ) {\n    return (\n      (wState === null || wState === undefined ? undefined : wState.closed) ||\n      (rState === null || rState === undefined ? undefined : rState.closed)\n    )\n  }\n  if (typeof stream._closed === 'boolean' && isOutgoingMessage(stream)) {\n    return stream._closed\n  }\n  return null\n}\nfunction isOutgoingMessage(stream) {\n  return (\n    typeof stream._closed === 'boolean' &&\n    typeof stream._defaultKeepAlive === 'boolean' &&\n    typeof stream._removedConnection === 'boolean' &&\n    typeof stream._removedContLen === 'boolean'\n  )\n}\nfunction isServerResponse(stream) {\n  return typeof stream._sent100 === 'boolean' && isOutgoingMessage(stream)\n}\nfunction isServerRequest(stream) {\n  var _stream$req\n  return (\n    typeof stream._consuming === 'boolean' &&\n    typeof stream._dumped === 'boolean' &&\n    ((_stream$req = stream.req) === null || _stream$req === undefined ? undefined : _stream$req.upgradeOrConnect) ===\n      undefined\n  )\n}\nfunction willEmitClose(stream) {\n  if (!isNodeStream(stream)) return null\n  const wState = stream._writableState\n  const rState = stream._readableState\n  const state = wState || rState\n  return (\n    (!state && isServerResponse(stream)) || !!(state && state.autoDestroy && state.emitClose && state.closed === false)\n  )\n}\nfunction isDisturbed(stream) {\n  var _stream$kIsDisturbed\n  return !!(\n    stream &&\n    ((_stream$kIsDisturbed = stream[kIsDisturbed]) !== null && _stream$kIsDisturbed !== undefined\n      ? _stream$kIsDisturbed\n      : stream.readableDidRead || stream.readableAborted)\n  )\n}\nfunction isErrored(stream) {\n  var _ref,\n    _ref2,\n    _ref3,\n    _ref4,\n    _ref5,\n    _stream$kIsErrored,\n    _stream$_readableStat3,\n    _stream$_writableStat3,\n    _stream$_readableStat4,\n    _stream$_writableStat4\n  return !!(\n    stream &&\n    ((_ref =\n      (_ref2 =\n        (_ref3 =\n          (_ref4 =\n            (_ref5 =\n              (_stream$kIsErrored = stream[kIsErrored]) !== null && _stream$kIsErrored !== undefined\n                ? _stream$kIsErrored\n                : stream.readableErrored) !== null && _ref5 !== undefined\n              ? _ref5\n              : stream.writableErrored) !== null && _ref4 !== undefined\n            ? _ref4\n            : (_stream$_readableStat3 = stream._readableState) === null || _stream$_readableStat3 === undefined\n            ? undefined\n            : _stream$_readableStat3.errorEmitted) !== null && _ref3 !== undefined\n          ? _ref3\n          : (_stream$_writableStat3 = stream._writableState) === null || _stream$_writableStat3 === undefined\n          ? undefined\n          : _stream$_writableStat3.errorEmitted) !== null && _ref2 !== undefined\n        ? _ref2\n        : (_stream$_readableStat4 = stream._readableState) === null || _stream$_readableStat4 === undefined\n        ? undefined\n        : _stream$_readableStat4.errored) !== null && _ref !== undefined\n      ? _ref\n      : (_stream$_writableStat4 = stream._writableState) === null || _stream$_writableStat4 === undefined\n      ? undefined\n      : _stream$_writableStat4.errored)\n  )\n}\nmodule.exports = {\n  isDestroyed,\n  kIsDestroyed,\n  isDisturbed,\n  kIsDisturbed,\n  isErrored,\n  kIsErrored,\n  isReadable,\n  kIsReadable,\n  kIsClosedPromise,\n  kControllerErrorFunction,\n  kIsWritable,\n  isClosed,\n  isDuplexNodeStream,\n  isFinished,\n  isIterable,\n  isReadableNodeStream,\n  isReadableStream,\n  isReadableEnded,\n  isReadableFinished,\n  isReadableErrored,\n  isNodeStream,\n  isWebStream,\n  isWritable,\n  isWritableNodeStream,\n  isWritableStream,\n  isWritableEnded,\n  isWritableFinished,\n  isWritableErrored,\n  isServerRequest,\n  isServerResponse,\n  willEmitClose,\n  isTransformStream\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,mBAAmB,EAAE,cAAc,EAAE,SAAS,EAAE;AAExD,4DAA4D;AAC5D,0DAA0D;AAC1D,6DAA6D;AAC7D,+CAA+C;AAC/C,MAAM,eAAe,UAAU;AAC/B,MAAM,aAAa,UAAU;AAC7B,MAAM,cAAc,UAAU;AAC9B,MAAM,cAAc,UAAU;AAC9B,MAAM,eAAe,UAAU;AAC/B,MAAM,mBAAmB,UAAU;AACnC,MAAM,2BAA2B,UAAU;AAC3C,SAAS,qBAAqB,GAAG,EAAE,SAAS,KAAK;IAC/C,IAAI;IACJ,OAAO,CAAC,CACN,CACE,OACA,OAAO,IAAI,IAAI,KAAK,cACpB,OAAO,IAAI,EAAE,KAAK,cAClB,CAAC,CAAC,UAAW,OAAO,IAAI,KAAK,KAAK,cAAc,OAAO,IAAI,MAAM,KAAK,UAAW,KACjF,CAAC,CAAC,IAAI,cAAc,IAClB,CAAC,CAAC,sBAAsB,IAAI,cAAc,MAAM,QAAQ,wBAAwB,YAC5E,YACA,oBAAoB,QAAQ,MAAM,KAAK,KAC7C,SAAS;IACT,CAAC,CAAC,IAAI,cAAc,IAAI,IAAI,cAAc,CAC5C,EAAE,sBAAsB;;AAE5B;AACA,SAAS,qBAAqB,GAAG;IAC/B,IAAI;IACJ,OAAO,CAAC,CACN,CACE,OACA,OAAO,IAAI,KAAK,KAAK,cACrB,OAAO,IAAI,EAAE,KAAK,cAClB,CAAC,CAAC,IAAI,cAAc,IAClB,CAAC,CAAC,sBAAsB,IAAI,cAAc,MAAM,QAAQ,wBAAwB,YAC5E,YACA,oBAAoB,QAAQ,MAAM,KAAK,CAC/C,EAAE,SAAS;;AAEf;AACA,SAAS,mBAAmB,GAAG;IAC7B,OAAO,CAAC,CAAC,CACP,OACA,OAAO,IAAI,IAAI,KAAK,cACpB,IAAI,cAAc,IAClB,OAAO,IAAI,EAAE,KAAK,cAClB,OAAO,IAAI,KAAK,KAAK,UACvB;AACF;AACA,SAAS,aAAa,GAAG;IACvB,OACE,OACA,CAAC,IAAI,cAAc,IACjB,IAAI,cAAc,IACjB,OAAO,IAAI,KAAK,KAAK,cAAc,OAAO,IAAI,EAAE,KAAK,cACrD,OAAO,IAAI,IAAI,KAAK,cAAc,OAAO,IAAI,EAAE,KAAK,UAAW;AAEtE;AACA,SAAS,iBAAiB,GAAG;IAC3B,OAAO,CAAC,CAAC,CACP,OACA,CAAC,aAAa,QACd,OAAO,IAAI,WAAW,KAAK,cAC3B,OAAO,IAAI,SAAS,KAAK,cACzB,OAAO,IAAI,MAAM,KAAK,UACxB;AACF;AACA,SAAS,iBAAiB,GAAG;IAC3B,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,QAAQ,OAAO,IAAI,SAAS,KAAK,cAAc,OAAO,IAAI,KAAK,KAAK,UAAU;AAC/G;AACA,SAAS,kBAAkB,GAAG;IAC5B,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,QAAQ,OAAO,IAAI,QAAQ,KAAK,YAAY,OAAO,IAAI,QAAQ,KAAK,QAAQ;AAC7G;AACA,SAAS,YAAY,GAAG;IACtB,OAAO,iBAAiB,QAAQ,iBAAiB,QAAQ,kBAAkB;AAC7E;AACA,SAAS,WAAW,GAAG,EAAE,OAAO;IAC9B,IAAI,OAAO,MAAM,OAAO;IACxB,IAAI,YAAY,MAAM,OAAO,OAAO,GAAG,CAAC,oBAAoB,KAAK;IACjE,IAAI,YAAY,OAAO,OAAO,OAAO,GAAG,CAAC,eAAe,KAAK;IAC7D,OAAO,OAAO,GAAG,CAAC,oBAAoB,KAAK,cAAc,OAAO,GAAG,CAAC,eAAe,KAAK;AAC1F;AACA,SAAS,YAAY,MAAM;IACzB,IAAI,CAAC,aAAa,SAAS,OAAO;IAClC,MAAM,SAAS,OAAO,cAAc;IACpC,MAAM,SAAS,OAAO,cAAc;IACpC,MAAM,QAAQ,UAAU;IACxB,OAAO,CAAC,CAAC,CAAC,OAAO,SAAS,IAAI,MAAM,CAAC,aAAa,IAAK,UAAU,QAAQ,UAAU,aAAa,MAAM,SAAS,AAAC;AAClH;AAEA,qBAAqB;AACrB,SAAS,gBAAgB,MAAM;IAC7B,IAAI,CAAC,qBAAqB,SAAS,OAAO;IAC1C,IAAI,OAAO,aAAa,KAAK,MAAM,OAAO;IAC1C,MAAM,SAAS,OAAO,cAAc;IACpC,IAAI,WAAW,QAAQ,WAAW,aAAa,OAAO,OAAO,EAAE,OAAO;IACtE,IAAI,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,KAAK,MAAM,WAAW,OAAO;IACtG,OAAO,OAAO,KAAK;AACrB;AAEA,yBAAyB;AACzB,SAAS,mBAAmB,MAAM,EAAE,MAAM;IACxC,IAAI,CAAC,qBAAqB,SAAS,OAAO;IAC1C,IAAI,OAAO,gBAAgB,KAAK,MAAM,OAAO;IAC7C,MAAM,SAAS,OAAO,cAAc;IACpC,IAAI,WAAW,QAAQ,WAAW,aAAa,OAAO,OAAO,EAAE,OAAO;IACtE,IAAI,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,QAAQ,MAAM,WAAW,OAAO;IACzG,OAAO,CAAC,CAAC,CAAC,OAAO,QAAQ,IAAK,WAAW,SAAS,OAAO,KAAK,KAAK,QAAQ,OAAO,MAAM,KAAK,CAAE;AACjG;AAEA,0BAA0B;AAC1B,SAAS,gBAAgB,MAAM;IAC7B,IAAI,CAAC,qBAAqB,SAAS,OAAO;IAC1C,IAAI,OAAO,aAAa,KAAK,MAAM,OAAO;IAC1C,MAAM,SAAS,OAAO,cAAc;IACpC,IAAI,CAAC,UAAU,OAAO,OAAO,EAAE,OAAO;IACtC,IAAI,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,KAAK,MAAM,WAAW,OAAO;IACtG,OAAO,OAAO,KAAK;AACrB;AAEA,sBAAsB;AACtB,SAAS,mBAAmB,MAAM,EAAE,MAAM;IACxC,IAAI,CAAC,qBAAqB,SAAS,OAAO;IAC1C,MAAM,SAAS,OAAO,cAAc;IACpC,IAAI,WAAW,QAAQ,WAAW,aAAa,OAAO,OAAO,EAAE,OAAO;IACtE,IAAI,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,UAAU,MAAM,WAAW,OAAO;IAC3G,OAAO,CAAC,CAAC,CAAC,OAAO,UAAU,IAAK,WAAW,SAAS,OAAO,KAAK,KAAK,QAAQ,OAAO,MAAM,KAAK,CAAE;AACnG;AACA,SAAS,WAAW,MAAM;IACxB,IAAI,UAAU,MAAM,CAAC,YAAY,IAAI,MAAM,OAAO,MAAM,CAAC,YAAY;IACrE,IAAI,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,QAAQ,MAAM,WAAW,OAAO;IACzG,IAAI,YAAY,SAAS,OAAO;IAChC,OAAO,qBAAqB,WAAW,OAAO,QAAQ,IAAI,CAAC,mBAAmB;AAChF;AACA,SAAS,WAAW,MAAM;IACxB,IAAI,UAAU,MAAM,CAAC,YAAY,IAAI,MAAM,OAAO,MAAM,CAAC,YAAY;IACrE,IAAI,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,QAAQ,MAAM,WAAW,OAAO;IACzG,IAAI,YAAY,SAAS,OAAO;IAChC,OAAO,qBAAqB,WAAW,OAAO,QAAQ,IAAI,CAAC,gBAAgB;AAC7E;AACA,SAAS,WAAW,MAAM,EAAE,IAAI;IAC9B,IAAI,CAAC,aAAa,SAAS;QACzB,OAAO;IACT;IACA,IAAI,YAAY,SAAS;QACvB,OAAO;IACT;IACA,IAAI,CAAC,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,MAAM,SAAS,WAAW,SAAS;QACrG,OAAO;IACT;IACA,IAAI,CAAC,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,MAAM,SAAS,WAAW,SAAS;QACrG,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,MAAM;IAC/B,IAAI,uBAAuB;IAC3B,IAAI,CAAC,aAAa,SAAS;QACzB,OAAO;IACT;IACA,IAAI,OAAO,eAAe,EAAE;QAC1B,OAAO,OAAO,eAAe;IAC/B;IACA,OAAO,CAAC,wBACN,CAAC,yBAAyB,OAAO,cAAc,MAAM,QAAQ,2BAA2B,YACpF,YACA,uBAAuB,OAAO,MAAM,QAAQ,0BAA0B,YACxE,wBACA;AACN;AACA,SAAS,kBAAkB,MAAM;IAC/B,IAAI,uBAAuB;IAC3B,IAAI,CAAC,aAAa,SAAS;QACzB,OAAO;IACT;IACA,IAAI,OAAO,eAAe,EAAE;QAC1B,OAAO,OAAO,eAAe;IAC/B;IACA,OAAO,CAAC,wBACN,CAAC,yBAAyB,OAAO,cAAc,MAAM,QAAQ,2BAA2B,YACpF,YACA,uBAAuB,OAAO,MAAM,QAAQ,0BAA0B,YACxE,wBACA;AACN;AACA,SAAS,SAAS,MAAM;IACtB,IAAI,CAAC,aAAa,SAAS;QACzB,OAAO;IACT;IACA,IAAI,OAAO,OAAO,MAAM,KAAK,WAAW;QACtC,OAAO,OAAO,MAAM;IACtB;IACA,MAAM,SAAS,OAAO,cAAc;IACpC,MAAM,SAAS,OAAO,cAAc;IACpC,IACE,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,MAAM,MAAM,aACjF,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,MAAM,MAAM,WACjF;QACA,OACE,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,MAAM,KACpE,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,MAAM;IAExE;IACA,IAAI,OAAO,OAAO,OAAO,KAAK,aAAa,kBAAkB,SAAS;QACpE,OAAO,OAAO,OAAO;IACvB;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,MAAM;IAC/B,OACE,OAAO,OAAO,OAAO,KAAK,aAC1B,OAAO,OAAO,iBAAiB,KAAK,aACpC,OAAO,OAAO,kBAAkB,KAAK,aACrC,OAAO,OAAO,eAAe,KAAK;AAEtC;AACA,SAAS,iBAAiB,MAAM;IAC9B,OAAO,OAAO,OAAO,QAAQ,KAAK,aAAa,kBAAkB;AACnE;AACA,SAAS,gBAAgB,MAAM;IAC7B,IAAI;IACJ,OACE,OAAO,OAAO,UAAU,KAAK,aAC7B,OAAO,OAAO,OAAO,KAAK,aAC1B,CAAC,CAAC,cAAc,OAAO,GAAG,MAAM,QAAQ,gBAAgB,YAAY,YAAY,YAAY,gBAAgB,MAC1G;AAEN;AACA,SAAS,cAAc,MAAM;IAC3B,IAAI,CAAC,aAAa,SAAS,OAAO;IAClC,MAAM,SAAS,OAAO,cAAc;IACpC,MAAM,SAAS,OAAO,cAAc;IACpC,MAAM,QAAQ,UAAU;IACxB,OACE,AAAC,CAAC,SAAS,iBAAiB,WAAY,CAAC,CAAC,CAAC,SAAS,MAAM,WAAW,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,KAAK,KAAK;AAEtH;AACA,SAAS,YAAY,MAAM;IACzB,IAAI;IACJ,OAAO,CAAC,CAAC,CACP,UACA,CAAC,CAAC,uBAAuB,MAAM,CAAC,aAAa,MAAM,QAAQ,yBAAyB,YAChF,uBACA,OAAO,eAAe,IAAI,OAAO,eAAe,CACtD;AACF;AACA,SAAS,UAAU,MAAM;IACvB,IAAI,MACF,OACA,OACA,OACA,OACA,oBACA,wBACA,wBACA,wBACA;IACF,OAAO,CAAC,CAAC,CACP,UACA,CAAC,CAAC,OACA,CAAC,QACC,CAAC,QACC,CAAC,QACC,CAAC,QACC,CAAC,qBAAqB,MAAM,CAAC,WAAW,MAAM,QAAQ,uBAAuB,YACzE,qBACA,OAAO,eAAe,MAAM,QAAQ,UAAU,YAChD,QACA,OAAO,eAAe,MAAM,QAAQ,UAAU,YAChD,QACA,CAAC,yBAAyB,OAAO,cAAc,MAAM,QAAQ,2BAA2B,YACxF,YACA,uBAAuB,YAAY,MAAM,QAAQ,UAAU,YAC7D,QACA,CAAC,yBAAyB,OAAO,cAAc,MAAM,QAAQ,2BAA2B,YACxF,YACA,uBAAuB,YAAY,MAAM,QAAQ,UAAU,YAC7D,QACA,CAAC,yBAAyB,OAAO,cAAc,MAAM,QAAQ,2BAA2B,YACxF,YACA,uBAAuB,OAAO,MAAM,QAAQ,SAAS,YACvD,OACA,CAAC,yBAAyB,OAAO,cAAc,MAAM,QAAQ,2BAA2B,YACxF,YACA,uBAAuB,OAAO,CACpC;AACF;AACA,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/destroy.js"], "sourcesContent": ["'use strict'\n\n/* replacement start */\n\nconst process = require('process/')\n\n/* replacement end */\n\nconst {\n  aggregateTwoErrors,\n  codes: { ERR_MULTIPLE_CALLBACK },\n  AbortError\n} = require('../../ours/errors')\nconst { Symbol } = require('../../ours/primordials')\nconst { kIsDestroyed, isDestroyed, isFinished, isServerRequest } = require('./utils')\nconst kDestroy = Symbol('kDestroy')\nconst kConstruct = Symbol('kConstruct')\nfunction checkError(err, w, r) {\n  if (err) {\n    // Avoid V8 leak, https://github.com/nodejs/node/pull/34103#issuecomment-652002364\n    err.stack // eslint-disable-line no-unused-expressions\n\n    if (w && !w.errored) {\n      w.errored = err\n    }\n    if (r && !r.errored) {\n      r.errored = err\n    }\n  }\n}\n\n// Backwards compat. cb() is undocumented and unused in core but\n// unfortunately might be used by modules.\nfunction destroy(err, cb) {\n  const r = this._readableState\n  const w = this._writableState\n  // With duplex streams we use the writable side for state.\n  const s = w || r\n  if ((w !== null && w !== undefined && w.destroyed) || (r !== null && r !== undefined && r.destroyed)) {\n    if (typeof cb === 'function') {\n      cb()\n    }\n    return this\n  }\n\n  // We set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n  checkError(err, w, r)\n  if (w) {\n    w.destroyed = true\n  }\n  if (r) {\n    r.destroyed = true\n  }\n\n  // If still constructing then defer calling _destroy.\n  if (!s.constructed) {\n    this.once(kDestroy, function (er) {\n      _destroy(this, aggregateTwoErrors(er, err), cb)\n    })\n  } else {\n    _destroy(this, err, cb)\n  }\n  return this\n}\nfunction _destroy(self, err, cb) {\n  let called = false\n  function onDestroy(err) {\n    if (called) {\n      return\n    }\n    called = true\n    const r = self._readableState\n    const w = self._writableState\n    checkError(err, w, r)\n    if (w) {\n      w.closed = true\n    }\n    if (r) {\n      r.closed = true\n    }\n    if (typeof cb === 'function') {\n      cb(err)\n    }\n    if (err) {\n      process.nextTick(emitErrorCloseNT, self, err)\n    } else {\n      process.nextTick(emitCloseNT, self)\n    }\n  }\n  try {\n    self._destroy(err || null, onDestroy)\n  } catch (err) {\n    onDestroy(err)\n  }\n}\nfunction emitErrorCloseNT(self, err) {\n  emitErrorNT(self, err)\n  emitCloseNT(self)\n}\nfunction emitCloseNT(self) {\n  const r = self._readableState\n  const w = self._writableState\n  if (w) {\n    w.closeEmitted = true\n  }\n  if (r) {\n    r.closeEmitted = true\n  }\n  if ((w !== null && w !== undefined && w.emitClose) || (r !== null && r !== undefined && r.emitClose)) {\n    self.emit('close')\n  }\n}\nfunction emitErrorNT(self, err) {\n  const r = self._readableState\n  const w = self._writableState\n  if ((w !== null && w !== undefined && w.errorEmitted) || (r !== null && r !== undefined && r.errorEmitted)) {\n    return\n  }\n  if (w) {\n    w.errorEmitted = true\n  }\n  if (r) {\n    r.errorEmitted = true\n  }\n  self.emit('error', err)\n}\nfunction undestroy() {\n  const r = this._readableState\n  const w = this._writableState\n  if (r) {\n    r.constructed = true\n    r.closed = false\n    r.closeEmitted = false\n    r.destroyed = false\n    r.errored = null\n    r.errorEmitted = false\n    r.reading = false\n    r.ended = r.readable === false\n    r.endEmitted = r.readable === false\n  }\n  if (w) {\n    w.constructed = true\n    w.destroyed = false\n    w.closed = false\n    w.closeEmitted = false\n    w.errored = null\n    w.errorEmitted = false\n    w.finalCalled = false\n    w.prefinished = false\n    w.ended = w.writable === false\n    w.ending = w.writable === false\n    w.finished = w.writable === false\n  }\n}\nfunction errorOrDestroy(stream, err, sync) {\n  // We have tests that rely on errors being emitted\n  // in the same tick, so changing this is semver major.\n  // For now when you opt-in to autoDestroy we allow\n  // the error to be emitted nextTick. In a future\n  // semver major update we should change the default to this.\n\n  const r = stream._readableState\n  const w = stream._writableState\n  if ((w !== null && w !== undefined && w.destroyed) || (r !== null && r !== undefined && r.destroyed)) {\n    return this\n  }\n  if ((r !== null && r !== undefined && r.autoDestroy) || (w !== null && w !== undefined && w.autoDestroy))\n    stream.destroy(err)\n  else if (err) {\n    // Avoid V8 leak, https://github.com/nodejs/node/pull/34103#issuecomment-652002364\n    err.stack // eslint-disable-line no-unused-expressions\n\n    if (w && !w.errored) {\n      w.errored = err\n    }\n    if (r && !r.errored) {\n      r.errored = err\n    }\n    if (sync) {\n      process.nextTick(emitErrorNT, stream, err)\n    } else {\n      emitErrorNT(stream, err)\n    }\n  }\n}\nfunction construct(stream, cb) {\n  if (typeof stream._construct !== 'function') {\n    return\n  }\n  const r = stream._readableState\n  const w = stream._writableState\n  if (r) {\n    r.constructed = false\n  }\n  if (w) {\n    w.constructed = false\n  }\n  stream.once(kConstruct, cb)\n  if (stream.listenerCount(kConstruct) > 1) {\n    // Duplex\n    return\n  }\n  process.nextTick(constructNT, stream)\n}\nfunction constructNT(stream) {\n  let called = false\n  function onConstruct(err) {\n    if (called) {\n      errorOrDestroy(stream, err !== null && err !== undefined ? err : new ERR_MULTIPLE_CALLBACK())\n      return\n    }\n    called = true\n    const r = stream._readableState\n    const w = stream._writableState\n    const s = w || r\n    if (r) {\n      r.constructed = true\n    }\n    if (w) {\n      w.constructed = true\n    }\n    if (s.destroyed) {\n      stream.emit(kDestroy, err)\n    } else if (err) {\n      errorOrDestroy(stream, err, true)\n    } else {\n      process.nextTick(emitConstructNT, stream)\n    }\n  }\n  try {\n    stream._construct((err) => {\n      process.nextTick(onConstruct, err)\n    })\n  } catch (err) {\n    process.nextTick(onConstruct, err)\n  }\n}\nfunction emitConstructNT(stream) {\n  stream.emit(kConstruct)\n}\nfunction isRequest(stream) {\n  return (stream === null || stream === undefined ? undefined : stream.setHeader) && typeof stream.abort === 'function'\n}\nfunction emitCloseLegacy(stream) {\n  stream.emit('close')\n}\nfunction emitErrorCloseLegacy(stream, err) {\n  stream.emit('error', err)\n  process.nextTick(emitCloseLegacy, stream)\n}\n\n// Normalize destroy for legacy.\nfunction destroyer(stream, err) {\n  if (!stream || isDestroyed(stream)) {\n    return\n  }\n  if (!err && !isFinished(stream)) {\n    err = new AbortError()\n  }\n\n  // TODO: Remove isRequest branches.\n  if (isServerRequest(stream)) {\n    stream.socket = null\n    stream.destroy(err)\n  } else if (isRequest(stream)) {\n    stream.abort()\n  } else if (isRequest(stream.req)) {\n    stream.req.abort()\n  } else if (typeof stream.destroy === 'function') {\n    stream.destroy(err)\n  } else if (typeof stream.close === 'function') {\n    // TODO: Don't lose err?\n    stream.close()\n  } else if (err) {\n    process.nextTick(emitErrorCloseLegacy, stream, err)\n  } else {\n    process.nextTick(emitCloseLegacy, stream)\n  }\n  if (!stream.destroyed) {\n    stream[kIsDestroyed] = true\n  }\n}\nmodule.exports = {\n  construct,\n  destroyer,\n  destroy,\n  undestroy,\n  errorOrDestroy\n}\n"], "names": [], "mappings": "AAEA,qBAAqB,GAErB,MAAM;;;;;;;;;GAAkB;AAExB,mBAAmB,GAEnB,MAAM,EACJ,kBAAkB,EAClB,OAAO,EAAE,qBAAqB,EAAE,EAChC,UAAU,EACX;AACD,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE;AAChE,MAAM,WAAW,OAAO;AACxB,MAAM,aAAa,OAAO;AAC1B,SAAS,WAAW,GAAG,EAAE,CAAC,EAAE,CAAC;IAC3B,IAAI,KAAK;QACP,kFAAkF;QAClF,IAAI,KAAK,EAAC,4CAA4C;QAEtD,IAAI,KAAK,CAAC,EAAE,OAAO,EAAE;YACnB,EAAE,OAAO,GAAG;QACd;QACA,IAAI,KAAK,CAAC,EAAE,OAAO,EAAE;YACnB,EAAE,OAAO,GAAG;QACd;IACF;AACF;AAEA,gEAAgE;AAChE,0CAA0C;AAC1C,SAAS,QAAQ,GAAG,EAAE,EAAE;IACtB,MAAM,IAAI,IAAI,CAAC,cAAc;IAC7B,MAAM,IAAI,IAAI,CAAC,cAAc;IAC7B,0DAA0D;IAC1D,MAAM,IAAI,KAAK;IACf,IAAI,AAAC,MAAM,QAAQ,MAAM,aAAa,EAAE,SAAS,IAAM,MAAM,QAAQ,MAAM,aAAa,EAAE,SAAS,EAAG;QACpG,IAAI,OAAO,OAAO,YAAY;YAC5B;QACF;QACA,OAAO,IAAI;IACb;IAEA,kEAAkE;IAClE,2EAA2E;IAC3E,WAAW,KAAK,GAAG;IACnB,IAAI,GAAG;QACL,EAAE,SAAS,GAAG;IAChB;IACA,IAAI,GAAG;QACL,EAAE,SAAS,GAAG;IAChB;IAEA,qDAAqD;IACrD,IAAI,CAAC,EAAE,WAAW,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,UAAU,SAAU,EAAE;YAC9B,SAAS,IAAI,EAAE,mBAAmB,IAAI,MAAM;QAC9C;IACF,OAAO;QACL,SAAS,IAAI,EAAE,KAAK;IACtB;IACA,OAAO,IAAI;AACb;AACA,SAAS,SAAS,IAAI,EAAE,GAAG,EAAE,EAAE;IAC7B,IAAI,SAAS;IACb,SAAS,UAAU,GAAG;QACpB,IAAI,QAAQ;YACV;QACF;QACA,SAAS;QACT,MAAM,IAAI,KAAK,cAAc;QAC7B,MAAM,IAAI,KAAK,cAAc;QAC7B,WAAW,KAAK,GAAG;QACnB,IAAI,GAAG;YACL,EAAE,MAAM,GAAG;QACb;QACA,IAAI,GAAG;YACL,EAAE,MAAM,GAAG;QACb;QACA,IAAI,OAAO,OAAO,YAAY;YAC5B,GAAG;QACL;QACA,IAAI,KAAK;YACP,QAAQ,QAAQ,CAAC,kBAAkB,MAAM;QAC3C,OAAO;YACL,QAAQ,QAAQ,CAAC,aAAa;QAChC;IACF;IACA,IAAI;QACF,KAAK,QAAQ,CAAC,OAAO,MAAM;IAC7B,EAAE,OAAO,KAAK;QACZ,UAAU;IACZ;AACF;AACA,SAAS,iBAAiB,IAAI,EAAE,GAAG;IACjC,YAAY,MAAM;IAClB,YAAY;AACd;AACA,SAAS,YAAY,IAAI;IACvB,MAAM,IAAI,KAAK,cAAc;IAC7B,MAAM,IAAI,KAAK,cAAc;IAC7B,IAAI,GAAG;QACL,EAAE,YAAY,GAAG;IACnB;IACA,IAAI,GAAG;QACL,EAAE,YAAY,GAAG;IACnB;IACA,IAAI,AAAC,MAAM,QAAQ,MAAM,aAAa,EAAE,SAAS,IAAM,MAAM,QAAQ,MAAM,aAAa,EAAE,SAAS,EAAG;QACpG,KAAK,IAAI,CAAC;IACZ;AACF;AACA,SAAS,YAAY,IAAI,EAAE,GAAG;IAC5B,MAAM,IAAI,KAAK,cAAc;IAC7B,MAAM,IAAI,KAAK,cAAc;IAC7B,IAAI,AAAC,MAAM,QAAQ,MAAM,aAAa,EAAE,YAAY,IAAM,MAAM,QAAQ,MAAM,aAAa,EAAE,YAAY,EAAG;QAC1G;IACF;IACA,IAAI,GAAG;QACL,EAAE,YAAY,GAAG;IACnB;IACA,IAAI,GAAG;QACL,EAAE,YAAY,GAAG;IACnB;IACA,KAAK,IAAI,CAAC,SAAS;AACrB;AACA,SAAS;IACP,MAAM,IAAI,IAAI,CAAC,cAAc;IAC7B,MAAM,IAAI,IAAI,CAAC,cAAc;IAC7B,IAAI,GAAG;QACL,EAAE,WAAW,GAAG;QAChB,EAAE,MAAM,GAAG;QACX,EAAE,YAAY,GAAG;QACjB,EAAE,SAAS,GAAG;QACd,EAAE,OAAO,GAAG;QACZ,EAAE,YAAY,GAAG;QACjB,EAAE,OAAO,GAAG;QACZ,EAAE,KAAK,GAAG,EAAE,QAAQ,KAAK;QACzB,EAAE,UAAU,GAAG,EAAE,QAAQ,KAAK;IAChC;IACA,IAAI,GAAG;QACL,EAAE,WAAW,GAAG;QAChB,EAAE,SAAS,GAAG;QACd,EAAE,MAAM,GAAG;QACX,EAAE,YAAY,GAAG;QACjB,EAAE,OAAO,GAAG;QACZ,EAAE,YAAY,GAAG;QACjB,EAAE,WAAW,GAAG;QAChB,EAAE,WAAW,GAAG;QAChB,EAAE,KAAK,GAAG,EAAE,QAAQ,KAAK;QACzB,EAAE,MAAM,GAAG,EAAE,QAAQ,KAAK;QAC1B,EAAE,QAAQ,GAAG,EAAE,QAAQ,KAAK;IAC9B;AACF;AACA,SAAS,eAAe,MAAM,EAAE,GAAG,EAAE,IAAI;IACvC,kDAAkD;IAClD,sDAAsD;IACtD,kDAAkD;IAClD,gDAAgD;IAChD,4DAA4D;IAE5D,MAAM,IAAI,OAAO,cAAc;IAC/B,MAAM,IAAI,OAAO,cAAc;IAC/B,IAAI,AAAC,MAAM,QAAQ,MAAM,aAAa,EAAE,SAAS,IAAM,MAAM,QAAQ,MAAM,aAAa,EAAE,SAAS,EAAG;QACpG,OAAO,IAAI;IACb;IACA,IAAI,AAAC,MAAM,QAAQ,MAAM,aAAa,EAAE,WAAW,IAAM,MAAM,QAAQ,MAAM,aAAa,EAAE,WAAW,EACrG,OAAO,OAAO,CAAC;SACZ,IAAI,KAAK;QACZ,kFAAkF;QAClF,IAAI,KAAK,EAAC,4CAA4C;QAEtD,IAAI,KAAK,CAAC,EAAE,OAAO,EAAE;YACnB,EAAE,OAAO,GAAG;QACd;QACA,IAAI,KAAK,CAAC,EAAE,OAAO,EAAE;YACnB,EAAE,OAAO,GAAG;QACd;QACA,IAAI,MAAM;YACR,QAAQ,QAAQ,CAAC,aAAa,QAAQ;QACxC,OAAO;YACL,YAAY,QAAQ;QACtB;IACF;AACF;AACA,SAAS,UAAU,MAAM,EAAE,EAAE;IAC3B,IAAI,OAAO,OAAO,UAAU,KAAK,YAAY;QAC3C;IACF;IACA,MAAM,IAAI,OAAO,cAAc;IAC/B,MAAM,IAAI,OAAO,cAAc;IAC/B,IAAI,GAAG;QACL,EAAE,WAAW,GAAG;IAClB;IACA,IAAI,GAAG;QACL,EAAE,WAAW,GAAG;IAClB;IACA,OAAO,IAAI,CAAC,YAAY;IACxB,IAAI,OAAO,aAAa,CAAC,cAAc,GAAG;QACxC,SAAS;QACT;IACF;IACA,QAAQ,QAAQ,CAAC,aAAa;AAChC;AACA,SAAS,YAAY,MAAM;IACzB,IAAI,SAAS;IACb,SAAS,YAAY,GAAG;QACtB,IAAI,QAAQ;YACV,eAAe,QAAQ,QAAQ,QAAQ,QAAQ,YAAY,MAAM,IAAI;YACrE;QACF;QACA,SAAS;QACT,MAAM,IAAI,OAAO,cAAc;QAC/B,MAAM,IAAI,OAAO,cAAc;QAC/B,MAAM,IAAI,KAAK;QACf,IAAI,GAAG;YACL,EAAE,WAAW,GAAG;QAClB;QACA,IAAI,GAAG;YACL,EAAE,WAAW,GAAG;QAClB;QACA,IAAI,EAAE,SAAS,EAAE;YACf,OAAO,IAAI,CAAC,UAAU;QACxB,OAAO,IAAI,KAAK;YACd,eAAe,QAAQ,KAAK;QAC9B,OAAO;YACL,QAAQ,QAAQ,CAAC,iBAAiB;QACpC;IACF;IACA,IAAI;QACF,OAAO,UAAU,CAAC,CAAC;YACjB,QAAQ,QAAQ,CAAC,aAAa;QAChC;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,QAAQ,CAAC,aAAa;IAChC;AACF;AACA,SAAS,gBAAgB,MAAM;IAC7B,OAAO,IAAI,CAAC;AACd;AACA,SAAS,UAAU,MAAM;IACvB,OAAO,CAAC,WAAW,QAAQ,WAAW,YAAY,YAAY,OAAO,SAAS,KAAK,OAAO,OAAO,KAAK,KAAK;AAC7G;AACA,SAAS,gBAAgB,MAAM;IAC7B,OAAO,IAAI,CAAC;AACd;AACA,SAAS,qBAAqB,MAAM,EAAE,GAAG;IACvC,OAAO,IAAI,CAAC,SAAS;IACrB,QAAQ,QAAQ,CAAC,iBAAiB;AACpC;AAEA,gCAAgC;AAChC,SAAS,UAAU,MAAM,EAAE,GAAG;IAC5B,IAAI,CAAC,UAAU,YAAY,SAAS;QAClC;IACF;IACA,IAAI,CAAC,OAAO,CAAC,WAAW,SAAS;QAC/B,MAAM,IAAI;IACZ;IAEA,mCAAmC;IACnC,IAAI,gBAAgB,SAAS;QAC3B,OAAO,MAAM,GAAG;QAChB,OAAO,OAAO,CAAC;IACjB,OAAO,IAAI,UAAU,SAAS;QAC5B,OAAO,KAAK;IACd,OAAO,IAAI,UAAU,OAAO,GAAG,GAAG;QAChC,OAAO,GAAG,CAAC,KAAK;IAClB,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,YAAY;QAC/C,OAAO,OAAO,CAAC;IACjB,OAAO,IAAI,OAAO,OAAO,KAAK,KAAK,YAAY;QAC7C,wBAAwB;QACxB,OAAO,KAAK;IACd,OAAO,IAAI,KAAK;QACd,QAAQ,QAAQ,CAAC,sBAAsB,QAAQ;IACjD,OAAO;QACL,QAAQ,QAAQ,CAAC,iBAAiB;IACpC;IACA,IAAI,CAAC,OAAO,SAAS,EAAE;QACrB,MAAM,CAAC,aAAa,GAAG;IACzB;AACF;AACA,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3073, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/ours/util.js"], "sourcesContent": ["'use strict'\n\nconst bufferModule = require('buffer')\nconst { format, inspect } = require('./util/inspect')\nconst {\n  codes: { ERR_INVALID_ARG_TYPE }\n} = require('./errors')\nconst { kResistStopPropagation, AggregateError, SymbolDispose } = require('./primordials')\nconst AbortSignal = globalThis.AbortSignal || require('abort-controller').AbortSignal\nconst AbortController = globalThis.AbortController || require('abort-controller').AbortController\nconst AsyncFunction = Object.getPrototypeOf(async function () {}).constructor\nconst Blob = globalThis.Blob || bufferModule.Blob\n/* eslint-disable indent */\nconst isBlob =\n  typeof Blob !== 'undefined'\n    ? function isBlob(b) {\n        // eslint-disable-next-line indent\n        return b instanceof Blob\n      }\n    : function isBlob(b) {\n        return false\n      }\n/* eslint-enable indent */\n\nconst validateAbortSignal = (signal, name) => {\n  if (signal !== undefined && (signal === null || typeof signal !== 'object' || !('aborted' in signal))) {\n    throw new ERR_INVALID_ARG_TYPE(name, 'AbortSignal', signal)\n  }\n}\nconst validateFunction = (value, name) => {\n  if (typeof value !== 'function') {\n    throw new ERR_INVALID_ARG_TYPE(name, 'Function', value)\n  }\n}\nmodule.exports = {\n  AggregateError,\n  kEmptyObject: Object.freeze({}),\n  once(callback) {\n    let called = false\n    return function (...args) {\n      if (called) {\n        return\n      }\n      called = true\n      callback.apply(this, args)\n    }\n  },\n  createDeferredPromise: function () {\n    let resolve\n    let reject\n\n    // eslint-disable-next-line promise/param-names\n    const promise = new Promise((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n    return {\n      promise,\n      resolve,\n      reject\n    }\n  },\n  promisify(fn) {\n    return new Promise((resolve, reject) => {\n      fn((err, ...args) => {\n        if (err) {\n          return reject(err)\n        }\n        return resolve(...args)\n      })\n    })\n  },\n  debuglog() {\n    return function () {}\n  },\n  format,\n  inspect,\n  types: {\n    isAsyncFunction(fn) {\n      return fn instanceof AsyncFunction\n    },\n    isArrayBufferView(arr) {\n      return ArrayBuffer.isView(arr)\n    }\n  },\n  isBlob,\n  deprecate(fn, message) {\n    return fn\n  },\n  addAbortListener:\n    require('events').addAbortListener ||\n    function addAbortListener(signal, listener) {\n      if (signal === undefined) {\n        throw new ERR_INVALID_ARG_TYPE('signal', 'AbortSignal', signal)\n      }\n      validateAbortSignal(signal, 'signal')\n      validateFunction(listener, 'listener')\n      let removeEventListener\n      if (signal.aborted) {\n        queueMicrotask(() => listener())\n      } else {\n        signal.addEventListener('abort', listener, {\n          __proto__: null,\n          once: true,\n          [kResistStopPropagation]: true\n        })\n        removeEventListener = () => {\n          signal.removeEventListener('abort', listener)\n        }\n      }\n      return {\n        __proto__: null,\n        [SymbolDispose]() {\n          var _removeEventListener\n          ;(_removeEventListener = removeEventListener) === null || _removeEventListener === undefined\n            ? undefined\n            : _removeEventListener()\n        }\n      }\n    },\n  AbortSignalAny:\n    AbortSignal.any ||\n    function AbortSignalAny(signals) {\n      // Fast path if there is only one signal.\n      if (signals.length === 1) {\n        return signals[0]\n      }\n      const ac = new AbortController()\n      const abort = () => ac.abort()\n      signals.forEach((signal) => {\n        validateAbortSignal(signal, 'signals')\n        signal.addEventListener('abort', abort, {\n          once: true\n        })\n      })\n      ac.signal.addEventListener(\n        'abort',\n        () => {\n          signals.forEach((signal) => signal.removeEventListener('abort', abort))\n        },\n        {\n          once: true\n        }\n      )\n      return ac.signal\n    }\n}\nmodule.exports.promisify.custom = Symbol.for('nodejs.util.promisify.custom')\n"], "names": [], "mappings": "AAEA,MAAM;AACN,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;AACzB,MAAM,EACJ,OAAO,EAAE,oBAAoB,EAAE,EAChC;AACD,MAAM,EAAE,sBAAsB,EAAE,cAAc,EAAE,aAAa,EAAE;AAC/D,MAAM,cAAc,WAAW,WAAW,IAAI,qHAA4B,WAAW;AACrF,MAAM,kBAAkB,WAAW,eAAe,IAAI,qHAA4B,eAAe;AACjG,MAAM,gBAAgB,OAAO,cAAc,CAAC,kBAAmB,GAAG,WAAW;AAC7E,MAAM,OAAO,WAAW,IAAI,IAAI,aAAa,IAAI;AACjD,yBAAyB,GACzB,MAAM,SACJ,OAAO,SAAS,cACZ,SAAS,OAAO,CAAC;IACf,kCAAkC;IAClC,OAAO,aAAa;AACtB,IACA,SAAS,OAAO,CAAC;IACf,OAAO;AACT;AACN,wBAAwB,GAExB,MAAM,sBAAsB,CAAC,QAAQ;IACnC,IAAI,WAAW,aAAa,CAAC,WAAW,QAAQ,OAAO,WAAW,YAAY,CAAC,CAAC,aAAa,MAAM,CAAC,GAAG;QACrG,MAAM,IAAI,qBAAqB,MAAM,eAAe;IACtD;AACF;AACA,MAAM,mBAAmB,CAAC,OAAO;IAC/B,IAAI,OAAO,UAAU,YAAY;QAC/B,MAAM,IAAI,qBAAqB,MAAM,YAAY;IACnD;AACF;AACA,OAAO,OAAO,GAAG;IACf;IACA,cAAc,OAAO,MAAM,CAAC,CAAC;IAC7B,MAAK,QAAQ;QACX,IAAI,SAAS;QACb,OAAO,SAAU,GAAG,IAAI;YACtB,IAAI,QAAQ;gBACV;YACF;YACA,SAAS;YACT,SAAS,KAAK,CAAC,IAAI,EAAE;QACvB;IACF;IACA,uBAAuB;QACrB,IAAI;QACJ,IAAI;QAEJ,+CAA+C;QAC/C,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK;YAChC,UAAU;YACV,SAAS;QACX;QACA,OAAO;YACL;YACA;YACA;QACF;IACF;IACA,WAAU,EAAE;QACV,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,GAAG,CAAC,KAAK,GAAG;gBACV,IAAI,KAAK;oBACP,OAAO,OAAO;gBAChB;gBACA,OAAO,WAAW;YACpB;QACF;IACF;IACA;QACE,OAAO,YAAa;IACtB;IACA;IACA;IACA,OAAO;QACL,iBAAgB,EAAE;YAChB,OAAO,cAAc;QACvB;QACA,mBAAkB,GAAG;YACnB,OAAO,YAAY,MAAM,CAAC;QAC5B;IACF;IACA;IACA,WAAU,EAAE,EAAE,OAAO;QACnB,OAAO;IACT;IACA,kBACE,uEAAkB,gBAAgB,IAClC,SAAS,iBAAiB,MAAM,EAAE,QAAQ;QACxC,IAAI,WAAW,WAAW;YACxB,MAAM,IAAI,qBAAqB,UAAU,eAAe;QAC1D;QACA,oBAAoB,QAAQ;QAC5B,iBAAiB,UAAU;QAC3B,IAAI;QACJ,IAAI,OAAO,OAAO,EAAE;YAClB,eAAe,IAAM;QACvB,OAAO;YACL,OAAO,gBAAgB,CAAC,SAAS,UAAU;gBACzC,WAAW;gBACX,MAAM;gBACN,CAAC,uBAAuB,EAAE;YAC5B;YACA,sBAAsB;gBACpB,OAAO,mBAAmB,CAAC,SAAS;YACtC;QACF;QACA,OAAO;YACL,WAAW;YACX,CAAC,cAAc;gBACb,IAAI;gBACH,CAAC,uBAAuB,mBAAmB,MAAM,QAAQ,yBAAyB,YAC/E,YACA;YACN;QACF;IACF;IACF,gBACE,YAAY,GAAG,IACf,SAAS,eAAe,OAAO;QAC7B,yCAAyC;QACzC,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,OAAO,OAAO,CAAC,EAAE;QACnB;QACA,MAAM,KAAK,IAAI;QACf,MAAM,QAAQ,IAAM,GAAG,KAAK;QAC5B,QAAQ,OAAO,CAAC,CAAC;YACf,oBAAoB,QAAQ;YAC5B,OAAO,gBAAgB,CAAC,SAAS,OAAO;gBACtC,MAAM;YACR;QACF;QACA,GAAG,MAAM,CAAC,gBAAgB,CACxB,SACA;YACE,QAAQ,OAAO,CAAC,CAAC,SAAW,OAAO,mBAAmB,CAAC,SAAS;QAClE,GACA;YACE,MAAM;QACR;QAEF,OAAO,GAAG,MAAM;IAClB;AACJ;AACA,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,GAAG,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/validators.js"], "sourcesContent": ["/* eslint jsdoc/require-jsdoc: \"error\" */\n\n'use strict'\n\nconst {\n  ArrayIsArray,\n  ArrayPrototypeIncludes,\n  ArrayPrototypeJoin,\n  ArrayPrototypeMap,\n  NumberIsInteger,\n  NumberIsNaN,\n  NumberMAX_SAFE_INTEGER,\n  NumberMIN_SAFE_INTEGER,\n  NumberParseInt,\n  ObjectPrototypeHasOwnProperty,\n  RegExpPrototypeExec,\n  String,\n  StringPrototypeToUpperCase,\n  StringPrototypeTrim\n} = require('../ours/primordials')\nconst {\n  hideStackFrames,\n  codes: { ERR_SOCKET_BAD_PORT, ERR_INVALID_ARG_TYPE, ERR_INVALID_ARG_VALUE, ERR_OUT_OF_RANGE, ERR_UNKNOWN_SIGNAL }\n} = require('../ours/errors')\nconst { normalizeEncoding } = require('../ours/util')\nconst { isAsyncFunction, isArrayBufferView } = require('../ours/util').types\nconst signals = {}\n\n/**\n * @param {*} value\n * @returns {boolean}\n */\nfunction isInt32(value) {\n  return value === (value | 0)\n}\n\n/**\n * @param {*} value\n * @returns {boolean}\n */\nfunction isUint32(value) {\n  return value === value >>> 0\n}\nconst octalReg = /^[0-7]+$/\nconst modeDesc = 'must be a 32-bit unsigned integer or an octal string'\n\n/**\n * Parse and validate values that will be converted into mode_t (the S_*\n * constants). Only valid numbers and octal strings are allowed. They could be\n * converted to 32-bit unsigned integers or non-negative signed integers in the\n * C++ land, but any value higher than 0o777 will result in platform-specific\n * behaviors.\n * @param {*} value Values to be validated\n * @param {string} name Name of the argument\n * @param {number} [def] If specified, will be returned for invalid values\n * @returns {number}\n */\nfunction parseFileMode(value, name, def) {\n  if (typeof value === 'undefined') {\n    value = def\n  }\n  if (typeof value === 'string') {\n    if (RegExpPrototypeExec(octalReg, value) === null) {\n      throw new ERR_INVALID_ARG_VALUE(name, value, modeDesc)\n    }\n    value = NumberParseInt(value, 8)\n  }\n  validateUint32(value, name)\n  return value\n}\n\n/**\n * @callback validateInteger\n * @param {*} value\n * @param {string} name\n * @param {number} [min]\n * @param {number} [max]\n * @returns {asserts value is number}\n */\n\n/** @type {validateInteger} */\nconst validateInteger = hideStackFrames((value, name, min = NumberMIN_SAFE_INTEGER, max = NumberMAX_SAFE_INTEGER) => {\n  if (typeof value !== 'number') throw new ERR_INVALID_ARG_TYPE(name, 'number', value)\n  if (!NumberIsInteger(value)) throw new ERR_OUT_OF_RANGE(name, 'an integer', value)\n  if (value < min || value > max) throw new ERR_OUT_OF_RANGE(name, `>= ${min} && <= ${max}`, value)\n})\n\n/**\n * @callback validateInt32\n * @param {*} value\n * @param {string} name\n * @param {number} [min]\n * @param {number} [max]\n * @returns {asserts value is number}\n */\n\n/** @type {validateInt32} */\nconst validateInt32 = hideStackFrames((value, name, min = -2147483648, max = 2147483647) => {\n  // The defaults for min and max correspond to the limits of 32-bit integers.\n  if (typeof value !== 'number') {\n    throw new ERR_INVALID_ARG_TYPE(name, 'number', value)\n  }\n  if (!NumberIsInteger(value)) {\n    throw new ERR_OUT_OF_RANGE(name, 'an integer', value)\n  }\n  if (value < min || value > max) {\n    throw new ERR_OUT_OF_RANGE(name, `>= ${min} && <= ${max}`, value)\n  }\n})\n\n/**\n * @callback validateUint32\n * @param {*} value\n * @param {string} name\n * @param {number|boolean} [positive=false]\n * @returns {asserts value is number}\n */\n\n/** @type {validateUint32} */\nconst validateUint32 = hideStackFrames((value, name, positive = false) => {\n  if (typeof value !== 'number') {\n    throw new ERR_INVALID_ARG_TYPE(name, 'number', value)\n  }\n  if (!NumberIsInteger(value)) {\n    throw new ERR_OUT_OF_RANGE(name, 'an integer', value)\n  }\n  const min = positive ? 1 : 0\n  // 2 ** 32 === 4294967296\n  const max = 4294967295\n  if (value < min || value > max) {\n    throw new ERR_OUT_OF_RANGE(name, `>= ${min} && <= ${max}`, value)\n  }\n})\n\n/**\n * @callback validateString\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is string}\n */\n\n/** @type {validateString} */\nfunction validateString(value, name) {\n  if (typeof value !== 'string') throw new ERR_INVALID_ARG_TYPE(name, 'string', value)\n}\n\n/**\n * @callback validateNumber\n * @param {*} value\n * @param {string} name\n * @param {number} [min]\n * @param {number} [max]\n * @returns {asserts value is number}\n */\n\n/** @type {validateNumber} */\nfunction validateNumber(value, name, min = undefined, max) {\n  if (typeof value !== 'number') throw new ERR_INVALID_ARG_TYPE(name, 'number', value)\n  if (\n    (min != null && value < min) ||\n    (max != null && value > max) ||\n    ((min != null || max != null) && NumberIsNaN(value))\n  ) {\n    throw new ERR_OUT_OF_RANGE(\n      name,\n      `${min != null ? `>= ${min}` : ''}${min != null && max != null ? ' && ' : ''}${max != null ? `<= ${max}` : ''}`,\n      value\n    )\n  }\n}\n\n/**\n * @callback validateOneOf\n * @template T\n * @param {T} value\n * @param {string} name\n * @param {T[]} oneOf\n */\n\n/** @type {validateOneOf} */\nconst validateOneOf = hideStackFrames((value, name, oneOf) => {\n  if (!ArrayPrototypeIncludes(oneOf, value)) {\n    const allowed = ArrayPrototypeJoin(\n      ArrayPrototypeMap(oneOf, (v) => (typeof v === 'string' ? `'${v}'` : String(v))),\n      ', '\n    )\n    const reason = 'must be one of: ' + allowed\n    throw new ERR_INVALID_ARG_VALUE(name, value, reason)\n  }\n})\n\n/**\n * @callback validateBoolean\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is boolean}\n */\n\n/** @type {validateBoolean} */\nfunction validateBoolean(value, name) {\n  if (typeof value !== 'boolean') throw new ERR_INVALID_ARG_TYPE(name, 'boolean', value)\n}\n\n/**\n * @param {any} options\n * @param {string} key\n * @param {boolean} defaultValue\n * @returns {boolean}\n */\nfunction getOwnPropertyValueOrDefault(options, key, defaultValue) {\n  return options == null || !ObjectPrototypeHasOwnProperty(options, key) ? defaultValue : options[key]\n}\n\n/**\n * @callback validateObject\n * @param {*} value\n * @param {string} name\n * @param {{\n *   allowArray?: boolean,\n *   allowFunction?: boolean,\n *   nullable?: boolean\n * }} [options]\n */\n\n/** @type {validateObject} */\nconst validateObject = hideStackFrames((value, name, options = null) => {\n  const allowArray = getOwnPropertyValueOrDefault(options, 'allowArray', false)\n  const allowFunction = getOwnPropertyValueOrDefault(options, 'allowFunction', false)\n  const nullable = getOwnPropertyValueOrDefault(options, 'nullable', false)\n  if (\n    (!nullable && value === null) ||\n    (!allowArray && ArrayIsArray(value)) ||\n    (typeof value !== 'object' && (!allowFunction || typeof value !== 'function'))\n  ) {\n    throw new ERR_INVALID_ARG_TYPE(name, 'Object', value)\n  }\n})\n\n/**\n * @callback validateDictionary - We are using the Web IDL Standard definition\n *                                of \"dictionary\" here, which means any value\n *                                whose Type is either Undefined, Null, or\n *                                Object (which includes functions).\n * @param {*} value\n * @param {string} name\n * @see https://webidl.spec.whatwg.org/#es-dictionary\n * @see https://tc39.es/ecma262/#table-typeof-operator-results\n */\n\n/** @type {validateDictionary} */\nconst validateDictionary = hideStackFrames((value, name) => {\n  if (value != null && typeof value !== 'object' && typeof value !== 'function') {\n    throw new ERR_INVALID_ARG_TYPE(name, 'a dictionary', value)\n  }\n})\n\n/**\n * @callback validateArray\n * @param {*} value\n * @param {string} name\n * @param {number} [minLength]\n * @returns {asserts value is any[]}\n */\n\n/** @type {validateArray} */\nconst validateArray = hideStackFrames((value, name, minLength = 0) => {\n  if (!ArrayIsArray(value)) {\n    throw new ERR_INVALID_ARG_TYPE(name, 'Array', value)\n  }\n  if (value.length < minLength) {\n    const reason = `must be longer than ${minLength}`\n    throw new ERR_INVALID_ARG_VALUE(name, value, reason)\n  }\n})\n\n/**\n * @callback validateStringArray\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is string[]}\n */\n\n/** @type {validateStringArray} */\nfunction validateStringArray(value, name) {\n  validateArray(value, name)\n  for (let i = 0; i < value.length; i++) {\n    validateString(value[i], `${name}[${i}]`)\n  }\n}\n\n/**\n * @callback validateBooleanArray\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is boolean[]}\n */\n\n/** @type {validateBooleanArray} */\nfunction validateBooleanArray(value, name) {\n  validateArray(value, name)\n  for (let i = 0; i < value.length; i++) {\n    validateBoolean(value[i], `${name}[${i}]`)\n  }\n}\n\n/**\n * @callback validateAbortSignalArray\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is AbortSignal[]}\n */\n\n/** @type {validateAbortSignalArray} */\nfunction validateAbortSignalArray(value, name) {\n  validateArray(value, name)\n  for (let i = 0; i < value.length; i++) {\n    const signal = value[i]\n    const indexedName = `${name}[${i}]`\n    if (signal == null) {\n      throw new ERR_INVALID_ARG_TYPE(indexedName, 'AbortSignal', signal)\n    }\n    validateAbortSignal(signal, indexedName)\n  }\n}\n\n/**\n * @param {*} signal\n * @param {string} [name='signal']\n * @returns {asserts signal is keyof signals}\n */\nfunction validateSignalName(signal, name = 'signal') {\n  validateString(signal, name)\n  if (signals[signal] === undefined) {\n    if (signals[StringPrototypeToUpperCase(signal)] !== undefined) {\n      throw new ERR_UNKNOWN_SIGNAL(signal + ' (signals must use all capital letters)')\n    }\n    throw new ERR_UNKNOWN_SIGNAL(signal)\n  }\n}\n\n/**\n * @callback validateBuffer\n * @param {*} buffer\n * @param {string} [name='buffer']\n * @returns {asserts buffer is ArrayBufferView}\n */\n\n/** @type {validateBuffer} */\nconst validateBuffer = hideStackFrames((buffer, name = 'buffer') => {\n  if (!isArrayBufferView(buffer)) {\n    throw new ERR_INVALID_ARG_TYPE(name, ['Buffer', 'TypedArray', 'DataView'], buffer)\n  }\n})\n\n/**\n * @param {string} data\n * @param {string} encoding\n */\nfunction validateEncoding(data, encoding) {\n  const normalizedEncoding = normalizeEncoding(encoding)\n  const length = data.length\n  if (normalizedEncoding === 'hex' && length % 2 !== 0) {\n    throw new ERR_INVALID_ARG_VALUE('encoding', encoding, `is invalid for data of length ${length}`)\n  }\n}\n\n/**\n * Check that the port number is not NaN when coerced to a number,\n * is an integer and that it falls within the legal range of port numbers.\n * @param {*} port\n * @param {string} [name='Port']\n * @param {boolean} [allowZero=true]\n * @returns {number}\n */\nfunction validatePort(port, name = 'Port', allowZero = true) {\n  if (\n    (typeof port !== 'number' && typeof port !== 'string') ||\n    (typeof port === 'string' && StringPrototypeTrim(port).length === 0) ||\n    +port !== +port >>> 0 ||\n    port > 0xffff ||\n    (port === 0 && !allowZero)\n  ) {\n    throw new ERR_SOCKET_BAD_PORT(name, port, allowZero)\n  }\n  return port | 0\n}\n\n/**\n * @callback validateAbortSignal\n * @param {*} signal\n * @param {string} name\n */\n\n/** @type {validateAbortSignal} */\nconst validateAbortSignal = hideStackFrames((signal, name) => {\n  if (signal !== undefined && (signal === null || typeof signal !== 'object' || !('aborted' in signal))) {\n    throw new ERR_INVALID_ARG_TYPE(name, 'AbortSignal', signal)\n  }\n})\n\n/**\n * @callback validateFunction\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is Function}\n */\n\n/** @type {validateFunction} */\nconst validateFunction = hideStackFrames((value, name) => {\n  if (typeof value !== 'function') throw new ERR_INVALID_ARG_TYPE(name, 'Function', value)\n})\n\n/**\n * @callback validatePlainFunction\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is Function}\n */\n\n/** @type {validatePlainFunction} */\nconst validatePlainFunction = hideStackFrames((value, name) => {\n  if (typeof value !== 'function' || isAsyncFunction(value)) throw new ERR_INVALID_ARG_TYPE(name, 'Function', value)\n})\n\n/**\n * @callback validateUndefined\n * @param {*} value\n * @param {string} name\n * @returns {asserts value is undefined}\n */\n\n/** @type {validateUndefined} */\nconst validateUndefined = hideStackFrames((value, name) => {\n  if (value !== undefined) throw new ERR_INVALID_ARG_TYPE(name, 'undefined', value)\n})\n\n/**\n * @template T\n * @param {T} value\n * @param {string} name\n * @param {T[]} union\n */\nfunction validateUnion(value, name, union) {\n  if (!ArrayPrototypeIncludes(union, value)) {\n    throw new ERR_INVALID_ARG_TYPE(name, `('${ArrayPrototypeJoin(union, '|')}')`, value)\n  }\n}\n\n/*\n  The rules for the Link header field are described here:\n  https://www.rfc-editor.org/rfc/rfc8288.html#section-3\n\n  This regex validates any string surrounded by angle brackets\n  (not necessarily a valid URI reference) followed by zero or more\n  link-params separated by semicolons.\n*/\nconst linkValueRegExp = /^(?:<[^>]*>)(?:\\s*;\\s*[^;\"\\s]+(?:=(\")?[^;\"\\s]*\\1)?)*$/\n\n/**\n * @param {any} value\n * @param {string} name\n */\nfunction validateLinkHeaderFormat(value, name) {\n  if (typeof value === 'undefined' || !RegExpPrototypeExec(linkValueRegExp, value)) {\n    throw new ERR_INVALID_ARG_VALUE(\n      name,\n      value,\n      'must be an array or string of format \"</styles.css>; rel=preload; as=style\"'\n    )\n  }\n}\n\n/**\n * @param {any} hints\n * @return {string}\n */\nfunction validateLinkHeaderValue(hints) {\n  if (typeof hints === 'string') {\n    validateLinkHeaderFormat(hints, 'hints')\n    return hints\n  } else if (ArrayIsArray(hints)) {\n    const hintsLength = hints.length\n    let result = ''\n    if (hintsLength === 0) {\n      return result\n    }\n    for (let i = 0; i < hintsLength; i++) {\n      const link = hints[i]\n      validateLinkHeaderFormat(link, 'hints')\n      result += link\n      if (i !== hintsLength - 1) {\n        result += ', '\n      }\n    }\n    return result\n  }\n  throw new ERR_INVALID_ARG_VALUE(\n    'hints',\n    hints,\n    'must be an array or string of format \"</styles.css>; rel=preload; as=style\"'\n  )\n}\nmodule.exports = {\n  isInt32,\n  isUint32,\n  parseFileMode,\n  validateArray,\n  validateStringArray,\n  validateBooleanArray,\n  validateAbortSignalArray,\n  validateBoolean,\n  validateBuffer,\n  validateDictionary,\n  validateEncoding,\n  validateFunction,\n  validateInt32,\n  validateInteger,\n  validateNumber,\n  validateObject,\n  validateOneOf,\n  validatePlainFunction,\n  validatePort,\n  validateSignalName,\n  validateString,\n  validateUint32,\n  validateUndefined,\n  validateUnion,\n  validateAbortSignal,\n  validateLinkHeaderValue\n}\n"], "names": [], "mappings": "AAAA,uCAAuC,GAIvC,MAAM,EACJ,YAAY,EACZ,sBAAsB,EACtB,kBAAkB,EAClB,iBAAiB,EACjB,eAAe,EACf,WAAW,EACX,sBAAsB,EACtB,sBAAsB,EACtB,cAAc,EACd,6BAA6B,EAC7B,mBAAmB,EACnB,MAAM,EACN,0BAA0B,EAC1B,mBAAmB,EACpB;AACD,MAAM,EACJ,eAAe,EACf,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,EAClH;AACD,MAAM,EAAE,iBAAiB,EAAE;AAC3B,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG,4GAAwB,KAAK;AAC5E,MAAM,UAAU,CAAC;AAEjB;;;CAGC,GACD,SAAS,QAAQ,KAAK;IACpB,OAAO,UAAU,CAAC,QAAQ,CAAC;AAC7B;AAEA;;;CAGC,GACD,SAAS,SAAS,KAAK;IACrB,OAAO,UAAU,UAAU;AAC7B;AACA,MAAM,WAAW;AACjB,MAAM,WAAW;AAEjB;;;;;;;;;;CAUC,GACD,SAAS,cAAc,KAAK,EAAE,IAAI,EAAE,GAAG;IACrC,IAAI,OAAO,UAAU,aAAa;QAChC,QAAQ;IACV;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,oBAAoB,UAAU,WAAW,MAAM;YACjD,MAAM,IAAI,sBAAsB,MAAM,OAAO;QAC/C;QACA,QAAQ,eAAe,OAAO;IAChC;IACA,eAAe,OAAO;IACtB,OAAO;AACT;AAEA;;;;;;;CAOC,GAED,4BAA4B,GAC5B,MAAM,kBAAkB,gBAAgB,CAAC,OAAO,MAAM,MAAM,sBAAsB,EAAE,MAAM,sBAAsB;IAC9G,IAAI,OAAO,UAAU,UAAU,MAAM,IAAI,qBAAqB,MAAM,UAAU;IAC9E,IAAI,CAAC,gBAAgB,QAAQ,MAAM,IAAI,iBAAiB,MAAM,cAAc;IAC5E,IAAI,QAAQ,OAAO,QAAQ,KAAK,MAAM,IAAI,iBAAiB,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,EAAE,KAAK,EAAE;AAC7F;AAEA;;;;;;;CAOC,GAED,0BAA0B,GAC1B,MAAM,gBAAgB,gBAAgB,CAAC,OAAO,MAAM,MAAM,CAAC,UAAU,EAAE,MAAM,UAAU;IACrF,4EAA4E;IAC5E,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,IAAI,qBAAqB,MAAM,UAAU;IACjD;IACA,IAAI,CAAC,gBAAgB,QAAQ;QAC3B,MAAM,IAAI,iBAAiB,MAAM,cAAc;IACjD;IACA,IAAI,QAAQ,OAAO,QAAQ,KAAK;QAC9B,MAAM,IAAI,iBAAiB,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,EAAE,KAAK,EAAE;IAC7D;AACF;AAEA;;;;;;CAMC,GAED,2BAA2B,GAC3B,MAAM,iBAAiB,gBAAgB,CAAC,OAAO,MAAM,WAAW,KAAK;IACnE,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,IAAI,qBAAqB,MAAM,UAAU;IACjD;IACA,IAAI,CAAC,gBAAgB,QAAQ;QAC3B,MAAM,IAAI,iBAAiB,MAAM,cAAc;IACjD;IACA,MAAM,MAAM,WAAW,IAAI;IAC3B,yBAAyB;IACzB,MAAM,MAAM;IACZ,IAAI,QAAQ,OAAO,QAAQ,KAAK;QAC9B,MAAM,IAAI,iBAAiB,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,EAAE,KAAK,EAAE;IAC7D;AACF;AAEA;;;;;CAKC,GAED,2BAA2B,GAC3B,SAAS,eAAe,KAAK,EAAE,IAAI;IACjC,IAAI,OAAO,UAAU,UAAU,MAAM,IAAI,qBAAqB,MAAM,UAAU;AAChF;AAEA;;;;;;;CAOC,GAED,2BAA2B,GAC3B,SAAS,eAAe,KAAK,EAAE,IAAI,EAAE,MAAM,SAAS,EAAE,GAAG;IACvD,IAAI,OAAO,UAAU,UAAU,MAAM,IAAI,qBAAqB,MAAM,UAAU;IAC9E,IACE,AAAC,OAAO,QAAQ,QAAQ,OACvB,OAAO,QAAQ,QAAQ,OACvB,CAAC,OAAO,QAAQ,OAAO,IAAI,KAAK,YAAY,QAC7C;QACA,MAAM,IAAI,iBACR,MACA,GAAG,OAAO,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK,OAAO,QAAQ,OAAO,OAAO,SAAS,KAAK,OAAO,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,EAC/G;IAEJ;AACF;AAEA;;;;;;CAMC,GAED,0BAA0B,GAC1B,MAAM,gBAAgB,gBAAgB,CAAC,OAAO,MAAM;IAClD,IAAI,CAAC,uBAAuB,OAAO,QAAQ;QACzC,MAAM,UAAU,mBACd,kBAAkB,OAAO,CAAC,IAAO,OAAO,MAAM,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,OAAO,KAC3E;QAEF,MAAM,SAAS,qBAAqB;QACpC,MAAM,IAAI,sBAAsB,MAAM,OAAO;IAC/C;AACF;AAEA;;;;;CAKC,GAED,4BAA4B,GAC5B,SAAS,gBAAgB,KAAK,EAAE,IAAI;IAClC,IAAI,OAAO,UAAU,WAAW,MAAM,IAAI,qBAAqB,MAAM,WAAW;AAClF;AAEA;;;;;CAKC,GACD,SAAS,6BAA6B,OAAO,EAAE,GAAG,EAAE,YAAY;IAC9D,OAAO,WAAW,QAAQ,CAAC,8BAA8B,SAAS,OAAO,eAAe,OAAO,CAAC,IAAI;AACtG;AAEA;;;;;;;;;CASC,GAED,2BAA2B,GAC3B,MAAM,iBAAiB,gBAAgB,CAAC,OAAO,MAAM,UAAU,IAAI;IACjE,MAAM,aAAa,6BAA6B,SAAS,cAAc;IACvE,MAAM,gBAAgB,6BAA6B,SAAS,iBAAiB;IAC7E,MAAM,WAAW,6BAA6B,SAAS,YAAY;IACnE,IACE,AAAC,CAAC,YAAY,UAAU,QACvB,CAAC,cAAc,aAAa,UAC5B,OAAO,UAAU,YAAY,CAAC,CAAC,iBAAiB,OAAO,UAAU,UAAU,GAC5E;QACA,MAAM,IAAI,qBAAqB,MAAM,UAAU;IACjD;AACF;AAEA;;;;;;;;;CASC,GAED,+BAA+B,GAC/B,MAAM,qBAAqB,gBAAgB,CAAC,OAAO;IACjD,IAAI,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY;QAC7E,MAAM,IAAI,qBAAqB,MAAM,gBAAgB;IACvD;AACF;AAEA;;;;;;CAMC,GAED,0BAA0B,GAC1B,MAAM,gBAAgB,gBAAgB,CAAC,OAAO,MAAM,YAAY,CAAC;IAC/D,IAAI,CAAC,aAAa,QAAQ;QACxB,MAAM,IAAI,qBAAqB,MAAM,SAAS;IAChD;IACA,IAAI,MAAM,MAAM,GAAG,WAAW;QAC5B,MAAM,SAAS,CAAC,oBAAoB,EAAE,WAAW;QACjD,MAAM,IAAI,sBAAsB,MAAM,OAAO;IAC/C;AACF;AAEA;;;;;CAKC,GAED,gCAAgC,GAChC,SAAS,oBAAoB,KAAK,EAAE,IAAI;IACtC,cAAc,OAAO;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,eAAe,KAAK,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1C;AACF;AAEA;;;;;CAKC,GAED,iCAAiC,GACjC,SAAS,qBAAqB,KAAK,EAAE,IAAI;IACvC,cAAc,OAAO;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3C;AACF;AAEA;;;;;CAKC,GAED,qCAAqC,GACrC,SAAS,yBAAyB,KAAK,EAAE,IAAI;IAC3C,cAAc,OAAO;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,SAAS,KAAK,CAAC,EAAE;QACvB,MAAM,cAAc,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QACnC,IAAI,UAAU,MAAM;YAClB,MAAM,IAAI,qBAAqB,aAAa,eAAe;QAC7D;QACA,oBAAoB,QAAQ;IAC9B;AACF;AAEA;;;;CAIC,GACD,SAAS,mBAAmB,MAAM,EAAE,OAAO,QAAQ;IACjD,eAAe,QAAQ;IACvB,IAAI,OAAO,CAAC,OAAO,KAAK,WAAW;QACjC,IAAI,OAAO,CAAC,2BAA2B,QAAQ,KAAK,WAAW;YAC7D,MAAM,IAAI,mBAAmB,SAAS;QACxC;QACA,MAAM,IAAI,mBAAmB;IAC/B;AACF;AAEA;;;;;CAKC,GAED,2BAA2B,GAC3B,MAAM,iBAAiB,gBAAgB,CAAC,QAAQ,OAAO,QAAQ;IAC7D,IAAI,CAAC,kBAAkB,SAAS;QAC9B,MAAM,IAAI,qBAAqB,MAAM;YAAC;YAAU;YAAc;SAAW,EAAE;IAC7E;AACF;AAEA;;;CAGC,GACD,SAAS,iBAAiB,IAAI,EAAE,QAAQ;IACtC,MAAM,qBAAqB,kBAAkB;IAC7C,MAAM,SAAS,KAAK,MAAM;IAC1B,IAAI,uBAAuB,SAAS,SAAS,MAAM,GAAG;QACpD,MAAM,IAAI,sBAAsB,YAAY,UAAU,CAAC,8BAA8B,EAAE,QAAQ;IACjG;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,IAAI,EAAE,OAAO,MAAM,EAAE,YAAY,IAAI;IACzD,IACE,AAAC,OAAO,SAAS,YAAY,OAAO,SAAS,YAC5C,OAAO,SAAS,YAAY,oBAAoB,MAAM,MAAM,KAAK,KAClE,CAAC,SAAS,CAAC,SAAS,KACpB,OAAO,UACN,SAAS,KAAK,CAAC,WAChB;QACA,MAAM,IAAI,oBAAoB,MAAM,MAAM;IAC5C;IACA,OAAO,OAAO;AAChB;AAEA;;;;CAIC,GAED,gCAAgC,GAChC,MAAM,sBAAsB,gBAAgB,CAAC,QAAQ;IACnD,IAAI,WAAW,aAAa,CAAC,WAAW,QAAQ,OAAO,WAAW,YAAY,CAAC,CAAC,aAAa,MAAM,CAAC,GAAG;QACrG,MAAM,IAAI,qBAAqB,MAAM,eAAe;IACtD;AACF;AAEA;;;;;CAKC,GAED,6BAA6B,GAC7B,MAAM,mBAAmB,gBAAgB,CAAC,OAAO;IAC/C,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI,qBAAqB,MAAM,YAAY;AACpF;AAEA;;;;;CAKC,GAED,kCAAkC,GAClC,MAAM,wBAAwB,gBAAgB,CAAC,OAAO;IACpD,IAAI,OAAO,UAAU,cAAc,gBAAgB,QAAQ,MAAM,IAAI,qBAAqB,MAAM,YAAY;AAC9G;AAEA;;;;;CAKC,GAED,8BAA8B,GAC9B,MAAM,oBAAoB,gBAAgB,CAAC,OAAO;IAChD,IAAI,UAAU,WAAW,MAAM,IAAI,qBAAqB,MAAM,aAAa;AAC7E;AAEA;;;;;CAKC,GACD,SAAS,cAAc,KAAK,EAAE,IAAI,EAAE,KAAK;IACvC,IAAI,CAAC,uBAAuB,OAAO,QAAQ;QACzC,MAAM,IAAI,qBAAqB,MAAM,CAAC,EAAE,EAAE,mBAAmB,OAAO,KAAK,EAAE,CAAC,EAAE;IAChF;AACF;AAEA;;;;;;;AAOA,GACA,MAAM,kBAAkB;AAExB;;;CAGC,GACD,SAAS,yBAAyB,KAAK,EAAE,IAAI;IAC3C,IAAI,OAAO,UAAU,eAAe,CAAC,oBAAoB,iBAAiB,QAAQ;QAChF,MAAM,IAAI,sBACR,MACA,OACA;IAEJ;AACF;AAEA;;;CAGC,GACD,SAAS,wBAAwB,KAAK;IACpC,IAAI,OAAO,UAAU,UAAU;QAC7B,yBAAyB,OAAO;QAChC,OAAO;IACT,OAAO,IAAI,aAAa,QAAQ;QAC9B,MAAM,cAAc,MAAM,MAAM;QAChC,IAAI,SAAS;QACb,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YACpC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,yBAAyB,MAAM;YAC/B,UAAU;YACV,IAAI,MAAM,cAAc,GAAG;gBACzB,UAAU;YACZ;QACF;QACA,OAAO;IACT;IACA,MAAM,IAAI,sBACR,SACA,OACA;AAEJ;AACA,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/end-of-stream.js"], "sourcesContent": ["// Ported from https://github.com/mafintosh/end-of-stream with\n// permission from the author, <PERSON> (@mafintosh).\n\n'use strict'\n\n/* replacement start */\n\nconst process = require('process/')\n\n/* replacement end */\n\nconst { AbortError, codes } = require('../../ours/errors')\nconst { ERR_INVALID_ARG_TYPE, ERR_STREAM_PREMATURE_CLOSE } = codes\nconst { kEmptyObject, once } = require('../../ours/util')\nconst { validateAbortSignal, validateFunction, validateObject, validateBoolean } = require('../validators')\nconst { Promise, PromisePrototypeThen, SymbolDispose } = require('../../ours/primordials')\nconst {\n  isClosed,\n  isReadable,\n  isReadableNodeStream,\n  isReadableStream,\n  isReadableFinished,\n  isReadableErrored,\n  isWritable,\n  isWritableNodeStream,\n  isWritableStream,\n  isWritableFinished,\n  isWritableErrored,\n  isNodeStream,\n  willEmitClose: _willEmitClose,\n  kIsClosedPromise\n} = require('./utils')\nlet addAbortListener\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function'\n}\nconst nop = () => {}\nfunction eos(stream, options, callback) {\n  var _options$readable, _options$writable\n  if (arguments.length === 2) {\n    callback = options\n    options = kEmptyObject\n  } else if (options == null) {\n    options = kEmptyObject\n  } else {\n    validateObject(options, 'options')\n  }\n  validateFunction(callback, 'callback')\n  validateAbortSignal(options.signal, 'options.signal')\n  callback = once(callback)\n  if (isReadableStream(stream) || isWritableStream(stream)) {\n    return eosWeb(stream, options, callback)\n  }\n  if (!isNodeStream(stream)) {\n    throw new ERR_INVALID_ARG_TYPE('stream', ['ReadableStream', 'WritableStream', 'Stream'], stream)\n  }\n  const readable =\n    (_options$readable = options.readable) !== null && _options$readable !== undefined\n      ? _options$readable\n      : isReadableNodeStream(stream)\n  const writable =\n    (_options$writable = options.writable) !== null && _options$writable !== undefined\n      ? _options$writable\n      : isWritableNodeStream(stream)\n  const wState = stream._writableState\n  const rState = stream._readableState\n  const onlegacyfinish = () => {\n    if (!stream.writable) {\n      onfinish()\n    }\n  }\n\n  // TODO (ronag): Improve soft detection to include core modules and\n  // common ecosystem modules that do properly emit 'close' but fail\n  // this generic check.\n  let willEmitClose =\n    _willEmitClose(stream) && isReadableNodeStream(stream) === readable && isWritableNodeStream(stream) === writable\n  let writableFinished = isWritableFinished(stream, false)\n  const onfinish = () => {\n    writableFinished = true\n    // Stream should not be destroyed here. If it is that\n    // means that user space is doing something differently and\n    // we cannot trust willEmitClose.\n    if (stream.destroyed) {\n      willEmitClose = false\n    }\n    if (willEmitClose && (!stream.readable || readable)) {\n      return\n    }\n    if (!readable || readableFinished) {\n      callback.call(stream)\n    }\n  }\n  let readableFinished = isReadableFinished(stream, false)\n  const onend = () => {\n    readableFinished = true\n    // Stream should not be destroyed here. If it is that\n    // means that user space is doing something differently and\n    // we cannot trust willEmitClose.\n    if (stream.destroyed) {\n      willEmitClose = false\n    }\n    if (willEmitClose && (!stream.writable || writable)) {\n      return\n    }\n    if (!writable || writableFinished) {\n      callback.call(stream)\n    }\n  }\n  const onerror = (err) => {\n    callback.call(stream, err)\n  }\n  let closed = isClosed(stream)\n  const onclose = () => {\n    closed = true\n    const errored = isWritableErrored(stream) || isReadableErrored(stream)\n    if (errored && typeof errored !== 'boolean') {\n      return callback.call(stream, errored)\n    }\n    if (readable && !readableFinished && isReadableNodeStream(stream, true)) {\n      if (!isReadableFinished(stream, false)) return callback.call(stream, new ERR_STREAM_PREMATURE_CLOSE())\n    }\n    if (writable && !writableFinished) {\n      if (!isWritableFinished(stream, false)) return callback.call(stream, new ERR_STREAM_PREMATURE_CLOSE())\n    }\n    callback.call(stream)\n  }\n  const onclosed = () => {\n    closed = true\n    const errored = isWritableErrored(stream) || isReadableErrored(stream)\n    if (errored && typeof errored !== 'boolean') {\n      return callback.call(stream, errored)\n    }\n    callback.call(stream)\n  }\n  const onrequest = () => {\n    stream.req.on('finish', onfinish)\n  }\n  if (isRequest(stream)) {\n    stream.on('complete', onfinish)\n    if (!willEmitClose) {\n      stream.on('abort', onclose)\n    }\n    if (stream.req) {\n      onrequest()\n    } else {\n      stream.on('request', onrequest)\n    }\n  } else if (writable && !wState) {\n    // legacy streams\n    stream.on('end', onlegacyfinish)\n    stream.on('close', onlegacyfinish)\n  }\n\n  // Not all streams will emit 'close' after 'aborted'.\n  if (!willEmitClose && typeof stream.aborted === 'boolean') {\n    stream.on('aborted', onclose)\n  }\n  stream.on('end', onend)\n  stream.on('finish', onfinish)\n  if (options.error !== false) {\n    stream.on('error', onerror)\n  }\n  stream.on('close', onclose)\n  if (closed) {\n    process.nextTick(onclose)\n  } else if (\n    (wState !== null && wState !== undefined && wState.errorEmitted) ||\n    (rState !== null && rState !== undefined && rState.errorEmitted)\n  ) {\n    if (!willEmitClose) {\n      process.nextTick(onclosed)\n    }\n  } else if (\n    !readable &&\n    (!willEmitClose || isReadable(stream)) &&\n    (writableFinished || isWritable(stream) === false)\n  ) {\n    process.nextTick(onclosed)\n  } else if (\n    !writable &&\n    (!willEmitClose || isWritable(stream)) &&\n    (readableFinished || isReadable(stream) === false)\n  ) {\n    process.nextTick(onclosed)\n  } else if (rState && stream.req && stream.aborted) {\n    process.nextTick(onclosed)\n  }\n  const cleanup = () => {\n    callback = nop\n    stream.removeListener('aborted', onclose)\n    stream.removeListener('complete', onfinish)\n    stream.removeListener('abort', onclose)\n    stream.removeListener('request', onrequest)\n    if (stream.req) stream.req.removeListener('finish', onfinish)\n    stream.removeListener('end', onlegacyfinish)\n    stream.removeListener('close', onlegacyfinish)\n    stream.removeListener('finish', onfinish)\n    stream.removeListener('end', onend)\n    stream.removeListener('error', onerror)\n    stream.removeListener('close', onclose)\n  }\n  if (options.signal && !closed) {\n    const abort = () => {\n      // Keep it because cleanup removes it.\n      const endCallback = callback\n      cleanup()\n      endCallback.call(\n        stream,\n        new AbortError(undefined, {\n          cause: options.signal.reason\n        })\n      )\n    }\n    if (options.signal.aborted) {\n      process.nextTick(abort)\n    } else {\n      addAbortListener = addAbortListener || require('../../ours/util').addAbortListener\n      const disposable = addAbortListener(options.signal, abort)\n      const originalCallback = callback\n      callback = once((...args) => {\n        disposable[SymbolDispose]()\n        originalCallback.apply(stream, args)\n      })\n    }\n  }\n  return cleanup\n}\nfunction eosWeb(stream, options, callback) {\n  let isAborted = false\n  let abort = nop\n  if (options.signal) {\n    abort = () => {\n      isAborted = true\n      callback.call(\n        stream,\n        new AbortError(undefined, {\n          cause: options.signal.reason\n        })\n      )\n    }\n    if (options.signal.aborted) {\n      process.nextTick(abort)\n    } else {\n      addAbortListener = addAbortListener || require('../../ours/util').addAbortListener\n      const disposable = addAbortListener(options.signal, abort)\n      const originalCallback = callback\n      callback = once((...args) => {\n        disposable[SymbolDispose]()\n        originalCallback.apply(stream, args)\n      })\n    }\n  }\n  const resolverFn = (...args) => {\n    if (!isAborted) {\n      process.nextTick(() => callback.apply(stream, args))\n    }\n  }\n  PromisePrototypeThen(stream[kIsClosedPromise].promise, resolverFn, resolverFn)\n  return nop\n}\nfunction finished(stream, opts) {\n  var _opts\n  let autoCleanup = false\n  if (opts === null) {\n    opts = kEmptyObject\n  }\n  if ((_opts = opts) !== null && _opts !== undefined && _opts.cleanup) {\n    validateBoolean(opts.cleanup, 'cleanup')\n    autoCleanup = opts.cleanup\n  }\n  return new Promise((resolve, reject) => {\n    const cleanup = eos(stream, opts, (err) => {\n      if (autoCleanup) {\n        cleanup()\n      }\n      if (err) {\n        reject(err)\n      } else {\n        resolve()\n      }\n    })\n  })\n}\nmodule.exports = eos\nmodule.exports.finished = finished\n"], "names": [], "mappings": "AAAA,8DAA8D;AAC9D,yDAAyD;AAIzD,qBAAqB,GAErB,MAAM;;;;;;;;;GAAkB;AAExB,mBAAmB,GAEnB,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE;AAC3B,MAAM,EAAE,oBAAoB,EAAE,0BAA0B,EAAE,GAAG;AAC7D,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE;AAC5B,MAAM,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE;AAChF,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE;AACtD,MAAM,EACJ,QAAQ,EACR,UAAU,EACV,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,iBAAiB,EACjB,UAAU,EACV,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,eAAe,cAAc,EAC7B,gBAAgB,EACjB;AACD,IAAI;AACJ,SAAS,UAAU,MAAM;IACvB,OAAO,OAAO,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK;AACrD;AACA,MAAM,MAAM,KAAO;AACnB,SAAS,IAAI,MAAM,EAAE,OAAO,EAAE,QAAQ;IACpC,IAAI,mBAAmB;IACvB,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,WAAW;QACX,UAAU;IACZ,OAAO,IAAI,WAAW,MAAM;QAC1B,UAAU;IACZ,OAAO;QACL,eAAe,SAAS;IAC1B;IACA,iBAAiB,UAAU;IAC3B,oBAAoB,QAAQ,MAAM,EAAE;IACpC,WAAW,KAAK;IAChB,IAAI,iBAAiB,WAAW,iBAAiB,SAAS;QACxD,OAAO,OAAO,QAAQ,SAAS;IACjC;IACA,IAAI,CAAC,aAAa,SAAS;QACzB,MAAM,IAAI,qBAAqB,UAAU;YAAC;YAAkB;YAAkB;SAAS,EAAE;IAC3F;IACA,MAAM,WACJ,CAAC,oBAAoB,QAAQ,QAAQ,MAAM,QAAQ,sBAAsB,YACrE,oBACA,qBAAqB;IAC3B,MAAM,WACJ,CAAC,oBAAoB,QAAQ,QAAQ,MAAM,QAAQ,sBAAsB,YACrE,oBACA,qBAAqB;IAC3B,MAAM,SAAS,OAAO,cAAc;IACpC,MAAM,SAAS,OAAO,cAAc;IACpC,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,QAAQ,EAAE;YACpB;QACF;IACF;IAEA,mEAAmE;IACnE,kEAAkE;IAClE,sBAAsB;IACtB,IAAI,gBACF,eAAe,WAAW,qBAAqB,YAAY,YAAY,qBAAqB,YAAY;IAC1G,IAAI,mBAAmB,mBAAmB,QAAQ;IAClD,MAAM,WAAW;QACf,mBAAmB;QACnB,qDAAqD;QACrD,2DAA2D;QAC3D,iCAAiC;QACjC,IAAI,OAAO,SAAS,EAAE;YACpB,gBAAgB;QAClB;QACA,IAAI,iBAAiB,CAAC,CAAC,OAAO,QAAQ,IAAI,QAAQ,GAAG;YACnD;QACF;QACA,IAAI,CAAC,YAAY,kBAAkB;YACjC,SAAS,IAAI,CAAC;QAChB;IACF;IACA,IAAI,mBAAmB,mBAAmB,QAAQ;IAClD,MAAM,QAAQ;QACZ,mBAAmB;QACnB,qDAAqD;QACrD,2DAA2D;QAC3D,iCAAiC;QACjC,IAAI,OAAO,SAAS,EAAE;YACpB,gBAAgB;QAClB;QACA,IAAI,iBAAiB,CAAC,CAAC,OAAO,QAAQ,IAAI,QAAQ,GAAG;YACnD;QACF;QACA,IAAI,CAAC,YAAY,kBAAkB;YACjC,SAAS,IAAI,CAAC;QAChB;IACF;IACA,MAAM,UAAU,CAAC;QACf,SAAS,IAAI,CAAC,QAAQ;IACxB;IACA,IAAI,SAAS,SAAS;IACtB,MAAM,UAAU;QACd,SAAS;QACT,MAAM,UAAU,kBAAkB,WAAW,kBAAkB;QAC/D,IAAI,WAAW,OAAO,YAAY,WAAW;YAC3C,OAAO,SAAS,IAAI,CAAC,QAAQ;QAC/B;QACA,IAAI,YAAY,CAAC,oBAAoB,qBAAqB,QAAQ,OAAO;YACvE,IAAI,CAAC,mBAAmB,QAAQ,QAAQ,OAAO,SAAS,IAAI,CAAC,QAAQ,IAAI;QAC3E;QACA,IAAI,YAAY,CAAC,kBAAkB;YACjC,IAAI,CAAC,mBAAmB,QAAQ,QAAQ,OAAO,SAAS,IAAI,CAAC,QAAQ,IAAI;QAC3E;QACA,SAAS,IAAI,CAAC;IAChB;IACA,MAAM,WAAW;QACf,SAAS;QACT,MAAM,UAAU,kBAAkB,WAAW,kBAAkB;QAC/D,IAAI,WAAW,OAAO,YAAY,WAAW;YAC3C,OAAO,SAAS,IAAI,CAAC,QAAQ;QAC/B;QACA,SAAS,IAAI,CAAC;IAChB;IACA,MAAM,YAAY;QAChB,OAAO,GAAG,CAAC,EAAE,CAAC,UAAU;IAC1B;IACA,IAAI,UAAU,SAAS;QACrB,OAAO,EAAE,CAAC,YAAY;QACtB,IAAI,CAAC,eAAe;YAClB,OAAO,EAAE,CAAC,SAAS;QACrB;QACA,IAAI,OAAO,GAAG,EAAE;YACd;QACF,OAAO;YACL,OAAO,EAAE,CAAC,WAAW;QACvB;IACF,OAAO,IAAI,YAAY,CAAC,QAAQ;QAC9B,iBAAiB;QACjB,OAAO,EAAE,CAAC,OAAO;QACjB,OAAO,EAAE,CAAC,SAAS;IACrB;IAEA,qDAAqD;IACrD,IAAI,CAAC,iBAAiB,OAAO,OAAO,OAAO,KAAK,WAAW;QACzD,OAAO,EAAE,CAAC,WAAW;IACvB;IACA,OAAO,EAAE,CAAC,OAAO;IACjB,OAAO,EAAE,CAAC,UAAU;IACpB,IAAI,QAAQ,KAAK,KAAK,OAAO;QAC3B,OAAO,EAAE,CAAC,SAAS;IACrB;IACA,OAAO,EAAE,CAAC,SAAS;IACnB,IAAI,QAAQ;QACV,QAAQ,QAAQ,CAAC;IACnB,OAAO,IACL,AAAC,WAAW,QAAQ,WAAW,aAAa,OAAO,YAAY,IAC9D,WAAW,QAAQ,WAAW,aAAa,OAAO,YAAY,EAC/D;QACA,IAAI,CAAC,eAAe;YAClB,QAAQ,QAAQ,CAAC;QACnB;IACF,OAAO,IACL,CAAC,YACD,CAAC,CAAC,iBAAiB,WAAW,OAAO,KACrC,CAAC,oBAAoB,WAAW,YAAY,KAAK,GACjD;QACA,QAAQ,QAAQ,CAAC;IACnB,OAAO,IACL,CAAC,YACD,CAAC,CAAC,iBAAiB,WAAW,OAAO,KACrC,CAAC,oBAAoB,WAAW,YAAY,KAAK,GACjD;QACA,QAAQ,QAAQ,CAAC;IACnB,OAAO,IAAI,UAAU,OAAO,GAAG,IAAI,OAAO,OAAO,EAAE;QACjD,QAAQ,QAAQ,CAAC;IACnB;IACA,MAAM,UAAU;QACd,WAAW;QACX,OAAO,cAAc,CAAC,WAAW;QACjC,OAAO,cAAc,CAAC,YAAY;QAClC,OAAO,cAAc,CAAC,SAAS;QAC/B,OAAO,cAAc,CAAC,WAAW;QACjC,IAAI,OAAO,GAAG,EAAE,OAAO,GAAG,CAAC,cAAc,CAAC,UAAU;QACpD,OAAO,cAAc,CAAC,OAAO;QAC7B,OAAO,cAAc,CAAC,SAAS;QAC/B,OAAO,cAAc,CAAC,UAAU;QAChC,OAAO,cAAc,CAAC,OAAO;QAC7B,OAAO,cAAc,CAAC,SAAS;QAC/B,OAAO,cAAc,CAAC,SAAS;IACjC;IACA,IAAI,QAAQ,MAAM,IAAI,CAAC,QAAQ;QAC7B,MAAM,QAAQ;YACZ,sCAAsC;YACtC,MAAM,cAAc;YACpB;YACA,YAAY,IAAI,CACd,QACA,IAAI,WAAW,WAAW;gBACxB,OAAO,QAAQ,MAAM,CAAC,MAAM;YAC9B;QAEJ;QACA,IAAI,QAAQ,MAAM,CAAC,OAAO,EAAE;YAC1B,QAAQ,QAAQ,CAAC;QACnB,OAAO;YACL,mBAAmB,oBAAoB,4GAA2B,gBAAgB;YAClF,MAAM,aAAa,iBAAiB,QAAQ,MAAM,EAAE;YACpD,MAAM,mBAAmB;YACzB,WAAW,KAAK,CAAC,GAAG;gBAClB,UAAU,CAAC,cAAc;gBACzB,iBAAiB,KAAK,CAAC,QAAQ;YACjC;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,OAAO,MAAM,EAAE,OAAO,EAAE,QAAQ;IACvC,IAAI,YAAY;IAChB,IAAI,QAAQ;IACZ,IAAI,QAAQ,MAAM,EAAE;QAClB,QAAQ;YACN,YAAY;YACZ,SAAS,IAAI,CACX,QACA,IAAI,WAAW,WAAW;gBACxB,OAAO,QAAQ,MAAM,CAAC,MAAM;YAC9B;QAEJ;QACA,IAAI,QAAQ,MAAM,CAAC,OAAO,EAAE;YAC1B,QAAQ,QAAQ,CAAC;QACnB,OAAO;YACL,mBAAmB,oBAAoB,4GAA2B,gBAAgB;YAClF,MAAM,aAAa,iBAAiB,QAAQ,MAAM,EAAE;YACpD,MAAM,mBAAmB;YACzB,WAAW,KAAK,CAAC,GAAG;gBAClB,UAAU,CAAC,cAAc;gBACzB,iBAAiB,KAAK,CAAC,QAAQ;YACjC;QACF;IACF;IACA,MAAM,aAAa,CAAC,GAAG;QACrB,IAAI,CAAC,WAAW;YACd,QAAQ,QAAQ,CAAC,IAAM,SAAS,KAAK,CAAC,QAAQ;QAChD;IACF;IACA,qBAAqB,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,YAAY;IACnE,OAAO;AACT;AACA,SAAS,SAAS,MAAM,EAAE,IAAI;IAC5B,IAAI;IACJ,IAAI,cAAc;IAClB,IAAI,SAAS,MAAM;QACjB,OAAO;IACT;IACA,IAAI,CAAC,QAAQ,IAAI,MAAM,QAAQ,UAAU,aAAa,MAAM,OAAO,EAAE;QACnE,gBAAgB,KAAK,OAAO,EAAE;QAC9B,cAAc,KAAK,OAAO;IAC5B;IACA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,UAAU,IAAI,QAAQ,MAAM,CAAC;YACjC,IAAI,aAAa;gBACf;YACF;YACA,IAAI,KAAK;gBACP,OAAO;YACT,OAAO;gBACL;YACF;QACF;IACF;AACF;AACA,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3851, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/add-abort-signal.js"], "sourcesContent": ["'use strict'\n\nconst { SymbolDispose } = require('../../ours/primordials')\nconst { AbortError, codes } = require('../../ours/errors')\nconst { isNodeStream, isWebStream, kControllerErrorFunction } = require('./utils')\nconst eos = require('./end-of-stream')\nconst { ERR_INVALID_ARG_TYPE } = codes\nlet addAbortListener\n\n// This method is inlined here for readable-stream\n// It also does not allow for signal to not exist on the stream\n// https://github.com/nodejs/node/pull/36061#discussion_r533718029\nconst validateAbortSignal = (signal, name) => {\n  if (typeof signal !== 'object' || !('aborted' in signal)) {\n    throw new ERR_INVALID_ARG_TYPE(name, 'AbortSignal', signal)\n  }\n}\nmodule.exports.addAbortSignal = function addAbortSignal(signal, stream) {\n  validateAbortSignal(signal, 'signal')\n  if (!isNodeStream(stream) && !isWebStream(stream)) {\n    throw new ERR_INVALID_ARG_TYPE('stream', ['ReadableStream', 'WritableStream', 'Stream'], stream)\n  }\n  return module.exports.addAbortSignalNoValidate(signal, stream)\n}\nmodule.exports.addAbortSignalNoValidate = function (signal, stream) {\n  if (typeof signal !== 'object' || !('aborted' in signal)) {\n    return stream\n  }\n  const onAbort = isNodeStream(stream)\n    ? () => {\n        stream.destroy(\n          new AbortError(undefined, {\n            cause: signal.reason\n          })\n        )\n      }\n    : () => {\n        stream[kControllerErrorFunction](\n          new AbortError(undefined, {\n            cause: signal.reason\n          })\n        )\n      }\n  if (signal.aborted) {\n    onAbort()\n  } else {\n    addAbortListener = addAbortListener || require('../../ours/util').addAbortListener\n    const disposable = addAbortListener(signal, onAbort)\n    eos(stream, disposable[SymbolDispose])\n  }\n  return stream\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,aAAa,EAAE;AACvB,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE;AAC3B,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,wBAAwB,EAAE;AAC7D,MAAM;AACN,MAAM,EAAE,oBAAoB,EAAE,GAAG;AACjC,IAAI;AAEJ,kDAAkD;AAClD,+DAA+D;AAC/D,kEAAkE;AAClE,MAAM,sBAAsB,CAAC,QAAQ;IACnC,IAAI,OAAO,WAAW,YAAY,CAAC,CAAC,aAAa,MAAM,GAAG;QACxD,MAAM,IAAI,qBAAqB,MAAM,eAAe;IACtD;AACF;AACA,OAAO,OAAO,CAAC,cAAc,GAAG,SAAS,eAAe,MAAM,EAAE,MAAM;IACpE,oBAAoB,QAAQ;IAC5B,IAAI,CAAC,aAAa,WAAW,CAAC,YAAY,SAAS;QACjD,MAAM,IAAI,qBAAqB,UAAU;YAAC;YAAkB;YAAkB;SAAS,EAAE;IAC3F;IACA,OAAO,OAAO,OAAO,CAAC,wBAAwB,CAAC,QAAQ;AACzD;AACA,OAAO,OAAO,CAAC,wBAAwB,GAAG,SAAU,MAAM,EAAE,MAAM;IAChE,IAAI,OAAO,WAAW,YAAY,CAAC,CAAC,aAAa,MAAM,GAAG;QACxD,OAAO;IACT;IACA,MAAM,UAAU,aAAa,UACzB;QACE,OAAO,OAAO,CACZ,IAAI,WAAW,WAAW;YACxB,OAAO,OAAO,MAAM;QACtB;IAEJ,IACA;QACE,MAAM,CAAC,yBAAyB,CAC9B,IAAI,WAAW,WAAW;YACxB,OAAO,OAAO,MAAM;QACtB;IAEJ;IACJ,IAAI,OAAO,OAAO,EAAE;QAClB;IACF,OAAO;QACL,mBAAmB,oBAAoB,4GAA2B,gBAAgB;QAClF,MAAM,aAAa,iBAAiB,QAAQ;QAC5C,IAAI,QAAQ,UAAU,CAAC,cAAc;IACvC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3902, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/state.js"], "sourcesContent": ["'use strict'\n\nconst { <PERSON><PERSON><PERSON>or, NumberIsInteger } = require('../../ours/primordials')\nconst { validateInteger } = require('../validators')\nconst { ERR_INVALID_ARG_VALUE } = require('../../ours/errors').codes\nlet defaultHighWaterMarkBytes = 16 * 1024\nlet defaultHighWaterMarkObjectMode = 16\nfunction highWaterMarkFrom(options, isDuplex, duplexKey) {\n  return options.highWaterMark != null ? options.highWaterMark : isDuplex ? options[duplexKey] : null\n}\nfunction getDefaultHighWaterMark(objectMode) {\n  return objectMode ? defaultHighWaterMarkObjectMode : defaultHighWaterMarkBytes\n}\nfunction setDefaultHighWaterMark(objectMode, value) {\n  validateInteger(value, 'value', 0)\n  if (objectMode) {\n    defaultHighWaterMarkObjectMode = value\n  } else {\n    defaultHighWaterMarkBytes = value\n  }\n}\nfunction getHighWaterMark(state, options, duplexKey, isDuplex) {\n  const hwm = highWaterMarkFrom(options, isDuplex, duplexKey)\n  if (hwm != null) {\n    if (!NumberIsInteger(hwm) || hwm < 0) {\n      const name = isDuplex ? `options.${duplexKey}` : 'options.highWaterMark'\n      throw new ERR_INVALID_ARG_VALUE(name, hwm)\n    }\n    return MathFloor(hwm)\n  }\n\n  // Default value\n  return getDefaultHighWaterMark(state.objectMode)\n}\nmodule.exports = {\n  getHighWaterMark,\n  getDefaultHighWaterMark,\n  setDefaultHighWaterMark\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE;AACpC,MAAM,EAAE,eAAe,EAAE;AACzB,MAAM,EAAE,qBAAqB,EAAE,GAAG,8GAA6B,KAAK;AACpE,IAAI,4BAA4B,KAAK;AACrC,IAAI,iCAAiC;AACrC,SAAS,kBAAkB,OAAO,EAAE,QAAQ,EAAE,SAAS;IACrD,OAAO,QAAQ,aAAa,IAAI,OAAO,QAAQ,aAAa,GAAG,WAAW,OAAO,CAAC,UAAU,GAAG;AACjG;AACA,SAAS,wBAAwB,UAAU;IACzC,OAAO,aAAa,iCAAiC;AACvD;AACA,SAAS,wBAAwB,UAAU,EAAE,KAAK;IAChD,gBAAgB,OAAO,SAAS;IAChC,IAAI,YAAY;QACd,iCAAiC;IACnC,OAAO;QACL,4BAA4B;IAC9B;AACF;AACA,SAAS,iBAAiB,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;IAC3D,MAAM,MAAM,kBAAkB,SAAS,UAAU;IACjD,IAAI,OAAO,MAAM;QACf,IAAI,CAAC,gBAAgB,QAAQ,MAAM,GAAG;YACpC,MAAM,OAAO,WAAW,CAAC,QAAQ,EAAE,WAAW,GAAG;YACjD,MAAM,IAAI,sBAAsB,MAAM;QACxC;QACA,OAAO,UAAU;IACnB;IAEA,gBAAgB;IAChB,OAAO,wBAAwB,MAAM,UAAU;AACjD;AACA,OAAO,OAAO,GAAG;IACf;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3942, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/buffer_list.js"], "sourcesContent": ["'use strict'\n\nconst { StringPrototypeSlice, SymbolIterator, TypedArrayPrototypeSet, Uint8Array } = require('../../ours/primordials')\nconst { Buffer } = require('buffer')\nconst { inspect } = require('../../ours/util')\nmodule.exports = class BufferList {\n  constructor() {\n    this.head = null\n    this.tail = null\n    this.length = 0\n  }\n  push(v) {\n    const entry = {\n      data: v,\n      next: null\n    }\n    if (this.length > 0) this.tail.next = entry\n    else this.head = entry\n    this.tail = entry\n    ++this.length\n  }\n  unshift(v) {\n    const entry = {\n      data: v,\n      next: this.head\n    }\n    if (this.length === 0) this.tail = entry\n    this.head = entry\n    ++this.length\n  }\n  shift() {\n    if (this.length === 0) return\n    const ret = this.head.data\n    if (this.length === 1) this.head = this.tail = null\n    else this.head = this.head.next\n    --this.length\n    return ret\n  }\n  clear() {\n    this.head = this.tail = null\n    this.length = 0\n  }\n  join(s) {\n    if (this.length === 0) return ''\n    let p = this.head\n    let ret = '' + p.data\n    while ((p = p.next) !== null) ret += s + p.data\n    return ret\n  }\n  concat(n) {\n    if (this.length === 0) return Buffer.alloc(0)\n    const ret = Buffer.allocUnsafe(n >>> 0)\n    let p = this.head\n    let i = 0\n    while (p) {\n      TypedArrayPrototypeSet(ret, p.data, i)\n      i += p.data.length\n      p = p.next\n    }\n    return ret\n  }\n\n  // Consumes a specified amount of bytes or characters from the buffered data.\n  consume(n, hasStrings) {\n    const data = this.head.data\n    if (n < data.length) {\n      // `slice` is the same for buffers and strings.\n      const slice = data.slice(0, n)\n      this.head.data = data.slice(n)\n      return slice\n    }\n    if (n === data.length) {\n      // First chunk is a perfect match.\n      return this.shift()\n    }\n    // Result spans more than one buffer.\n    return hasStrings ? this._getString(n) : this._getBuffer(n)\n  }\n  first() {\n    return this.head.data\n  }\n  *[SymbolIterator]() {\n    for (let p = this.head; p; p = p.next) {\n      yield p.data\n    }\n  }\n\n  // Consumes a specified amount of characters from the buffered data.\n  _getString(n) {\n    let ret = ''\n    let p = this.head\n    let c = 0\n    do {\n      const str = p.data\n      if (n > str.length) {\n        ret += str\n        n -= str.length\n      } else {\n        if (n === str.length) {\n          ret += str\n          ++c\n          if (p.next) this.head = p.next\n          else this.head = this.tail = null\n        } else {\n          ret += StringPrototypeSlice(str, 0, n)\n          this.head = p\n          p.data = StringPrototypeSlice(str, n)\n        }\n        break\n      }\n      ++c\n    } while ((p = p.next) !== null)\n    this.length -= c\n    return ret\n  }\n\n  // Consumes a specified amount of bytes from the buffered data.\n  _getBuffer(n) {\n    const ret = Buffer.allocUnsafe(n)\n    const retLen = n\n    let p = this.head\n    let c = 0\n    do {\n      const buf = p.data\n      if (n > buf.length) {\n        TypedArrayPrototypeSet(ret, buf, retLen - n)\n        n -= buf.length\n      } else {\n        if (n === buf.length) {\n          TypedArrayPrototypeSet(ret, buf, retLen - n)\n          ++c\n          if (p.next) this.head = p.next\n          else this.head = this.tail = null\n        } else {\n          TypedArrayPrototypeSet(ret, new Uint8Array(buf.buffer, buf.byteOffset, n), retLen - n)\n          this.head = p\n          p.data = buf.slice(n)\n        }\n        break\n      }\n      ++c\n    } while ((p = p.next) !== null)\n    this.length -= c\n    return ret\n  }\n\n  // Make sure the linked list only shows the minimal necessary information.\n  [Symbol.for('nodejs.util.inspect.custom')](_, options) {\n    return inspect(this, {\n      ...options,\n      // Only inspect one level.\n      depth: 0,\n      // It should not recurse.\n      customInspect: false\n    })\n  }\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,oBAAoB,EAAE,cAAc,EAAE,sBAAsB,EAAE,UAAU,EAAE;AAClF,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,OAAO,EAAE;AACjB,OAAO,OAAO,GAAG,MAAM;IACrB,aAAc;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,KAAK,CAAC,EAAE;QACN,MAAM,QAAQ;YACZ,MAAM;YACN,MAAM;QACR;QACA,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;aACjC,IAAI,CAAC,IAAI,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG;QACZ,EAAE,IAAI,CAAC,MAAM;IACf;IACA,QAAQ,CAAC,EAAE;QACT,MAAM,QAAQ;YACZ,MAAM;YACN,MAAM,IAAI,CAAC,IAAI;QACjB;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG;QACnC,IAAI,CAAC,IAAI,GAAG;QACZ,EAAE,IAAI,CAAC,MAAM;IACf;IACA,QAAQ;QACN,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG;QACvB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;QAC1B,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;aAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;QAC/B,EAAE,IAAI,CAAC,MAAM;QACb,OAAO;IACT;IACA,QAAQ;QACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,KAAK,CAAC,EAAE;QACN,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;QAC9B,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,MAAM,KAAK,EAAE,IAAI;QACrB,MAAO,CAAC,IAAI,EAAE,IAAI,MAAM,KAAM,OAAO,IAAI,EAAE,IAAI;QAC/C,OAAO;IACT;IACA,OAAO,CAAC,EAAE;QACR,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,CAAC;QAC3C,MAAM,MAAM,OAAO,WAAW,CAAC,MAAM;QACrC,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,IAAI;QACR,MAAO,EAAG;YACR,uBAAuB,KAAK,EAAE,IAAI,EAAE;YACpC,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,IAAI,EAAE,IAAI;QACZ;QACA,OAAO;IACT;IAEA,6EAA6E;IAC7E,QAAQ,CAAC,EAAE,UAAU,EAAE;QACrB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QAC3B,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,+CAA+C;YAC/C,MAAM,QAAQ,KAAK,KAAK,CAAC,GAAG;YAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC;YAC5B,OAAO;QACT;QACA,IAAI,MAAM,KAAK,MAAM,EAAE;YACrB,kCAAkC;YAClC,OAAO,IAAI,CAAC,KAAK;QACnB;QACA,qCAAqC;QACrC,OAAO,aAAa,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC;IAC3D;IACA,QAAQ;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACvB;IACA,CAAC,CAAC,eAAe,GAAG;QAClB,IAAK,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,CAAE;YACrC,MAAM,EAAE,IAAI;QACd;IACF;IAEA,oEAAoE;IACpE,WAAW,CAAC,EAAE;QACZ,IAAI,MAAM;QACV,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,IAAI;QACR,GAAG;YACD,MAAM,MAAM,EAAE,IAAI;YAClB,IAAI,IAAI,IAAI,MAAM,EAAE;gBAClB,OAAO;gBACP,KAAK,IAAI,MAAM;YACjB,OAAO;gBACL,IAAI,MAAM,IAAI,MAAM,EAAE;oBACpB,OAAO;oBACP,EAAE;oBACF,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI;yBACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;gBAC/B,OAAO;oBACL,OAAO,qBAAqB,KAAK,GAAG;oBACpC,IAAI,CAAC,IAAI,GAAG;oBACZ,EAAE,IAAI,GAAG,qBAAqB,KAAK;gBACrC;gBACA;YACF;YACA,EAAE;QACJ,QAAS,CAAC,IAAI,EAAE,IAAI,MAAM,KAAK;QAC/B,IAAI,CAAC,MAAM,IAAI;QACf,OAAO;IACT;IAEA,+DAA+D;IAC/D,WAAW,CAAC,EAAE;QACZ,MAAM,MAAM,OAAO,WAAW,CAAC;QAC/B,MAAM,SAAS;QACf,IAAI,IAAI,IAAI,CAAC,IAAI;QACjB,IAAI,IAAI;QACR,GAAG;YACD,MAAM,MAAM,EAAE,IAAI;YAClB,IAAI,IAAI,IAAI,MAAM,EAAE;gBAClB,uBAAuB,KAAK,KAAK,SAAS;gBAC1C,KAAK,IAAI,MAAM;YACjB,OAAO;gBACL,IAAI,MAAM,IAAI,MAAM,EAAE;oBACpB,uBAAuB,KAAK,KAAK,SAAS;oBAC1C,EAAE;oBACF,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI;yBACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;gBAC/B,OAAO;oBACL,uBAAuB,KAAK,IAAI,WAAW,IAAI,MAAM,EAAE,IAAI,UAAU,EAAE,IAAI,SAAS;oBACpF,IAAI,CAAC,IAAI,GAAG;oBACZ,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC;gBACrB;gBACA;YACF;YACA,EAAE;QACJ,QAAS,CAAC,IAAI,EAAE,IAAI,MAAM,KAAK;QAC/B,IAAI,CAAC,MAAM,IAAI;QACf,OAAO;IACT;IAEA,0EAA0E;IAC1E,CAAC,OAAO,GAAG,CAAC,8BAA8B,CAAC,CAAC,EAAE,OAAO,EAAE;QACrD,OAAO,QAAQ,IAAI,EAAE;YACnB,GAAG,OAAO;YACV,0BAA0B;YAC1B,OAAO;YACP,yBAAyB;YACzB,eAAe;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4097, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/from.js"], "sourcesContent": ["'use strict'\n\n/* replacement start */\n\nconst process = require('process/')\n\n/* replacement end */\n\nconst { PromisePrototypeThen, SymbolAsyncIterator, SymbolIterator } = require('../../ours/primordials')\nconst { <PERSON>uffer } = require('buffer')\nconst { ERR_INVALID_ARG_TYPE, ERR_STREAM_NULL_VALUES } = require('../../ours/errors').codes\nfunction from(Readable, iterable, opts) {\n  let iterator\n  if (typeof iterable === 'string' || iterable instanceof Buffer) {\n    return new Readable({\n      objectMode: true,\n      ...opts,\n      read() {\n        this.push(iterable)\n        this.push(null)\n      }\n    })\n  }\n  let isAsync\n  if (iterable && iterable[SymbolAsyncIterator]) {\n    isAsync = true\n    iterator = iterable[SymbolAsyncIterator]()\n  } else if (iterable && iterable[SymbolIterator]) {\n    isAsync = false\n    iterator = iterable[SymbolIterator]()\n  } else {\n    throw new ERR_INVALID_ARG_TYPE('iterable', ['Iterable'], iterable)\n  }\n  const readable = new Readable({\n    objectMode: true,\n    highWaterMark: 1,\n    // TODO(ronag): What options should be allowed?\n    ...opts\n  })\n\n  // Flag to protect against _read\n  // being called before last iteration completion.\n  let reading = false\n  readable._read = function () {\n    if (!reading) {\n      reading = true\n      next()\n    }\n  }\n  readable._destroy = function (error, cb) {\n    PromisePrototypeThen(\n      close(error),\n      () => process.nextTick(cb, error),\n      // nextTick is here in case cb throws\n      (e) => process.nextTick(cb, e || error)\n    )\n  }\n  async function close(error) {\n    const hadError = error !== undefined && error !== null\n    const hasThrow = typeof iterator.throw === 'function'\n    if (hadError && hasThrow) {\n      const { value, done } = await iterator.throw(error)\n      await value\n      if (done) {\n        return\n      }\n    }\n    if (typeof iterator.return === 'function') {\n      const { value } = await iterator.return()\n      await value\n    }\n  }\n  async function next() {\n    for (;;) {\n      try {\n        const { value, done } = isAsync ? await iterator.next() : iterator.next()\n        if (done) {\n          readable.push(null)\n        } else {\n          const res = value && typeof value.then === 'function' ? await value : value\n          if (res === null) {\n            reading = false\n            throw new ERR_STREAM_NULL_VALUES()\n          } else if (readable.push(res)) {\n            continue\n          } else {\n            reading = false\n          }\n        }\n      } catch (err) {\n        readable.destroy(err)\n      }\n      break\n    }\n  }\n  return readable\n}\nmodule.exports = from\n"], "names": [], "mappings": "AAEA,qBAAqB,GAErB,MAAM;;;;;;;;;GAAkB;AAExB,mBAAmB,GAEnB,MAAM,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,cAAc,EAAE;AACnE,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,GAAG,8GAA6B,KAAK;AAC3F,SAAS,KAAK,QAAQ,EAAE,QAAQ,EAAE,IAAI;IACpC,IAAI;IACJ,IAAI,OAAO,aAAa,YAAY,oBAAoB,QAAQ;QAC9D,OAAO,IAAI,SAAS;YAClB,YAAY;YACZ,GAAG,IAAI;YACP;gBACE,IAAI,CAAC,IAAI,CAAC;gBACV,IAAI,CAAC,IAAI,CAAC;YACZ;QACF;IACF;IACA,IAAI;IACJ,IAAI,YAAY,QAAQ,CAAC,oBAAoB,EAAE;QAC7C,UAAU;QACV,WAAW,QAAQ,CAAC,oBAAoB;IAC1C,OAAO,IAAI,YAAY,QAAQ,CAAC,eAAe,EAAE;QAC/C,UAAU;QACV,WAAW,QAAQ,CAAC,eAAe;IACrC,OAAO;QACL,MAAM,IAAI,qBAAqB,YAAY;YAAC;SAAW,EAAE;IAC3D;IACA,MAAM,WAAW,IAAI,SAAS;QAC5B,YAAY;QACZ,eAAe;QACf,+CAA+C;QAC/C,GAAG,IAAI;IACT;IAEA,gCAAgC;IAChC,iDAAiD;IACjD,IAAI,UAAU;IACd,SAAS,KAAK,GAAG;QACf,IAAI,CAAC,SAAS;YACZ,UAAU;YACV;QACF;IACF;IACA,SAAS,QAAQ,GAAG,SAAU,KAAK,EAAE,EAAE;QACrC,qBACE,MAAM,QACN,IAAM,QAAQ,QAAQ,CAAC,IAAI,QAC3B,qCAAqC;QACrC,CAAC,IAAM,QAAQ,QAAQ,CAAC,IAAI,KAAK;IAErC;IACA,eAAe,MAAM,KAAK;QACxB,MAAM,WAAW,UAAU,aAAa,UAAU;QAClD,MAAM,WAAW,OAAO,SAAS,KAAK,KAAK;QAC3C,IAAI,YAAY,UAAU;YACxB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,SAAS,KAAK,CAAC;YAC7C,MAAM;YACN,IAAI,MAAM;gBACR;YACF;QACF;QACA,IAAI,OAAO,SAAS,MAAM,KAAK,YAAY;YACzC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,MAAM;YACvC,MAAM;QACR;IACF;IACA,eAAe;QACb,OAAS;YACP,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,UAAU,MAAM,SAAS,IAAI,KAAK,SAAS,IAAI;gBACvE,IAAI,MAAM;oBACR,SAAS,IAAI,CAAC;gBAChB,OAAO;oBACL,MAAM,MAAM,SAAS,OAAO,MAAM,IAAI,KAAK,aAAa,MAAM,QAAQ;oBACtE,IAAI,QAAQ,MAAM;wBAChB,UAAU;wBACV,MAAM,IAAI;oBACZ,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM;wBAC7B;oBACF,OAAO;wBACL,UAAU;oBACZ;gBACF;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS,OAAO,CAAC;YACnB;YACA;QACF;IACF;IACA,OAAO;AACT;AACA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4198, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/readable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict'\n\n/* replacement start */\n\nconst process = require('process/')\n\n/* replacement end */\n\nconst {\n  ArrayPrototypeIndexOf,\n  NumberIsInteger,\n  NumberIsNaN,\n  NumberParseInt,\n  ObjectDefineProperties,\n  ObjectKeys,\n  ObjectSetPrototypeOf,\n  Promise,\n  SafeSet,\n  SymbolAsyncDispose,\n  SymbolAsyncIterator,\n  Symbol\n} = require('../../ours/primordials')\nmodule.exports = Readable\nReadable.ReadableState = ReadableState\nconst { EventEmitter: EE } = require('events')\nconst { Stream, prependListener } = require('./legacy')\nconst { Buffer } = require('buffer')\nconst { addAbortSignal } = require('./add-abort-signal')\nconst eos = require('./end-of-stream')\nlet debug = require('../../ours/util').debuglog('stream', (fn) => {\n  debug = fn\n})\nconst BufferList = require('./buffer_list')\nconst destroyImpl = require('./destroy')\nconst { getHighWaterMark, getDefaultHighWaterMark } = require('./state')\nconst {\n  aggregateTwoErrors,\n  codes: {\n    ERR_INVALID_ARG_TYPE,\n    ERR_METHOD_NOT_IMPLEMENTED,\n    ERR_OUT_OF_RANGE,\n    ERR_STREAM_PUSH_AFTER_EOF,\n    ERR_STREAM_UNSHIFT_AFTER_END_EVENT\n  },\n  AbortError\n} = require('../../ours/errors')\nconst { validateObject } = require('../validators')\nconst kPaused = Symbol('kPaused')\nconst { StringDecoder } = require('string_decoder/')\nconst from = require('./from')\nObjectSetPrototypeOf(Readable.prototype, Stream.prototype)\nObjectSetPrototypeOf(Readable, Stream)\nconst nop = () => {}\nconst { errorOrDestroy } = destroyImpl\nconst kObjectMode = 1 << 0\nconst kEnded = 1 << 1\nconst kEndEmitted = 1 << 2\nconst kReading = 1 << 3\nconst kConstructed = 1 << 4\nconst kSync = 1 << 5\nconst kNeedReadable = 1 << 6\nconst kEmittedReadable = 1 << 7\nconst kReadableListening = 1 << 8\nconst kResumeScheduled = 1 << 9\nconst kErrorEmitted = 1 << 10\nconst kEmitClose = 1 << 11\nconst kAutoDestroy = 1 << 12\nconst kDestroyed = 1 << 13\nconst kClosed = 1 << 14\nconst kCloseEmitted = 1 << 15\nconst kMultiAwaitDrain = 1 << 16\nconst kReadingMore = 1 << 17\nconst kDataEmitted = 1 << 18\n\n// TODO(benjamingr) it is likely slower to do it this way than with free functions\nfunction makeBitMapDescriptor(bit) {\n  return {\n    enumerable: false,\n    get() {\n      return (this.state & bit) !== 0\n    },\n    set(value) {\n      if (value) this.state |= bit\n      else this.state &= ~bit\n    }\n  }\n}\nObjectDefineProperties(ReadableState.prototype, {\n  objectMode: makeBitMapDescriptor(kObjectMode),\n  ended: makeBitMapDescriptor(kEnded),\n  endEmitted: makeBitMapDescriptor(kEndEmitted),\n  reading: makeBitMapDescriptor(kReading),\n  // Stream is still being constructed and cannot be\n  // destroyed until construction finished or failed.\n  // Async construction is opt in, therefore we start as\n  // constructed.\n  constructed: makeBitMapDescriptor(kConstructed),\n  // A flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n  sync: makeBitMapDescriptor(kSync),\n  // Whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n  needReadable: makeBitMapDescriptor(kNeedReadable),\n  emittedReadable: makeBitMapDescriptor(kEmittedReadable),\n  readableListening: makeBitMapDescriptor(kReadableListening),\n  resumeScheduled: makeBitMapDescriptor(kResumeScheduled),\n  // True if the error was already emitted and should not be thrown again.\n  errorEmitted: makeBitMapDescriptor(kErrorEmitted),\n  emitClose: makeBitMapDescriptor(kEmitClose),\n  autoDestroy: makeBitMapDescriptor(kAutoDestroy),\n  // Has it been destroyed.\n  destroyed: makeBitMapDescriptor(kDestroyed),\n  // Indicates whether the stream has finished destroying.\n  closed: makeBitMapDescriptor(kClosed),\n  // True if close has been emitted or would have been emitted\n  // depending on emitClose.\n  closeEmitted: makeBitMapDescriptor(kCloseEmitted),\n  multiAwaitDrain: makeBitMapDescriptor(kMultiAwaitDrain),\n  // If true, a maybeReadMore has been scheduled.\n  readingMore: makeBitMapDescriptor(kReadingMore),\n  dataEmitted: makeBitMapDescriptor(kDataEmitted)\n})\nfunction ReadableState(options, stream, isDuplex) {\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof require('./duplex')\n\n  // Bit map field to store ReadableState more effciently with 1 bit per field\n  // instead of a V8 slot per field.\n  this.state = kEmitClose | kAutoDestroy | kConstructed | kSync\n  // Object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away.\n  if (options && options.objectMode) this.state |= kObjectMode\n  if (isDuplex && options && options.readableObjectMode) this.state |= kObjectMode\n\n  // The point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n  this.highWaterMark = options\n    ? getHighWaterMark(this, options, 'readableHighWaterMark', isDuplex)\n    : getDefaultHighWaterMark(false)\n\n  // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift().\n  this.buffer = new BufferList()\n  this.length = 0\n  this.pipes = []\n  this.flowing = null\n  this[kPaused] = null\n\n  // Should close be emitted on destroy. Defaults to true.\n  if (options && options.emitClose === false) this.state &= ~kEmitClose\n\n  // Should .destroy() be called after 'end' (and potentially 'finish').\n  if (options && options.autoDestroy === false) this.state &= ~kAutoDestroy\n\n  // Indicates whether the stream has errored. When true no further\n  // _read calls, 'data' or 'readable' events should occur. This is needed\n  // since when autoDestroy is disabled we need a way to tell whether the\n  // stream has failed.\n  this.errored = null\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = (options && options.defaultEncoding) || 'utf8'\n\n  // Ref the piped dest which we need a drain event on it\n  // type: null | Writable | Set<Writable>.\n  this.awaitDrainWriters = null\n  this.decoder = null\n  this.encoding = null\n  if (options && options.encoding) {\n    this.decoder = new StringDecoder(options.encoding)\n    this.encoding = options.encoding\n  }\n}\nfunction Readable(options) {\n  if (!(this instanceof Readable)) return new Readable(options)\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the ReadableState constructor, at least with V8 6.5.\n  const isDuplex = this instanceof require('./duplex')\n  this._readableState = new ReadableState(options, this, isDuplex)\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read\n    if (typeof options.destroy === 'function') this._destroy = options.destroy\n    if (typeof options.construct === 'function') this._construct = options.construct\n    if (options.signal && !isDuplex) addAbortSignal(options.signal, this)\n  }\n  Stream.call(this, options)\n  destroyImpl.construct(this, () => {\n    if (this._readableState.needReadable) {\n      maybeReadMore(this, this._readableState)\n    }\n  })\n}\nReadable.prototype.destroy = destroyImpl.destroy\nReadable.prototype._undestroy = destroyImpl.undestroy\nReadable.prototype._destroy = function (err, cb) {\n  cb(err)\n}\nReadable.prototype[EE.captureRejectionSymbol] = function (err) {\n  this.destroy(err)\n}\nReadable.prototype[SymbolAsyncDispose] = function () {\n  let error\n  if (!this.destroyed) {\n    error = this.readableEnded ? null : new AbortError()\n    this.destroy(error)\n  }\n  return new Promise((resolve, reject) => eos(this, (err) => (err && err !== error ? reject(err) : resolve(null))))\n}\n\n// Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\nReadable.prototype.push = function (chunk, encoding) {\n  return readableAddChunk(this, chunk, encoding, false)\n}\n\n// Unshift should *always* be something directly out of read().\nReadable.prototype.unshift = function (chunk, encoding) {\n  return readableAddChunk(this, chunk, encoding, true)\n}\nfunction readableAddChunk(stream, chunk, encoding, addToFront) {\n  debug('readableAddChunk', chunk)\n  const state = stream._readableState\n  let err\n  if ((state.state & kObjectMode) === 0) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding\n      if (state.encoding !== encoding) {\n        if (addToFront && state.encoding) {\n          // When unshifting, if state.encoding is set, we have to save\n          // the string in the BufferList with the state encoding.\n          chunk = Buffer.from(chunk, encoding).toString(state.encoding)\n        } else {\n          chunk = Buffer.from(chunk, encoding)\n          encoding = ''\n        }\n      }\n    } else if (chunk instanceof Buffer) {\n      encoding = ''\n    } else if (Stream._isUint8Array(chunk)) {\n      chunk = Stream._uint8ArrayToBuffer(chunk)\n      encoding = ''\n    } else if (chunk != null) {\n      err = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk)\n    }\n  }\n  if (err) {\n    errorOrDestroy(stream, err)\n  } else if (chunk === null) {\n    state.state &= ~kReading\n    onEofChunk(stream, state)\n  } else if ((state.state & kObjectMode) !== 0 || (chunk && chunk.length > 0)) {\n    if (addToFront) {\n      if ((state.state & kEndEmitted) !== 0) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT())\n      else if (state.destroyed || state.errored) return false\n      else addChunk(stream, state, chunk, true)\n    } else if (state.ended) {\n      errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF())\n    } else if (state.destroyed || state.errored) {\n      return false\n    } else {\n      state.state &= ~kReading\n      if (state.decoder && !encoding) {\n        chunk = state.decoder.write(chunk)\n        if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false)\n        else maybeReadMore(stream, state)\n      } else {\n        addChunk(stream, state, chunk, false)\n      }\n    }\n  } else if (!addToFront) {\n    state.state &= ~kReading\n    maybeReadMore(stream, state)\n  }\n\n  // We can push more data if we are below the highWaterMark.\n  // Also, if we have no data yet, we can stand some more bytes.\n  // This is to work around cases where hwm=0, such as the repl.\n  return !state.ended && (state.length < state.highWaterMark || state.length === 0)\n}\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync && stream.listenerCount('data') > 0) {\n    // Use the guard to avoid creating `Set()` repeatedly\n    // when we have multiple pipes.\n    if ((state.state & kMultiAwaitDrain) !== 0) {\n      state.awaitDrainWriters.clear()\n    } else {\n      state.awaitDrainWriters = null\n    }\n    state.dataEmitted = true\n    stream.emit('data', chunk)\n  } else {\n    // Update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length\n    if (addToFront) state.buffer.unshift(chunk)\n    else state.buffer.push(chunk)\n    if ((state.state & kNeedReadable) !== 0) emitReadable(stream)\n  }\n  maybeReadMore(stream, state)\n}\nReadable.prototype.isPaused = function () {\n  const state = this._readableState\n  return state[kPaused] === true || state.flowing === false\n}\n\n// Backwards compatibility.\nReadable.prototype.setEncoding = function (enc) {\n  const decoder = new StringDecoder(enc)\n  this._readableState.decoder = decoder\n  // If setEncoding(null), decoder.encoding equals utf8.\n  this._readableState.encoding = this._readableState.decoder.encoding\n  const buffer = this._readableState.buffer\n  // Iterate over current buffer to convert already stored Buffers:\n  let content = ''\n  for (const data of buffer) {\n    content += decoder.write(data)\n  }\n  buffer.clear()\n  if (content !== '') buffer.push(content)\n  this._readableState.length = content.length\n  return this\n}\n\n// Don't raise the hwm > 1GB.\nconst MAX_HWM = 0x40000000\nfunction computeNewHighWaterMark(n) {\n  if (n > MAX_HWM) {\n    throw new ERR_OUT_OF_RANGE('size', '<= 1GiB', n)\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts.\n    n--\n    n |= n >>> 1\n    n |= n >>> 2\n    n |= n >>> 4\n    n |= n >>> 8\n    n |= n >>> 16\n    n++\n  }\n  return n\n}\n\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || (state.length === 0 && state.ended)) return 0\n  if ((state.state & kObjectMode) !== 0) return 1\n  if (NumberIsNaN(n)) {\n    // Only flow one buffer at a time.\n    if (state.flowing && state.length) return state.buffer.first().length\n    return state.length\n  }\n  if (n <= state.length) return n\n  return state.ended ? state.length : 0\n}\n\n// You can override either this method, or the async _read(n) below.\nReadable.prototype.read = function (n) {\n  debug('read', n)\n  // Same as parseInt(undefined, 10), however V8 7.3 performance regressed\n  // in this scenario, so we are doing it manually.\n  if (n === undefined) {\n    n = NaN\n  } else if (!NumberIsInteger(n)) {\n    n = NumberParseInt(n, 10)\n  }\n  const state = this._readableState\n  const nOrig = n\n\n  // If we're asking for more than the current hwm, then raise the hwm.\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n)\n  if (n !== 0) state.state &= ~kEmittedReadable\n\n  // If we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n  if (\n    n === 0 &&\n    state.needReadable &&\n    ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) || state.ended)\n  ) {\n    debug('read: emitReadable', state.length, state.ended)\n    if (state.length === 0 && state.ended) endReadable(this)\n    else emitReadable(this)\n    return null\n  }\n  n = howMuchToRead(n, state)\n\n  // If we've ended, and we're now clear, then finish it up.\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this)\n    return null\n  }\n\n  // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n\n  // if we need a readable event, then we need to do some reading.\n  let doRead = (state.state & kNeedReadable) !== 0\n  debug('need readable', doRead)\n\n  // If we currently have less than the highWaterMark, then also read some.\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true\n    debug('length less than watermark', doRead)\n  }\n\n  // However, if we've ended, then there's no point, if we're already\n  // reading, then it's unnecessary, if we're constructing we have to wait,\n  // and if we're destroyed or errored, then it's not allowed,\n  if (state.ended || state.reading || state.destroyed || state.errored || !state.constructed) {\n    doRead = false\n    debug('reading, ended or constructing', doRead)\n  } else if (doRead) {\n    debug('do read')\n    state.state |= kReading | kSync\n    // If the length is currently zero, then we *need* a readable event.\n    if (state.length === 0) state.state |= kNeedReadable\n\n    // Call internal read method\n    try {\n      this._read(state.highWaterMark)\n    } catch (err) {\n      errorOrDestroy(this, err)\n    }\n    state.state &= ~kSync\n\n    // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n    if (!state.reading) n = howMuchToRead(nOrig, state)\n  }\n  let ret\n  if (n > 0) ret = fromList(n, state)\n  else ret = null\n  if (ret === null) {\n    state.needReadable = state.length <= state.highWaterMark\n    n = 0\n  } else {\n    state.length -= n\n    if (state.multiAwaitDrain) {\n      state.awaitDrainWriters.clear()\n    } else {\n      state.awaitDrainWriters = null\n    }\n  }\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true\n\n    // If we tried to read() past the EOF, then emit end on the next tick.\n    if (nOrig !== n && state.ended) endReadable(this)\n  }\n  if (ret !== null && !state.errorEmitted && !state.closeEmitted) {\n    state.dataEmitted = true\n    this.emit('data', ret)\n  }\n  return ret\n}\nfunction onEofChunk(stream, state) {\n  debug('onEofChunk')\n  if (state.ended) return\n  if (state.decoder) {\n    const chunk = state.decoder.end()\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk)\n      state.length += state.objectMode ? 1 : chunk.length\n    }\n  }\n  state.ended = true\n  if (state.sync) {\n    // If we are sync, wait until next tick to emit the data.\n    // Otherwise we risk emitting data in the flow()\n    // the readable code triggers during a read() call.\n    emitReadable(stream)\n  } else {\n    // Emit 'readable' now to make sure it gets picked up.\n    state.needReadable = false\n    state.emittedReadable = true\n    // We have to emit readable now that we are EOF. Modules\n    // in the ecosystem (e.g. dicer) rely on this event being sync.\n    emitReadable_(stream)\n  }\n}\n\n// Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\nfunction emitReadable(stream) {\n  const state = stream._readableState\n  debug('emitReadable', state.needReadable, state.emittedReadable)\n  state.needReadable = false\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing)\n    state.emittedReadable = true\n    process.nextTick(emitReadable_, stream)\n  }\n}\nfunction emitReadable_(stream) {\n  const state = stream._readableState\n  debug('emitReadable_', state.destroyed, state.length, state.ended)\n  if (!state.destroyed && !state.errored && (state.length || state.ended)) {\n    stream.emit('readable')\n    state.emittedReadable = false\n  }\n\n  // The stream needs another readable event if:\n  // 1. It is not flowing, as the flow mechanism will take\n  //    care of it.\n  // 2. It is not ended.\n  // 3. It is below the highWaterMark, so we can schedule\n  //    another readable later.\n  state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark\n  flow(stream)\n}\n\n// At this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore && state.constructed) {\n    state.readingMore = true\n    process.nextTick(maybeReadMore_, stream, state)\n  }\n}\nfunction maybeReadMore_(stream, state) {\n  // Attempt to read more data if we should.\n  //\n  // The conditions for reading more data are (one of):\n  // - Not enough data buffered (state.length < state.highWaterMark). The loop\n  //   is responsible for filling the buffer with enough data if such data\n  //   is available. If highWaterMark is 0 and we are not in the flowing mode\n  //   we should _not_ attempt to buffer any extra data. We'll get more data\n  //   when the stream consumer calls read() instead.\n  // - No data in the buffer, and the stream is in flowing mode. In this mode\n  //   the loop below is responsible for ensuring read() is called. Failing to\n  //   call read here would abort the flow and there's no other mechanism for\n  //   continuing the flow if the stream consumer has just subscribed to the\n  //   'data' event.\n  //\n  // In addition to the above conditions to keep reading data, the following\n  // conditions prevent the data from being read:\n  // - The stream has ended (state.ended).\n  // - There is already a pending 'read' operation (state.reading). This is a\n  //   case where the stream has called the implementation defined _read()\n  //   method, but they are processing the call asynchronously and have _not_\n  //   called push() with new data. In this case we skip performing more\n  //   read()s. The execution ends in this method again after the _read() ends\n  //   up calling push() with more data.\n  while (\n    !state.reading &&\n    !state.ended &&\n    (state.length < state.highWaterMark || (state.flowing && state.length === 0))\n  ) {\n    const len = state.length\n    debug('maybeReadMore read 0')\n    stream.read(0)\n    if (len === state.length)\n      // Didn't get any data, stop spinning.\n      break\n  }\n  state.readingMore = false\n}\n\n// Abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\nReadable.prototype._read = function (n) {\n  throw new ERR_METHOD_NOT_IMPLEMENTED('_read()')\n}\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  const src = this\n  const state = this._readableState\n  if (state.pipes.length === 1) {\n    if (!state.multiAwaitDrain) {\n      state.multiAwaitDrain = true\n      state.awaitDrainWriters = new SafeSet(state.awaitDrainWriters ? [state.awaitDrainWriters] : [])\n    }\n  }\n  state.pipes.push(dest)\n  debug('pipe count=%d opts=%j', state.pipes.length, pipeOpts)\n  const doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr\n  const endFn = doEnd ? onend : unpipe\n  if (state.endEmitted) process.nextTick(endFn)\n  else src.once('end', endFn)\n  dest.on('unpipe', onunpipe)\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe')\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true\n        cleanup()\n      }\n    }\n  }\n  function onend() {\n    debug('onend')\n    dest.end()\n  }\n  let ondrain\n  let cleanedUp = false\n  function cleanup() {\n    debug('cleanup')\n    // Cleanup event handlers once the pipe is broken.\n    dest.removeListener('close', onclose)\n    dest.removeListener('finish', onfinish)\n    if (ondrain) {\n      dest.removeListener('drain', ondrain)\n    }\n    dest.removeListener('error', onerror)\n    dest.removeListener('unpipe', onunpipe)\n    src.removeListener('end', onend)\n    src.removeListener('end', unpipe)\n    src.removeListener('data', ondata)\n    cleanedUp = true\n\n    // If the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n    if (ondrain && state.awaitDrainWriters && (!dest._writableState || dest._writableState.needDrain)) ondrain()\n  }\n  function pause() {\n    // If the user unpiped during `dest.write()`, it is possible\n    // to get stuck in a permanently paused state if that write\n    // also returned false.\n    // => Check whether `dest` is still a piping destination.\n    if (!cleanedUp) {\n      if (state.pipes.length === 1 && state.pipes[0] === dest) {\n        debug('false write response, pause', 0)\n        state.awaitDrainWriters = dest\n        state.multiAwaitDrain = false\n      } else if (state.pipes.length > 1 && state.pipes.includes(dest)) {\n        debug('false write response, pause', state.awaitDrainWriters.size)\n        state.awaitDrainWriters.add(dest)\n      }\n      src.pause()\n    }\n    if (!ondrain) {\n      // When the dest drains, it reduces the awaitDrain counter\n      // on the source.  This would be more elegant with a .once()\n      // handler in flow(), but adding and removing repeatedly is\n      // too slow.\n      ondrain = pipeOnDrain(src, dest)\n      dest.on('drain', ondrain)\n    }\n  }\n  src.on('data', ondata)\n  function ondata(chunk) {\n    debug('ondata')\n    const ret = dest.write(chunk)\n    debug('dest.write', ret)\n    if (ret === false) {\n      pause()\n    }\n  }\n\n  // If the dest has an error, then stop piping into it.\n  // However, don't suppress the throwing behavior for this.\n  function onerror(er) {\n    debug('onerror', er)\n    unpipe()\n    dest.removeListener('error', onerror)\n    if (dest.listenerCount('error') === 0) {\n      const s = dest._writableState || dest._readableState\n      if (s && !s.errorEmitted) {\n        // User incorrectly emitted 'error' directly on the stream.\n        errorOrDestroy(dest, er)\n      } else {\n        dest.emit('error', er)\n      }\n    }\n  }\n\n  // Make sure our error handler is attached before userland ones.\n  prependListener(dest, 'error', onerror)\n\n  // Both close and finish should trigger unpipe, but only once.\n  function onclose() {\n    dest.removeListener('finish', onfinish)\n    unpipe()\n  }\n  dest.once('close', onclose)\n  function onfinish() {\n    debug('onfinish')\n    dest.removeListener('close', onclose)\n    unpipe()\n  }\n  dest.once('finish', onfinish)\n  function unpipe() {\n    debug('unpipe')\n    src.unpipe(dest)\n  }\n\n  // Tell the dest that it's being piped to.\n  dest.emit('pipe', src)\n\n  // Start the flow if it hasn't been started already.\n\n  if (dest.writableNeedDrain === true) {\n    pause()\n  } else if (!state.flowing) {\n    debug('pipe resume')\n    src.resume()\n  }\n  return dest\n}\nfunction pipeOnDrain(src, dest) {\n  return function pipeOnDrainFunctionResult() {\n    const state = src._readableState\n\n    // `ondrain` will call directly,\n    // `this` maybe not a reference to dest,\n    // so we use the real dest here.\n    if (state.awaitDrainWriters === dest) {\n      debug('pipeOnDrain', 1)\n      state.awaitDrainWriters = null\n    } else if (state.multiAwaitDrain) {\n      debug('pipeOnDrain', state.awaitDrainWriters.size)\n      state.awaitDrainWriters.delete(dest)\n    }\n    if ((!state.awaitDrainWriters || state.awaitDrainWriters.size === 0) && src.listenerCount('data')) {\n      src.resume()\n    }\n  }\n}\nReadable.prototype.unpipe = function (dest) {\n  const state = this._readableState\n  const unpipeInfo = {\n    hasUnpiped: false\n  }\n\n  // If we're not piping anywhere, then do nothing.\n  if (state.pipes.length === 0) return this\n  if (!dest) {\n    // remove all.\n    const dests = state.pipes\n    state.pipes = []\n    this.pause()\n    for (let i = 0; i < dests.length; i++)\n      dests[i].emit('unpipe', this, {\n        hasUnpiped: false\n      })\n    return this\n  }\n\n  // Try to find the right one.\n  const index = ArrayPrototypeIndexOf(state.pipes, dest)\n  if (index === -1) return this\n  state.pipes.splice(index, 1)\n  if (state.pipes.length === 0) this.pause()\n  dest.emit('unpipe', this, unpipeInfo)\n  return this\n}\n\n// Set up data events if they are asked for\n// Ensure readable listeners eventually get something.\nReadable.prototype.on = function (ev, fn) {\n  const res = Stream.prototype.on.call(this, ev, fn)\n  const state = this._readableState\n  if (ev === 'data') {\n    // Update readableListening so that resume() may be a no-op\n    // a few lines down. This is needed to support once('readable').\n    state.readableListening = this.listenerCount('readable') > 0\n\n    // Try start flowing on next tick if stream isn't explicitly paused.\n    if (state.flowing !== false) this.resume()\n  } else if (ev === 'readable') {\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true\n      state.flowing = false\n      state.emittedReadable = false\n      debug('on readable', state.length, state.reading)\n      if (state.length) {\n        emitReadable(this)\n      } else if (!state.reading) {\n        process.nextTick(nReadingNextTick, this)\n      }\n    }\n  }\n  return res\n}\nReadable.prototype.addListener = Readable.prototype.on\nReadable.prototype.removeListener = function (ev, fn) {\n  const res = Stream.prototype.removeListener.call(this, ev, fn)\n  if (ev === 'readable') {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this)\n  }\n  return res\n}\nReadable.prototype.off = Readable.prototype.removeListener\nReadable.prototype.removeAllListeners = function (ev) {\n  const res = Stream.prototype.removeAllListeners.apply(this, arguments)\n  if (ev === 'readable' || ev === undefined) {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this)\n  }\n  return res\n}\nfunction updateReadableListening(self) {\n  const state = self._readableState\n  state.readableListening = self.listenerCount('readable') > 0\n  if (state.resumeScheduled && state[kPaused] === false) {\n    // Flowing needs to be set to true now, otherwise\n    // the upcoming resume will not flow.\n    state.flowing = true\n\n    // Crude way to check if we should resume.\n  } else if (self.listenerCount('data') > 0) {\n    self.resume()\n  } else if (!state.readableListening) {\n    state.flowing = null\n  }\n}\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0')\n  self.read(0)\n}\n\n// pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\nReadable.prototype.resume = function () {\n  const state = this._readableState\n  if (!state.flowing) {\n    debug('resume')\n    // We flow only if there is no one listening\n    // for readable, but we still have to call\n    // resume().\n    state.flowing = !state.readableListening\n    resume(this, state)\n  }\n  state[kPaused] = false\n  return this\n}\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true\n    process.nextTick(resume_, stream, state)\n  }\n}\nfunction resume_(stream, state) {\n  debug('resume', state.reading)\n  if (!state.reading) {\n    stream.read(0)\n  }\n  state.resumeScheduled = false\n  stream.emit('resume')\n  flow(stream)\n  if (state.flowing && !state.reading) stream.read(0)\n}\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing)\n  if (this._readableState.flowing !== false) {\n    debug('pause')\n    this._readableState.flowing = false\n    this.emit('pause')\n  }\n  this._readableState[kPaused] = true\n  return this\n}\nfunction flow(stream) {\n  const state = stream._readableState\n  debug('flow', state.flowing)\n  while (state.flowing && stream.read() !== null);\n}\n\n// Wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\nReadable.prototype.wrap = function (stream) {\n  let paused = false\n\n  // TODO (ronag): Should this.destroy(err) emit\n  // 'error' on the wrapped stream? Would require\n  // a static factory method, e.g. Readable.wrap(stream).\n\n  stream.on('data', (chunk) => {\n    if (!this.push(chunk) && stream.pause) {\n      paused = true\n      stream.pause()\n    }\n  })\n  stream.on('end', () => {\n    this.push(null)\n  })\n  stream.on('error', (err) => {\n    errorOrDestroy(this, err)\n  })\n  stream.on('close', () => {\n    this.destroy()\n  })\n  stream.on('destroy', () => {\n    this.destroy()\n  })\n  this._read = () => {\n    if (paused && stream.resume) {\n      paused = false\n      stream.resume()\n    }\n  }\n\n  // Proxy all the other methods. Important when wrapping filters and duplexes.\n  const streamKeys = ObjectKeys(stream)\n  for (let j = 1; j < streamKeys.length; j++) {\n    const i = streamKeys[j]\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = stream[i].bind(stream)\n    }\n  }\n  return this\n}\nReadable.prototype[SymbolAsyncIterator] = function () {\n  return streamToAsyncIterator(this)\n}\nReadable.prototype.iterator = function (options) {\n  if (options !== undefined) {\n    validateObject(options, 'options')\n  }\n  return streamToAsyncIterator(this, options)\n}\nfunction streamToAsyncIterator(stream, options) {\n  if (typeof stream.read !== 'function') {\n    stream = Readable.wrap(stream, {\n      objectMode: true\n    })\n  }\n  const iter = createAsyncIterator(stream, options)\n  iter.stream = stream\n  return iter\n}\nasync function* createAsyncIterator(stream, options) {\n  let callback = nop\n  function next(resolve) {\n    if (this === stream) {\n      callback()\n      callback = nop\n    } else {\n      callback = resolve\n    }\n  }\n  stream.on('readable', next)\n  let error\n  const cleanup = eos(\n    stream,\n    {\n      writable: false\n    },\n    (err) => {\n      error = err ? aggregateTwoErrors(error, err) : null\n      callback()\n      callback = nop\n    }\n  )\n  try {\n    while (true) {\n      const chunk = stream.destroyed ? null : stream.read()\n      if (chunk !== null) {\n        yield chunk\n      } else if (error) {\n        throw error\n      } else if (error === null) {\n        return\n      } else {\n        await new Promise(next)\n      }\n    }\n  } catch (err) {\n    error = aggregateTwoErrors(error, err)\n    throw error\n  } finally {\n    if (\n      (error || (options === null || options === undefined ? undefined : options.destroyOnReturn) !== false) &&\n      (error === undefined || stream._readableState.autoDestroy)\n    ) {\n      destroyImpl.destroyer(stream, null)\n    } else {\n      stream.off('readable', next)\n      cleanup()\n    }\n  }\n}\n\n// Making it explicit these properties are not enumerable\n// because otherwise some prototype manipulation in\n// userland will fail.\nObjectDefineProperties(Readable.prototype, {\n  readable: {\n    __proto__: null,\n    get() {\n      const r = this._readableState\n      // r.readable === false means that this is part of a Duplex stream\n      // where the readable side was disabled upon construction.\n      // Compat. The user might manually disable readable side through\n      // deprecated setter.\n      return !!r && r.readable !== false && !r.destroyed && !r.errorEmitted && !r.endEmitted\n    },\n    set(val) {\n      // Backwards compat.\n      if (this._readableState) {\n        this._readableState.readable = !!val\n      }\n    }\n  },\n  readableDidRead: {\n    __proto__: null,\n    enumerable: false,\n    get: function () {\n      return this._readableState.dataEmitted\n    }\n  },\n  readableAborted: {\n    __proto__: null,\n    enumerable: false,\n    get: function () {\n      return !!(\n        this._readableState.readable !== false &&\n        (this._readableState.destroyed || this._readableState.errored) &&\n        !this._readableState.endEmitted\n      )\n    }\n  },\n  readableHighWaterMark: {\n    __proto__: null,\n    enumerable: false,\n    get: function () {\n      return this._readableState.highWaterMark\n    }\n  },\n  readableBuffer: {\n    __proto__: null,\n    enumerable: false,\n    get: function () {\n      return this._readableState && this._readableState.buffer\n    }\n  },\n  readableFlowing: {\n    __proto__: null,\n    enumerable: false,\n    get: function () {\n      return this._readableState.flowing\n    },\n    set: function (state) {\n      if (this._readableState) {\n        this._readableState.flowing = state\n      }\n    }\n  },\n  readableLength: {\n    __proto__: null,\n    enumerable: false,\n    get() {\n      return this._readableState.length\n    }\n  },\n  readableObjectMode: {\n    __proto__: null,\n    enumerable: false,\n    get() {\n      return this._readableState ? this._readableState.objectMode : false\n    }\n  },\n  readableEncoding: {\n    __proto__: null,\n    enumerable: false,\n    get() {\n      return this._readableState ? this._readableState.encoding : null\n    }\n  },\n  errored: {\n    __proto__: null,\n    enumerable: false,\n    get() {\n      return this._readableState ? this._readableState.errored : null\n    }\n  },\n  closed: {\n    __proto__: null,\n    get() {\n      return this._readableState ? this._readableState.closed : false\n    }\n  },\n  destroyed: {\n    __proto__: null,\n    enumerable: false,\n    get() {\n      return this._readableState ? this._readableState.destroyed : false\n    },\n    set(value) {\n      // We ignore the value if the stream\n      // has not been initialized yet.\n      if (!this._readableState) {\n        return\n      }\n\n      // Backward compatibility, the user is explicitly\n      // managing destroyed.\n      this._readableState.destroyed = value\n    }\n  },\n  readableEnded: {\n    __proto__: null,\n    enumerable: false,\n    get() {\n      return this._readableState ? this._readableState.endEmitted : false\n    }\n  }\n})\nObjectDefineProperties(ReadableState.prototype, {\n  // Legacy getter for `pipesCount`.\n  pipesCount: {\n    __proto__: null,\n    get() {\n      return this.pipes.length\n    }\n  },\n  // Legacy property for `paused`.\n  paused: {\n    __proto__: null,\n    get() {\n      return this[kPaused] !== false\n    },\n    set(value) {\n      this[kPaused] = !!value\n    }\n  }\n})\n\n// Exposed for testing purposes only.\nReadable._fromList = fromList\n\n// Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromList(n, state) {\n  // nothing buffered.\n  if (state.length === 0) return null\n  let ret\n  if (state.objectMode) ret = state.buffer.shift()\n  else if (!n || n >= state.length) {\n    // Read it all, truncate the list.\n    if (state.decoder) ret = state.buffer.join('')\n    else if (state.buffer.length === 1) ret = state.buffer.first()\n    else ret = state.buffer.concat(state.length)\n    state.buffer.clear()\n  } else {\n    // read part of list.\n    ret = state.buffer.consume(n, state.decoder)\n  }\n  return ret\n}\nfunction endReadable(stream) {\n  const state = stream._readableState\n  debug('endReadable', state.endEmitted)\n  if (!state.endEmitted) {\n    state.ended = true\n    process.nextTick(endReadableNT, state, stream)\n  }\n}\nfunction endReadableNT(state, stream) {\n  debug('endReadableNT', state.endEmitted, state.length)\n\n  // Check that we didn't get one last unshift.\n  if (!state.errored && !state.closeEmitted && !state.endEmitted && state.length === 0) {\n    state.endEmitted = true\n    stream.emit('end')\n    if (stream.writable && stream.allowHalfOpen === false) {\n      process.nextTick(endWritableNT, stream)\n    } else if (state.autoDestroy) {\n      // In case of duplex streams we need a way to detect\n      // if the writable side is ready for autoDestroy as well.\n      const wState = stream._writableState\n      const autoDestroy =\n        !wState ||\n        (wState.autoDestroy &&\n          // We don't expect the writable to ever 'finish'\n          // if writable is explicitly set to false.\n          (wState.finished || wState.writable === false))\n      if (autoDestroy) {\n        stream.destroy()\n      }\n    }\n  }\n}\nfunction endWritableNT(stream) {\n  const writable = stream.writable && !stream.writableEnded && !stream.destroyed\n  if (writable) {\n    stream.end()\n  }\n}\nReadable.from = function (iterable, opts) {\n  return from(Readable, iterable, opts)\n}\nlet webStreamsAdapters\n\n// Lazy to avoid circular references\nfunction lazyWebStreams() {\n  if (webStreamsAdapters === undefined) webStreamsAdapters = {}\n  return webStreamsAdapters\n}\nReadable.fromWeb = function (readableStream, options) {\n  return lazyWebStreams().newStreamReadableFromReadableStream(readableStream, options)\n}\nReadable.toWeb = function (streamReadable, options) {\n  return lazyWebStreams().newReadableStreamFromStreamReadable(streamReadable, options)\n}\nReadable.wrap = function (src, options) {\n  var _ref, _src$readableObjectMo\n  return new Readable({\n    objectMode:\n      (_ref =\n        (_src$readableObjectMo = src.readableObjectMode) !== null && _src$readableObjectMo !== undefined\n          ? _src$readableObjectMo\n          : src.objectMode) !== null && _ref !== undefined\n        ? _ref\n        : true,\n    ...options,\n    destroy(err, callback) {\n      destroyImpl.destroyer(src, err)\n      callback(err)\n    }\n  }).wrap(src)\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAIzC,qBAAqB,GAErB,MAAM;;;;;;;;;GAAkB;AAExB,mBAAmB,GAEnB,MAAM,EACJ,qBAAqB,EACrB,eAAe,EACf,WAAW,EACX,cAAc,EACd,sBAAsB,EACtB,UAAU,EACV,oBAAoB,EACpB,OAAO,EACP,OAAO,EACP,kBAAkB,EAClB,mBAAmB,EACnB,MAAM,EACP;AACD,OAAO,OAAO,GAAG;AACjB,SAAS,aAAa,GAAG;AACzB,MAAM,EAAE,cAAc,EAAE,EAAE;AAC1B,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE;AACjC,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,cAAc,EAAE;AACxB,MAAM;AACN,IAAI,QAAQ,4GAA2B,QAAQ,CAAC,UAAU,CAAC;IACzD,QAAQ;AACV;AACA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,gBAAgB,EAAE,uBAAuB,EAAE;AACnD,MAAM,EACJ,kBAAkB,EAClB,OAAO,EACL,oBAAoB,EACpB,0BAA0B,EAC1B,gBAAgB,EAChB,yBAAyB,EACzB,kCAAkC,EACnC,EACD,UAAU,EACX;AACD,MAAM,EAAE,cAAc,EAAE;AACxB,MAAM,UAAU,OAAO;AACvB,MAAM,EAAE,aAAa,EAAE;;;;;;;;;GAAW;AAClC,MAAM;AACN,qBAAqB,SAAS,SAAS,EAAE,OAAO,SAAS;AACzD,qBAAqB,UAAU;AAC/B,MAAM,MAAM,KAAO;AACnB,MAAM,EAAE,cAAc,EAAE,GAAG;AAC3B,MAAM,cAAc,KAAK;AACzB,MAAM,SAAS,KAAK;AACpB,MAAM,cAAc,KAAK;AACzB,MAAM,WAAW,KAAK;AACtB,MAAM,eAAe,KAAK;AAC1B,MAAM,QAAQ,KAAK;AACnB,MAAM,gBAAgB,KAAK;AAC3B,MAAM,mBAAmB,KAAK;AAC9B,MAAM,qBAAqB,KAAK;AAChC,MAAM,mBAAmB,KAAK;AAC9B,MAAM,gBAAgB,KAAK;AAC3B,MAAM,aAAa,KAAK;AACxB,MAAM,eAAe,KAAK;AAC1B,MAAM,aAAa,KAAK;AACxB,MAAM,UAAU,KAAK;AACrB,MAAM,gBAAgB,KAAK;AAC3B,MAAM,mBAAmB,KAAK;AAC9B,MAAM,eAAe,KAAK;AAC1B,MAAM,eAAe,KAAK;AAE1B,kFAAkF;AAClF,SAAS,qBAAqB,GAAG;IAC/B,OAAO;QACL,YAAY;QACZ;YACE,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,MAAM;QAChC;QACA,KAAI,KAAK;YACP,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI;iBACpB,IAAI,CAAC,KAAK,IAAI,CAAC;QACtB;IACF;AACF;AACA,uBAAuB,cAAc,SAAS,EAAE;IAC9C,YAAY,qBAAqB;IACjC,OAAO,qBAAqB;IAC5B,YAAY,qBAAqB;IACjC,SAAS,qBAAqB;IAC9B,kDAAkD;IAClD,mDAAmD;IACnD,sDAAsD;IACtD,eAAe;IACf,aAAa,qBAAqB;IAClC,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,yCAAyC;IACzC,MAAM,qBAAqB;IAC3B,qDAAqD;IACrD,mDAAmD;IACnD,cAAc,qBAAqB;IACnC,iBAAiB,qBAAqB;IACtC,mBAAmB,qBAAqB;IACxC,iBAAiB,qBAAqB;IACtC,wEAAwE;IACxE,cAAc,qBAAqB;IACnC,WAAW,qBAAqB;IAChC,aAAa,qBAAqB;IAClC,yBAAyB;IACzB,WAAW,qBAAqB;IAChC,wDAAwD;IACxD,QAAQ,qBAAqB;IAC7B,4DAA4D;IAC5D,0BAA0B;IAC1B,cAAc,qBAAqB;IACnC,iBAAiB,qBAAqB;IACtC,+CAA+C;IAC/C,aAAa,qBAAqB;IAClC,aAAa,qBAAqB;AACpC;AACA,SAAS,cAAc,OAAO,EAAE,MAAM,EAAE,QAAQ;IAC9C,2DAA2D;IAC3D,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,2EAA2E;IAC3E,IAAI,OAAO,aAAa,WAAW,WAAW;IAE9C,4EAA4E;IAC5E,kCAAkC;IAClC,IAAI,CAAC,KAAK,GAAG,aAAa,eAAe,eAAe;IACxD,2DAA2D;IAC3D,yDAAyD;IACzD,IAAI,WAAW,QAAQ,UAAU,EAAE,IAAI,CAAC,KAAK,IAAI;IACjD,IAAI,YAAY,WAAW,QAAQ,kBAAkB,EAAE,IAAI,CAAC,KAAK,IAAI;IAErE,iEAAiE;IACjE,uEAAuE;IACvE,IAAI,CAAC,aAAa,GAAG,UACjB,iBAAiB,IAAI,EAAE,SAAS,yBAAyB,YACzD,wBAAwB;IAE5B,6EAA6E;IAC7E,iEAAiE;IACjE,iBAAiB;IACjB,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,KAAK,GAAG,EAAE;IACf,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;IAEhB,wDAAwD;IACxD,IAAI,WAAW,QAAQ,SAAS,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC;IAE3D,sEAAsE;IACtE,IAAI,WAAW,QAAQ,WAAW,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC;IAE7D,iEAAiE;IACjE,wEAAwE;IACxE,uEAAuE;IACvE,qBAAqB;IACrB,IAAI,CAAC,OAAO,GAAG;IAEf,sEAAsE;IACtE,6DAA6D;IAC7D,uDAAuD;IACvD,IAAI,CAAC,eAAe,GAAG,AAAC,WAAW,QAAQ,eAAe,IAAK;IAE/D,uDAAuD;IACvD,yCAAyC;IACzC,IAAI,CAAC,iBAAiB,GAAG;IACzB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,WAAW,QAAQ,QAAQ,EAAE;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,QAAQ,QAAQ;QACjD,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;IAClC;AACF;AACA,SAAS,SAAS,OAAO;IACvB,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,GAAG,OAAO,IAAI,SAAS;IAErD,yEAAyE;IACzE,uDAAuD;IACvD,MAAM,WAAW,IAAI;IACrB,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,SAAS,IAAI,EAAE;IACvD,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,IAAI,KAAK,YAAY,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI;QACjE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;QAC1E,IAAI,OAAO,QAAQ,SAAS,KAAK,YAAY,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;QAChF,IAAI,QAAQ,MAAM,IAAI,CAAC,UAAU,eAAe,QAAQ,MAAM,EAAE,IAAI;IACtE;IACA,OAAO,IAAI,CAAC,IAAI,EAAE;IAClB,YAAY,SAAS,CAAC,IAAI,EAAE;QAC1B,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;YACpC,cAAc,IAAI,EAAE,IAAI,CAAC,cAAc;QACzC;IACF;AACF;AACA,SAAS,SAAS,CAAC,OAAO,GAAG,YAAY,OAAO;AAChD,SAAS,SAAS,CAAC,UAAU,GAAG,YAAY,SAAS;AACrD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC7C,GAAG;AACL;AACA,SAAS,SAAS,CAAC,GAAG,sBAAsB,CAAC,GAAG,SAAU,GAAG;IAC3D,IAAI,CAAC,OAAO,CAAC;AACf;AACA,SAAS,SAAS,CAAC,mBAAmB,GAAG;IACvC,IAAI;IACJ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACnB,QAAQ,IAAI,CAAC,aAAa,GAAG,OAAO,IAAI;QACxC,IAAI,CAAC,OAAO,CAAC;IACf;IACA,OAAO,IAAI,QAAQ,CAAC,SAAS,SAAW,IAAI,IAAI,EAAE,CAAC,MAAS,OAAO,QAAQ,QAAQ,OAAO,OAAO,QAAQ;AAC3G;AAEA,mDAAmD;AACnD,+DAA+D;AAC/D,6DAA6D;AAC7D,qBAAqB;AACrB,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,QAAQ;IACjD,OAAO,iBAAiB,IAAI,EAAE,OAAO,UAAU;AACjD;AAEA,+DAA+D;AAC/D,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,QAAQ;IACpD,OAAO,iBAAiB,IAAI,EAAE,OAAO,UAAU;AACjD;AACA,SAAS,iBAAiB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU;IAC3D,MAAM,oBAAoB;IAC1B,MAAM,QAAQ,OAAO,cAAc;IACnC,IAAI;IACJ,IAAI,CAAC,MAAM,KAAK,GAAG,WAAW,MAAM,GAAG;QACrC,IAAI,OAAO,UAAU,UAAU;YAC7B,WAAW,YAAY,MAAM,eAAe;YAC5C,IAAI,MAAM,QAAQ,KAAK,UAAU;gBAC/B,IAAI,cAAc,MAAM,QAAQ,EAAE;oBAChC,6DAA6D;oBAC7D,wDAAwD;oBACxD,QAAQ,OAAO,IAAI,CAAC,OAAO,UAAU,QAAQ,CAAC,MAAM,QAAQ;gBAC9D,OAAO;oBACL,QAAQ,OAAO,IAAI,CAAC,OAAO;oBAC3B,WAAW;gBACb;YACF;QACF,OAAO,IAAI,iBAAiB,QAAQ;YAClC,WAAW;QACb,OAAO,IAAI,OAAO,aAAa,CAAC,QAAQ;YACtC,QAAQ,OAAO,mBAAmB,CAAC;YACnC,WAAW;QACb,OAAO,IAAI,SAAS,MAAM;YACxB,MAAM,IAAI,qBAAqB,SAAS;gBAAC;gBAAU;gBAAU;aAAa,EAAE;QAC9E;IACF;IACA,IAAI,KAAK;QACP,eAAe,QAAQ;IACzB,OAAO,IAAI,UAAU,MAAM;QACzB,MAAM,KAAK,IAAI,CAAC;QAChB,WAAW,QAAQ;IACrB,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG,WAAW,MAAM,KAAM,SAAS,MAAM,MAAM,GAAG,GAAI;QAC3E,IAAI,YAAY;YACd,IAAI,CAAC,MAAM,KAAK,GAAG,WAAW,MAAM,GAAG,eAAe,QAAQ,IAAI;iBAC7D,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,EAAE,OAAO;iBAC7C,SAAS,QAAQ,OAAO,OAAO;QACtC,OAAO,IAAI,MAAM,KAAK,EAAE;YACtB,eAAe,QAAQ,IAAI;QAC7B,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,EAAE;YAC3C,OAAO;QACT,OAAO;YACL,MAAM,KAAK,IAAI,CAAC;YAChB,IAAI,MAAM,OAAO,IAAI,CAAC,UAAU;gBAC9B,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;gBAC5B,IAAI,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG,SAAS,QAAQ,OAAO,OAAO;qBACtE,cAAc,QAAQ;YAC7B,OAAO;gBACL,SAAS,QAAQ,OAAO,OAAO;YACjC;QACF;IACF,OAAO,IAAI,CAAC,YAAY;QACtB,MAAM,KAAK,IAAI,CAAC;QAChB,cAAc,QAAQ;IACxB;IAEA,2DAA2D;IAC3D,8DAA8D;IAC9D,8DAA8D;IAC9D,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,MAAM,aAAa,IAAI,MAAM,MAAM,KAAK,CAAC;AAClF;AACA,SAAS,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU;IAChD,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,IAAI,OAAO,aAAa,CAAC,UAAU,GAAG;QAC1F,qDAAqD;QACrD,+BAA+B;QAC/B,IAAI,CAAC,MAAM,KAAK,GAAG,gBAAgB,MAAM,GAAG;YAC1C,MAAM,iBAAiB,CAAC,KAAK;QAC/B,OAAO;YACL,MAAM,iBAAiB,GAAG;QAC5B;QACA,MAAM,WAAW,GAAG;QACpB,OAAO,IAAI,CAAC,QAAQ;IACtB,OAAO;QACL,0BAA0B;QAC1B,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACnD,IAAI,YAAY,MAAM,MAAM,CAAC,OAAO,CAAC;aAChC,MAAM,MAAM,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,MAAM,KAAK,GAAG,aAAa,MAAM,GAAG,aAAa;IACxD;IACA,cAAc,QAAQ;AACxB;AACA,SAAS,SAAS,CAAC,QAAQ,GAAG;IAC5B,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,OAAO,KAAK,CAAC,QAAQ,KAAK,QAAQ,MAAM,OAAO,KAAK;AACtD;AAEA,2BAA2B;AAC3B,SAAS,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG;IAC5C,MAAM,UAAU,IAAI,cAAc;IAClC,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;IAC9B,sDAAsD;IACtD,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ;IACnE,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,MAAM;IACzC,iEAAiE;IACjE,IAAI,UAAU;IACd,KAAK,MAAM,QAAQ,OAAQ;QACzB,WAAW,QAAQ,KAAK,CAAC;IAC3B;IACA,OAAO,KAAK;IACZ,IAAI,YAAY,IAAI,OAAO,IAAI,CAAC;IAChC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,QAAQ,MAAM;IAC3C,OAAO,IAAI;AACb;AAEA,6BAA6B;AAC7B,MAAM,UAAU;AAChB,SAAS,wBAAwB,CAAC;IAChC,IAAI,IAAI,SAAS;QACf,MAAM,IAAI,iBAAiB,QAAQ,WAAW;IAChD,OAAO;QACL,2EAA2E;QAC3E,gBAAgB;QAChB;QACA,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX,KAAK,MAAM;QACX;IACF;IACA,OAAO;AACT;AAEA,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,cAAc,CAAC,EAAE,KAAK;IAC7B,IAAI,KAAK,KAAM,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAG,OAAO;IAC1D,IAAI,CAAC,MAAM,KAAK,GAAG,WAAW,MAAM,GAAG,OAAO;IAC9C,IAAI,YAAY,IAAI;QAClB,kCAAkC;QAClC,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK,GAAG,MAAM;QACrE,OAAO,MAAM,MAAM;IACrB;IACA,IAAI,KAAK,MAAM,MAAM,EAAE,OAAO;IAC9B,OAAO,MAAM,KAAK,GAAG,MAAM,MAAM,GAAG;AACtC;AAEA,oEAAoE;AACpE,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,CAAC;IACnC,MAAM,QAAQ;IACd,wEAAwE;IACxE,iDAAiD;IACjD,IAAI,MAAM,WAAW;QACnB,IAAI;IACN,OAAO,IAAI,CAAC,gBAAgB,IAAI;QAC9B,IAAI,eAAe,GAAG;IACxB;IACA,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,MAAM,QAAQ;IAEd,qEAAqE;IACrE,IAAI,IAAI,MAAM,aAAa,EAAE,MAAM,aAAa,GAAG,wBAAwB;IAC3E,IAAI,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC;IAE7B,6DAA6D;IAC7D,gEAAgE;IAChE,oCAAoC;IACpC,IACE,MAAM,KACN,MAAM,YAAY,IAClB,CAAC,CAAC,MAAM,aAAa,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GACpG;QACA,MAAM,sBAAsB,MAAM,MAAM,EAAE,MAAM,KAAK;QACrD,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,YAAY,IAAI;aAClD,aAAa,IAAI;QACtB,OAAO;IACT;IACA,IAAI,cAAc,GAAG;IAErB,0DAA0D;IAC1D,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE;QAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,YAAY,IAAI;QACxC,OAAO;IACT;IAEA,oDAAoD;IACpD,4DAA4D;IAC5D,6DAA6D;IAC7D,6DAA6D;IAC7D,2DAA2D;IAC3D,iCAAiC;IACjC,EAAE;IACF,qBAAqB;IACrB,6DAA6D;IAC7D,0BAA0B;IAC1B,EAAE;IACF,oEAAoE;IACpE,kEAAkE;IAClE,kEAAkE;IAClE,mEAAmE;IACnE,sCAAsC;IACtC,qEAAqE;IACrE,sEAAsE;IACtE,kBAAkB;IAClB,EAAE;IACF,sEAAsE;IAEtE,gEAAgE;IAChE,IAAI,SAAS,CAAC,MAAM,KAAK,GAAG,aAAa,MAAM;IAC/C,MAAM,iBAAiB;IAEvB,yEAAyE;IACzE,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,GAAG,IAAI,MAAM,aAAa,EAAE;QAChE,SAAS;QACT,MAAM,8BAA8B;IACtC;IAEA,mEAAmE;IACnE,yEAAyE;IACzE,4DAA4D;IAC5D,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,WAAW,EAAE;QAC1F,SAAS;QACT,MAAM,kCAAkC;IAC1C,OAAO,IAAI,QAAQ;QACjB,MAAM;QACN,MAAM,KAAK,IAAI,WAAW;QAC1B,oEAAoE;QACpE,IAAI,MAAM,MAAM,KAAK,GAAG,MAAM,KAAK,IAAI;QAEvC,4BAA4B;QAC5B,IAAI;YACF,IAAI,CAAC,KAAK,CAAC,MAAM,aAAa;QAChC,EAAE,OAAO,KAAK;YACZ,eAAe,IAAI,EAAE;QACvB;QACA,MAAM,KAAK,IAAI,CAAC;QAEhB,oEAAoE;QACpE,sEAAsE;QACtE,IAAI,CAAC,MAAM,OAAO,EAAE,IAAI,cAAc,OAAO;IAC/C;IACA,IAAI;IACJ,IAAI,IAAI,GAAG,MAAM,SAAS,GAAG;SACxB,MAAM;IACX,IAAI,QAAQ,MAAM;QAChB,MAAM,YAAY,GAAG,MAAM,MAAM,IAAI,MAAM,aAAa;QACxD,IAAI;IACN,OAAO;QACL,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,eAAe,EAAE;YACzB,MAAM,iBAAiB,CAAC,KAAK;QAC/B,OAAO;YACL,MAAM,iBAAiB,GAAG;QAC5B;IACF;IACA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,yDAAyD;QACzD,oDAAoD;QACpD,IAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAY,GAAG;QAEvC,sEAAsE;QACtE,IAAI,UAAU,KAAK,MAAM,KAAK,EAAE,YAAY,IAAI;IAClD;IACA,IAAI,QAAQ,QAAQ,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM,YAAY,EAAE;QAC9D,MAAM,WAAW,GAAG;QACpB,IAAI,CAAC,IAAI,CAAC,QAAQ;IACpB;IACA,OAAO;AACT;AACA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,MAAM;IACN,IAAI,MAAM,KAAK,EAAE;IACjB,IAAI,MAAM,OAAO,EAAE;QACjB,MAAM,QAAQ,MAAM,OAAO,CAAC,GAAG;QAC/B,IAAI,SAAS,MAAM,MAAM,EAAE;YACzB,MAAM,MAAM,CAAC,IAAI,CAAC;YAClB,MAAM,MAAM,IAAI,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QACrD;IACF;IACA,MAAM,KAAK,GAAG;IACd,IAAI,MAAM,IAAI,EAAE;QACd,yDAAyD;QACzD,gDAAgD;QAChD,mDAAmD;QACnD,aAAa;IACf,OAAO;QACL,sDAAsD;QACtD,MAAM,YAAY,GAAG;QACrB,MAAM,eAAe,GAAG;QACxB,wDAAwD;QACxD,+DAA+D;QAC/D,cAAc;IAChB;AACF;AAEA,wEAAwE;AACxE,qEAAqE;AACrE,uDAAuD;AACvD,SAAS,aAAa,MAAM;IAC1B,MAAM,QAAQ,OAAO,cAAc;IACnC,MAAM,gBAAgB,MAAM,YAAY,EAAE,MAAM,eAAe;IAC/D,MAAM,YAAY,GAAG;IACrB,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,MAAM,gBAAgB,MAAM,OAAO;QACnC,MAAM,eAAe,GAAG;QACxB,QAAQ,QAAQ,CAAC,eAAe;IAClC;AACF;AACA,SAAS,cAAc,MAAM;IAC3B,MAAM,QAAQ,OAAO,cAAc;IACnC,MAAM,iBAAiB,MAAM,SAAS,EAAE,MAAM,MAAM,EAAE,MAAM,KAAK;IACjE,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,KAAK,GAAG;QACvE,OAAO,IAAI,CAAC;QACZ,MAAM,eAAe,GAAG;IAC1B;IAEA,8CAA8C;IAC9C,wDAAwD;IACxD,iBAAiB;IACjB,sBAAsB;IACtB,uDAAuD;IACvD,6BAA6B;IAC7B,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,aAAa;IAC1F,KAAK;AACP;AAEA,oEAAoE;AACpE,mEAAmE;AACnE,iEAAiE;AACjE,oBAAoB;AACpB,iEAAiE;AACjE,wDAAwD;AACxD,SAAS,cAAc,MAAM,EAAE,KAAK;IAClC,IAAI,CAAC,MAAM,WAAW,IAAI,MAAM,WAAW,EAAE;QAC3C,MAAM,WAAW,GAAG;QACpB,QAAQ,QAAQ,CAAC,gBAAgB,QAAQ;IAC3C;AACF;AACA,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,0CAA0C;IAC1C,EAAE;IACF,qDAAqD;IACrD,4EAA4E;IAC5E,wEAAwE;IACxE,2EAA2E;IAC3E,0EAA0E;IAC1E,mDAAmD;IACnD,2EAA2E;IAC3E,4EAA4E;IAC5E,2EAA2E;IAC3E,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,0EAA0E;IAC1E,+CAA+C;IAC/C,wCAAwC;IACxC,2EAA2E;IAC3E,wEAAwE;IACxE,2EAA2E;IAC3E,sEAAsE;IACtE,4EAA4E;IAC5E,sCAAsC;IACtC,MACE,CAAC,MAAM,OAAO,IACd,CAAC,MAAM,KAAK,IACZ,CAAC,MAAM,MAAM,GAAG,MAAM,aAAa,IAAK,MAAM,OAAO,IAAI,MAAM,MAAM,KAAK,CAAE,EAC5E;QACA,MAAM,MAAM,MAAM,MAAM;QACxB,MAAM;QACN,OAAO,IAAI,CAAC;QACZ,IAAI,QAAQ,MAAM,MAAM,EAEtB;IACJ;IACA,MAAM,WAAW,GAAG;AACtB;AAEA,yEAAyE;AACzE,kDAAkD;AAClD,qEAAqE;AACrE,8CAA8C;AAC9C,SAAS,SAAS,CAAC,KAAK,GAAG,SAAU,CAAC;IACpC,MAAM,IAAI,2BAA2B;AACvC;AACA,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,QAAQ;IAChD,MAAM,MAAM,IAAI;IAChB,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;QAC5B,IAAI,CAAC,MAAM,eAAe,EAAE;YAC1B,MAAM,eAAe,GAAG;YACxB,MAAM,iBAAiB,GAAG,IAAI,QAAQ,MAAM,iBAAiB,GAAG;gBAAC,MAAM,iBAAiB;aAAC,GAAG,EAAE;QAChG;IACF;IACA,MAAM,KAAK,CAAC,IAAI,CAAC;IACjB,MAAM,yBAAyB,MAAM,KAAK,CAAC,MAAM,EAAE;IACnD,MAAM,QAAQ,CAAC,CAAC,YAAY,SAAS,GAAG,KAAK,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,SAAS,QAAQ,MAAM;IACzG,MAAM,QAAQ,QAAQ,QAAQ;IAC9B,IAAI,MAAM,UAAU,EAAE,QAAQ,QAAQ,CAAC;SAClC,IAAI,IAAI,CAAC,OAAO;IACrB,KAAK,EAAE,CAAC,UAAU;IAClB,SAAS,SAAS,QAAQ,EAAE,UAAU;QACpC,MAAM;QACN,IAAI,aAAa,KAAK;YACpB,IAAI,cAAc,WAAW,UAAU,KAAK,OAAO;gBACjD,WAAW,UAAU,GAAG;gBACxB;YACF;QACF;IACF;IACA,SAAS;QACP,MAAM;QACN,KAAK,GAAG;IACV;IACA,IAAI;IACJ,IAAI,YAAY;IAChB,SAAS;QACP,MAAM;QACN,kDAAkD;QAClD,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,IAAI,SAAS;YACX,KAAK,cAAc,CAAC,SAAS;QAC/B;QACA,KAAK,cAAc,CAAC,SAAS;QAC7B,KAAK,cAAc,CAAC,UAAU;QAC9B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,OAAO;QAC1B,IAAI,cAAc,CAAC,QAAQ;QAC3B,YAAY;QAEZ,uDAAuD;QACvD,yDAAyD;QACzD,iBAAiB;QACjB,6DAA6D;QAC7D,6DAA6D;QAC7D,IAAI,WAAW,MAAM,iBAAiB,IAAI,CAAC,CAAC,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,SAAS,GAAG;IACrG;IACA,SAAS;QACP,4DAA4D;QAC5D,2DAA2D;QAC3D,uBAAuB;QACvB,yDAAyD;QACzD,IAAI,CAAC,WAAW;YACd,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,MAAM;gBACvD,MAAM,+BAA+B;gBACrC,MAAM,iBAAiB,GAAG;gBAC1B,MAAM,eAAe,GAAG;YAC1B,OAAO,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,KAAK,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAO;gBAC/D,MAAM,+BAA+B,MAAM,iBAAiB,CAAC,IAAI;gBACjE,MAAM,iBAAiB,CAAC,GAAG,CAAC;YAC9B;YACA,IAAI,KAAK;QACX;QACA,IAAI,CAAC,SAAS;YACZ,0DAA0D;YAC1D,4DAA4D;YAC5D,2DAA2D;YAC3D,YAAY;YACZ,UAAU,YAAY,KAAK;YAC3B,KAAK,EAAE,CAAC,SAAS;QACnB;IACF;IACA,IAAI,EAAE,CAAC,QAAQ;IACf,SAAS,OAAO,KAAK;QACnB,MAAM;QACN,MAAM,MAAM,KAAK,KAAK,CAAC;QACvB,MAAM,cAAc;QACpB,IAAI,QAAQ,OAAO;YACjB;QACF;IACF;IAEA,sDAAsD;IACtD,0DAA0D;IAC1D,SAAS,QAAQ,EAAE;QACjB,MAAM,WAAW;QACjB;QACA,KAAK,cAAc,CAAC,SAAS;QAC7B,IAAI,KAAK,aAAa,CAAC,aAAa,GAAG;YACrC,MAAM,IAAI,KAAK,cAAc,IAAI,KAAK,cAAc;YACpD,IAAI,KAAK,CAAC,EAAE,YAAY,EAAE;gBACxB,2DAA2D;gBAC3D,eAAe,MAAM;YACvB,OAAO;gBACL,KAAK,IAAI,CAAC,SAAS;YACrB;QACF;IACF;IAEA,gEAAgE;IAChE,gBAAgB,MAAM,SAAS;IAE/B,8DAA8D;IAC9D,SAAS;QACP,KAAK,cAAc,CAAC,UAAU;QAC9B;IACF;IACA,KAAK,IAAI,CAAC,SAAS;IACnB,SAAS;QACP,MAAM;QACN,KAAK,cAAc,CAAC,SAAS;QAC7B;IACF;IACA,KAAK,IAAI,CAAC,UAAU;IACpB,SAAS;QACP,MAAM;QACN,IAAI,MAAM,CAAC;IACb;IAEA,0CAA0C;IAC1C,KAAK,IAAI,CAAC,QAAQ;IAElB,oDAAoD;IAEpD,IAAI,KAAK,iBAAiB,KAAK,MAAM;QACnC;IACF,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;QACzB,MAAM;QACN,IAAI,MAAM;IACZ;IACA,OAAO;AACT;AACA,SAAS,YAAY,GAAG,EAAE,IAAI;IAC5B,OAAO,SAAS;QACd,MAAM,QAAQ,IAAI,cAAc;QAEhC,gCAAgC;QAChC,wCAAwC;QACxC,gCAAgC;QAChC,IAAI,MAAM,iBAAiB,KAAK,MAAM;YACpC,MAAM,eAAe;YACrB,MAAM,iBAAiB,GAAG;QAC5B,OAAO,IAAI,MAAM,eAAe,EAAE;YAChC,MAAM,eAAe,MAAM,iBAAiB,CAAC,IAAI;YACjD,MAAM,iBAAiB,CAAC,MAAM,CAAC;QACjC;QACA,IAAI,CAAC,CAAC,MAAM,iBAAiB,IAAI,MAAM,iBAAiB,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,aAAa,CAAC,SAAS;YACjG,IAAI,MAAM;QACZ;IACF;AACF;AACA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI;IACxC,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,MAAM,aAAa;QACjB,YAAY;IACd;IAEA,iDAAiD;IACjD,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG,OAAO,IAAI;IACzC,IAAI,CAAC,MAAM;QACT,cAAc;QACd,MAAM,QAAQ,MAAM,KAAK;QACzB,MAAM,KAAK,GAAG,EAAE;QAChB,IAAI,CAAC,KAAK;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAChC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;YAC5B,YAAY;QACd;QACF,OAAO,IAAI;IACb;IAEA,6BAA6B;IAC7B,MAAM,QAAQ,sBAAsB,MAAM,KAAK,EAAE;IACjD,IAAI,UAAU,CAAC,GAAG,OAAO,IAAI;IAC7B,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO;IAC1B,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;IACxC,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE;IAC1B,OAAO,IAAI;AACb;AAEA,2CAA2C;AAC3C,sDAAsD;AACtD,SAAS,SAAS,CAAC,EAAE,GAAG,SAAU,EAAE,EAAE,EAAE;IACtC,MAAM,MAAM,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;IAC/C,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,IAAI,OAAO,QAAQ;QACjB,2DAA2D;QAC3D,gEAAgE;QAChE,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc;QAE3D,oEAAoE;QACpE,IAAI,MAAM,OAAO,KAAK,OAAO,IAAI,CAAC,MAAM;IAC1C,OAAO,IAAI,OAAO,YAAY;QAC5B,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,iBAAiB,EAAE;YACjD,MAAM,iBAAiB,GAAG,MAAM,YAAY,GAAG;YAC/C,MAAM,OAAO,GAAG;YAChB,MAAM,eAAe,GAAG;YACxB,MAAM,eAAe,MAAM,MAAM,EAAE,MAAM,OAAO;YAChD,IAAI,MAAM,MAAM,EAAE;gBAChB,aAAa,IAAI;YACnB,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;gBACzB,QAAQ,QAAQ,CAAC,kBAAkB,IAAI;YACzC;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,SAAS,CAAC,WAAW,GAAG,SAAS,SAAS,CAAC,EAAE;AACtD,SAAS,SAAS,CAAC,cAAc,GAAG,SAAU,EAAE,EAAE,EAAE;IAClD,MAAM,MAAM,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;IAC3D,IAAI,OAAO,YAAY;QACrB,0DAA0D;QAC1D,6DAA6D;QAC7D,+DAA+D;QAC/D,+DAA+D;QAC/D,2CAA2C;QAC3C,UAAU;QACV,QAAQ,QAAQ,CAAC,yBAAyB,IAAI;IAChD;IACA,OAAO;AACT;AACA,SAAS,SAAS,CAAC,GAAG,GAAG,SAAS,SAAS,CAAC,cAAc;AAC1D,SAAS,SAAS,CAAC,kBAAkB,GAAG,SAAU,EAAE;IAClD,MAAM,MAAM,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE;IAC5D,IAAI,OAAO,cAAc,OAAO,WAAW;QACzC,0DAA0D;QAC1D,6DAA6D;QAC7D,+DAA+D;QAC/D,+DAA+D;QAC/D,2CAA2C;QAC3C,UAAU;QACV,QAAQ,QAAQ,CAAC,yBAAyB,IAAI;IAChD;IACA,OAAO;AACT;AACA,SAAS,wBAAwB,IAAI;IACnC,MAAM,QAAQ,KAAK,cAAc;IACjC,MAAM,iBAAiB,GAAG,KAAK,aAAa,CAAC,cAAc;IAC3D,IAAI,MAAM,eAAe,IAAI,KAAK,CAAC,QAAQ,KAAK,OAAO;QACrD,iDAAiD;QACjD,qCAAqC;QACrC,MAAM,OAAO,GAAG;IAEhB,0CAA0C;IAC5C,OAAO,IAAI,KAAK,aAAa,CAAC,UAAU,GAAG;QACzC,KAAK,MAAM;IACb,OAAO,IAAI,CAAC,MAAM,iBAAiB,EAAE;QACnC,MAAM,OAAO,GAAG;IAClB;AACF;AACA,SAAS,iBAAiB,IAAI;IAC5B,MAAM;IACN,KAAK,IAAI,CAAC;AACZ;AAEA,sEAAsE;AACtE,oDAAoD;AACpD,SAAS,SAAS,CAAC,MAAM,GAAG;IAC1B,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;QACN,4CAA4C;QAC5C,0CAA0C;QAC1C,YAAY;QACZ,MAAM,OAAO,GAAG,CAAC,MAAM,iBAAiB;QACxC,OAAO,IAAI,EAAE;IACf;IACA,KAAK,CAAC,QAAQ,GAAG;IACjB,OAAO,IAAI;AACb;AACA,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,IAAI,CAAC,MAAM,eAAe,EAAE;QAC1B,MAAM,eAAe,GAAG;QACxB,QAAQ,QAAQ,CAAC,SAAS,QAAQ;IACpC;AACF;AACA,SAAS,QAAQ,MAAM,EAAE,KAAK;IAC5B,MAAM,UAAU,MAAM,OAAO;IAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,OAAO,IAAI,CAAC;IACd;IACA,MAAM,eAAe,GAAG;IACxB,OAAO,IAAI,CAAC;IACZ,KAAK;IACL,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE,OAAO,IAAI,CAAC;AACnD;AACA,SAAS,SAAS,CAAC,KAAK,GAAG;IACzB,MAAM,yBAAyB,IAAI,CAAC,cAAc,CAAC,OAAO;IAC1D,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,OAAO;QACzC,MAAM;QACN,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC9B,IAAI,CAAC,IAAI,CAAC;IACZ;IACA,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;IAC/B,OAAO,IAAI;AACb;AACA,SAAS,KAAK,MAAM;IAClB,MAAM,QAAQ,OAAO,cAAc;IACnC,MAAM,QAAQ,MAAM,OAAO;IAC3B,MAAO,MAAM,OAAO,IAAI,OAAO,IAAI,OAAO;AAC5C;AAEA,qDAAqD;AACrD,uDAAuD;AACvD,6CAA6C;AAC7C,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM;IACxC,IAAI,SAAS;IAEb,8CAA8C;IAC9C,+CAA+C;IAC/C,uDAAuD;IAEvD,OAAO,EAAE,CAAC,QAAQ,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,OAAO,KAAK,EAAE;YACrC,SAAS;YACT,OAAO,KAAK;QACd;IACF;IACA,OAAO,EAAE,CAAC,OAAO;QACf,IAAI,CAAC,IAAI,CAAC;IACZ;IACA,OAAO,EAAE,CAAC,SAAS,CAAC;QAClB,eAAe,IAAI,EAAE;IACvB;IACA,OAAO,EAAE,CAAC,SAAS;QACjB,IAAI,CAAC,OAAO;IACd;IACA,OAAO,EAAE,CAAC,WAAW;QACnB,IAAI,CAAC,OAAO;IACd;IACA,IAAI,CAAC,KAAK,GAAG;QACX,IAAI,UAAU,OAAO,MAAM,EAAE;YAC3B,SAAS;YACT,OAAO,MAAM;QACf;IACF;IAEA,6EAA6E;IAC7E,MAAM,aAAa,WAAW;IAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,MAAM,IAAI,UAAU,CAAC,EAAE;QACvB,IAAI,IAAI,CAAC,EAAE,KAAK,aAAa,OAAO,MAAM,CAAC,EAAE,KAAK,YAAY;YAC5D,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;QAC3B;IACF;IACA,OAAO,IAAI;AACb;AACA,SAAS,SAAS,CAAC,oBAAoB,GAAG;IACxC,OAAO,sBAAsB,IAAI;AACnC;AACA,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,OAAO;IAC7C,IAAI,YAAY,WAAW;QACzB,eAAe,SAAS;IAC1B;IACA,OAAO,sBAAsB,IAAI,EAAE;AACrC;AACA,SAAS,sBAAsB,MAAM,EAAE,OAAO;IAC5C,IAAI,OAAO,OAAO,IAAI,KAAK,YAAY;QACrC,SAAS,SAAS,IAAI,CAAC,QAAQ;YAC7B,YAAY;QACd;IACF;IACA,MAAM,OAAO,oBAAoB,QAAQ;IACzC,KAAK,MAAM,GAAG;IACd,OAAO;AACT;AACA,gBAAgB,oBAAoB,MAAM,EAAE,OAAO;IACjD,IAAI,WAAW;IACf,SAAS,KAAK,OAAO;QACnB,IAAI,IAAI,KAAK,QAAQ;YACnB;YACA,WAAW;QACb,OAAO;YACL,WAAW;QACb;IACF;IACA,OAAO,EAAE,CAAC,YAAY;IACtB,IAAI;IACJ,MAAM,UAAU,IACd,QACA;QACE,UAAU;IACZ,GACA,CAAC;QACC,QAAQ,MAAM,mBAAmB,OAAO,OAAO;QAC/C;QACA,WAAW;IACb;IAEF,IAAI;QACF,MAAO,KAAM;YACX,MAAM,QAAQ,OAAO,SAAS,GAAG,OAAO,OAAO,IAAI;YACnD,IAAI,UAAU,MAAM;gBAClB,MAAM;YACR,OAAO,IAAI,OAAO;gBAChB,MAAM;YACR,OAAO,IAAI,UAAU,MAAM;gBACzB;YACF,OAAO;gBACL,MAAM,IAAI,QAAQ;YACpB;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,mBAAmB,OAAO;QAClC,MAAM;IACR,SAAU;QACR,IACE,CAAC,SAAS,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,eAAe,MAAM,KAAK,KACrG,CAAC,UAAU,aAAa,OAAO,cAAc,CAAC,WAAW,GACzD;YACA,YAAY,SAAS,CAAC,QAAQ;QAChC,OAAO;YACL,OAAO,GAAG,CAAC,YAAY;YACvB;QACF;IACF;AACF;AAEA,yDAAyD;AACzD,mDAAmD;AACnD,sBAAsB;AACtB,uBAAuB,SAAS,SAAS,EAAE;IACzC,UAAU;QACR,WAAW;QACX;YACE,MAAM,IAAI,IAAI,CAAC,cAAc;YAC7B,kEAAkE;YAClE,0DAA0D;YAC1D,gEAAgE;YAChE,qBAAqB;YACrB,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,KAAK,SAAS,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,EAAE,UAAU;QACxF;QACA,KAAI,GAAG;YACL,oBAAoB;YACpB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,CAAC;YACnC;QACF;IACF;IACA,iBAAiB;QACf,WAAW;QACX,YAAY;QACZ,KAAK;YACH,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW;QACxC;IACF;IACA,iBAAiB;QACf,WAAW;QACX,YAAY;QACZ,KAAK;YACH,OAAO,CAAC,CAAC,CACP,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,SACjC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAC7D,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,AACjC;QACF;IACF;IACA,uBAAuB;QACrB,WAAW;QACX,YAAY;QACZ,KAAK;YACH,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa;QAC1C;IACF;IACA,gBAAgB;QACd,WAAW;QACX,YAAY;QACZ,KAAK;YACH,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;QAC1D;IACF;IACA,iBAAiB;QACf,WAAW;QACX,YAAY;QACZ,KAAK;YACH,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO;QACpC;QACA,KAAK,SAAU,KAAK;YAClB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;YAChC;QACF;IACF;IACA,gBAAgB;QACd,WAAW;QACX,YAAY;QACZ;YACE,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM;QACnC;IACF;IACA,oBAAoB;QAClB,WAAW;QACX,YAAY;QACZ;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QAChE;IACF;IACA,kBAAkB;QAChB,WAAW;QACX,YAAY;QACZ;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;QAC9D;IACF;IACA,SAAS;QACP,WAAW;QACX,YAAY;QACZ;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC7D;IACF;IACA,QAAQ;QACN,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;QAC5D;IACF;IACA,WAAW;QACT,WAAW;QACX,YAAY;QACZ;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAC/D;QACA,KAAI,KAAK;YACP,oCAAoC;YACpC,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB;YACF;YAEA,iDAAiD;YACjD,sBAAsB;YACtB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAClC;IACF;IACA,eAAe;QACb,WAAW;QACX,YAAY;QACZ;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QAChE;IACF;AACF;AACA,uBAAuB,cAAc,SAAS,EAAE;IAC9C,kCAAkC;IAClC,YAAY;QACV,WAAW;QACX;YACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;QAC1B;IACF;IACA,gCAAgC;IAChC,QAAQ;QACN,WAAW;QACX;YACE,OAAO,IAAI,CAAC,QAAQ,KAAK;QAC3B;QACA,KAAI,KAAK;YACP,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QACpB;IACF;AACF;AAEA,qCAAqC;AACrC,SAAS,SAAS,GAAG;AAErB,8CAA8C;AAC9C,iEAAiE;AACjE,6EAA6E;AAC7E,gCAAgC;AAChC,SAAS,SAAS,CAAC,EAAE,KAAK;IACxB,oBAAoB;IACpB,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAC/B,IAAI;IACJ,IAAI,MAAM,UAAU,EAAE,MAAM,MAAM,MAAM,CAAC,KAAK;SACzC,IAAI,CAAC,KAAK,KAAK,MAAM,MAAM,EAAE;QAChC,kCAAkC;QAClC,IAAI,MAAM,OAAO,EAAE,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC;aACtC,IAAI,MAAM,MAAM,CAAC,MAAM,KAAK,GAAG,MAAM,MAAM,MAAM,CAAC,KAAK;aACvD,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,MAAM;QAC3C,MAAM,MAAM,CAAC,KAAK;IACpB,OAAO;QACL,qBAAqB;QACrB,MAAM,MAAM,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,OAAO;IAC7C;IACA,OAAO;AACT;AACA,SAAS,YAAY,MAAM;IACzB,MAAM,QAAQ,OAAO,cAAc;IACnC,MAAM,eAAe,MAAM,UAAU;IACrC,IAAI,CAAC,MAAM,UAAU,EAAE;QACrB,MAAM,KAAK,GAAG;QACd,QAAQ,QAAQ,CAAC,eAAe,OAAO;IACzC;AACF;AACA,SAAS,cAAc,KAAK,EAAE,MAAM;IAClC,MAAM,iBAAiB,MAAM,UAAU,EAAE,MAAM,MAAM;IAErD,6CAA6C;IAC7C,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK,GAAG;QACpF,MAAM,UAAU,GAAG;QACnB,OAAO,IAAI,CAAC;QACZ,IAAI,OAAO,QAAQ,IAAI,OAAO,aAAa,KAAK,OAAO;YACrD,QAAQ,QAAQ,CAAC,eAAe;QAClC,OAAO,IAAI,MAAM,WAAW,EAAE;YAC5B,oDAAoD;YACpD,yDAAyD;YACzD,MAAM,SAAS,OAAO,cAAc;YACpC,MAAM,cACJ,CAAC,UACA,OAAO,WAAW,IACjB,gDAAgD;YAChD,0CAA0C;YAC1C,CAAC,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,KAAK;YACjD,IAAI,aAAa;gBACf,OAAO,OAAO;YAChB;QACF;IACF;AACF;AACA,SAAS,cAAc,MAAM;IAC3B,MAAM,WAAW,OAAO,QAAQ,IAAI,CAAC,OAAO,aAAa,IAAI,CAAC,OAAO,SAAS;IAC9E,IAAI,UAAU;QACZ,OAAO,GAAG;IACZ;AACF;AACA,SAAS,IAAI,GAAG,SAAU,QAAQ,EAAE,IAAI;IACtC,OAAO,KAAK,UAAU,UAAU;AAClC;AACA,IAAI;AAEJ,oCAAoC;AACpC,SAAS;IACP,IAAI,uBAAuB,WAAW,qBAAqB,CAAC;IAC5D,OAAO;AACT;AACA,SAAS,OAAO,GAAG,SAAU,cAAc,EAAE,OAAO;IAClD,OAAO,iBAAiB,mCAAmC,CAAC,gBAAgB;AAC9E;AACA,SAAS,KAAK,GAAG,SAAU,cAAc,EAAE,OAAO;IAChD,OAAO,iBAAiB,mCAAmC,CAAC,gBAAgB;AAC9E;AACA,SAAS,IAAI,GAAG,SAAU,GAAG,EAAE,OAAO;IACpC,IAAI,MAAM;IACV,OAAO,IAAI,SAAS;QAClB,YACE,CAAC,OACC,CAAC,wBAAwB,IAAI,kBAAkB,MAAM,QAAQ,0BAA0B,YACnF,wBACA,IAAI,UAAU,MAAM,QAAQ,SAAS,YACvC,OACA;QACN,GAAG,OAAO;QACV,SAAQ,GAAG,EAAE,QAAQ;YACnB,YAAY,SAAS,CAAC,KAAK;YAC3B,SAAS;QACX;IACF,GAAG,IAAI,CAAC;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5396, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/duplexify.js"], "sourcesContent": ["/* replacement start */\n\nconst process = require('process/')\n\n/* replacement end */\n\n;('use strict')\nconst bufferModule = require('buffer')\nconst {\n  isReadable,\n  isWritable,\n  isIterable,\n  isNodeStream,\n  isReadableNodeStream,\n  isWritableNodeStream,\n  isDuplexNodeStream,\n  isReadableStream,\n  isWritableStream\n} = require('./utils')\nconst eos = require('./end-of-stream')\nconst {\n  AbortError,\n  codes: { ERR_INVALID_ARG_TYPE, ERR_INVALID_RETURN_VALUE }\n} = require('../../ours/errors')\nconst { destroyer } = require('./destroy')\nconst Duplex = require('./duplex')\nconst Readable = require('./readable')\nconst Writable = require('./writable')\nconst { createDeferredPromise } = require('../../ours/util')\nconst from = require('./from')\nconst Blob = globalThis.Blob || bufferModule.Blob\nconst isBlob =\n  typeof Blob !== 'undefined'\n    ? function isBlob(b) {\n        return b instanceof Blob\n      }\n    : function isBlob(b) {\n        return false\n      }\nconst AbortController = globalThis.AbortController || require('abort-controller').AbortController\nconst { FunctionPrototypeCall } = require('../../ours/primordials')\n\n// This is needed for pre node 17.\nclass Duplexify extends Duplex {\n  constructor(options) {\n    super(options)\n\n    // https://github.com/nodejs/node/pull/34385\n\n    if ((options === null || options === undefined ? undefined : options.readable) === false) {\n      this._readableState.readable = false\n      this._readableState.ended = true\n      this._readableState.endEmitted = true\n    }\n    if ((options === null || options === undefined ? undefined : options.writable) === false) {\n      this._writableState.writable = false\n      this._writableState.ending = true\n      this._writableState.ended = true\n      this._writableState.finished = true\n    }\n  }\n}\nmodule.exports = function duplexify(body, name) {\n  if (isDuplexNodeStream(body)) {\n    return body\n  }\n  if (isReadableNodeStream(body)) {\n    return _duplexify({\n      readable: body\n    })\n  }\n  if (isWritableNodeStream(body)) {\n    return _duplexify({\n      writable: body\n    })\n  }\n  if (isNodeStream(body)) {\n    return _duplexify({\n      writable: false,\n      readable: false\n    })\n  }\n  if (isReadableStream(body)) {\n    return _duplexify({\n      readable: Readable.fromWeb(body)\n    })\n  }\n  if (isWritableStream(body)) {\n    return _duplexify({\n      writable: Writable.fromWeb(body)\n    })\n  }\n  if (typeof body === 'function') {\n    const { value, write, final, destroy } = fromAsyncGen(body)\n    if (isIterable(value)) {\n      return from(Duplexify, value, {\n        // TODO (ronag): highWaterMark?\n        objectMode: true,\n        write,\n        final,\n        destroy\n      })\n    }\n    const then = value === null || value === undefined ? undefined : value.then\n    if (typeof then === 'function') {\n      let d\n      const promise = FunctionPrototypeCall(\n        then,\n        value,\n        (val) => {\n          if (val != null) {\n            throw new ERR_INVALID_RETURN_VALUE('nully', 'body', val)\n          }\n        },\n        (err) => {\n          destroyer(d, err)\n        }\n      )\n      return (d = new Duplexify({\n        // TODO (ronag): highWaterMark?\n        objectMode: true,\n        readable: false,\n        write,\n        final(cb) {\n          final(async () => {\n            try {\n              await promise\n              process.nextTick(cb, null)\n            } catch (err) {\n              process.nextTick(cb, err)\n            }\n          })\n        },\n        destroy\n      }))\n    }\n    throw new ERR_INVALID_RETURN_VALUE('Iterable, AsyncIterable or AsyncFunction', name, value)\n  }\n  if (isBlob(body)) {\n    return duplexify(body.arrayBuffer())\n  }\n  if (isIterable(body)) {\n    return from(Duplexify, body, {\n      // TODO (ronag): highWaterMark?\n      objectMode: true,\n      writable: false\n    })\n  }\n  if (\n    isReadableStream(body === null || body === undefined ? undefined : body.readable) &&\n    isWritableStream(body === null || body === undefined ? undefined : body.writable)\n  ) {\n    return Duplexify.fromWeb(body)\n  }\n  if (\n    typeof (body === null || body === undefined ? undefined : body.writable) === 'object' ||\n    typeof (body === null || body === undefined ? undefined : body.readable) === 'object'\n  ) {\n    const readable =\n      body !== null && body !== undefined && body.readable\n        ? isReadableNodeStream(body === null || body === undefined ? undefined : body.readable)\n          ? body === null || body === undefined\n            ? undefined\n            : body.readable\n          : duplexify(body.readable)\n        : undefined\n    const writable =\n      body !== null && body !== undefined && body.writable\n        ? isWritableNodeStream(body === null || body === undefined ? undefined : body.writable)\n          ? body === null || body === undefined\n            ? undefined\n            : body.writable\n          : duplexify(body.writable)\n        : undefined\n    return _duplexify({\n      readable,\n      writable\n    })\n  }\n  const then = body === null || body === undefined ? undefined : body.then\n  if (typeof then === 'function') {\n    let d\n    FunctionPrototypeCall(\n      then,\n      body,\n      (val) => {\n        if (val != null) {\n          d.push(val)\n        }\n        d.push(null)\n      },\n      (err) => {\n        destroyer(d, err)\n      }\n    )\n    return (d = new Duplexify({\n      objectMode: true,\n      writable: false,\n      read() {}\n    }))\n  }\n  throw new ERR_INVALID_ARG_TYPE(\n    name,\n    [\n      'Blob',\n      'ReadableStream',\n      'WritableStream',\n      'Stream',\n      'Iterable',\n      'AsyncIterable',\n      'Function',\n      '{ readable, writable } pair',\n      'Promise'\n    ],\n    body\n  )\n}\nfunction fromAsyncGen(fn) {\n  let { promise, resolve } = createDeferredPromise()\n  const ac = new AbortController()\n  const signal = ac.signal\n  const value = fn(\n    (async function* () {\n      while (true) {\n        const _promise = promise\n        promise = null\n        const { chunk, done, cb } = await _promise\n        process.nextTick(cb)\n        if (done) return\n        if (signal.aborted)\n          throw new AbortError(undefined, {\n            cause: signal.reason\n          })\n        ;({ promise, resolve } = createDeferredPromise())\n        yield chunk\n      }\n    })(),\n    {\n      signal\n    }\n  )\n  return {\n    value,\n    write(chunk, encoding, cb) {\n      const _resolve = resolve\n      resolve = null\n      _resolve({\n        chunk,\n        done: false,\n        cb\n      })\n    },\n    final(cb) {\n      const _resolve = resolve\n      resolve = null\n      _resolve({\n        done: true,\n        cb\n      })\n    },\n    destroy(err, cb) {\n      ac.abort()\n      cb(err)\n    }\n  }\n}\nfunction _duplexify(pair) {\n  const r = pair.readable && typeof pair.readable.read !== 'function' ? Readable.wrap(pair.readable) : pair.readable\n  const w = pair.writable\n  let readable = !!isReadable(r)\n  let writable = !!isWritable(w)\n  let ondrain\n  let onfinish\n  let onreadable\n  let onclose\n  let d\n  function onfinished(err) {\n    const cb = onclose\n    onclose = null\n    if (cb) {\n      cb(err)\n    } else if (err) {\n      d.destroy(err)\n    }\n  }\n\n  // TODO(ronag): Avoid double buffering.\n  // Implement Writable/Readable/Duplex traits.\n  // See, https://github.com/nodejs/node/pull/33515.\n  d = new Duplexify({\n    // TODO (ronag): highWaterMark?\n    readableObjectMode: !!(r !== null && r !== undefined && r.readableObjectMode),\n    writableObjectMode: !!(w !== null && w !== undefined && w.writableObjectMode),\n    readable,\n    writable\n  })\n  if (writable) {\n    eos(w, (err) => {\n      writable = false\n      if (err) {\n        destroyer(r, err)\n      }\n      onfinished(err)\n    })\n    d._write = function (chunk, encoding, callback) {\n      if (w.write(chunk, encoding)) {\n        callback()\n      } else {\n        ondrain = callback\n      }\n    }\n    d._final = function (callback) {\n      w.end()\n      onfinish = callback\n    }\n    w.on('drain', function () {\n      if (ondrain) {\n        const cb = ondrain\n        ondrain = null\n        cb()\n      }\n    })\n    w.on('finish', function () {\n      if (onfinish) {\n        const cb = onfinish\n        onfinish = null\n        cb()\n      }\n    })\n  }\n  if (readable) {\n    eos(r, (err) => {\n      readable = false\n      if (err) {\n        destroyer(r, err)\n      }\n      onfinished(err)\n    })\n    r.on('readable', function () {\n      if (onreadable) {\n        const cb = onreadable\n        onreadable = null\n        cb()\n      }\n    })\n    r.on('end', function () {\n      d.push(null)\n    })\n    d._read = function () {\n      while (true) {\n        const buf = r.read()\n        if (buf === null) {\n          onreadable = d._read\n          return\n        }\n        if (!d.push(buf)) {\n          return\n        }\n      }\n    }\n  }\n  d._destroy = function (err, callback) {\n    if (!err && onclose !== null) {\n      err = new AbortError()\n    }\n    onreadable = null\n    ondrain = null\n    onfinish = null\n    if (onclose === null) {\n      callback(err)\n    } else {\n      onclose = callback\n      destroyer(w, err)\n      destroyer(r, err)\n    }\n  }\n  return d\n}\n"], "names": [], "mappings": "AAAA,qBAAqB,GAErB,MAAM;;;;;;;;;GAAkB;AAItB;AACF,MAAM;AACN,MAAM,EACJ,UAAU,EACV,UAAU,EACV,UAAU,EACV,YAAY,EACZ,oBAAoB,EACpB,oBAAoB,EACpB,kBAAkB,EAClB,gBAAgB,EAChB,gBAAgB,EACjB;AACD,MAAM;AACN,MAAM,EACJ,UAAU,EACV,OAAO,EAAE,oBAAoB,EAAE,wBAAwB,EAAE,EAC1D;AACD,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,qBAAqB,EAAE;AAC/B,MAAM;AACN,MAAM,OAAO,WAAW,IAAI,IAAI,aAAa,IAAI;AACjD,MAAM,SACJ,OAAO,SAAS,cACZ,SAAS,OAAO,CAAC;IACf,OAAO,aAAa;AACtB,IACA,SAAS,OAAO,CAAC;IACf,OAAO;AACT;AACN,MAAM,kBAAkB,WAAW,eAAe,IAAI,qHAA4B,eAAe;AACjG,MAAM,EAAE,qBAAqB,EAAE;AAE/B,kCAAkC;AAClC,MAAM,kBAAkB;IACtB,YAAY,OAAO,CAAE;QACnB,KAAK,CAAC;QAEN,4CAA4C;QAE5C,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,QAAQ,MAAM,OAAO;YACxF,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;YAC/B,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;YAC5B,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QACnC;QACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,QAAQ,MAAM,OAAO;YACxF,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;YAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;YAC7B,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;YAC5B,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;QACjC;IACF;AACF;AACA,OAAO,OAAO,GAAG,SAAS,UAAU,IAAI,EAAE,IAAI;IAC5C,IAAI,mBAAmB,OAAO;QAC5B,OAAO;IACT;IACA,IAAI,qBAAqB,OAAO;QAC9B,OAAO,WAAW;YAChB,UAAU;QACZ;IACF;IACA,IAAI,qBAAqB,OAAO;QAC9B,OAAO,WAAW;YAChB,UAAU;QACZ;IACF;IACA,IAAI,aAAa,OAAO;QACtB,OAAO,WAAW;YAChB,UAAU;YACV,UAAU;QACZ;IACF;IACA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,WAAW;YAChB,UAAU,SAAS,OAAO,CAAC;QAC7B;IACF;IACA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,WAAW;YAChB,UAAU,SAAS,OAAO,CAAC;QAC7B;IACF;IACA,IAAI,OAAO,SAAS,YAAY;QAC9B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,aAAa;QACtD,IAAI,WAAW,QAAQ;YACrB,OAAO,KAAK,WAAW,OAAO;gBAC5B,+BAA+B;gBAC/B,YAAY;gBACZ;gBACA;gBACA;YACF;QACF;QACA,MAAM,OAAO,UAAU,QAAQ,UAAU,YAAY,YAAY,MAAM,IAAI;QAC3E,IAAI,OAAO,SAAS,YAAY;YAC9B,IAAI;YACJ,MAAM,UAAU,sBACd,MACA,OACA,CAAC;gBACC,IAAI,OAAO,MAAM;oBACf,MAAM,IAAI,yBAAyB,SAAS,QAAQ;gBACtD;YACF,GACA,CAAC;gBACC,UAAU,GAAG;YACf;YAEF,OAAQ,IAAI,IAAI,UAAU;gBACxB,+BAA+B;gBAC/B,YAAY;gBACZ,UAAU;gBACV;gBACA,OAAM,EAAE;oBACN,MAAM;wBACJ,IAAI;4BACF,MAAM;4BACN,QAAQ,QAAQ,CAAC,IAAI;wBACvB,EAAE,OAAO,KAAK;4BACZ,QAAQ,QAAQ,CAAC,IAAI;wBACvB;oBACF;gBACF;gBACA;YACF;QACF;QACA,MAAM,IAAI,yBAAyB,4CAA4C,MAAM;IACvF;IACA,IAAI,OAAO,OAAO;QAChB,OAAO,UAAU,KAAK,WAAW;IACnC;IACA,IAAI,WAAW,OAAO;QACpB,OAAO,KAAK,WAAW,MAAM;YAC3B,+BAA+B;YAC/B,YAAY;YACZ,UAAU;QACZ;IACF;IACA,IACE,iBAAiB,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,KAChF,iBAAiB,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,GAChF;QACA,OAAO,UAAU,OAAO,CAAC;IAC3B;IACA,IACE,OAAO,CAAC,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,MAAM,YAC7E,OAAO,CAAC,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,MAAM,UAC7E;QACA,MAAM,WACJ,SAAS,QAAQ,SAAS,aAAa,KAAK,QAAQ,GAChD,qBAAqB,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,IAClF,SAAS,QAAQ,SAAS,YACxB,YACA,KAAK,QAAQ,GACf,UAAU,KAAK,QAAQ,IACzB;QACN,MAAM,WACJ,SAAS,QAAQ,SAAS,aAAa,KAAK,QAAQ,GAChD,qBAAqB,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ,IAClF,SAAS,QAAQ,SAAS,YACxB,YACA,KAAK,QAAQ,GACf,UAAU,KAAK,QAAQ,IACzB;QACN,OAAO,WAAW;YAChB;YACA;QACF;IACF;IACA,MAAM,OAAO,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,IAAI;IACxE,IAAI,OAAO,SAAS,YAAY;QAC9B,IAAI;QACJ,sBACE,MACA,MACA,CAAC;YACC,IAAI,OAAO,MAAM;gBACf,EAAE,IAAI,CAAC;YACT;YACA,EAAE,IAAI,CAAC;QACT,GACA,CAAC;YACC,UAAU,GAAG;QACf;QAEF,OAAQ,IAAI,IAAI,UAAU;YACxB,YAAY;YACZ,UAAU;YACV,SAAQ;QACV;IACF;IACA,MAAM,IAAI,qBACR,MACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,EACD;AAEJ;AACA,SAAS,aAAa,EAAE;IACtB,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAC3B,MAAM,KAAK,IAAI;IACf,MAAM,SAAS,GAAG,MAAM;IACxB,MAAM,QAAQ,GACZ,AAAC;QACC,MAAO,KAAM;YACX,MAAM,WAAW;YACjB,UAAU;YACV,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,MAAM;YAClC,QAAQ,QAAQ,CAAC;YACjB,IAAI,MAAM;YACV,IAAI,OAAO,OAAO,EAChB,MAAM,IAAI,WAAW,WAAW;gBAC9B,OAAO,OAAO,MAAM;YACtB;YACD,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,uBAAuB;YAChD,MAAM;QACR;IACF,KACA;QACE;IACF;IAEF,OAAO;QACL;QACA,OAAM,KAAK,EAAE,QAAQ,EAAE,EAAE;YACvB,MAAM,WAAW;YACjB,UAAU;YACV,SAAS;gBACP;gBACA,MAAM;gBACN;YACF;QACF;QACA,OAAM,EAAE;YACN,MAAM,WAAW;YACjB,UAAU;YACV,SAAS;gBACP,MAAM;gBACN;YACF;QACF;QACA,SAAQ,GAAG,EAAE,EAAE;YACb,GAAG,KAAK;YACR,GAAG;QACL;IACF;AACF;AACA,SAAS,WAAW,IAAI;IACtB,MAAM,IAAI,KAAK,QAAQ,IAAI,OAAO,KAAK,QAAQ,CAAC,IAAI,KAAK,aAAa,SAAS,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ;IAClH,MAAM,IAAI,KAAK,QAAQ;IACvB,IAAI,WAAW,CAAC,CAAC,WAAW;IAC5B,IAAI,WAAW,CAAC,CAAC,WAAW;IAC5B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS,WAAW,GAAG;QACrB,MAAM,KAAK;QACX,UAAU;QACV,IAAI,IAAI;YACN,GAAG;QACL,OAAO,IAAI,KAAK;YACd,EAAE,OAAO,CAAC;QACZ;IACF;IAEA,uCAAuC;IACvC,6CAA6C;IAC7C,kDAAkD;IAClD,IAAI,IAAI,UAAU;QAChB,+BAA+B;QAC/B,oBAAoB,CAAC,CAAC,CAAC,MAAM,QAAQ,MAAM,aAAa,EAAE,kBAAkB;QAC5E,oBAAoB,CAAC,CAAC,CAAC,MAAM,QAAQ,MAAM,aAAa,EAAE,kBAAkB;QAC5E;QACA;IACF;IACA,IAAI,UAAU;QACZ,IAAI,GAAG,CAAC;YACN,WAAW;YACX,IAAI,KAAK;gBACP,UAAU,GAAG;YACf;YACA,WAAW;QACb;QACA,EAAE,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ;YAC5C,IAAI,EAAE,KAAK,CAAC,OAAO,WAAW;gBAC5B;YACF,OAAO;gBACL,UAAU;YACZ;QACF;QACA,EAAE,MAAM,GAAG,SAAU,QAAQ;YAC3B,EAAE,GAAG;YACL,WAAW;QACb;QACA,EAAE,EAAE,CAAC,SAAS;YACZ,IAAI,SAAS;gBACX,MAAM,KAAK;gBACX,UAAU;gBACV;YACF;QACF;QACA,EAAE,EAAE,CAAC,UAAU;YACb,IAAI,UAAU;gBACZ,MAAM,KAAK;gBACX,WAAW;gBACX;YACF;QACF;IACF;IACA,IAAI,UAAU;QACZ,IAAI,GAAG,CAAC;YACN,WAAW;YACX,IAAI,KAAK;gBACP,UAAU,GAAG;YACf;YACA,WAAW;QACb;QACA,EAAE,EAAE,CAAC,YAAY;YACf,IAAI,YAAY;gBACd,MAAM,KAAK;gBACX,aAAa;gBACb;YACF;QACF;QACA,EAAE,EAAE,CAAC,OAAO;YACV,EAAE,IAAI,CAAC;QACT;QACA,EAAE,KAAK,GAAG;YACR,MAAO,KAAM;gBACX,MAAM,MAAM,EAAE,IAAI;gBAClB,IAAI,QAAQ,MAAM;oBAChB,aAAa,EAAE,KAAK;oBACpB;gBACF;gBACA,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM;oBAChB;gBACF;YACF;QACF;IACF;IACA,EAAE,QAAQ,GAAG,SAAU,GAAG,EAAE,QAAQ;QAClC,IAAI,CAAC,OAAO,YAAY,MAAM;YAC5B,MAAM,IAAI;QACZ;QACA,aAAa;QACb,UAAU;QACV,WAAW;QACX,IAAI,YAAY,MAAM;YACpB,SAAS;QACX,OAAO;YACL,UAAU;YACV,UAAU,GAAG;YACb,UAAU,GAAG;QACf;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5724, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/duplex.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototype inheritance, this class\n// prototypically inherits from Readable, and then parasitically from\n// Writable.\n\n'use strict'\n\nconst {\n  ObjectDefineProperties,\n  ObjectGetOwnPropertyDescriptor,\n  ObjectKeys,\n  ObjectSetPrototypeOf\n} = require('../../ours/primordials')\nmodule.exports = Duplex\nconst Readable = require('./readable')\nconst Writable = require('./writable')\nObjectSetPrototypeOf(Duplex.prototype, Readable.prototype)\nObjectSetPrototypeOf(Duplex, Readable)\n{\n  const keys = ObjectKeys(Writable.prototype)\n  // Allow the keys array to be GC'ed.\n  for (let i = 0; i < keys.length; i++) {\n    const method = keys[i]\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method]\n  }\n}\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options)\n  Readable.call(this, options)\n  Writable.call(this, options)\n  if (options) {\n    this.allowHalfOpen = options.allowHalfOpen !== false\n    if (options.readable === false) {\n      this._readableState.readable = false\n      this._readableState.ended = true\n      this._readableState.endEmitted = true\n    }\n    if (options.writable === false) {\n      this._writableState.writable = false\n      this._writableState.ending = true\n      this._writableState.ended = true\n      this._writableState.finished = true\n    }\n  } else {\n    this.allowHalfOpen = true\n  }\n}\nObjectDefineProperties(Duplex.prototype, {\n  writable: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writable')\n  },\n  writableHighWaterMark: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableHighWaterMark')\n  },\n  writableObjectMode: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableObjectMode')\n  },\n  writableBuffer: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableBuffer')\n  },\n  writableLength: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableLength')\n  },\n  writableFinished: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableFinished')\n  },\n  writableCorked: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableCorked')\n  },\n  writableEnded: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableEnded')\n  },\n  writableNeedDrain: {\n    __proto__: null,\n    ...ObjectGetOwnPropertyDescriptor(Writable.prototype, 'writableNeedDrain')\n  },\n  destroyed: {\n    __proto__: null,\n    get() {\n      if (this._readableState === undefined || this._writableState === undefined) {\n        return false\n      }\n      return this._readableState.destroyed && this._writableState.destroyed\n    },\n    set(value) {\n      // Backward compatibility, the user is explicitly\n      // managing destroyed.\n      if (this._readableState && this._writableState) {\n        this._readableState.destroyed = value\n        this._writableState.destroyed = value\n      }\n    }\n  }\n})\nlet webStreamsAdapters\n\n// Lazy to avoid circular references\nfunction lazyWebStreams() {\n  if (webStreamsAdapters === undefined) webStreamsAdapters = {}\n  return webStreamsAdapters\n}\nDuplex.fromWeb = function (pair, options) {\n  return lazyWebStreams().newStreamDuplexFromReadableWritablePair(pair, options)\n}\nDuplex.toWeb = function (duplex) {\n  return lazyWebStreams().newReadableWritablePairFromDuplex(duplex)\n}\nlet duplexify\nDuplex.from = function (body) {\n  if (!duplexify) {\n    duplexify = require('./duplexify')\n  }\n  return duplexify(body, 'body')\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,uEAAuE;AACvE,mEAAmE;AACnE,qEAAqE;AACrE,YAAY;AAIZ,MAAM,EACJ,sBAAsB,EACtB,8BAA8B,EAC9B,UAAU,EACV,oBAAoB,EACrB;AACD,OAAO,OAAO,GAAG;AACjB,MAAM;AACN,MAAM;AACN,qBAAqB,OAAO,SAAS,EAAE,SAAS,SAAS;AACzD,qBAAqB,QAAQ;AAC7B;IACE,MAAM,OAAO,WAAW,SAAS,SAAS;IAC1C,oCAAoC;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,SAAS,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC,OAAO,SAAS,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,SAAS,CAAC,OAAO;IACtF;AACF,CACA,SAAS,OAAO,OAAO;IACrB,IAAI,CAAC,CAAC,IAAI,YAAY,MAAM,GAAG,OAAO,IAAI,OAAO;IACjD,SAAS,IAAI,CAAC,IAAI,EAAE;IACpB,SAAS,IAAI,CAAC,IAAI,EAAE;IACpB,IAAI,SAAS;QACX,IAAI,CAAC,aAAa,GAAG,QAAQ,aAAa,KAAK;QAC/C,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC9B,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;YAC/B,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;YAC5B,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QACnC;QACA,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC9B,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;YAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;YAC7B,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;YAC5B,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;QACjC;IACF,OAAO;QACL,IAAI,CAAC,aAAa,GAAG;IACvB;AACF;AACA,uBAAuB,OAAO,SAAS,EAAE;IACvC,UAAU;QACR,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,WAAW;IACnE;IACA,uBAAuB;QACrB,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,wBAAwB;IAChF;IACA,oBAAoB;QAClB,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,qBAAqB;IAC7E;IACA,gBAAgB;QACd,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,iBAAiB;IACzE;IACA,gBAAgB;QACd,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,iBAAiB;IACzE;IACA,kBAAkB;QAChB,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,mBAAmB;IAC3E;IACA,gBAAgB;QACd,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,iBAAiB;IACzE;IACA,eAAe;QACb,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,gBAAgB;IACxE;IACA,mBAAmB;QACjB,WAAW;QACX,GAAG,+BAA+B,SAAS,SAAS,EAAE,oBAAoB;IAC5E;IACA,WAAW;QACT,WAAW;QACX;YACE,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,IAAI,CAAC,cAAc,KAAK,WAAW;gBAC1E,OAAO;YACT;YACA,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;QACvE;QACA,KAAI,KAAK;YACP,iDAAiD;YACjD,sBAAsB;YACtB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC9C,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;gBAChC,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;YAClC;QACF;IACF;AACF;AACA,IAAI;AAEJ,oCAAoC;AACpC,SAAS;IACP,IAAI,uBAAuB,WAAW,qBAAqB,CAAC;IAC5D,OAAO;AACT;AACA,OAAO,OAAO,GAAG,SAAU,IAAI,EAAE,OAAO;IACtC,OAAO,iBAAiB,uCAAuC,CAAC,MAAM;AACxE;AACA,OAAO,KAAK,GAAG,SAAU,MAAM;IAC7B,OAAO,iBAAiB,iCAAiC,CAAC;AAC5D;AACA,IAAI;AACJ,OAAO,IAAI,GAAG,SAAU,IAAI;IAC1B,IAAI,CAAC,WAAW;QACd;IACF;IACA,OAAO,UAAU,MAAM;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5860, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/writable.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n\n'use strict'\n\n/* replacement start */\n\nconst process = require('process/')\n\n/* replacement end */\n\nconst {\n  ArrayPrototypeSlice,\n  Error,\n  FunctionPrototypeSymbolHasInstance,\n  ObjectDefineProperty,\n  ObjectDefineProperties,\n  ObjectSetPrototypeOf,\n  StringPrototypeToLowerCase,\n  Symbol,\n  SymbolHasInstance\n} = require('../../ours/primordials')\nmodule.exports = Writable\nWritable.WritableState = WritableState\nconst { EventEmitter: EE } = require('events')\nconst Stream = require('./legacy').Stream\nconst { Buffer } = require('buffer')\nconst destroyImpl = require('./destroy')\nconst { addAbortSignal } = require('./add-abort-signal')\nconst { getHighWaterMark, getDefaultHighWaterMark } = require('./state')\nconst {\n  ERR_INVALID_ARG_TYPE,\n  ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_MULTIPLE_CALLBACK,\n  ERR_STREAM_CANNOT_PIPE,\n  ERR_STREAM_DESTROYED,\n  ERR_STREAM_ALREADY_FINISHED,\n  ERR_STREAM_NULL_VALUES,\n  ERR_STREAM_WRITE_AFTER_END,\n  ERR_UNKNOWN_ENCODING\n} = require('../../ours/errors').codes\nconst { errorOrDestroy } = destroyImpl\nObjectSetPrototypeOf(Writable.prototype, Stream.prototype)\nObjectSetPrototypeOf(Writable, Stream)\nfunction nop() {}\nconst kOnFinished = Symbol('kOnFinished')\nfunction WritableState(options, stream, isDuplex) {\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream,\n  // e.g. options.readableObjectMode vs. options.writableObjectMode, etc.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof require('./duplex')\n\n  // Object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n  this.objectMode = !!(options && options.objectMode)\n  if (isDuplex) this.objectMode = this.objectMode || !!(options && options.writableObjectMode)\n\n  // The point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write().\n  this.highWaterMark = options\n    ? getHighWaterMark(this, options, 'writableHighWaterMark', isDuplex)\n    : getDefaultHighWaterMark(false)\n\n  // if _final has been called.\n  this.finalCalled = false\n\n  // drain event flag.\n  this.needDrain = false\n  // At the start of calling end()\n  this.ending = false\n  // When end() has been called, and returned.\n  this.ended = false\n  // When 'finish' is emitted.\n  this.finished = false\n\n  // Has it been destroyed\n  this.destroyed = false\n\n  // Should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n  const noDecode = !!(options && options.decodeStrings === false)\n  this.decodeStrings = !noDecode\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = (options && options.defaultEncoding) || 'utf8'\n\n  // Not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n  this.length = 0\n\n  // A flag to see when we're in the middle of a write.\n  this.writing = false\n\n  // When true all writes will be buffered until .uncork() call.\n  this.corked = 0\n\n  // A flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n  this.sync = true\n\n  // A flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n  this.bufferProcessing = false\n\n  // The callback that's passed to _write(chunk, cb).\n  this.onwrite = onwrite.bind(undefined, stream)\n\n  // The callback that the user supplies to write(chunk, encoding, cb).\n  this.writecb = null\n\n  // The amount that is being written when _write is called.\n  this.writelen = 0\n\n  // Storage for data passed to the afterWrite() callback in case of\n  // synchronous _write() completion.\n  this.afterWriteTickInfo = null\n  resetBuffer(this)\n\n  // Number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted.\n  this.pendingcb = 0\n\n  // Stream is still being constructed and cannot be\n  // destroyed until construction finished or failed.\n  // Async construction is opt in, therefore we start as\n  // constructed.\n  this.constructed = true\n\n  // Emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams.\n  this.prefinished = false\n\n  // True if the error was already emitted and should not be thrown again.\n  this.errorEmitted = false\n\n  // Should close be emitted on destroy. Defaults to true.\n  this.emitClose = !options || options.emitClose !== false\n\n  // Should .destroy() be called after 'finish' (and potentially 'end').\n  this.autoDestroy = !options || options.autoDestroy !== false\n\n  // Indicates whether the stream has errored. When true all write() calls\n  // should return false. This is needed since when autoDestroy\n  // is disabled we need a way to tell whether the stream has failed.\n  this.errored = null\n\n  // Indicates whether the stream has finished destroying.\n  this.closed = false\n\n  // True if close has been emitted or would have been emitted\n  // depending on emitClose.\n  this.closeEmitted = false\n  this[kOnFinished] = []\n}\nfunction resetBuffer(state) {\n  state.buffered = []\n  state.bufferedIndex = 0\n  state.allBuffers = true\n  state.allNoop = true\n}\nWritableState.prototype.getBuffer = function getBuffer() {\n  return ArrayPrototypeSlice(this.buffered, this.bufferedIndex)\n}\nObjectDefineProperty(WritableState.prototype, 'bufferedRequestCount', {\n  __proto__: null,\n  get() {\n    return this.buffered.length - this.bufferedIndex\n  }\n})\nfunction Writable(options) {\n  // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the WritableState constructor, at least with V8 6.5.\n  const isDuplex = this instanceof require('./duplex')\n  if (!isDuplex && !FunctionPrototypeSymbolHasInstance(Writable, this)) return new Writable(options)\n  this._writableState = new WritableState(options, this, isDuplex)\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write\n    if (typeof options.writev === 'function') this._writev = options.writev\n    if (typeof options.destroy === 'function') this._destroy = options.destroy\n    if (typeof options.final === 'function') this._final = options.final\n    if (typeof options.construct === 'function') this._construct = options.construct\n    if (options.signal) addAbortSignal(options.signal, this)\n  }\n  Stream.call(this, options)\n  destroyImpl.construct(this, () => {\n    const state = this._writableState\n    if (!state.writing) {\n      clearBuffer(this, state)\n    }\n    finishMaybe(this, state)\n  })\n}\nObjectDefineProperty(Writable, SymbolHasInstance, {\n  __proto__: null,\n  value: function (object) {\n    if (FunctionPrototypeSymbolHasInstance(this, object)) return true\n    if (this !== Writable) return false\n    return object && object._writableState instanceof WritableState\n  }\n})\n\n// Otherwise people can pipe Writable streams, which is just wrong.\nWritable.prototype.pipe = function () {\n  errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE())\n}\nfunction _write(stream, chunk, encoding, cb) {\n  const state = stream._writableState\n  if (typeof encoding === 'function') {\n    cb = encoding\n    encoding = state.defaultEncoding\n  } else {\n    if (!encoding) encoding = state.defaultEncoding\n    else if (encoding !== 'buffer' && !Buffer.isEncoding(encoding)) throw new ERR_UNKNOWN_ENCODING(encoding)\n    if (typeof cb !== 'function') cb = nop\n  }\n  if (chunk === null) {\n    throw new ERR_STREAM_NULL_VALUES()\n  } else if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      if (state.decodeStrings !== false) {\n        chunk = Buffer.from(chunk, encoding)\n        encoding = 'buffer'\n      }\n    } else if (chunk instanceof Buffer) {\n      encoding = 'buffer'\n    } else if (Stream._isUint8Array(chunk)) {\n      chunk = Stream._uint8ArrayToBuffer(chunk)\n      encoding = 'buffer'\n    } else {\n      throw new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk)\n    }\n  }\n  let err\n  if (state.ending) {\n    err = new ERR_STREAM_WRITE_AFTER_END()\n  } else if (state.destroyed) {\n    err = new ERR_STREAM_DESTROYED('write')\n  }\n  if (err) {\n    process.nextTick(cb, err)\n    errorOrDestroy(stream, err, true)\n    return err\n  }\n  state.pendingcb++\n  return writeOrBuffer(stream, state, chunk, encoding, cb)\n}\nWritable.prototype.write = function (chunk, encoding, cb) {\n  return _write(this, chunk, encoding, cb) === true\n}\nWritable.prototype.cork = function () {\n  this._writableState.corked++\n}\nWritable.prototype.uncork = function () {\n  const state = this._writableState\n  if (state.corked) {\n    state.corked--\n    if (!state.writing) clearBuffer(this, state)\n  }\n}\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = StringPrototypeToLowerCase(encoding)\n  if (!Buffer.isEncoding(encoding)) throw new ERR_UNKNOWN_ENCODING(encoding)\n  this._writableState.defaultEncoding = encoding\n  return this\n}\n\n// If we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\nfunction writeOrBuffer(stream, state, chunk, encoding, callback) {\n  const len = state.objectMode ? 1 : chunk.length\n  state.length += len\n\n  // stream._write resets state.length\n  const ret = state.length < state.highWaterMark\n  // We must ensure that previous needDrain will not be reset to false.\n  if (!ret) state.needDrain = true\n  if (state.writing || state.corked || state.errored || !state.constructed) {\n    state.buffered.push({\n      chunk,\n      encoding,\n      callback\n    })\n    if (state.allBuffers && encoding !== 'buffer') {\n      state.allBuffers = false\n    }\n    if (state.allNoop && callback !== nop) {\n      state.allNoop = false\n    }\n  } else {\n    state.writelen = len\n    state.writecb = callback\n    state.writing = true\n    state.sync = true\n    stream._write(chunk, encoding, state.onwrite)\n    state.sync = false\n  }\n\n  // Return false if errored or destroyed in order to break\n  // any synchronous while(stream.write(data)) loops.\n  return ret && !state.errored && !state.destroyed\n}\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len\n  state.writecb = cb\n  state.writing = true\n  state.sync = true\n  if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'))\n  else if (writev) stream._writev(chunk, state.onwrite)\n  else stream._write(chunk, encoding, state.onwrite)\n  state.sync = false\n}\nfunction onwriteError(stream, state, er, cb) {\n  --state.pendingcb\n  cb(er)\n  // Ensure callbacks are invoked even when autoDestroy is\n  // not enabled. Passing `er` here doesn't make sense since\n  // it's related to one specific write, not to the buffered\n  // writes.\n  errorBuffer(state)\n  // This can emit error, but error must always follow cb.\n  errorOrDestroy(stream, er)\n}\nfunction onwrite(stream, er) {\n  const state = stream._writableState\n  const sync = state.sync\n  const cb = state.writecb\n  if (typeof cb !== 'function') {\n    errorOrDestroy(stream, new ERR_MULTIPLE_CALLBACK())\n    return\n  }\n  state.writing = false\n  state.writecb = null\n  state.length -= state.writelen\n  state.writelen = 0\n  if (er) {\n    // Avoid V8 leak, https://github.com/nodejs/node/pull/34103#issuecomment-652002364\n    er.stack // eslint-disable-line no-unused-expressions\n\n    if (!state.errored) {\n      state.errored = er\n    }\n\n    // In case of duplex streams we need to notify the readable side of the\n    // error.\n    if (stream._readableState && !stream._readableState.errored) {\n      stream._readableState.errored = er\n    }\n    if (sync) {\n      process.nextTick(onwriteError, stream, state, er, cb)\n    } else {\n      onwriteError(stream, state, er, cb)\n    }\n  } else {\n    if (state.buffered.length > state.bufferedIndex) {\n      clearBuffer(stream, state)\n    }\n    if (sync) {\n      // It is a common case that the callback passed to .write() is always\n      // the same. In that case, we do not schedule a new nextTick(), but\n      // rather just increase a counter, to improve performance and avoid\n      // memory allocations.\n      if (state.afterWriteTickInfo !== null && state.afterWriteTickInfo.cb === cb) {\n        state.afterWriteTickInfo.count++\n      } else {\n        state.afterWriteTickInfo = {\n          count: 1,\n          cb,\n          stream,\n          state\n        }\n        process.nextTick(afterWriteTick, state.afterWriteTickInfo)\n      }\n    } else {\n      afterWrite(stream, state, 1, cb)\n    }\n  }\n}\nfunction afterWriteTick({ stream, state, count, cb }) {\n  state.afterWriteTickInfo = null\n  return afterWrite(stream, state, count, cb)\n}\nfunction afterWrite(stream, state, count, cb) {\n  const needDrain = !state.ending && !stream.destroyed && state.length === 0 && state.needDrain\n  if (needDrain) {\n    state.needDrain = false\n    stream.emit('drain')\n  }\n  while (count-- > 0) {\n    state.pendingcb--\n    cb()\n  }\n  if (state.destroyed) {\n    errorBuffer(state)\n  }\n  finishMaybe(stream, state)\n}\n\n// If there's something in the buffer waiting, then invoke callbacks.\nfunction errorBuffer(state) {\n  if (state.writing) {\n    return\n  }\n  for (let n = state.bufferedIndex; n < state.buffered.length; ++n) {\n    var _state$errored\n    const { chunk, callback } = state.buffered[n]\n    const len = state.objectMode ? 1 : chunk.length\n    state.length -= len\n    callback(\n      (_state$errored = state.errored) !== null && _state$errored !== undefined\n        ? _state$errored\n        : new ERR_STREAM_DESTROYED('write')\n    )\n  }\n  const onfinishCallbacks = state[kOnFinished].splice(0)\n  for (let i = 0; i < onfinishCallbacks.length; i++) {\n    var _state$errored2\n    onfinishCallbacks[i](\n      (_state$errored2 = state.errored) !== null && _state$errored2 !== undefined\n        ? _state$errored2\n        : new ERR_STREAM_DESTROYED('end')\n    )\n  }\n  resetBuffer(state)\n}\n\n// If there's something in the buffer waiting, then process it.\nfunction clearBuffer(stream, state) {\n  if (state.corked || state.bufferProcessing || state.destroyed || !state.constructed) {\n    return\n  }\n  const { buffered, bufferedIndex, objectMode } = state\n  const bufferedLength = buffered.length - bufferedIndex\n  if (!bufferedLength) {\n    return\n  }\n  let i = bufferedIndex\n  state.bufferProcessing = true\n  if (bufferedLength > 1 && stream._writev) {\n    state.pendingcb -= bufferedLength - 1\n    const callback = state.allNoop\n      ? nop\n      : (err) => {\n          for (let n = i; n < buffered.length; ++n) {\n            buffered[n].callback(err)\n          }\n        }\n    // Make a copy of `buffered` if it's going to be used by `callback` above,\n    // since `doWrite` will mutate the array.\n    const chunks = state.allNoop && i === 0 ? buffered : ArrayPrototypeSlice(buffered, i)\n    chunks.allBuffers = state.allBuffers\n    doWrite(stream, state, true, state.length, chunks, '', callback)\n    resetBuffer(state)\n  } else {\n    do {\n      const { chunk, encoding, callback } = buffered[i]\n      buffered[i++] = null\n      const len = objectMode ? 1 : chunk.length\n      doWrite(stream, state, false, len, chunk, encoding, callback)\n    } while (i < buffered.length && !state.writing)\n    if (i === buffered.length) {\n      resetBuffer(state)\n    } else if (i > 256) {\n      buffered.splice(0, i)\n      state.bufferedIndex = 0\n    } else {\n      state.bufferedIndex = i\n    }\n  }\n  state.bufferProcessing = false\n}\nWritable.prototype._write = function (chunk, encoding, cb) {\n  if (this._writev) {\n    this._writev(\n      [\n        {\n          chunk,\n          encoding\n        }\n      ],\n      cb\n    )\n  } else {\n    throw new ERR_METHOD_NOT_IMPLEMENTED('_write()')\n  }\n}\nWritable.prototype._writev = null\nWritable.prototype.end = function (chunk, encoding, cb) {\n  const state = this._writableState\n  if (typeof chunk === 'function') {\n    cb = chunk\n    chunk = null\n    encoding = null\n  } else if (typeof encoding === 'function') {\n    cb = encoding\n    encoding = null\n  }\n  let err\n  if (chunk !== null && chunk !== undefined) {\n    const ret = _write(this, chunk, encoding)\n    if (ret instanceof Error) {\n      err = ret\n    }\n  }\n\n  // .end() fully uncorks.\n  if (state.corked) {\n    state.corked = 1\n    this.uncork()\n  }\n  if (err) {\n    // Do nothing...\n  } else if (!state.errored && !state.ending) {\n    // This is forgiving in terms of unnecessary calls to end() and can hide\n    // logic errors. However, usually such errors are harmless and causing a\n    // hard error can be disproportionately destructive. It is not always\n    // trivial for the user to determine whether end() needs to be called\n    // or not.\n\n    state.ending = true\n    finishMaybe(this, state, true)\n    state.ended = true\n  } else if (state.finished) {\n    err = new ERR_STREAM_ALREADY_FINISHED('end')\n  } else if (state.destroyed) {\n    err = new ERR_STREAM_DESTROYED('end')\n  }\n  if (typeof cb === 'function') {\n    if (err || state.finished) {\n      process.nextTick(cb, err)\n    } else {\n      state[kOnFinished].push(cb)\n    }\n  }\n  return this\n}\nfunction needFinish(state) {\n  return (\n    state.ending &&\n    !state.destroyed &&\n    state.constructed &&\n    state.length === 0 &&\n    !state.errored &&\n    state.buffered.length === 0 &&\n    !state.finished &&\n    !state.writing &&\n    !state.errorEmitted &&\n    !state.closeEmitted\n  )\n}\nfunction callFinal(stream, state) {\n  let called = false\n  function onFinish(err) {\n    if (called) {\n      errorOrDestroy(stream, err !== null && err !== undefined ? err : ERR_MULTIPLE_CALLBACK())\n      return\n    }\n    called = true\n    state.pendingcb--\n    if (err) {\n      const onfinishCallbacks = state[kOnFinished].splice(0)\n      for (let i = 0; i < onfinishCallbacks.length; i++) {\n        onfinishCallbacks[i](err)\n      }\n      errorOrDestroy(stream, err, state.sync)\n    } else if (needFinish(state)) {\n      state.prefinished = true\n      stream.emit('prefinish')\n      // Backwards compat. Don't check state.sync here.\n      // Some streams assume 'finish' will be emitted\n      // asynchronously relative to _final callback.\n      state.pendingcb++\n      process.nextTick(finish, stream, state)\n    }\n  }\n  state.sync = true\n  state.pendingcb++\n  try {\n    stream._final(onFinish)\n  } catch (err) {\n    onFinish(err)\n  }\n  state.sync = false\n}\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function' && !state.destroyed) {\n      state.finalCalled = true\n      callFinal(stream, state)\n    } else {\n      state.prefinished = true\n      stream.emit('prefinish')\n    }\n  }\n}\nfunction finishMaybe(stream, state, sync) {\n  if (needFinish(state)) {\n    prefinish(stream, state)\n    if (state.pendingcb === 0) {\n      if (sync) {\n        state.pendingcb++\n        process.nextTick(\n          (stream, state) => {\n            if (needFinish(state)) {\n              finish(stream, state)\n            } else {\n              state.pendingcb--\n            }\n          },\n          stream,\n          state\n        )\n      } else if (needFinish(state)) {\n        state.pendingcb++\n        finish(stream, state)\n      }\n    }\n  }\n}\nfunction finish(stream, state) {\n  state.pendingcb--\n  state.finished = true\n  const onfinishCallbacks = state[kOnFinished].splice(0)\n  for (let i = 0; i < onfinishCallbacks.length; i++) {\n    onfinishCallbacks[i]()\n  }\n  stream.emit('finish')\n  if (state.autoDestroy) {\n    // In case of duplex streams we need a way to detect\n    // if the readable side is ready for autoDestroy as well.\n    const rState = stream._readableState\n    const autoDestroy =\n      !rState ||\n      (rState.autoDestroy &&\n        // We don't expect the readable to ever 'end'\n        // if readable is explicitly set to false.\n        (rState.endEmitted || rState.readable === false))\n    if (autoDestroy) {\n      stream.destroy()\n    }\n  }\n}\nObjectDefineProperties(Writable.prototype, {\n  closed: {\n    __proto__: null,\n    get() {\n      return this._writableState ? this._writableState.closed : false\n    }\n  },\n  destroyed: {\n    __proto__: null,\n    get() {\n      return this._writableState ? this._writableState.destroyed : false\n    },\n    set(value) {\n      // Backward compatibility, the user is explicitly managing destroyed.\n      if (this._writableState) {\n        this._writableState.destroyed = value\n      }\n    }\n  },\n  writable: {\n    __proto__: null,\n    get() {\n      const w = this._writableState\n      // w.writable === false means that this is part of a Duplex stream\n      // where the writable side was disabled upon construction.\n      // Compat. The user might manually disable writable side through\n      // deprecated setter.\n      return !!w && w.writable !== false && !w.destroyed && !w.errored && !w.ending && !w.ended\n    },\n    set(val) {\n      // Backwards compatible.\n      if (this._writableState) {\n        this._writableState.writable = !!val\n      }\n    }\n  },\n  writableFinished: {\n    __proto__: null,\n    get() {\n      return this._writableState ? this._writableState.finished : false\n    }\n  },\n  writableObjectMode: {\n    __proto__: null,\n    get() {\n      return this._writableState ? this._writableState.objectMode : false\n    }\n  },\n  writableBuffer: {\n    __proto__: null,\n    get() {\n      return this._writableState && this._writableState.getBuffer()\n    }\n  },\n  writableEnded: {\n    __proto__: null,\n    get() {\n      return this._writableState ? this._writableState.ending : false\n    }\n  },\n  writableNeedDrain: {\n    __proto__: null,\n    get() {\n      const wState = this._writableState\n      if (!wState) return false\n      return !wState.destroyed && !wState.ending && wState.needDrain\n    }\n  },\n  writableHighWaterMark: {\n    __proto__: null,\n    get() {\n      return this._writableState && this._writableState.highWaterMark\n    }\n  },\n  writableCorked: {\n    __proto__: null,\n    get() {\n      return this._writableState ? this._writableState.corked : 0\n    }\n  },\n  writableLength: {\n    __proto__: null,\n    get() {\n      return this._writableState && this._writableState.length\n    }\n  },\n  errored: {\n    __proto__: null,\n    enumerable: false,\n    get() {\n      return this._writableState ? this._writableState.errored : null\n    }\n  },\n  writableAborted: {\n    __proto__: null,\n    enumerable: false,\n    get: function () {\n      return !!(\n        this._writableState.writable !== false &&\n        (this._writableState.destroyed || this._writableState.errored) &&\n        !this._writableState.finished\n      )\n    }\n  }\n})\nconst destroy = destroyImpl.destroy\nWritable.prototype.destroy = function (err, cb) {\n  const state = this._writableState\n\n  // Invoke pending callbacks.\n  if (!state.destroyed && (state.bufferedIndex < state.buffered.length || state[kOnFinished].length)) {\n    process.nextTick(errorBuffer, state)\n  }\n  destroy.call(this, err, cb)\n  return this\n}\nWritable.prototype._undestroy = destroyImpl.undestroy\nWritable.prototype._destroy = function (err, cb) {\n  cb(err)\n}\nWritable.prototype[EE.captureRejectionSymbol] = function (err) {\n  this.destroy(err)\n}\nlet webStreamsAdapters\n\n// Lazy to avoid circular references\nfunction lazyWebStreams() {\n  if (webStreamsAdapters === undefined) webStreamsAdapters = {}\n  return webStreamsAdapters\n}\nWritable.fromWeb = function (writableStream, options) {\n  return lazyWebStreams().newStreamWritableFromWritableStream(writableStream, options)\n}\nWritable.toWeb = function (streamWritable) {\n  return lazyWebStreams().newWritableStreamFromStreamWritable(streamWritable)\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,uCAAuC;AACvC,wEAAwE;AACxE,0CAA0C;AAI1C,qBAAqB,GAErB,MAAM;;;;;;;;;GAAkB;AAExB,mBAAmB,GAEnB,MAAM,EACJ,mBAAmB,EACnB,KAAK,EACL,kCAAkC,EAClC,oBAAoB,EACpB,sBAAsB,EACtB,oBAAoB,EACpB,0BAA0B,EAC1B,MAAM,EACN,iBAAiB,EAClB;AACD,OAAO,OAAO,GAAG;AACjB,SAAS,aAAa,GAAG;AACzB,MAAM,EAAE,cAAc,EAAE,EAAE;AAC1B,MAAM,SAAS,0HAAoB,MAAM;AACzC,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM;AACN,MAAM,EAAE,cAAc,EAAE;AACxB,MAAM,EAAE,gBAAgB,EAAE,uBAAuB,EAAE;AACnD,MAAM,EACJ,oBAAoB,EACpB,0BAA0B,EAC1B,qBAAqB,EACrB,sBAAsB,EACtB,oBAAoB,EACpB,2BAA2B,EAC3B,sBAAsB,EACtB,0BAA0B,EAC1B,oBAAoB,EACrB,GAAG,8GAA6B,KAAK;AACtC,MAAM,EAAE,cAAc,EAAE,GAAG;AAC3B,qBAAqB,SAAS,SAAS,EAAE,OAAO,SAAS;AACzD,qBAAqB,UAAU;AAC/B,SAAS,OAAO;AAChB,MAAM,cAAc,OAAO;AAC3B,SAAS,cAAc,OAAO,EAAE,MAAM,EAAE,QAAQ;IAC9C,2DAA2D;IAC3D,2BAA2B;IAC3B,2DAA2D;IAC3D,uEAAuE;IACvE,uEAAuE;IACvE,IAAI,OAAO,aAAa,WAAW,WAAW;IAE9C,4DAA4D;IAC5D,+BAA+B;IAC/B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,WAAW,QAAQ,UAAU;IAClD,IAAI,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,WAAW,QAAQ,kBAAkB;IAE3F,oDAAoD;IACpD,iEAAiE;IACjE,2DAA2D;IAC3D,IAAI,CAAC,aAAa,GAAG,UACjB,iBAAiB,IAAI,EAAE,SAAS,yBAAyB,YACzD,wBAAwB;IAE5B,6BAA6B;IAC7B,IAAI,CAAC,WAAW,GAAG;IAEnB,oBAAoB;IACpB,IAAI,CAAC,SAAS,GAAG;IACjB,gCAAgC;IAChC,IAAI,CAAC,MAAM,GAAG;IACd,4CAA4C;IAC5C,IAAI,CAAC,KAAK,GAAG;IACb,4BAA4B;IAC5B,IAAI,CAAC,QAAQ,GAAG;IAEhB,wBAAwB;IACxB,IAAI,CAAC,SAAS,GAAG;IAEjB,kEAAkE;IAClE,kEAAkE;IAClE,6BAA6B;IAC7B,MAAM,WAAW,CAAC,CAAC,CAAC,WAAW,QAAQ,aAAa,KAAK,KAAK;IAC9D,IAAI,CAAC,aAAa,GAAG,CAAC;IAEtB,sEAAsE;IACtE,6DAA6D;IAC7D,uDAAuD;IACvD,IAAI,CAAC,eAAe,GAAG,AAAC,WAAW,QAAQ,eAAe,IAAK;IAE/D,2DAA2D;IAC3D,6DAA6D;IAC7D,kBAAkB;IAClB,IAAI,CAAC,MAAM,GAAG;IAEd,qDAAqD;IACrD,IAAI,CAAC,OAAO,GAAG;IAEf,8DAA8D;IAC9D,IAAI,CAAC,MAAM,GAAG;IAEd,qEAAqE;IACrE,iEAAiE;IACjE,oEAAoE;IACpE,0CAA0C;IAC1C,IAAI,CAAC,IAAI,GAAG;IAEZ,sEAAsE;IACtE,oEAAoE;IACpE,6CAA6C;IAC7C,IAAI,CAAC,gBAAgB,GAAG;IAExB,mDAAmD;IACnD,IAAI,CAAC,OAAO,GAAG,QAAQ,IAAI,CAAC,WAAW;IAEvC,qEAAqE;IACrE,IAAI,CAAC,OAAO,GAAG;IAEf,0DAA0D;IAC1D,IAAI,CAAC,QAAQ,GAAG;IAEhB,kEAAkE;IAClE,mCAAmC;IACnC,IAAI,CAAC,kBAAkB,GAAG;IAC1B,YAAY,IAAI;IAEhB,kDAAkD;IAClD,iDAAiD;IACjD,IAAI,CAAC,SAAS,GAAG;IAEjB,kDAAkD;IAClD,mDAAmD;IACnD,sDAAsD;IACtD,eAAe;IACf,IAAI,CAAC,WAAW,GAAG;IAEnB,mEAAmE;IACnE,sDAAsD;IACtD,IAAI,CAAC,WAAW,GAAG;IAEnB,wEAAwE;IACxE,IAAI,CAAC,YAAY,GAAG;IAEpB,wDAAwD;IACxD,IAAI,CAAC,SAAS,GAAG,CAAC,WAAW,QAAQ,SAAS,KAAK;IAEnD,sEAAsE;IACtE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,QAAQ,WAAW,KAAK;IAEvD,wEAAwE;IACxE,6DAA6D;IAC7D,mEAAmE;IACnE,IAAI,CAAC,OAAO,GAAG;IAEf,wDAAwD;IACxD,IAAI,CAAC,MAAM,GAAG;IAEd,4DAA4D;IAC5D,0BAA0B;IAC1B,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,YAAY,GAAG,EAAE;AACxB;AACA,SAAS,YAAY,KAAK;IACxB,MAAM,QAAQ,GAAG,EAAE;IACnB,MAAM,aAAa,GAAG;IACtB,MAAM,UAAU,GAAG;IACnB,MAAM,OAAO,GAAG;AAClB;AACA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAS;IAC3C,OAAO,oBAAoB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa;AAC9D;AACA,qBAAqB,cAAc,SAAS,EAAE,wBAAwB;IACpE,WAAW;IACX;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa;IAClD;AACF;AACA,SAAS,SAAS,OAAO;IACvB,6CAA6C;IAC7C,kEAAkE;IAClE,mEAAmE;IAEnE,8EAA8E;IAC9E,2EAA2E;IAC3E,0DAA0D;IAE1D,yEAAyE;IACzE,uDAAuD;IACvD,MAAM,WAAW,IAAI;IACrB,IAAI,CAAC,YAAY,CAAC,mCAAmC,UAAU,IAAI,GAAG,OAAO,IAAI,SAAS;IAC1F,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,SAAS,IAAI,EAAE;IACvD,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QACpE,IAAI,OAAO,QAAQ,MAAM,KAAK,YAAY,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;QACvE,IAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;QAC1E,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QACpE,IAAI,OAAO,QAAQ,SAAS,KAAK,YAAY,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;QAChF,IAAI,QAAQ,MAAM,EAAE,eAAe,QAAQ,MAAM,EAAE,IAAI;IACzD;IACA,OAAO,IAAI,CAAC,IAAI,EAAE;IAClB,YAAY,SAAS,CAAC,IAAI,EAAE;QAC1B,MAAM,QAAQ,IAAI,CAAC,cAAc;QACjC,IAAI,CAAC,MAAM,OAAO,EAAE;YAClB,YAAY,IAAI,EAAE;QACpB;QACA,YAAY,IAAI,EAAE;IACpB;AACF;AACA,qBAAqB,UAAU,mBAAmB;IAChD,WAAW;IACX,OAAO,SAAU,MAAM;QACrB,IAAI,mCAAmC,IAAI,EAAE,SAAS,OAAO;QAC7D,IAAI,IAAI,KAAK,UAAU,OAAO;QAC9B,OAAO,UAAU,OAAO,cAAc,YAAY;IACpD;AACF;AAEA,mEAAmE;AACnE,SAAS,SAAS,CAAC,IAAI,GAAG;IACxB,eAAe,IAAI,EAAE,IAAI;AAC3B;AACA,SAAS,OAAO,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IACzC,MAAM,QAAQ,OAAO,cAAc;IACnC,IAAI,OAAO,aAAa,YAAY;QAClC,KAAK;QACL,WAAW,MAAM,eAAe;IAClC,OAAO;QACL,IAAI,CAAC,UAAU,WAAW,MAAM,eAAe;aAC1C,IAAI,aAAa,YAAY,CAAC,OAAO,UAAU,CAAC,WAAW,MAAM,IAAI,qBAAqB;QAC/F,IAAI,OAAO,OAAO,YAAY,KAAK;IACrC;IACA,IAAI,UAAU,MAAM;QAClB,MAAM,IAAI;IACZ,OAAO,IAAI,CAAC,MAAM,UAAU,EAAE;QAC5B,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,MAAM,aAAa,KAAK,OAAO;gBACjC,QAAQ,OAAO,IAAI,CAAC,OAAO;gBAC3B,WAAW;YACb;QACF,OAAO,IAAI,iBAAiB,QAAQ;YAClC,WAAW;QACb,OAAO,IAAI,OAAO,aAAa,CAAC,QAAQ;YACtC,QAAQ,OAAO,mBAAmB,CAAC;YACnC,WAAW;QACb,OAAO;YACL,MAAM,IAAI,qBAAqB,SAAS;gBAAC;gBAAU;gBAAU;aAAa,EAAE;QAC9E;IACF;IACA,IAAI;IACJ,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,IAAI;IACZ,OAAO,IAAI,MAAM,SAAS,EAAE;QAC1B,MAAM,IAAI,qBAAqB;IACjC;IACA,IAAI,KAAK;QACP,QAAQ,QAAQ,CAAC,IAAI;QACrB,eAAe,QAAQ,KAAK;QAC5B,OAAO;IACT;IACA,MAAM,SAAS;IACf,OAAO,cAAc,QAAQ,OAAO,OAAO,UAAU;AACvD;AACA,SAAS,SAAS,CAAC,KAAK,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACtD,OAAO,OAAO,IAAI,EAAE,OAAO,UAAU,QAAQ;AAC/C;AACA,SAAS,SAAS,CAAC,IAAI,GAAG;IACxB,IAAI,CAAC,cAAc,CAAC,MAAM;AAC5B;AACA,SAAS,SAAS,CAAC,MAAM,GAAG;IAC1B,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM;QACZ,IAAI,CAAC,MAAM,OAAO,EAAE,YAAY,IAAI,EAAE;IACxC;AACF;AACA,SAAS,SAAS,CAAC,kBAAkB,GAAG,SAAS,mBAAmB,QAAQ;IAC1E,6CAA6C;IAC7C,IAAI,OAAO,aAAa,UAAU,WAAW,2BAA2B;IACxE,IAAI,CAAC,OAAO,UAAU,CAAC,WAAW,MAAM,IAAI,qBAAqB;IACjE,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG;IACtC,OAAO,IAAI;AACb;AAEA,yDAAyD;AACzD,2DAA2D;AAC3D,oEAAoE;AACpE,SAAS,cAAc,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAC7D,MAAM,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;IAC/C,MAAM,MAAM,IAAI;IAEhB,oCAAoC;IACpC,MAAM,MAAM,MAAM,MAAM,GAAG,MAAM,aAAa;IAC9C,qEAAqE;IACrE,IAAI,CAAC,KAAK,MAAM,SAAS,GAAG;IAC5B,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,WAAW,EAAE;QACxE,MAAM,QAAQ,CAAC,IAAI,CAAC;YAClB;YACA;YACA;QACF;QACA,IAAI,MAAM,UAAU,IAAI,aAAa,UAAU;YAC7C,MAAM,UAAU,GAAG;QACrB;QACA,IAAI,MAAM,OAAO,IAAI,aAAa,KAAK;YACrC,MAAM,OAAO,GAAG;QAClB;IACF,OAAO;QACL,MAAM,QAAQ,GAAG;QACjB,MAAM,OAAO,GAAG;QAChB,MAAM,OAAO,GAAG;QAChB,MAAM,IAAI,GAAG;QACb,OAAO,MAAM,CAAC,OAAO,UAAU,MAAM,OAAO;QAC5C,MAAM,IAAI,GAAG;IACf;IAEA,yDAAyD;IACzD,mDAAmD;IACnD,OAAO,OAAO,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,SAAS;AAClD;AACA,SAAS,QAAQ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,MAAM,QAAQ,GAAG;IACjB,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG;IAChB,MAAM,IAAI,GAAG;IACb,IAAI,MAAM,SAAS,EAAE,MAAM,OAAO,CAAC,IAAI,qBAAqB;SACvD,IAAI,QAAQ,OAAO,OAAO,CAAC,OAAO,MAAM,OAAO;SAC/C,OAAO,MAAM,CAAC,OAAO,UAAU,MAAM,OAAO;IACjD,MAAM,IAAI,GAAG;AACf;AACA,SAAS,aAAa,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;IACzC,EAAE,MAAM,SAAS;IACjB,GAAG;IACH,wDAAwD;IACxD,0DAA0D;IAC1D,0DAA0D;IAC1D,UAAU;IACV,YAAY;IACZ,wDAAwD;IACxD,eAAe,QAAQ;AACzB;AACA,SAAS,QAAQ,MAAM,EAAE,EAAE;IACzB,MAAM,QAAQ,OAAO,cAAc;IACnC,MAAM,OAAO,MAAM,IAAI;IACvB,MAAM,KAAK,MAAM,OAAO;IACxB,IAAI,OAAO,OAAO,YAAY;QAC5B,eAAe,QAAQ,IAAI;QAC3B;IACF;IACA,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG;IAChB,MAAM,MAAM,IAAI,MAAM,QAAQ;IAC9B,MAAM,QAAQ,GAAG;IACjB,IAAI,IAAI;QACN,kFAAkF;QAClF,GAAG,KAAK,EAAC,4CAA4C;QAErD,IAAI,CAAC,MAAM,OAAO,EAAE;YAClB,MAAM,OAAO,GAAG;QAClB;QAEA,uEAAuE;QACvE,SAAS;QACT,IAAI,OAAO,cAAc,IAAI,CAAC,OAAO,cAAc,CAAC,OAAO,EAAE;YAC3D,OAAO,cAAc,CAAC,OAAO,GAAG;QAClC;QACA,IAAI,MAAM;YACR,QAAQ,QAAQ,CAAC,cAAc,QAAQ,OAAO,IAAI;QACpD,OAAO;YACL,aAAa,QAAQ,OAAO,IAAI;QAClC;IACF,OAAO;QACL,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,MAAM,aAAa,EAAE;YAC/C,YAAY,QAAQ;QACtB;QACA,IAAI,MAAM;YACR,qEAAqE;YACrE,mEAAmE;YACnE,mEAAmE;YACnE,sBAAsB;YACtB,IAAI,MAAM,kBAAkB,KAAK,QAAQ,MAAM,kBAAkB,CAAC,EAAE,KAAK,IAAI;gBAC3E,MAAM,kBAAkB,CAAC,KAAK;YAChC,OAAO;gBACL,MAAM,kBAAkB,GAAG;oBACzB,OAAO;oBACP;oBACA;oBACA;gBACF;gBACA,QAAQ,QAAQ,CAAC,gBAAgB,MAAM,kBAAkB;YAC3D;QACF,OAAO;YACL,WAAW,QAAQ,OAAO,GAAG;QAC/B;IACF;AACF;AACA,SAAS,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE;IAClD,MAAM,kBAAkB,GAAG;IAC3B,OAAO,WAAW,QAAQ,OAAO,OAAO;AAC1C;AACA,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;IAC1C,MAAM,YAAY,CAAC,MAAM,MAAM,IAAI,CAAC,OAAO,SAAS,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,SAAS;IAC7F,IAAI,WAAW;QACb,MAAM,SAAS,GAAG;QAClB,OAAO,IAAI,CAAC;IACd;IACA,MAAO,UAAU,EAAG;QAClB,MAAM,SAAS;QACf;IACF;IACA,IAAI,MAAM,SAAS,EAAE;QACnB,YAAY;IACd;IACA,YAAY,QAAQ;AACtB;AAEA,qEAAqE;AACrE,SAAS,YAAY,KAAK;IACxB,IAAI,MAAM,OAAO,EAAE;QACjB;IACF;IACA,IAAK,IAAI,IAAI,MAAM,aAAa,EAAE,IAAI,MAAM,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAG;QAChE,IAAI;QACJ,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAE;QAC7C,MAAM,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM;QAC/C,MAAM,MAAM,IAAI;QAChB,SACE,CAAC,iBAAiB,MAAM,OAAO,MAAM,QAAQ,mBAAmB,YAC5D,iBACA,IAAI,qBAAqB;IAEjC;IACA,MAAM,oBAAoB,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;IACpD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,IAAK;QACjD,IAAI;QACJ,iBAAiB,CAAC,EAAE,CAClB,CAAC,kBAAkB,MAAM,OAAO,MAAM,QAAQ,oBAAoB,YAC9D,kBACA,IAAI,qBAAqB;IAEjC;IACA,YAAY;AACd;AAEA,+DAA+D;AAC/D,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,MAAM,MAAM,IAAI,MAAM,gBAAgB,IAAI,MAAM,SAAS,IAAI,CAAC,MAAM,WAAW,EAAE;QACnF;IACF;IACA,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG;IAChD,MAAM,iBAAiB,SAAS,MAAM,GAAG;IACzC,IAAI,CAAC,gBAAgB;QACnB;IACF;IACA,IAAI,IAAI;IACR,MAAM,gBAAgB,GAAG;IACzB,IAAI,iBAAiB,KAAK,OAAO,OAAO,EAAE;QACxC,MAAM,SAAS,IAAI,iBAAiB;QACpC,MAAM,WAAW,MAAM,OAAO,GAC1B,MACA,CAAC;YACC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,EAAE,EAAG;gBACxC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC;YACvB;QACF;QACJ,0EAA0E;QAC1E,yCAAyC;QACzC,MAAM,SAAS,MAAM,OAAO,IAAI,MAAM,IAAI,WAAW,oBAAoB,UAAU;QACnF,OAAO,UAAU,GAAG,MAAM,UAAU;QACpC,QAAQ,QAAQ,OAAO,MAAM,MAAM,MAAM,EAAE,QAAQ,IAAI;QACvD,YAAY;IACd,OAAO;QACL,GAAG;YACD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,EAAE;YACjD,QAAQ,CAAC,IAAI,GAAG;YAChB,MAAM,MAAM,aAAa,IAAI,MAAM,MAAM;YACzC,QAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU;QACtD,QAAS,IAAI,SAAS,MAAM,IAAI,CAAC,MAAM,OAAO,CAAC;QAC/C,IAAI,MAAM,SAAS,MAAM,EAAE;YACzB,YAAY;QACd,OAAO,IAAI,IAAI,KAAK;YAClB,SAAS,MAAM,CAAC,GAAG;YACnB,MAAM,aAAa,GAAG;QACxB,OAAO;YACL,MAAM,aAAa,GAAG;QACxB;IACF;IACA,MAAM,gBAAgB,GAAG;AAC3B;AACA,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACvD,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,IAAI,CAAC,OAAO,CACV;YACE;gBACE;gBACA;YACF;SACD,EACD;IAEJ,OAAO;QACL,MAAM,IAAI,2BAA2B;IACvC;AACF;AACA,SAAS,SAAS,CAAC,OAAO,GAAG;AAC7B,SAAS,SAAS,CAAC,GAAG,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IACpD,MAAM,QAAQ,IAAI,CAAC,cAAc;IACjC,IAAI,OAAO,UAAU,YAAY;QAC/B,KAAK;QACL,QAAQ;QACR,WAAW;IACb,OAAO,IAAI,OAAO,aAAa,YAAY;QACzC,KAAK;QACL,WAAW;IACb;IACA,IAAI;IACJ,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,MAAM,MAAM,OAAO,IAAI,EAAE,OAAO;QAChC,IAAI,eAAe,OAAO;YACxB,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM,GAAG;QACf,IAAI,CAAC,MAAM;IACb;IACA,IAAI,KAAK;IACP,gBAAgB;IAClB,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE;QAC1C,wEAAwE;QACxE,wEAAwE;QACxE,qEAAqE;QACrE,qEAAqE;QACrE,UAAU;QAEV,MAAM,MAAM,GAAG;QACf,YAAY,IAAI,EAAE,OAAO;QACzB,MAAM,KAAK,GAAG;IAChB,OAAO,IAAI,MAAM,QAAQ,EAAE;QACzB,MAAM,IAAI,4BAA4B;IACxC,OAAO,IAAI,MAAM,SAAS,EAAE;QAC1B,MAAM,IAAI,qBAAqB;IACjC;IACA,IAAI,OAAO,OAAO,YAAY;QAC5B,IAAI,OAAO,MAAM,QAAQ,EAAE;YACzB,QAAQ,QAAQ,CAAC,IAAI;QACvB,OAAO;YACL,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC;QAC1B;IACF;IACA,OAAO,IAAI;AACb;AACA,SAAS,WAAW,KAAK;IACvB,OACE,MAAM,MAAM,IACZ,CAAC,MAAM,SAAS,IAChB,MAAM,WAAW,IACjB,MAAM,MAAM,KAAK,KACjB,CAAC,MAAM,OAAO,IACd,MAAM,QAAQ,CAAC,MAAM,KAAK,KAC1B,CAAC,MAAM,QAAQ,IACf,CAAC,MAAM,OAAO,IACd,CAAC,MAAM,YAAY,IACnB,CAAC,MAAM,YAAY;AAEvB;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,SAAS;IACb,SAAS,SAAS,GAAG;QACnB,IAAI,QAAQ;YACV,eAAe,QAAQ,QAAQ,QAAQ,QAAQ,YAAY,MAAM;YACjE;QACF;QACA,SAAS;QACT,MAAM,SAAS;QACf,IAAI,KAAK;YACP,MAAM,oBAAoB,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;YACpD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,IAAK;gBACjD,iBAAiB,CAAC,EAAE,CAAC;YACvB;YACA,eAAe,QAAQ,KAAK,MAAM,IAAI;QACxC,OAAO,IAAI,WAAW,QAAQ;YAC5B,MAAM,WAAW,GAAG;YACpB,OAAO,IAAI,CAAC;YACZ,iDAAiD;YACjD,+CAA+C;YAC/C,8CAA8C;YAC9C,MAAM,SAAS;YACf,QAAQ,QAAQ,CAAC,QAAQ,QAAQ;QACnC;IACF;IACA,MAAM,IAAI,GAAG;IACb,MAAM,SAAS;IACf,IAAI;QACF,OAAO,MAAM,CAAC;IAChB,EAAE,OAAO,KAAK;QACZ,SAAS;IACX;IACA,MAAM,IAAI,GAAG;AACf;AACA,SAAS,UAAU,MAAM,EAAE,KAAK;IAC9B,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,WAAW,EAAE;QAC5C,IAAI,OAAO,OAAO,MAAM,KAAK,cAAc,CAAC,MAAM,SAAS,EAAE;YAC3D,MAAM,WAAW,GAAG;YACpB,UAAU,QAAQ;QACpB,OAAO;YACL,MAAM,WAAW,GAAG;YACpB,OAAO,IAAI,CAAC;QACd;IACF;AACF;AACA,SAAS,YAAY,MAAM,EAAE,KAAK,EAAE,IAAI;IACtC,IAAI,WAAW,QAAQ;QACrB,UAAU,QAAQ;QAClB,IAAI,MAAM,SAAS,KAAK,GAAG;YACzB,IAAI,MAAM;gBACR,MAAM,SAAS;gBACf,QAAQ,QAAQ,CACd,CAAC,QAAQ;oBACP,IAAI,WAAW,QAAQ;wBACrB,OAAO,QAAQ;oBACjB,OAAO;wBACL,MAAM,SAAS;oBACjB;gBACF,GACA,QACA;YAEJ,OAAO,IAAI,WAAW,QAAQ;gBAC5B,MAAM,SAAS;gBACf,OAAO,QAAQ;YACjB;QACF;IACF;AACF;AACA,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,MAAM,SAAS;IACf,MAAM,QAAQ,GAAG;IACjB,MAAM,oBAAoB,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;IACpD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,IAAK;QACjD,iBAAiB,CAAC,EAAE;IACtB;IACA,OAAO,IAAI,CAAC;IACZ,IAAI,MAAM,WAAW,EAAE;QACrB,oDAAoD;QACpD,yDAAyD;QACzD,MAAM,SAAS,OAAO,cAAc;QACpC,MAAM,cACJ,CAAC,UACA,OAAO,WAAW,IACjB,6CAA6C;QAC7C,0CAA0C;QAC1C,CAAC,OAAO,UAAU,IAAI,OAAO,QAAQ,KAAK,KAAK;QACnD,IAAI,aAAa;YACf,OAAO,OAAO;QAChB;IACF;AACF;AACA,uBAAuB,SAAS,SAAS,EAAE;IACzC,QAAQ;QACN,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;QAC5D;IACF;IACA,WAAW;QACT,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;QAC/D;QACA,KAAI,KAAK;YACP,qEAAqE;YACrE,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG;YAClC;QACF;IACF;IACA,UAAU;QACR,WAAW;QACX;YACE,MAAM,IAAI,IAAI,CAAC,cAAc;YAC7B,kEAAkE;YAClE,0DAA0D;YAC1D,gEAAgE;YAChE,qBAAqB;YACrB,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,KAAK,SAAS,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE,KAAK;QAC3F;QACA,KAAI,GAAG;YACL,wBAAwB;YACxB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,CAAC;YACnC;QACF;IACF;IACA,kBAAkB;QAChB,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;QAC9D;IACF;IACA,oBAAoB;QAClB,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QAChE;IACF;IACA,gBAAgB;QACd,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;QAC7D;IACF;IACA,eAAe;QACb,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;QAC5D;IACF;IACA,mBAAmB;QACjB,WAAW;QACX;YACE,MAAM,SAAS,IAAI,CAAC,cAAc;YAClC,IAAI,CAAC,QAAQ,OAAO;YACpB,OAAO,CAAC,OAAO,SAAS,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,SAAS;QAChE;IACF;IACA,uBAAuB;QACrB,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa;QACjE;IACF;IACA,gBAAgB;QACd,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;QAC5D;IACF;IACA,gBAAgB;QACd,WAAW;QACX;YACE,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;QAC1D;IACF;IACA,SAAS;QACP,WAAW;QACX,YAAY;QACZ;YACE,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC7D;IACF;IACA,iBAAiB;QACf,WAAW;QACX,YAAY;QACZ,KAAK;YACH,OAAO,CAAC,CAAC,CACP,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,SACjC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAC7D,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,AAC/B;QACF;IACF;AACF;AACA,MAAM,UAAU,YAAY,OAAO;AACnC,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG,EAAE,EAAE;IAC5C,MAAM,QAAQ,IAAI,CAAC,cAAc;IAEjC,4BAA4B;IAC5B,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG;QAClG,QAAQ,QAAQ,CAAC,aAAa;IAChC;IACA,QAAQ,IAAI,CAAC,IAAI,EAAE,KAAK;IACxB,OAAO,IAAI;AACb;AACA,SAAS,SAAS,CAAC,UAAU,GAAG,YAAY,SAAS;AACrD,SAAS,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;IAC7C,GAAG;AACL;AACA,SAAS,SAAS,CAAC,GAAG,sBAAsB,CAAC,GAAG,SAAU,GAAG;IAC3D,IAAI,CAAC,OAAO,CAAC;AACf;AACA,IAAI;AAEJ,oCAAoC;AACpC,SAAS;IACP,IAAI,uBAAuB,WAAW,qBAAqB,CAAC;IAC5D,OAAO;AACT;AACA,SAAS,OAAO,GAAG,SAAU,cAAc,EAAE,OAAO;IAClD,OAAO,iBAAiB,mCAAmC,CAAC,gBAAgB;AAC9E;AACA,SAAS,KAAK,GAAG,SAAU,cAAc;IACvC,OAAO,iBAAiB,mCAAmC,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6591, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/transform.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n\n'use strict'\n\nconst { ObjectSetPrototypeOf, Symbol } = require('../../ours/primordials')\nmodule.exports = Transform\nconst { ERR_METHOD_NOT_IMPLEMENTED } = require('../../ours/errors').codes\nconst Duplex = require('./duplex')\nconst { getHighWaterMark } = require('./state')\nObjectSetPrototypeOf(Transform.prototype, Duplex.prototype)\nObjectSetPrototypeOf(Transform, Duplex)\nconst kCallback = Symbol('kCallback')\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options)\n\n  // TODO (ronag): This should preferably always be\n  // applied but would be semver-major. Or even better;\n  // make Transform a Readable with the Writable interface.\n  const readableHighWaterMark = options ? getHighWaterMark(this, options, 'readableHighWaterMark', true) : null\n  if (readableHighWaterMark === 0) {\n    // A Duplex will buffer both on the writable and readable side while\n    // a Transform just wants to buffer hwm number of elements. To avoid\n    // buffering twice we disable buffering on the writable side.\n    options = {\n      ...options,\n      highWaterMark: null,\n      readableHighWaterMark,\n      // TODO (ronag): 0 is not optimal since we have\n      // a \"bug\" where we check needDrain before calling _write and not after.\n      // Refs: https://github.com/nodejs/node/pull/32887\n      // Refs: https://github.com/nodejs/node/pull/35941\n      writableHighWaterMark: options.writableHighWaterMark || 0\n    }\n  }\n  Duplex.call(this, options)\n\n  // We have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n  this._readableState.sync = false\n  this[kCallback] = null\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform\n    if (typeof options.flush === 'function') this._flush = options.flush\n  }\n\n  // When the writable side finishes, then flush out anything remaining.\n  // Backwards compat. Some Transform streams incorrectly implement _final\n  // instead of or in addition to _flush. By using 'prefinish' instead of\n  // implementing _final we continue supporting this unfortunate use case.\n  this.on('prefinish', prefinish)\n}\nfunction final(cb) {\n  if (typeof this._flush === 'function' && !this.destroyed) {\n    this._flush((er, data) => {\n      if (er) {\n        if (cb) {\n          cb(er)\n        } else {\n          this.destroy(er)\n        }\n        return\n      }\n      if (data != null) {\n        this.push(data)\n      }\n      this.push(null)\n      if (cb) {\n        cb()\n      }\n    })\n  } else {\n    this.push(null)\n    if (cb) {\n      cb()\n    }\n  }\n}\nfunction prefinish() {\n  if (this._final !== final) {\n    final.call(this)\n  }\n}\nTransform.prototype._final = final\nTransform.prototype._transform = function (chunk, encoding, callback) {\n  throw new ERR_METHOD_NOT_IMPLEMENTED('_transform()')\n}\nTransform.prototype._write = function (chunk, encoding, callback) {\n  const rState = this._readableState\n  const wState = this._writableState\n  const length = rState.length\n  this._transform(chunk, encoding, (err, val) => {\n    if (err) {\n      callback(err)\n      return\n    }\n    if (val != null) {\n      this.push(val)\n    }\n    if (\n      wState.ended ||\n      // Backwards compat.\n      length === rState.length ||\n      // Backwards compat.\n      rState.length < rState.highWaterMark\n    ) {\n      callback()\n    } else {\n      this[kCallback] = callback\n    }\n  })\n}\nTransform.prototype._read = function () {\n  if (this[kCallback]) {\n    const callback = this[kCallback]\n    this[kCallback] = null\n    callback()\n  }\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,gEAAgE;AAChE,8DAA8D;AAC9D,uEAAuE;AACvE,sEAAsE;AACtE,iDAAiD;AACjD,EAAE;AACF,gEAAgE;AAChE,qEAAqE;AACrE,kEAAkE;AAClE,0DAA0D;AAC1D,EAAE;AACF,yBAAyB;AACzB,EAAE;AACF,wEAAwE;AACxE,sEAAsE;AACtE,mEAAmE;AACnE,gEAAgE;AAChE,oDAAoD;AACpD,EAAE;AACF,uEAAuE;AACvE,oEAAoE;AACpE,qEAAqE;AACrE,uEAAuE;AACvE,qEAAqE;AACrE,sEAAsE;AACtE,EAAE;AACF,sEAAsE;AACtE,0EAA0E;AAC1E,yEAAyE;AACzE,oEAAoE;AACpE,sEAAsE;AACtE,sEAAsE;AACtE,wEAAwE;AACxE,sEAAsE;AACtE,qEAAqE;AACrE,oEAAoE;AACpE,yCAAyC;AACzC,EAAE;AACF,yEAAyE;AACzE,yEAAyE;AACzE,+DAA+D;AAI/D,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE;AACtC,OAAO,OAAO,GAAG;AACjB,MAAM,EAAE,0BAA0B,EAAE,GAAG,8GAA6B,KAAK;AACzE,MAAM;AACN,MAAM,EAAE,gBAAgB,EAAE;AAC1B,qBAAqB,UAAU,SAAS,EAAE,OAAO,SAAS;AAC1D,qBAAqB,WAAW;AAChC,MAAM,YAAY,OAAO;AACzB,SAAS,UAAU,OAAO;IACxB,IAAI,CAAC,CAAC,IAAI,YAAY,SAAS,GAAG,OAAO,IAAI,UAAU;IAEvD,iDAAiD;IACjD,qDAAqD;IACrD,yDAAyD;IACzD,MAAM,wBAAwB,UAAU,iBAAiB,IAAI,EAAE,SAAS,yBAAyB,QAAQ;IACzG,IAAI,0BAA0B,GAAG;QAC/B,oEAAoE;QACpE,oEAAoE;QACpE,6DAA6D;QAC7D,UAAU;YACR,GAAG,OAAO;YACV,eAAe;YACf;YACA,+CAA+C;YAC/C,wEAAwE;YACxE,kDAAkD;YAClD,kDAAkD;YAClD,uBAAuB,QAAQ,qBAAqB,IAAI;QAC1D;IACF;IACA,OAAO,IAAI,CAAC,IAAI,EAAE;IAElB,kEAAkE;IAClE,gEAAgE;IAChE,mBAAmB;IACnB,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG;IAC3B,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,SAAS;QACX,IAAI,OAAO,QAAQ,SAAS,KAAK,YAAY,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;QAChF,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IACtE;IAEA,sEAAsE;IACtE,wEAAwE;IACxE,uEAAuE;IACvE,wEAAwE;IACxE,IAAI,CAAC,EAAE,CAAC,aAAa;AACvB;AACA,SAAS,MAAM,EAAE;IACf,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE;QACxD,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI;YACf,IAAI,IAAI;gBACN,IAAI,IAAI;oBACN,GAAG;gBACL,OAAO;oBACL,IAAI,CAAC,OAAO,CAAC;gBACf;gBACA;YACF;YACA,IAAI,QAAQ,MAAM;gBAChB,IAAI,CAAC,IAAI,CAAC;YACZ;YACA,IAAI,CAAC,IAAI,CAAC;YACV,IAAI,IAAI;gBACN;YACF;QACF;IACF,OAAO;QACL,IAAI,CAAC,IAAI,CAAC;QACV,IAAI,IAAI;YACN;QACF;IACF;AACF;AACA,SAAS;IACP,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO;QACzB,MAAM,IAAI,CAAC,IAAI;IACjB;AACF;AACA,UAAU,SAAS,CAAC,MAAM,GAAG;AAC7B,UAAU,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAClE,MAAM,IAAI,2BAA2B;AACvC;AACA,UAAU,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAC9D,MAAM,SAAS,IAAI,CAAC,cAAc;IAClC,MAAM,SAAS,IAAI,CAAC,cAAc;IAClC,MAAM,SAAS,OAAO,MAAM;IAC5B,IAAI,CAAC,UAAU,CAAC,OAAO,UAAU,CAAC,KAAK;QACrC,IAAI,KAAK;YACP,SAAS;YACT;QACF;QACA,IAAI,OAAO,MAAM;YACf,IAAI,CAAC,IAAI,CAAC;QACZ;QACA,IACE,OAAO,KAAK,IACZ,oBAAoB;QACpB,WAAW,OAAO,MAAM,IACxB,oBAAoB;QACpB,OAAO,MAAM,GAAG,OAAO,aAAa,EACpC;YACA;QACF,OAAO;YACL,IAAI,CAAC,UAAU,GAAG;QACpB;IACF;AACF;AACA,UAAU,SAAS,CAAC,KAAK,GAAG;IAC1B,IAAI,IAAI,CAAC,UAAU,EAAE;QACnB,MAAM,WAAW,IAAI,CAAC,UAAU;QAChC,IAAI,CAAC,UAAU,GAAG;QAClB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6764, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/passthrough.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a passthrough stream.\n// basically just the most minimal sort of Transform stream.\n// Every written chunk gets output as-is.\n\n'use strict'\n\nconst { ObjectSetPrototypeOf } = require('../../ours/primordials')\nmodule.exports = PassThrough\nconst Transform = require('./transform')\nObjectSetPrototypeOf(PassThrough.prototype, Transform.prototype)\nObjectSetPrototypeOf(PassThrough, Transform)\nfunction PassThrough(options) {\n  if (!(this instanceof PassThrough)) return new PassThrough(options)\n  Transform.call(this, options)\n}\nPassThrough.prototype._transform = function (chunk, encoding, cb) {\n  cb(null, chunk)\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC,wBAAwB;AACxB,4DAA4D;AAC5D,yCAAyC;AAIzC,MAAM,EAAE,oBAAoB,EAAE;AAC9B,OAAO,OAAO,GAAG;AACjB,MAAM;AACN,qBAAqB,YAAY,SAAS,EAAE,UAAU,SAAS;AAC/D,qBAAqB,aAAa;AAClC,SAAS,YAAY,OAAO;IAC1B,IAAI,CAAC,CAAC,IAAI,YAAY,WAAW,GAAG,OAAO,IAAI,YAAY;IAC3D,UAAU,IAAI,CAAC,IAAI,EAAE;AACvB;AACA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,EAAE;IAC9D,GAAG,MAAM;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6802, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/pipeline.js"], "sourcesContent": ["/* replacement start */\n\nconst process = require('process/')\n\n/* replacement end */\n// Ported from https://github.com/mafintosh/pump with\n// permission from the author, <PERSON> (@mafintosh).\n\n;('use strict')\nconst { ArrayIsArray, Promise, SymbolAsyncIterator, SymbolDispose } = require('../../ours/primordials')\nconst eos = require('./end-of-stream')\nconst { once } = require('../../ours/util')\nconst destroyImpl = require('./destroy')\nconst Duplex = require('./duplex')\nconst {\n  aggregateTwoErrors,\n  codes: {\n    ERR_INVALID_ARG_TYPE,\n    ERR_INVALID_RETURN_VALUE,\n    ERR_MISSING_ARGS,\n    ERR_STREAM_DESTROYED,\n    ERR_STREAM_PREMATURE_CLOSE\n  },\n  AbortError\n} = require('../../ours/errors')\nconst { validateFunction, validateAbortSignal } = require('../validators')\nconst {\n  isIterable,\n  isReadable,\n  isReadableNodeStream,\n  isNodeStream,\n  isTransformStream,\n  isWebStream,\n  isReadableStream,\n  isReadableFinished\n} = require('./utils')\nconst AbortController = globalThis.AbortController || require('abort-controller').AbortController\nlet PassThrough\nlet Readable\nlet addAbortListener\nfunction destroyer(stream, reading, writing) {\n  let finished = false\n  stream.on('close', () => {\n    finished = true\n  })\n  const cleanup = eos(\n    stream,\n    {\n      readable: reading,\n      writable: writing\n    },\n    (err) => {\n      finished = !err\n    }\n  )\n  return {\n    destroy: (err) => {\n      if (finished) return\n      finished = true\n      destroyImpl.destroyer(stream, err || new ERR_STREAM_DESTROYED('pipe'))\n    },\n    cleanup\n  }\n}\nfunction popCallback(streams) {\n  // Streams should never be an empty array. It should always contain at least\n  // a single stream. Therefore optimize for the average case instead of\n  // checking for length === 0 as well.\n  validateFunction(streams[streams.length - 1], 'streams[stream.length - 1]')\n  return streams.pop()\n}\nfunction makeAsyncIterable(val) {\n  if (isIterable(val)) {\n    return val\n  } else if (isReadableNodeStream(val)) {\n    // Legacy streams are not Iterable.\n    return fromReadable(val)\n  }\n  throw new ERR_INVALID_ARG_TYPE('val', ['Readable', 'Iterable', 'AsyncIterable'], val)\n}\nasync function* fromReadable(val) {\n  if (!Readable) {\n    Readable = require('./readable')\n  }\n  yield* Readable.prototype[SymbolAsyncIterator].call(val)\n}\nasync function pumpToNode(iterable, writable, finish, { end }) {\n  let error\n  let onresolve = null\n  const resume = (err) => {\n    if (err) {\n      error = err\n    }\n    if (onresolve) {\n      const callback = onresolve\n      onresolve = null\n      callback()\n    }\n  }\n  const wait = () =>\n    new Promise((resolve, reject) => {\n      if (error) {\n        reject(error)\n      } else {\n        onresolve = () => {\n          if (error) {\n            reject(error)\n          } else {\n            resolve()\n          }\n        }\n      }\n    })\n  writable.on('drain', resume)\n  const cleanup = eos(\n    writable,\n    {\n      readable: false\n    },\n    resume\n  )\n  try {\n    if (writable.writableNeedDrain) {\n      await wait()\n    }\n    for await (const chunk of iterable) {\n      if (!writable.write(chunk)) {\n        await wait()\n      }\n    }\n    if (end) {\n      writable.end()\n      await wait()\n    }\n    finish()\n  } catch (err) {\n    finish(error !== err ? aggregateTwoErrors(error, err) : err)\n  } finally {\n    cleanup()\n    writable.off('drain', resume)\n  }\n}\nasync function pumpToWeb(readable, writable, finish, { end }) {\n  if (isTransformStream(writable)) {\n    writable = writable.writable\n  }\n  // https://streams.spec.whatwg.org/#example-manual-write-with-backpressure\n  const writer = writable.getWriter()\n  try {\n    for await (const chunk of readable) {\n      await writer.ready\n      writer.write(chunk).catch(() => {})\n    }\n    await writer.ready\n    if (end) {\n      await writer.close()\n    }\n    finish()\n  } catch (err) {\n    try {\n      await writer.abort(err)\n      finish(err)\n    } catch (err) {\n      finish(err)\n    }\n  }\n}\nfunction pipeline(...streams) {\n  return pipelineImpl(streams, once(popCallback(streams)))\n}\nfunction pipelineImpl(streams, callback, opts) {\n  if (streams.length === 1 && ArrayIsArray(streams[0])) {\n    streams = streams[0]\n  }\n  if (streams.length < 2) {\n    throw new ERR_MISSING_ARGS('streams')\n  }\n  const ac = new AbortController()\n  const signal = ac.signal\n  const outerSignal = opts === null || opts === undefined ? undefined : opts.signal\n\n  // Need to cleanup event listeners if last stream is readable\n  // https://github.com/nodejs/node/issues/35452\n  const lastStreamCleanup = []\n  validateAbortSignal(outerSignal, 'options.signal')\n  function abort() {\n    finishImpl(new AbortError())\n  }\n  addAbortListener = addAbortListener || require('../../ours/util').addAbortListener\n  let disposable\n  if (outerSignal) {\n    disposable = addAbortListener(outerSignal, abort)\n  }\n  let error\n  let value\n  const destroys = []\n  let finishCount = 0\n  function finish(err) {\n    finishImpl(err, --finishCount === 0)\n  }\n  function finishImpl(err, final) {\n    var _disposable\n    if (err && (!error || error.code === 'ERR_STREAM_PREMATURE_CLOSE')) {\n      error = err\n    }\n    if (!error && !final) {\n      return\n    }\n    while (destroys.length) {\n      destroys.shift()(error)\n    }\n    ;(_disposable = disposable) === null || _disposable === undefined ? undefined : _disposable[SymbolDispose]()\n    ac.abort()\n    if (final) {\n      if (!error) {\n        lastStreamCleanup.forEach((fn) => fn())\n      }\n      process.nextTick(callback, error, value)\n    }\n  }\n  let ret\n  for (let i = 0; i < streams.length; i++) {\n    const stream = streams[i]\n    const reading = i < streams.length - 1\n    const writing = i > 0\n    const end = reading || (opts === null || opts === undefined ? undefined : opts.end) !== false\n    const isLastStream = i === streams.length - 1\n    if (isNodeStream(stream)) {\n      if (end) {\n        const { destroy, cleanup } = destroyer(stream, reading, writing)\n        destroys.push(destroy)\n        if (isReadable(stream) && isLastStream) {\n          lastStreamCleanup.push(cleanup)\n        }\n      }\n\n      // Catch stream errors that occur after pipe/pump has completed.\n      function onError(err) {\n        if (err && err.name !== 'AbortError' && err.code !== 'ERR_STREAM_PREMATURE_CLOSE') {\n          finish(err)\n        }\n      }\n      stream.on('error', onError)\n      if (isReadable(stream) && isLastStream) {\n        lastStreamCleanup.push(() => {\n          stream.removeListener('error', onError)\n        })\n      }\n    }\n    if (i === 0) {\n      if (typeof stream === 'function') {\n        ret = stream({\n          signal\n        })\n        if (!isIterable(ret)) {\n          throw new ERR_INVALID_RETURN_VALUE('Iterable, AsyncIterable or Stream', 'source', ret)\n        }\n      } else if (isIterable(stream) || isReadableNodeStream(stream) || isTransformStream(stream)) {\n        ret = stream\n      } else {\n        ret = Duplex.from(stream)\n      }\n    } else if (typeof stream === 'function') {\n      if (isTransformStream(ret)) {\n        var _ret\n        ret = makeAsyncIterable((_ret = ret) === null || _ret === undefined ? undefined : _ret.readable)\n      } else {\n        ret = makeAsyncIterable(ret)\n      }\n      ret = stream(ret, {\n        signal\n      })\n      if (reading) {\n        if (!isIterable(ret, true)) {\n          throw new ERR_INVALID_RETURN_VALUE('AsyncIterable', `transform[${i - 1}]`, ret)\n        }\n      } else {\n        var _ret2\n        if (!PassThrough) {\n          PassThrough = require('./passthrough')\n        }\n\n        // If the last argument to pipeline is not a stream\n        // we must create a proxy stream so that pipeline(...)\n        // always returns a stream which can be further\n        // composed through `.pipe(stream)`.\n\n        const pt = new PassThrough({\n          objectMode: true\n        })\n\n        // Handle Promises/A+ spec, `then` could be a getter that throws on\n        // second use.\n        const then = (_ret2 = ret) === null || _ret2 === undefined ? undefined : _ret2.then\n        if (typeof then === 'function') {\n          finishCount++\n          then.call(\n            ret,\n            (val) => {\n              value = val\n              if (val != null) {\n                pt.write(val)\n              }\n              if (end) {\n                pt.end()\n              }\n              process.nextTick(finish)\n            },\n            (err) => {\n              pt.destroy(err)\n              process.nextTick(finish, err)\n            }\n          )\n        } else if (isIterable(ret, true)) {\n          finishCount++\n          pumpToNode(ret, pt, finish, {\n            end\n          })\n        } else if (isReadableStream(ret) || isTransformStream(ret)) {\n          const toRead = ret.readable || ret\n          finishCount++\n          pumpToNode(toRead, pt, finish, {\n            end\n          })\n        } else {\n          throw new ERR_INVALID_RETURN_VALUE('AsyncIterable or Promise', 'destination', ret)\n        }\n        ret = pt\n        const { destroy, cleanup } = destroyer(ret, false, true)\n        destroys.push(destroy)\n        if (isLastStream) {\n          lastStreamCleanup.push(cleanup)\n        }\n      }\n    } else if (isNodeStream(stream)) {\n      if (isReadableNodeStream(ret)) {\n        finishCount += 2\n        const cleanup = pipe(ret, stream, finish, {\n          end\n        })\n        if (isReadable(stream) && isLastStream) {\n          lastStreamCleanup.push(cleanup)\n        }\n      } else if (isTransformStream(ret) || isReadableStream(ret)) {\n        const toRead = ret.readable || ret\n        finishCount++\n        pumpToNode(toRead, stream, finish, {\n          end\n        })\n      } else if (isIterable(ret)) {\n        finishCount++\n        pumpToNode(ret, stream, finish, {\n          end\n        })\n      } else {\n        throw new ERR_INVALID_ARG_TYPE(\n          'val',\n          ['Readable', 'Iterable', 'AsyncIterable', 'ReadableStream', 'TransformStream'],\n          ret\n        )\n      }\n      ret = stream\n    } else if (isWebStream(stream)) {\n      if (isReadableNodeStream(ret)) {\n        finishCount++\n        pumpToWeb(makeAsyncIterable(ret), stream, finish, {\n          end\n        })\n      } else if (isReadableStream(ret) || isIterable(ret)) {\n        finishCount++\n        pumpToWeb(ret, stream, finish, {\n          end\n        })\n      } else if (isTransformStream(ret)) {\n        finishCount++\n        pumpToWeb(ret.readable, stream, finish, {\n          end\n        })\n      } else {\n        throw new ERR_INVALID_ARG_TYPE(\n          'val',\n          ['Readable', 'Iterable', 'AsyncIterable', 'ReadableStream', 'TransformStream'],\n          ret\n        )\n      }\n      ret = stream\n    } else {\n      ret = Duplex.from(stream)\n    }\n  }\n  if (\n    (signal !== null && signal !== undefined && signal.aborted) ||\n    (outerSignal !== null && outerSignal !== undefined && outerSignal.aborted)\n  ) {\n    process.nextTick(abort)\n  }\n  return ret\n}\nfunction pipe(src, dst, finish, { end }) {\n  let ended = false\n  dst.on('close', () => {\n    if (!ended) {\n      // Finish if the destination closes before the source has completed.\n      finish(new ERR_STREAM_PREMATURE_CLOSE())\n    }\n  })\n  src.pipe(dst, {\n    end: false\n  }) // If end is true we already will have a listener to end dst.\n\n  if (end) {\n    // Compat. Before node v10.12.0 stdio used to throw an error so\n    // pipe() did/does not end() stdio destinations.\n    // Now they allow it but \"secretly\" don't close the underlying fd.\n\n    function endFn() {\n      ended = true\n      dst.end()\n    }\n    if (isReadableFinished(src)) {\n      // End the destination if the source has already ended.\n      process.nextTick(endFn)\n    } else {\n      src.once('end', endFn)\n    }\n  } else {\n    finish()\n  }\n  eos(\n    src,\n    {\n      readable: true,\n      writable: false\n    },\n    (err) => {\n      const rState = src._readableState\n      if (\n        err &&\n        err.code === 'ERR_STREAM_PREMATURE_CLOSE' &&\n        rState &&\n        rState.ended &&\n        !rState.errored &&\n        !rState.errorEmitted\n      ) {\n        // Some readable streams will emit 'close' before 'end'. However, since\n        // this is on the readable side 'end' should still be emitted if the\n        // stream has been ended and no error emitted. This should be allowed in\n        // favor of backwards compatibility. Since the stream is piped to a\n        // destination this should not result in any observable difference.\n        // We don't need to check if this is a writable premature close since\n        // eos will only fail with premature close on the reading side for\n        // duplex streams.\n        src.once('end', finish).once('error', finish)\n      } else {\n        finish(err)\n      }\n    }\n  )\n  return eos(\n    dst,\n    {\n      readable: false,\n      writable: true\n    },\n    finish\n  )\n}\nmodule.exports = {\n  pipelineImpl,\n  pipeline\n}\n"], "names": [], "mappings": "AAAA,qBAAqB,GAErB,MAAM;;;;;;;;;GAAkB;AAMtB;AACF,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,mBAAmB,EAAE,aAAa,EAAE;AACnE,MAAM;AACN,MAAM,EAAE,IAAI,EAAE;AACd,MAAM;AACN,MAAM;AACN,MAAM,EACJ,kBAAkB,EAClB,OAAO,EACL,oBAAoB,EACpB,wBAAwB,EACxB,gBAAgB,EAChB,oBAAoB,EACpB,0BAA0B,EAC3B,EACD,UAAU,EACX;AACD,MAAM,EAAE,gBAAgB,EAAE,mBAAmB,EAAE;AAC/C,MAAM,EACJ,UAAU,EACV,UAAU,EACV,oBAAoB,EACpB,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,gBAAgB,EAChB,kBAAkB,EACnB;AACD,MAAM,kBAAkB,WAAW,eAAe,IAAI,qHAA4B,eAAe;AACjG,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,UAAU,MAAM,EAAE,OAAO,EAAE,OAAO;IACzC,IAAI,WAAW;IACf,OAAO,EAAE,CAAC,SAAS;QACjB,WAAW;IACb;IACA,MAAM,UAAU,IACd,QACA;QACE,UAAU;QACV,UAAU;IACZ,GACA,CAAC;QACC,WAAW,CAAC;IACd;IAEF,OAAO;QACL,SAAS,CAAC;YACR,IAAI,UAAU;YACd,WAAW;YACX,YAAY,SAAS,CAAC,QAAQ,OAAO,IAAI,qBAAqB;QAChE;QACA;IACF;AACF;AACA,SAAS,YAAY,OAAO;IAC1B,4EAA4E;IAC5E,sEAAsE;IACtE,qCAAqC;IACrC,iBAAiB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,EAAE;IAC9C,OAAO,QAAQ,GAAG;AACpB;AACA,SAAS,kBAAkB,GAAG;IAC5B,IAAI,WAAW,MAAM;QACnB,OAAO;IACT,OAAO,IAAI,qBAAqB,MAAM;QACpC,mCAAmC;QACnC,OAAO,aAAa;IACtB;IACA,MAAM,IAAI,qBAAqB,OAAO;QAAC;QAAY;QAAY;KAAgB,EAAE;AACnF;AACA,gBAAgB,aAAa,GAAG;IAC9B,IAAI,CAAC,UAAU;QACb;IACF;IACA,OAAO,SAAS,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC;AACtD;AACA,eAAe,WAAW,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE;IAC3D,IAAI;IACJ,IAAI,YAAY;IAChB,MAAM,SAAS,CAAC;QACd,IAAI,KAAK;YACP,QAAQ;QACV;QACA,IAAI,WAAW;YACb,MAAM,WAAW;YACjB,YAAY;YACZ;QACF;IACF;IACA,MAAM,OAAO,IACX,IAAI,QAAQ,CAAC,SAAS;YACpB,IAAI,OAAO;gBACT,OAAO;YACT,OAAO;gBACL,YAAY;oBACV,IAAI,OAAO;wBACT,OAAO;oBACT,OAAO;wBACL;oBACF;gBACF;YACF;QACF;IACF,SAAS,EAAE,CAAC,SAAS;IACrB,MAAM,UAAU,IACd,UACA;QACE,UAAU;IACZ,GACA;IAEF,IAAI;QACF,IAAI,SAAS,iBAAiB,EAAE;YAC9B,MAAM;QACR;QACA,WAAW,MAAM,SAAS,SAAU;YAClC,IAAI,CAAC,SAAS,KAAK,CAAC,QAAQ;gBAC1B,MAAM;YACR;QACF;QACA,IAAI,KAAK;YACP,SAAS,GAAG;YACZ,MAAM;QACR;QACA;IACF,EAAE,OAAO,KAAK;QACZ,OAAO,UAAU,MAAM,mBAAmB,OAAO,OAAO;IAC1D,SAAU;QACR;QACA,SAAS,GAAG,CAAC,SAAS;IACxB;AACF;AACA,eAAe,UAAU,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE;IAC1D,IAAI,kBAAkB,WAAW;QAC/B,WAAW,SAAS,QAAQ;IAC9B;IACA,0EAA0E;IAC1E,MAAM,SAAS,SAAS,SAAS;IACjC,IAAI;QACF,WAAW,MAAM,SAAS,SAAU;YAClC,MAAM,OAAO,KAAK;YAClB,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,KAAO;QACnC;QACA,MAAM,OAAO,KAAK;QAClB,IAAI,KAAK;YACP,MAAM,OAAO,KAAK;QACpB;QACA;IACF,EAAE,OAAO,KAAK;QACZ,IAAI;YACF,MAAM,OAAO,KAAK,CAAC;YACnB,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,OAAO;QACT;IACF;AACF;AACA,SAAS,SAAS,GAAG,OAAO;IAC1B,OAAO,aAAa,SAAS,KAAK,YAAY;AAChD;AACA,SAAS,aAAa,OAAO,EAAE,QAAQ,EAAE,IAAI;IAC3C,IAAI,QAAQ,MAAM,KAAK,KAAK,aAAa,OAAO,CAAC,EAAE,GAAG;QACpD,UAAU,OAAO,CAAC,EAAE;IACtB;IACA,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,MAAM,IAAI,iBAAiB;IAC7B;IACA,MAAM,KAAK,IAAI;IACf,MAAM,SAAS,GAAG,MAAM;IACxB,MAAM,cAAc,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,MAAM;IAEjF,6DAA6D;IAC7D,8CAA8C;IAC9C,MAAM,oBAAoB,EAAE;IAC5B,oBAAoB,aAAa;IACjC,SAAS;QACP,WAAW,IAAI;IACjB;IACA,mBAAmB,oBAAoB,4GAA2B,gBAAgB;IAClF,IAAI;IACJ,IAAI,aAAa;QACf,aAAa,iBAAiB,aAAa;IAC7C;IACA,IAAI;IACJ,IAAI;IACJ,MAAM,WAAW,EAAE;IACnB,IAAI,cAAc;IAClB,SAAS,OAAO,GAAG;QACjB,WAAW,KAAK,EAAE,gBAAgB;IACpC;IACA,SAAS,WAAW,GAAG,EAAE,KAAK;QAC5B,IAAI;QACJ,IAAI,OAAO,CAAC,CAAC,SAAS,MAAM,IAAI,KAAK,4BAA4B,GAAG;YAClE,QAAQ;QACV;QACA,IAAI,CAAC,SAAS,CAAC,OAAO;YACpB;QACF;QACA,MAAO,SAAS,MAAM,CAAE;YACtB,SAAS,KAAK,GAAG;QACnB;;QACC,CAAC,cAAc,UAAU,MAAM,QAAQ,gBAAgB,YAAY,YAAY,WAAW,CAAC,cAAc;QAC1G,GAAG,KAAK;QACR,IAAI,OAAO;YACT,IAAI,CAAC,OAAO;gBACV,kBAAkB,OAAO,CAAC,CAAC,KAAO;YACpC;YACA,QAAQ,QAAQ,CAAC,UAAU,OAAO;QACpC;IACF;IACA,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,SAAS,OAAO,CAAC,EAAE;QACzB,MAAM,UAAU,IAAI,QAAQ,MAAM,GAAG;QACrC,MAAM,UAAU,IAAI;QACpB,MAAM,MAAM,WAAW,CAAC,SAAS,QAAQ,SAAS,YAAY,YAAY,KAAK,GAAG,MAAM;QACxF,MAAM,eAAe,MAAM,QAAQ,MAAM,GAAG;QAC5C,IAAI,aAAa,SAAS;YACxB,IAAI,KAAK;gBACP,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,UAAU,QAAQ,SAAS;gBACxD,SAAS,IAAI,CAAC;gBACd,IAAI,WAAW,WAAW,cAAc;oBACtC,kBAAkB,IAAI,CAAC;gBACzB;YACF;YAEA,gEAAgE;YAChE,SAAS,QAAQ,GAAG;gBAClB,IAAI,OAAO,IAAI,IAAI,KAAK,gBAAgB,IAAI,IAAI,KAAK,8BAA8B;oBACjF,OAAO;gBACT;YACF;YACA,OAAO,EAAE,CAAC,SAAS;YACnB,IAAI,WAAW,WAAW,cAAc;gBACtC,kBAAkB,IAAI,CAAC;oBACrB,OAAO,cAAc,CAAC,SAAS;gBACjC;YACF;QACF;QACA,IAAI,MAAM,GAAG;YACX,IAAI,OAAO,WAAW,YAAY;gBAChC,MAAM,OAAO;oBACX;gBACF;gBACA,IAAI,CAAC,WAAW,MAAM;oBACpB,MAAM,IAAI,yBAAyB,qCAAqC,UAAU;gBACpF;YACF,OAAO,IAAI,WAAW,WAAW,qBAAqB,WAAW,kBAAkB,SAAS;gBAC1F,MAAM;YACR,OAAO;gBACL,MAAM,OAAO,IAAI,CAAC;YACpB;QACF,OAAO,IAAI,OAAO,WAAW,YAAY;YACvC,IAAI,kBAAkB,MAAM;gBAC1B,IAAI;gBACJ,MAAM,kBAAkB,CAAC,OAAO,GAAG,MAAM,QAAQ,SAAS,YAAY,YAAY,KAAK,QAAQ;YACjG,OAAO;gBACL,MAAM,kBAAkB;YAC1B;YACA,MAAM,OAAO,KAAK;gBAChB;YACF;YACA,IAAI,SAAS;gBACX,IAAI,CAAC,WAAW,KAAK,OAAO;oBAC1B,MAAM,IAAI,yBAAyB,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;gBAC7E;YACF,OAAO;gBACL,IAAI;gBACJ,IAAI,CAAC,aAAa;oBAChB;gBACF;gBAEA,mDAAmD;gBACnD,sDAAsD;gBACtD,+CAA+C;gBAC/C,oCAAoC;gBAEpC,MAAM,KAAK,IAAI,YAAY;oBACzB,YAAY;gBACd;gBAEA,mEAAmE;gBACnE,cAAc;gBACd,MAAM,OAAO,CAAC,QAAQ,GAAG,MAAM,QAAQ,UAAU,YAAY,YAAY,MAAM,IAAI;gBACnF,IAAI,OAAO,SAAS,YAAY;oBAC9B;oBACA,KAAK,IAAI,CACP,KACA,CAAC;wBACC,QAAQ;wBACR,IAAI,OAAO,MAAM;4BACf,GAAG,KAAK,CAAC;wBACX;wBACA,IAAI,KAAK;4BACP,GAAG,GAAG;wBACR;wBACA,QAAQ,QAAQ,CAAC;oBACnB,GACA,CAAC;wBACC,GAAG,OAAO,CAAC;wBACX,QAAQ,QAAQ,CAAC,QAAQ;oBAC3B;gBAEJ,OAAO,IAAI,WAAW,KAAK,OAAO;oBAChC;oBACA,WAAW,KAAK,IAAI,QAAQ;wBAC1B;oBACF;gBACF,OAAO,IAAI,iBAAiB,QAAQ,kBAAkB,MAAM;oBAC1D,MAAM,SAAS,IAAI,QAAQ,IAAI;oBAC/B;oBACA,WAAW,QAAQ,IAAI,QAAQ;wBAC7B;oBACF;gBACF,OAAO;oBACL,MAAM,IAAI,yBAAyB,4BAA4B,eAAe;gBAChF;gBACA,MAAM;gBACN,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,UAAU,KAAK,OAAO;gBACnD,SAAS,IAAI,CAAC;gBACd,IAAI,cAAc;oBAChB,kBAAkB,IAAI,CAAC;gBACzB;YACF;QACF,OAAO,IAAI,aAAa,SAAS;YAC/B,IAAI,qBAAqB,MAAM;gBAC7B,eAAe;gBACf,MAAM,UAAU,KAAK,KAAK,QAAQ,QAAQ;oBACxC;gBACF;gBACA,IAAI,WAAW,WAAW,cAAc;oBACtC,kBAAkB,IAAI,CAAC;gBACzB;YACF,OAAO,IAAI,kBAAkB,QAAQ,iBAAiB,MAAM;gBAC1D,MAAM,SAAS,IAAI,QAAQ,IAAI;gBAC/B;gBACA,WAAW,QAAQ,QAAQ,QAAQ;oBACjC;gBACF;YACF,OAAO,IAAI,WAAW,MAAM;gBAC1B;gBACA,WAAW,KAAK,QAAQ,QAAQ;oBAC9B;gBACF;YACF,OAAO;gBACL,MAAM,IAAI,qBACR,OACA;oBAAC;oBAAY;oBAAY;oBAAiB;oBAAkB;iBAAkB,EAC9E;YAEJ;YACA,MAAM;QACR,OAAO,IAAI,YAAY,SAAS;YAC9B,IAAI,qBAAqB,MAAM;gBAC7B;gBACA,UAAU,kBAAkB,MAAM,QAAQ,QAAQ;oBAChD;gBACF;YACF,OAAO,IAAI,iBAAiB,QAAQ,WAAW,MAAM;gBACnD;gBACA,UAAU,KAAK,QAAQ,QAAQ;oBAC7B;gBACF;YACF,OAAO,IAAI,kBAAkB,MAAM;gBACjC;gBACA,UAAU,IAAI,QAAQ,EAAE,QAAQ,QAAQ;oBACtC;gBACF;YACF,OAAO;gBACL,MAAM,IAAI,qBACR,OACA;oBAAC;oBAAY;oBAAY;oBAAiB;oBAAkB;iBAAkB,EAC9E;YAEJ;YACA,MAAM;QACR,OAAO;YACL,MAAM,OAAO,IAAI,CAAC;QACpB;IACF;IACA,IACE,AAAC,WAAW,QAAQ,WAAW,aAAa,OAAO,OAAO,IACzD,gBAAgB,QAAQ,gBAAgB,aAAa,YAAY,OAAO,EACzE;QACA,QAAQ,QAAQ,CAAC;IACnB;IACA,OAAO;AACT;AACA,SAAS,KAAK,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE;IACrC,IAAI,QAAQ;IACZ,IAAI,EAAE,CAAC,SAAS;QACd,IAAI,CAAC,OAAO;YACV,oEAAoE;YACpE,OAAO,IAAI;QACb;IACF;IACA,IAAI,IAAI,CAAC,KAAK;QACZ,KAAK;IACP,IAAG,6DAA6D;IAEhE,IAAI,KAAK;QACP,+DAA+D;QAC/D,gDAAgD;QAChD,kEAAkE;QAElE,SAAS;YACP,QAAQ;YACR,IAAI,GAAG;QACT;QACA,IAAI,mBAAmB,MAAM;YAC3B,uDAAuD;YACvD,QAAQ,QAAQ,CAAC;QACnB,OAAO;YACL,IAAI,IAAI,CAAC,OAAO;QAClB;IACF,OAAO;QACL;IACF;IACA,IACE,KACA;QACE,UAAU;QACV,UAAU;IACZ,GACA,CAAC;QACC,MAAM,SAAS,IAAI,cAAc;QACjC,IACE,OACA,IAAI,IAAI,KAAK,gCACb,UACA,OAAO,KAAK,IACZ,CAAC,OAAO,OAAO,IACf,CAAC,OAAO,YAAY,EACpB;YACA,uEAAuE;YACvE,oEAAoE;YACpE,wEAAwE;YACxE,mEAAmE;YACnE,mEAAmE;YACnE,qEAAqE;YACrE,kEAAkE;YAClE,kBAAkB;YAClB,IAAI,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,SAAS;QACxC,OAAO;YACL,OAAO;QACT;IACF;IAEF,OAAO,IACL,KACA;QACE,UAAU;QACV,UAAU;IACZ,GACA;AAEJ;AACA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/compose.js"], "sourcesContent": ["'use strict'\n\nconst { pipeline } = require('./pipeline')\nconst Duplex = require('./duplex')\nconst { destroyer } = require('./destroy')\nconst {\n  isNodeStream,\n  isReadable,\n  isWritable,\n  isWebStream,\n  isTransformStream,\n  isWritableStream,\n  isReadableStream\n} = require('./utils')\nconst {\n  AbortError,\n  codes: { ERR_INVALID_ARG_VALUE, ERR_MISSING_ARGS }\n} = require('../../ours/errors')\nconst eos = require('./end-of-stream')\nmodule.exports = function compose(...streams) {\n  if (streams.length === 0) {\n    throw new ERR_MISSING_ARGS('streams')\n  }\n  if (streams.length === 1) {\n    return Duplex.from(streams[0])\n  }\n  const orgStreams = [...streams]\n  if (typeof streams[0] === 'function') {\n    streams[0] = Duplex.from(streams[0])\n  }\n  if (typeof streams[streams.length - 1] === 'function') {\n    const idx = streams.length - 1\n    streams[idx] = Duplex.from(streams[idx])\n  }\n  for (let n = 0; n < streams.length; ++n) {\n    if (!isNodeStream(streams[n]) && !isWebStream(streams[n])) {\n      // TODO(ronag): Add checks for non streams.\n      continue\n    }\n    if (\n      n < streams.length - 1 &&\n      !(isReadable(streams[n]) || isReadableStream(streams[n]) || isTransformStream(streams[n]))\n    ) {\n      throw new ERR_INVALID_ARG_VALUE(`streams[${n}]`, orgStreams[n], 'must be readable')\n    }\n    if (n > 0 && !(isWritable(streams[n]) || isWritableStream(streams[n]) || isTransformStream(streams[n]))) {\n      throw new ERR_INVALID_ARG_VALUE(`streams[${n}]`, orgStreams[n], 'must be writable')\n    }\n  }\n  let ondrain\n  let onfinish\n  let onreadable\n  let onclose\n  let d\n  function onfinished(err) {\n    const cb = onclose\n    onclose = null\n    if (cb) {\n      cb(err)\n    } else if (err) {\n      d.destroy(err)\n    } else if (!readable && !writable) {\n      d.destroy()\n    }\n  }\n  const head = streams[0]\n  const tail = pipeline(streams, onfinished)\n  const writable = !!(isWritable(head) || isWritableStream(head) || isTransformStream(head))\n  const readable = !!(isReadable(tail) || isReadableStream(tail) || isTransformStream(tail))\n\n  // TODO(ronag): Avoid double buffering.\n  // Implement Writable/Readable/Duplex traits.\n  // See, https://github.com/nodejs/node/pull/33515.\n  d = new Duplex({\n    // TODO (ronag): highWaterMark?\n    writableObjectMode: !!(head !== null && head !== undefined && head.writableObjectMode),\n    readableObjectMode: !!(tail !== null && tail !== undefined && tail.readableObjectMode),\n    writable,\n    readable\n  })\n  if (writable) {\n    if (isNodeStream(head)) {\n      d._write = function (chunk, encoding, callback) {\n        if (head.write(chunk, encoding)) {\n          callback()\n        } else {\n          ondrain = callback\n        }\n      }\n      d._final = function (callback) {\n        head.end()\n        onfinish = callback\n      }\n      head.on('drain', function () {\n        if (ondrain) {\n          const cb = ondrain\n          ondrain = null\n          cb()\n        }\n      })\n    } else if (isWebStream(head)) {\n      const writable = isTransformStream(head) ? head.writable : head\n      const writer = writable.getWriter()\n      d._write = async function (chunk, encoding, callback) {\n        try {\n          await writer.ready\n          writer.write(chunk).catch(() => {})\n          callback()\n        } catch (err) {\n          callback(err)\n        }\n      }\n      d._final = async function (callback) {\n        try {\n          await writer.ready\n          writer.close().catch(() => {})\n          onfinish = callback\n        } catch (err) {\n          callback(err)\n        }\n      }\n    }\n    const toRead = isTransformStream(tail) ? tail.readable : tail\n    eos(toRead, () => {\n      if (onfinish) {\n        const cb = onfinish\n        onfinish = null\n        cb()\n      }\n    })\n  }\n  if (readable) {\n    if (isNodeStream(tail)) {\n      tail.on('readable', function () {\n        if (onreadable) {\n          const cb = onreadable\n          onreadable = null\n          cb()\n        }\n      })\n      tail.on('end', function () {\n        d.push(null)\n      })\n      d._read = function () {\n        while (true) {\n          const buf = tail.read()\n          if (buf === null) {\n            onreadable = d._read\n            return\n          }\n          if (!d.push(buf)) {\n            return\n          }\n        }\n      }\n    } else if (isWebStream(tail)) {\n      const readable = isTransformStream(tail) ? tail.readable : tail\n      const reader = readable.getReader()\n      d._read = async function () {\n        while (true) {\n          try {\n            const { value, done } = await reader.read()\n            if (!d.push(value)) {\n              return\n            }\n            if (done) {\n              d.push(null)\n              return\n            }\n          } catch {\n            return\n          }\n        }\n      }\n    }\n  }\n  d._destroy = function (err, callback) {\n    if (!err && onclose !== null) {\n      err = new AbortError()\n    }\n    onreadable = null\n    ondrain = null\n    onfinish = null\n    if (onclose === null) {\n      callback(err)\n    } else {\n      onclose = callback\n      if (isNodeStream(tail)) {\n        destroyer(tail, err)\n      }\n    }\n  }\n  return d\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,QAAQ,EAAE;AAClB,MAAM;AACN,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM,EACJ,YAAY,EACZ,UAAU,EACV,UAAU,EACV,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EACjB;AACD,MAAM,EACJ,UAAU,EACV,OAAO,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,EACnD;AACD,MAAM;AACN,OAAO,OAAO,GAAG,SAAS,QAAQ,GAAG,OAAO;IAC1C,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,MAAM,IAAI,iBAAiB;IAC7B;IACA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IAC/B;IACA,MAAM,aAAa;WAAI;KAAQ;IAC/B,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,YAAY;QACpC,OAAO,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACrC;IACA,IAAI,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,KAAK,YAAY;QACrD,MAAM,MAAM,QAAQ,MAAM,GAAG;QAC7B,OAAO,CAAC,IAAI,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IACzC;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;QACvC,IAAI,CAAC,aAAa,OAAO,CAAC,EAAE,KAAK,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG;YAEzD;QACF;QACA,IACE,IAAI,QAAQ,MAAM,GAAG,KACrB,CAAC,CAAC,WAAW,OAAO,CAAC,EAAE,KAAK,iBAAiB,OAAO,CAAC,EAAE,KAAK,kBAAkB,OAAO,CAAC,EAAE,CAAC,GACzF;YACA,MAAM,IAAI,sBAAsB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE;QAClE;QACA,IAAI,IAAI,KAAK,CAAC,CAAC,WAAW,OAAO,CAAC,EAAE,KAAK,iBAAiB,OAAO,CAAC,EAAE,KAAK,kBAAkB,OAAO,CAAC,EAAE,CAAC,GAAG;YACvG,MAAM,IAAI,sBAAsB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,EAAE;QAClE;IACF;IACA,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS,WAAW,GAAG;QACrB,MAAM,KAAK;QACX,UAAU;QACV,IAAI,IAAI;YACN,GAAG;QACL,OAAO,IAAI,KAAK;YACd,EAAE,OAAO,CAAC;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU;YACjC,EAAE,OAAO;QACX;IACF;IACA,MAAM,OAAO,OAAO,CAAC,EAAE;IACvB,MAAM,OAAO,SAAS,SAAS;IAC/B,MAAM,WAAW,CAAC,CAAC,CAAC,WAAW,SAAS,iBAAiB,SAAS,kBAAkB,KAAK;IACzF,MAAM,WAAW,CAAC,CAAC,CAAC,WAAW,SAAS,iBAAiB,SAAS,kBAAkB,KAAK;IAEzF,uCAAuC;IACvC,6CAA6C;IAC7C,kDAAkD;IAClD,IAAI,IAAI,OAAO;QACb,+BAA+B;QAC/B,oBAAoB,CAAC,CAAC,CAAC,SAAS,QAAQ,SAAS,aAAa,KAAK,kBAAkB;QACrF,oBAAoB,CAAC,CAAC,CAAC,SAAS,QAAQ,SAAS,aAAa,KAAK,kBAAkB;QACrF;QACA;IACF;IACA,IAAI,UAAU;QACZ,IAAI,aAAa,OAAO;YACtB,EAAE,MAAM,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ;gBAC5C,IAAI,KAAK,KAAK,CAAC,OAAO,WAAW;oBAC/B;gBACF,OAAO;oBACL,UAAU;gBACZ;YACF;YACA,EAAE,MAAM,GAAG,SAAU,QAAQ;gBAC3B,KAAK,GAAG;gBACR,WAAW;YACb;YACA,KAAK,EAAE,CAAC,SAAS;gBACf,IAAI,SAAS;oBACX,MAAM,KAAK;oBACX,UAAU;oBACV;gBACF;YACF;QACF,OAAO,IAAI,YAAY,OAAO;YAC5B,MAAM,WAAW,kBAAkB,QAAQ,KAAK,QAAQ,GAAG;YAC3D,MAAM,SAAS,SAAS,SAAS;YACjC,EAAE,MAAM,GAAG,eAAgB,KAAK,EAAE,QAAQ,EAAE,QAAQ;gBAClD,IAAI;oBACF,MAAM,OAAO,KAAK;oBAClB,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,KAAO;oBACjC;gBACF,EAAE,OAAO,KAAK;oBACZ,SAAS;gBACX;YACF;YACA,EAAE,MAAM,GAAG,eAAgB,QAAQ;gBACjC,IAAI;oBACF,MAAM,OAAO,KAAK;oBAClB,OAAO,KAAK,GAAG,KAAK,CAAC,KAAO;oBAC5B,WAAW;gBACb,EAAE,OAAO,KAAK;oBACZ,SAAS;gBACX;YACF;QACF;QACA,MAAM,SAAS,kBAAkB,QAAQ,KAAK,QAAQ,GAAG;QACzD,IAAI,QAAQ;YACV,IAAI,UAAU;gBACZ,MAAM,KAAK;gBACX,WAAW;gBACX;YACF;QACF;IACF;IACA,IAAI,UAAU;QACZ,IAAI,aAAa,OAAO;YACtB,KAAK,EAAE,CAAC,YAAY;gBAClB,IAAI,YAAY;oBACd,MAAM,KAAK;oBACX,aAAa;oBACb;gBACF;YACF;YACA,KAAK,EAAE,CAAC,OAAO;gBACb,EAAE,IAAI,CAAC;YACT;YACA,EAAE,KAAK,GAAG;gBACR,MAAO,KAAM;oBACX,MAAM,MAAM,KAAK,IAAI;oBACrB,IAAI,QAAQ,MAAM;wBAChB,aAAa,EAAE,KAAK;wBACpB;oBACF;oBACA,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM;wBAChB;oBACF;gBACF;YACF;QACF,OAAO,IAAI,YAAY,OAAO;YAC5B,MAAM,WAAW,kBAAkB,QAAQ,KAAK,QAAQ,GAAG;YAC3D,MAAM,SAAS,SAAS,SAAS;YACjC,EAAE,KAAK,GAAG;gBACR,MAAO,KAAM;oBACX,IAAI;wBACF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,OAAO,IAAI;wBACzC,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ;4BAClB;wBACF;wBACA,IAAI,MAAM;4BACR,EAAE,IAAI,CAAC;4BACP;wBACF;oBACF,EAAE,OAAM;wBACN;oBACF;gBACF;YACF;QACF;IACF;IACA,EAAE,QAAQ,GAAG,SAAU,GAAG,EAAE,QAAQ;QAClC,IAAI,CAAC,OAAO,YAAY,MAAM;YAC5B,MAAM,IAAI;QACZ;QACA,aAAa;QACb,UAAU;QACV,WAAW;QACX,IAAI,YAAY,MAAM;YACpB,SAAS;QACX,OAAO;YACL,UAAU;YACV,IAAI,aAAa,OAAO;gBACtB,UAAU,MAAM;YAClB;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7413, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/internal/streams/operators.js"], "sourcesContent": ["'use strict'\n\nconst AbortController = globalThis.AbortController || require('abort-controller').AbortController\nconst {\n  codes: { ERR_INVALID_ARG_VALUE, ERR_INVALID_ARG_TYPE, ERR_MISSING_ARGS, ERR_OUT_OF_RANGE },\n  AbortError\n} = require('../../ours/errors')\nconst { validateAbortSignal, validateInteger, validateObject } = require('../validators')\nconst kWeakHandler = require('../../ours/primordials').Symbol('kWeak')\nconst kResistStopPropagation = require('../../ours/primordials').Symbol('kResistStopPropagation')\nconst { finished } = require('./end-of-stream')\nconst staticCompose = require('./compose')\nconst { addAbortSignalNoValidate } = require('./add-abort-signal')\nconst { isWritable, isNodeStream } = require('./utils')\nconst { deprecate } = require('../../ours/util')\nconst {\n  ArrayPrototypePush,\n  Boolean,\n  MathFloor,\n  Number,\n  NumberIsNaN,\n  Promise,\n  PromiseReject,\n  PromiseResolve,\n  PromisePrototypeThen,\n  Symbol\n} = require('../../ours/primordials')\nconst kEmpty = Symbol('kEmpty')\nconst kEof = Symbol('kEof')\nfunction compose(stream, options) {\n  if (options != null) {\n    validateObject(options, 'options')\n  }\n  if ((options === null || options === undefined ? undefined : options.signal) != null) {\n    validateAbortSignal(options.signal, 'options.signal')\n  }\n  if (isNodeStream(stream) && !isWritable(stream)) {\n    throw new ERR_INVALID_ARG_VALUE('stream', stream, 'must be writable')\n  }\n  const composedStream = staticCompose(this, stream)\n  if (options !== null && options !== undefined && options.signal) {\n    // Not validating as we already validated before\n    addAbortSignalNoValidate(options.signal, composedStream)\n  }\n  return composedStream\n}\nfunction map(fn, options) {\n  if (typeof fn !== 'function') {\n    throw new ERR_INVALID_ARG_TYPE('fn', ['Function', 'AsyncFunction'], fn)\n  }\n  if (options != null) {\n    validateObject(options, 'options')\n  }\n  if ((options === null || options === undefined ? undefined : options.signal) != null) {\n    validateAbortSignal(options.signal, 'options.signal')\n  }\n  let concurrency = 1\n  if ((options === null || options === undefined ? undefined : options.concurrency) != null) {\n    concurrency = MathFloor(options.concurrency)\n  }\n  let highWaterMark = concurrency - 1\n  if ((options === null || options === undefined ? undefined : options.highWaterMark) != null) {\n    highWaterMark = MathFloor(options.highWaterMark)\n  }\n  validateInteger(concurrency, 'options.concurrency', 1)\n  validateInteger(highWaterMark, 'options.highWaterMark', 0)\n  highWaterMark += concurrency\n  return async function* map() {\n    const signal = require('../../ours/util').AbortSignalAny(\n      [options === null || options === undefined ? undefined : options.signal].filter(Boolean)\n    )\n    const stream = this\n    const queue = []\n    const signalOpt = {\n      signal\n    }\n    let next\n    let resume\n    let done = false\n    let cnt = 0\n    function onCatch() {\n      done = true\n      afterItemProcessed()\n    }\n    function afterItemProcessed() {\n      cnt -= 1\n      maybeResume()\n    }\n    function maybeResume() {\n      if (resume && !done && cnt < concurrency && queue.length < highWaterMark) {\n        resume()\n        resume = null\n      }\n    }\n    async function pump() {\n      try {\n        for await (let val of stream) {\n          if (done) {\n            return\n          }\n          if (signal.aborted) {\n            throw new AbortError()\n          }\n          try {\n            val = fn(val, signalOpt)\n            if (val === kEmpty) {\n              continue\n            }\n            val = PromiseResolve(val)\n          } catch (err) {\n            val = PromiseReject(err)\n          }\n          cnt += 1\n          PromisePrototypeThen(val, afterItemProcessed, onCatch)\n          queue.push(val)\n          if (next) {\n            next()\n            next = null\n          }\n          if (!done && (queue.length >= highWaterMark || cnt >= concurrency)) {\n            await new Promise((resolve) => {\n              resume = resolve\n            })\n          }\n        }\n        queue.push(kEof)\n      } catch (err) {\n        const val = PromiseReject(err)\n        PromisePrototypeThen(val, afterItemProcessed, onCatch)\n        queue.push(val)\n      } finally {\n        done = true\n        if (next) {\n          next()\n          next = null\n        }\n      }\n    }\n    pump()\n    try {\n      while (true) {\n        while (queue.length > 0) {\n          const val = await queue[0]\n          if (val === kEof) {\n            return\n          }\n          if (signal.aborted) {\n            throw new AbortError()\n          }\n          if (val !== kEmpty) {\n            yield val\n          }\n          queue.shift()\n          maybeResume()\n        }\n        await new Promise((resolve) => {\n          next = resolve\n        })\n      }\n    } finally {\n      done = true\n      if (resume) {\n        resume()\n        resume = null\n      }\n    }\n  }.call(this)\n}\nfunction asIndexedPairs(options = undefined) {\n  if (options != null) {\n    validateObject(options, 'options')\n  }\n  if ((options === null || options === undefined ? undefined : options.signal) != null) {\n    validateAbortSignal(options.signal, 'options.signal')\n  }\n  return async function* asIndexedPairs() {\n    let index = 0\n    for await (const val of this) {\n      var _options$signal\n      if (\n        options !== null &&\n        options !== undefined &&\n        (_options$signal = options.signal) !== null &&\n        _options$signal !== undefined &&\n        _options$signal.aborted\n      ) {\n        throw new AbortError({\n          cause: options.signal.reason\n        })\n      }\n      yield [index++, val]\n    }\n  }.call(this)\n}\nasync function some(fn, options = undefined) {\n  for await (const unused of filter.call(this, fn, options)) {\n    return true\n  }\n  return false\n}\nasync function every(fn, options = undefined) {\n  if (typeof fn !== 'function') {\n    throw new ERR_INVALID_ARG_TYPE('fn', ['Function', 'AsyncFunction'], fn)\n  }\n  // https://en.wikipedia.org/wiki/De_Morgan%27s_laws\n  return !(await some.call(\n    this,\n    async (...args) => {\n      return !(await fn(...args))\n    },\n    options\n  ))\n}\nasync function find(fn, options) {\n  for await (const result of filter.call(this, fn, options)) {\n    return result\n  }\n  return undefined\n}\nasync function forEach(fn, options) {\n  if (typeof fn !== 'function') {\n    throw new ERR_INVALID_ARG_TYPE('fn', ['Function', 'AsyncFunction'], fn)\n  }\n  async function forEachFn(value, options) {\n    await fn(value, options)\n    return kEmpty\n  }\n  // eslint-disable-next-line no-unused-vars\n  for await (const unused of map.call(this, forEachFn, options));\n}\nfunction filter(fn, options) {\n  if (typeof fn !== 'function') {\n    throw new ERR_INVALID_ARG_TYPE('fn', ['Function', 'AsyncFunction'], fn)\n  }\n  async function filterFn(value, options) {\n    if (await fn(value, options)) {\n      return value\n    }\n    return kEmpty\n  }\n  return map.call(this, filterFn, options)\n}\n\n// Specific to provide better error to reduce since the argument is only\n// missing if the stream has no items in it - but the code is still appropriate\nclass ReduceAwareErrMissingArgs extends ERR_MISSING_ARGS {\n  constructor() {\n    super('reduce')\n    this.message = 'Reduce of an empty stream requires an initial value'\n  }\n}\nasync function reduce(reducer, initialValue, options) {\n  var _options$signal2\n  if (typeof reducer !== 'function') {\n    throw new ERR_INVALID_ARG_TYPE('reducer', ['Function', 'AsyncFunction'], reducer)\n  }\n  if (options != null) {\n    validateObject(options, 'options')\n  }\n  if ((options === null || options === undefined ? undefined : options.signal) != null) {\n    validateAbortSignal(options.signal, 'options.signal')\n  }\n  let hasInitialValue = arguments.length > 1\n  if (\n    options !== null &&\n    options !== undefined &&\n    (_options$signal2 = options.signal) !== null &&\n    _options$signal2 !== undefined &&\n    _options$signal2.aborted\n  ) {\n    const err = new AbortError(undefined, {\n      cause: options.signal.reason\n    })\n    this.once('error', () => {}) // The error is already propagated\n    await finished(this.destroy(err))\n    throw err\n  }\n  const ac = new AbortController()\n  const signal = ac.signal\n  if (options !== null && options !== undefined && options.signal) {\n    const opts = {\n      once: true,\n      [kWeakHandler]: this,\n      [kResistStopPropagation]: true\n    }\n    options.signal.addEventListener('abort', () => ac.abort(), opts)\n  }\n  let gotAnyItemFromStream = false\n  try {\n    for await (const value of this) {\n      var _options$signal3\n      gotAnyItemFromStream = true\n      if (\n        options !== null &&\n        options !== undefined &&\n        (_options$signal3 = options.signal) !== null &&\n        _options$signal3 !== undefined &&\n        _options$signal3.aborted\n      ) {\n        throw new AbortError()\n      }\n      if (!hasInitialValue) {\n        initialValue = value\n        hasInitialValue = true\n      } else {\n        initialValue = await reducer(initialValue, value, {\n          signal\n        })\n      }\n    }\n    if (!gotAnyItemFromStream && !hasInitialValue) {\n      throw new ReduceAwareErrMissingArgs()\n    }\n  } finally {\n    ac.abort()\n  }\n  return initialValue\n}\nasync function toArray(options) {\n  if (options != null) {\n    validateObject(options, 'options')\n  }\n  if ((options === null || options === undefined ? undefined : options.signal) != null) {\n    validateAbortSignal(options.signal, 'options.signal')\n  }\n  const result = []\n  for await (const val of this) {\n    var _options$signal4\n    if (\n      options !== null &&\n      options !== undefined &&\n      (_options$signal4 = options.signal) !== null &&\n      _options$signal4 !== undefined &&\n      _options$signal4.aborted\n    ) {\n      throw new AbortError(undefined, {\n        cause: options.signal.reason\n      })\n    }\n    ArrayPrototypePush(result, val)\n  }\n  return result\n}\nfunction flatMap(fn, options) {\n  const values = map.call(this, fn, options)\n  return async function* flatMap() {\n    for await (const val of values) {\n      yield* val\n    }\n  }.call(this)\n}\nfunction toIntegerOrInfinity(number) {\n  // We coerce here to align with the spec\n  // https://github.com/tc39/proposal-iterator-helpers/issues/169\n  number = Number(number)\n  if (NumberIsNaN(number)) {\n    return 0\n  }\n  if (number < 0) {\n    throw new ERR_OUT_OF_RANGE('number', '>= 0', number)\n  }\n  return number\n}\nfunction drop(number, options = undefined) {\n  if (options != null) {\n    validateObject(options, 'options')\n  }\n  if ((options === null || options === undefined ? undefined : options.signal) != null) {\n    validateAbortSignal(options.signal, 'options.signal')\n  }\n  number = toIntegerOrInfinity(number)\n  return async function* drop() {\n    var _options$signal5\n    if (\n      options !== null &&\n      options !== undefined &&\n      (_options$signal5 = options.signal) !== null &&\n      _options$signal5 !== undefined &&\n      _options$signal5.aborted\n    ) {\n      throw new AbortError()\n    }\n    for await (const val of this) {\n      var _options$signal6\n      if (\n        options !== null &&\n        options !== undefined &&\n        (_options$signal6 = options.signal) !== null &&\n        _options$signal6 !== undefined &&\n        _options$signal6.aborted\n      ) {\n        throw new AbortError()\n      }\n      if (number-- <= 0) {\n        yield val\n      }\n    }\n  }.call(this)\n}\nfunction take(number, options = undefined) {\n  if (options != null) {\n    validateObject(options, 'options')\n  }\n  if ((options === null || options === undefined ? undefined : options.signal) != null) {\n    validateAbortSignal(options.signal, 'options.signal')\n  }\n  number = toIntegerOrInfinity(number)\n  return async function* take() {\n    var _options$signal7\n    if (\n      options !== null &&\n      options !== undefined &&\n      (_options$signal7 = options.signal) !== null &&\n      _options$signal7 !== undefined &&\n      _options$signal7.aborted\n    ) {\n      throw new AbortError()\n    }\n    for await (const val of this) {\n      var _options$signal8\n      if (\n        options !== null &&\n        options !== undefined &&\n        (_options$signal8 = options.signal) !== null &&\n        _options$signal8 !== undefined &&\n        _options$signal8.aborted\n      ) {\n        throw new AbortError()\n      }\n      if (number-- > 0) {\n        yield val\n      }\n\n      // Don't get another item from iterator in case we reached the end\n      if (number <= 0) {\n        return\n      }\n    }\n  }.call(this)\n}\nmodule.exports.streamReturningOperators = {\n  asIndexedPairs: deprecate(asIndexedPairs, 'readable.asIndexedPairs will be removed in a future version.'),\n  drop,\n  filter,\n  flatMap,\n  map,\n  take,\n  compose\n}\nmodule.exports.promiseReturningOperators = {\n  every,\n  forEach,\n  reduce,\n  toArray,\n  some,\n  find\n}\n"], "names": [], "mappings": "AAEA,MAAM,kBAAkB,WAAW,eAAe,IAAI,qHAA4B,eAAe;AACjG,MAAM,EACJ,OAAO,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,EAC1F,UAAU,EACX;AACD,MAAM,EAAE,mBAAmB,EAAE,eAAe,EAAE,cAAc,EAAE;AAC9D,MAAM,eAAe,mHAAkC,MAAM,CAAC;AAC9D,MAAM,yBAAyB,mHAAkC,MAAM,CAAC;AACxE,MAAM,EAAE,QAAQ,EAAE;AAClB,MAAM;AACN,MAAM,EAAE,wBAAwB,EAAE;AAClC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE;AAClC,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM,EACJ,kBAAkB,EAClB,OAAO,EACP,SAAS,EACT,MAAM,EACN,WAAW,EACX,OAAO,EACP,aAAa,EACb,cAAc,EACd,oBAAoB,EACpB,MAAM,EACP;AACD,MAAM,SAAS,OAAO;AACtB,MAAM,OAAO,OAAO;AACpB,SAAS,QAAQ,MAAM,EAAE,OAAO;IAC9B,IAAI,WAAW,MAAM;QACnB,eAAe,SAAS;IAC1B;IACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM,KAAK,MAAM;QACpF,oBAAoB,QAAQ,MAAM,EAAE;IACtC;IACA,IAAI,aAAa,WAAW,CAAC,WAAW,SAAS;QAC/C,MAAM,IAAI,sBAAsB,UAAU,QAAQ;IACpD;IACA,MAAM,iBAAiB,cAAc,IAAI,EAAE;IAC3C,IAAI,YAAY,QAAQ,YAAY,aAAa,QAAQ,MAAM,EAAE;QAC/D,gDAAgD;QAChD,yBAAyB,QAAQ,MAAM,EAAE;IAC3C;IACA,OAAO;AACT;AACA,SAAS,IAAI,EAAE,EAAE,OAAO;IACtB,IAAI,OAAO,OAAO,YAAY;QAC5B,MAAM,IAAI,qBAAqB,MAAM;YAAC;YAAY;SAAgB,EAAE;IACtE;IACA,IAAI,WAAW,MAAM;QACnB,eAAe,SAAS;IAC1B;IACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM,KAAK,MAAM;QACpF,oBAAoB,QAAQ,MAAM,EAAE;IACtC;IACA,IAAI,cAAc;IAClB,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,WAAW,KAAK,MAAM;QACzF,cAAc,UAAU,QAAQ,WAAW;IAC7C;IACA,IAAI,gBAAgB,cAAc;IAClC,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,aAAa,KAAK,MAAM;QAC3F,gBAAgB,UAAU,QAAQ,aAAa;IACjD;IACA,gBAAgB,aAAa,uBAAuB;IACpD,gBAAgB,eAAe,yBAAyB;IACxD,iBAAiB;IACjB,OAAO,CAAA,gBAAgB;QACrB,MAAM,SAAS,4GAA2B,cAAc,CACtD;YAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM;SAAC,CAAC,MAAM,CAAC;QAElF,MAAM,SAAS,IAAI;QACnB,MAAM,QAAQ,EAAE;QAChB,MAAM,YAAY;YAChB;QACF;QACA,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO;QACX,IAAI,MAAM;QACV,SAAS;YACP,OAAO;YACP;QACF;QACA,SAAS;YACP,OAAO;YACP;QACF;QACA,SAAS;YACP,IAAI,UAAU,CAAC,QAAQ,MAAM,eAAe,MAAM,MAAM,GAAG,eAAe;gBACxE;gBACA,SAAS;YACX;QACF;QACA,eAAe;YACb,IAAI;gBACF,WAAW,IAAI,OAAO,OAAQ;oBAC5B,IAAI,MAAM;wBACR;oBACF;oBACA,IAAI,OAAO,OAAO,EAAE;wBAClB,MAAM,IAAI;oBACZ;oBACA,IAAI;wBACF,MAAM,GAAG,KAAK;wBACd,IAAI,QAAQ,QAAQ;4BAClB;wBACF;wBACA,MAAM,eAAe;oBACvB,EAAE,OAAO,KAAK;wBACZ,MAAM,cAAc;oBACtB;oBACA,OAAO;oBACP,qBAAqB,KAAK,oBAAoB;oBAC9C,MAAM,IAAI,CAAC;oBACX,IAAI,MAAM;wBACR;wBACA,OAAO;oBACT;oBACA,IAAI,CAAC,QAAQ,CAAC,MAAM,MAAM,IAAI,iBAAiB,OAAO,WAAW,GAAG;wBAClE,MAAM,IAAI,QAAQ,CAAC;4BACjB,SAAS;wBACX;oBACF;gBACF;gBACA,MAAM,IAAI,CAAC;YACb,EAAE,OAAO,KAAK;gBACZ,MAAM,MAAM,cAAc;gBAC1B,qBAAqB,KAAK,oBAAoB;gBAC9C,MAAM,IAAI,CAAC;YACb,SAAU;gBACR,OAAO;gBACP,IAAI,MAAM;oBACR;oBACA,OAAO;gBACT;YACF;QACF;QACA;QACA,IAAI;YACF,MAAO,KAAM;gBACX,MAAO,MAAM,MAAM,GAAG,EAAG;oBACvB,MAAM,MAAM,MAAM,KAAK,CAAC,EAAE;oBAC1B,IAAI,QAAQ,MAAM;wBAChB;oBACF;oBACA,IAAI,OAAO,OAAO,EAAE;wBAClB,MAAM,IAAI;oBACZ;oBACA,IAAI,QAAQ,QAAQ;wBAClB,MAAM;oBACR;oBACA,MAAM,KAAK;oBACX;gBACF;gBACA,MAAM,IAAI,QAAQ,CAAC;oBACjB,OAAO;gBACT;YACF;QACF,SAAU;YACR,OAAO;YACP,IAAI,QAAQ;gBACV;gBACA,SAAS;YACX;QACF;IACF,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AACA,SAAS,eAAe,UAAU,SAAS;IACzC,IAAI,WAAW,MAAM;QACnB,eAAe,SAAS;IAC1B;IACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM,KAAK,MAAM;QACpF,oBAAoB,QAAQ,MAAM,EAAE;IACtC;IACA,OAAO,CAAA,gBAAgB;QACrB,IAAI,QAAQ;QACZ,WAAW,MAAM,OAAO,IAAI,CAAE;YAC5B,IAAI;YACJ,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,kBAAkB,QAAQ,MAAM,MAAM,QACvC,oBAAoB,aACpB,gBAAgB,OAAO,EACvB;gBACA,MAAM,IAAI,WAAW;oBACnB,OAAO,QAAQ,MAAM,CAAC,MAAM;gBAC9B;YACF;YACA,MAAM;gBAAC;gBAAS;aAAI;QACtB;IACF,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AACA,eAAe,KAAK,EAAE,EAAE,UAAU,SAAS;IACzC,WAAW,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,SAAU;QACzD,OAAO;IACT;IACA,OAAO;AACT;AACA,eAAe,MAAM,EAAE,EAAE,UAAU,SAAS;IAC1C,IAAI,OAAO,OAAO,YAAY;QAC5B,MAAM,IAAI,qBAAqB,MAAM;YAAC;YAAY;SAAgB,EAAE;IACtE;IACA,mDAAmD;IACnD,OAAO,CAAE,MAAM,KAAK,IAAI,CACtB,IAAI,EACJ,OAAO,GAAG;QACR,OAAO,CAAE,MAAM,MAAM;IACvB,GACA;AAEJ;AACA,eAAe,KAAK,EAAE,EAAE,OAAO;IAC7B,WAAW,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,SAAU;QACzD,OAAO;IACT;IACA,OAAO;AACT;AACA,eAAe,QAAQ,EAAE,EAAE,OAAO;IAChC,IAAI,OAAO,OAAO,YAAY;QAC5B,MAAM,IAAI,qBAAqB,MAAM;YAAC;YAAY;SAAgB,EAAE;IACtE;IACA,eAAe,UAAU,KAAK,EAAE,OAAO;QACrC,MAAM,GAAG,OAAO;QAChB,OAAO;IACT;IACA,0CAA0C;IAC1C,WAAW,MAAM,UAAU,IAAI,IAAI,CAAC,IAAI,EAAE,WAAW;AACvD;AACA,SAAS,OAAO,EAAE,EAAE,OAAO;IACzB,IAAI,OAAO,OAAO,YAAY;QAC5B,MAAM,IAAI,qBAAqB,MAAM;YAAC;YAAY;SAAgB,EAAE;IACtE;IACA,eAAe,SAAS,KAAK,EAAE,OAAO;QACpC,IAAI,MAAM,GAAG,OAAO,UAAU;YAC5B,OAAO;QACT;QACA,OAAO;IACT;IACA,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,UAAU;AAClC;AAEA,wEAAwE;AACxE,+EAA+E;AAC/E,MAAM,kCAAkC;IACtC,aAAc;QACZ,KAAK,CAAC;QACN,IAAI,CAAC,OAAO,GAAG;IACjB;AACF;AACA,eAAe,OAAO,OAAO,EAAE,YAAY,EAAE,OAAO;IAClD,IAAI;IACJ,IAAI,OAAO,YAAY,YAAY;QACjC,MAAM,IAAI,qBAAqB,WAAW;YAAC;YAAY;SAAgB,EAAE;IAC3E;IACA,IAAI,WAAW,MAAM;QACnB,eAAe,SAAS;IAC1B;IACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM,KAAK,MAAM;QACpF,oBAAoB,QAAQ,MAAM,EAAE;IACtC;IACA,IAAI,kBAAkB,UAAU,MAAM,GAAG;IACzC,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QACxC,qBAAqB,aACrB,iBAAiB,OAAO,EACxB;QACA,MAAM,MAAM,IAAI,WAAW,WAAW;YACpC,OAAO,QAAQ,MAAM,CAAC,MAAM;QAC9B;QACA,IAAI,CAAC,IAAI,CAAC,SAAS,KAAO,IAAG,kCAAkC;QAC/D,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC;QAC5B,MAAM;IACR;IACA,MAAM,KAAK,IAAI;IACf,MAAM,SAAS,GAAG,MAAM;IACxB,IAAI,YAAY,QAAQ,YAAY,aAAa,QAAQ,MAAM,EAAE;QAC/D,MAAM,OAAO;YACX,MAAM;YACN,CAAC,aAAa,EAAE,IAAI;YACpB,CAAC,uBAAuB,EAAE;QAC5B;QACA,QAAQ,MAAM,CAAC,gBAAgB,CAAC,SAAS,IAAM,GAAG,KAAK,IAAI;IAC7D;IACA,IAAI,uBAAuB;IAC3B,IAAI;QACF,WAAW,MAAM,SAAS,IAAI,CAAE;YAC9B,IAAI;YACJ,uBAAuB;YACvB,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QACxC,qBAAqB,aACrB,iBAAiB,OAAO,EACxB;gBACA,MAAM,IAAI;YACZ;YACA,IAAI,CAAC,iBAAiB;gBACpB,eAAe;gBACf,kBAAkB;YACpB,OAAO;gBACL,eAAe,MAAM,QAAQ,cAAc,OAAO;oBAChD;gBACF;YACF;QACF;QACA,IAAI,CAAC,wBAAwB,CAAC,iBAAiB;YAC7C,MAAM,IAAI;QACZ;IACF,SAAU;QACR,GAAG,KAAK;IACV;IACA,OAAO;AACT;AACA,eAAe,QAAQ,OAAO;IAC5B,IAAI,WAAW,MAAM;QACnB,eAAe,SAAS;IAC1B;IACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM,KAAK,MAAM;QACpF,oBAAoB,QAAQ,MAAM,EAAE;IACtC;IACA,MAAM,SAAS,EAAE;IACjB,WAAW,MAAM,OAAO,IAAI,CAAE;QAC5B,IAAI;QACJ,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QACxC,qBAAqB,aACrB,iBAAiB,OAAO,EACxB;YACA,MAAM,IAAI,WAAW,WAAW;gBAC9B,OAAO,QAAQ,MAAM,CAAC,MAAM;YAC9B;QACF;QACA,mBAAmB,QAAQ;IAC7B;IACA,OAAO;AACT;AACA,SAAS,QAAQ,EAAE,EAAE,OAAO;IAC1B,MAAM,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;IAClC,OAAO,CAAA,gBAAgB;QACrB,WAAW,MAAM,OAAO,OAAQ;YAC9B,OAAO;QACT;IACF,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AACA,SAAS,oBAAoB,MAAM;IACjC,wCAAwC;IACxC,+DAA+D;IAC/D,SAAS,OAAO;IAChB,IAAI,YAAY,SAAS;QACvB,OAAO;IACT;IACA,IAAI,SAAS,GAAG;QACd,MAAM,IAAI,iBAAiB,UAAU,QAAQ;IAC/C;IACA,OAAO;AACT;AACA,SAAS,KAAK,MAAM,EAAE,UAAU,SAAS;IACvC,IAAI,WAAW,MAAM;QACnB,eAAe,SAAS;IAC1B;IACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM,KAAK,MAAM;QACpF,oBAAoB,QAAQ,MAAM,EAAE;IACtC;IACA,SAAS,oBAAoB;IAC7B,OAAO,CAAA,gBAAgB;QACrB,IAAI;QACJ,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QACxC,qBAAqB,aACrB,iBAAiB,OAAO,EACxB;YACA,MAAM,IAAI;QACZ;QACA,WAAW,MAAM,OAAO,IAAI,CAAE;YAC5B,IAAI;YACJ,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QACxC,qBAAqB,aACrB,iBAAiB,OAAO,EACxB;gBACA,MAAM,IAAI;YACZ;YACA,IAAI,YAAY,GAAG;gBACjB,MAAM;YACR;QACF;IACF,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AACA,SAAS,KAAK,MAAM,EAAE,UAAU,SAAS;IACvC,IAAI,WAAW,MAAM;QACnB,eAAe,SAAS;IAC1B;IACA,IAAI,CAAC,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,MAAM,KAAK,MAAM;QACpF,oBAAoB,QAAQ,MAAM,EAAE;IACtC;IACA,SAAS,oBAAoB;IAC7B,OAAO,CAAA,gBAAgB;QACrB,IAAI;QACJ,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QACxC,qBAAqB,aACrB,iBAAiB,OAAO,EACxB;YACA,MAAM,IAAI;QACZ;QACA,WAAW,MAAM,OAAO,IAAI,CAAE;YAC5B,IAAI;YACJ,IACE,YAAY,QACZ,YAAY,aACZ,CAAC,mBAAmB,QAAQ,MAAM,MAAM,QACxC,qBAAqB,aACrB,iBAAiB,OAAO,EACxB;gBACA,MAAM,IAAI;YACZ;YACA,IAAI,WAAW,GAAG;gBAChB,MAAM;YACR;YAEA,kEAAkE;YAClE,IAAI,UAAU,GAAG;gBACf;YACF;QACF;IACF,CAAA,EAAE,IAAI,CAAC,IAAI;AACb;AACA,OAAO,OAAO,CAAC,wBAAwB,GAAG;IACxC,gBAAgB,UAAU,gBAAgB;IAC1C;IACA;IACA;IACA;IACA;IACA;AACF;AACA,OAAO,OAAO,CAAC,yBAAyB,GAAG;IACzC;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7822, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/stream/promises.js"], "sourcesContent": ["'use strict'\n\nconst { ArrayPrototypePop, Promise } = require('../ours/primordials')\nconst { isIterable, isNodeStream, isWebStream } = require('../internal/streams/utils')\nconst { pipelineImpl: pl } = require('../internal/streams/pipeline')\nconst { finished } = require('../internal/streams/end-of-stream')\nrequire('../../lib/stream.js')\nfunction pipeline(...streams) {\n  return new Promise((resolve, reject) => {\n    let signal\n    let end\n    const lastArg = streams[streams.length - 1]\n    if (\n      lastArg &&\n      typeof lastArg === 'object' &&\n      !isNodeStream(lastArg) &&\n      !isIterable(lastArg) &&\n      !isWebStream(lastArg)\n    ) {\n      const options = ArrayPrototypePop(streams)\n      signal = options.signal\n      end = options.end\n    }\n    pl(\n      streams,\n      (err, value) => {\n        if (err) {\n          reject(err)\n        } else {\n          resolve(value)\n        }\n      },\n      {\n        signal,\n        end\n      }\n    )\n  })\n}\nmodule.exports = {\n  finished,\n  pipeline\n}\n"], "names": [], "mappings": "AAEA,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE;AACpC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE;AAC/C,MAAM,EAAE,cAAc,EAAE,EAAE;AAC1B,MAAM,EAAE,QAAQ,EAAE;;AAElB,SAAS,SAAS,GAAG,OAAO;IAC1B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;QACJ,IAAI;QACJ,MAAM,UAAU,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAC3C,IACE,WACA,OAAO,YAAY,YACnB,CAAC,aAAa,YACd,CAAC,WAAW,YACZ,CAAC,YAAY,UACb;YACA,MAAM,UAAU,kBAAkB;YAClC,SAAS,QAAQ,MAAM;YACvB,MAAM,QAAQ,GAAG;QACnB;QACA,GACE,SACA,CAAC,KAAK;YACJ,IAAI,KAAK;gBACP,OAAO;YACT,OAAO;gBACL,QAAQ;YACV;QACF,GACA;YACE;YACA;QACF;IAEJ;AACF;AACA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7857, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/stream.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict'\n\n/* replacement start */\n\nconst { Buffer } = require('buffer')\n\n/* replacement end */\n\nconst { ObjectDefineProperty, ObjectKeys, ReflectApply } = require('./ours/primordials')\nconst {\n  promisify: { custom: customPromisify }\n} = require('./ours/util')\nconst { streamReturningOperators, promiseReturningOperators } = require('./internal/streams/operators')\nconst {\n  codes: { ERR_ILLEGAL_CONSTRUCTOR }\n} = require('./ours/errors')\nconst compose = require('./internal/streams/compose')\nconst { setDefaultHighWaterMark, getDefaultHighWaterMark } = require('./internal/streams/state')\nconst { pipeline } = require('./internal/streams/pipeline')\nconst { destroyer } = require('./internal/streams/destroy')\nconst eos = require('./internal/streams/end-of-stream')\nconst internalBuffer = {}\nconst promises = require('./stream/promises')\nconst utils = require('./internal/streams/utils')\nconst Stream = (module.exports = require('./internal/streams/legacy').Stream)\nStream.isDestroyed = utils.isDestroyed\nStream.isDisturbed = utils.isDisturbed\nStream.isErrored = utils.isErrored\nStream.isReadable = utils.isReadable\nStream.isWritable = utils.isWritable\nStream.Readable = require('./internal/streams/readable')\nfor (const key of ObjectKeys(streamReturningOperators)) {\n  const op = streamReturningOperators[key]\n  function fn(...args) {\n    if (new.target) {\n      throw ERR_ILLEGAL_CONSTRUCTOR()\n    }\n    return Stream.Readable.from(ReflectApply(op, this, args))\n  }\n  ObjectDefineProperty(fn, 'name', {\n    __proto__: null,\n    value: op.name\n  })\n  ObjectDefineProperty(fn, 'length', {\n    __proto__: null,\n    value: op.length\n  })\n  ObjectDefineProperty(Stream.Readable.prototype, key, {\n    __proto__: null,\n    value: fn,\n    enumerable: false,\n    configurable: true,\n    writable: true\n  })\n}\nfor (const key of ObjectKeys(promiseReturningOperators)) {\n  const op = promiseReturningOperators[key]\n  function fn(...args) {\n    if (new.target) {\n      throw ERR_ILLEGAL_CONSTRUCTOR()\n    }\n    return ReflectApply(op, this, args)\n  }\n  ObjectDefineProperty(fn, 'name', {\n    __proto__: null,\n    value: op.name\n  })\n  ObjectDefineProperty(fn, 'length', {\n    __proto__: null,\n    value: op.length\n  })\n  ObjectDefineProperty(Stream.Readable.prototype, key, {\n    __proto__: null,\n    value: fn,\n    enumerable: false,\n    configurable: true,\n    writable: true\n  })\n}\nStream.Writable = require('./internal/streams/writable')\nStream.Duplex = require('./internal/streams/duplex')\nStream.Transform = require('./internal/streams/transform')\nStream.PassThrough = require('./internal/streams/passthrough')\nStream.pipeline = pipeline\nconst { addAbortSignal } = require('./internal/streams/add-abort-signal')\nStream.addAbortSignal = addAbortSignal\nStream.finished = eos\nStream.destroy = destroyer\nStream.compose = compose\nStream.setDefaultHighWaterMark = setDefaultHighWaterMark\nStream.getDefaultHighWaterMark = getDefaultHighWaterMark\nObjectDefineProperty(Stream, 'promises', {\n  __proto__: null,\n  configurable: true,\n  enumerable: true,\n  get() {\n    return promises\n  }\n})\nObjectDefineProperty(pipeline, customPromisify, {\n  __proto__: null,\n  enumerable: true,\n  get() {\n    return promises.pipeline\n  }\n})\nObjectDefineProperty(eos, customPromisify, {\n  __proto__: null,\n  enumerable: true,\n  get() {\n    return promises.finished\n  }\n})\n\n// Backwards-compat with node 0.4.x\nStream.Stream = Stream\nStream._isUint8Array = function isUint8Array(value) {\n  return value instanceof Uint8Array\n}\nStream._uint8ArrayToBuffer = function _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk.buffer, chunk.byteOffset, chunk.byteLength)\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAIzC,qBAAqB,GAErB,MAAM,EAAE,MAAM,EAAE;AAEhB,mBAAmB,GAEnB,MAAM,EAAE,oBAAoB,EAAE,UAAU,EAAE,YAAY,EAAE;AACxD,MAAM,EACJ,WAAW,EAAE,QAAQ,eAAe,EAAE,EACvC;AACD,MAAM,EAAE,wBAAwB,EAAE,yBAAyB,EAAE;AAC7D,MAAM,EACJ,OAAO,EAAE,uBAAuB,EAAE,EACnC;AACD,MAAM;AACN,MAAM,EAAE,uBAAuB,EAAE,uBAAuB,EAAE;AAC1D,MAAM,EAAE,QAAQ,EAAE;AAClB,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM;AACN,MAAM,iBAAiB,CAAC;AACxB,MAAM;AACN,MAAM;AACN,MAAM,SAAU,OAAO,OAAO,GAAG,0HAAqC,MAAM;AAC5E,OAAO,WAAW,GAAG,MAAM,WAAW;AACtC,OAAO,WAAW,GAAG,MAAM,WAAW;AACtC,OAAO,SAAS,GAAG,MAAM,SAAS;AAClC,OAAO,UAAU,GAAG,MAAM,UAAU;AACpC,OAAO,UAAU,GAAG,MAAM,UAAU;AACpC,OAAO,QAAQ;AACf,KAAK,MAAM,OAAO,WAAW,0BAA2B;IACtD,MAAM,KAAK,wBAAwB,CAAC,IAAI;IACxC,SAAS,GAAG,GAAG,IAAI;QACjB,IAAI,YAAY;YACd,MAAM;QACR;QACA,OAAO,OAAO,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;IACrD;IACA,qBAAqB,IAAI,QAAQ;QAC/B,WAAW;QACX,OAAO,GAAG,IAAI;IAChB;IACA,qBAAqB,IAAI,UAAU;QACjC,WAAW;QACX,OAAO,GAAG,MAAM;IAClB;IACA,qBAAqB,OAAO,QAAQ,CAAC,SAAS,EAAE,KAAK;QACnD,WAAW;QACX,OAAO;QACP,YAAY;QACZ,cAAc;QACd,UAAU;IACZ;AACF;AACA,KAAK,MAAM,OAAO,WAAW,2BAA4B;IACvD,MAAM,KAAK,yBAAyB,CAAC,IAAI;IACzC,SAAS,IAAG,GAAG,IAAI;QACjB,IAAI,YAAY;YACd,MAAM;QACR;QACA,OAAO,aAAa,IAAI,IAAI,EAAE;IAChC;IACA,qBAAqB,KAAI,QAAQ;QAC/B,WAAW;QACX,OAAO,GAAG,IAAI;IAChB;IACA,qBAAqB,KAAI,UAAU;QACjC,WAAW;QACX,OAAO,GAAG,MAAM;IAClB;IACA,qBAAqB,OAAO,QAAQ,CAAC,SAAS,EAAE,KAAK;QACnD,WAAW;QACX,OAAO;QACP,YAAY;QACZ,cAAc;QACd,UAAU;IACZ;AACF;AACA,OAAO,QAAQ;AACf,OAAO,MAAM;AACb,OAAO,SAAS;AAChB,OAAO,WAAW;AAClB,OAAO,QAAQ,GAAG;AAClB,MAAM,EAAE,cAAc,EAAE;AACxB,OAAO,cAAc,GAAG;AACxB,OAAO,QAAQ,GAAG;AAClB,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,GAAG;AACjB,OAAO,uBAAuB,GAAG;AACjC,OAAO,uBAAuB,GAAG;AACjC,qBAAqB,QAAQ,YAAY;IACvC,WAAW;IACX,cAAc;IACd,YAAY;IACZ;QACE,OAAO;IACT;AACF;AACA,qBAAqB,UAAU,iBAAiB;IAC9C,WAAW;IACX,YAAY;IACZ;QACE,OAAO,SAAS,QAAQ;IAC1B;AACF;AACA,qBAAqB,KAAK,iBAAiB;IACzC,WAAW;IACX,YAAY;IACZ;QACE,OAAO,SAAS,QAAQ;IAC1B;AACF;AAEA,mCAAmC;AACnC,OAAO,MAAM,GAAG;AAChB,OAAO,aAAa,GAAG,SAAS,aAAa,KAAK;IAChD,OAAO,iBAAiB;AAC1B;AACA,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,KAAK;IAC7D,OAAO,OAAO,IAAI,CAAC,MAAM,MAAM,EAAE,MAAM,UAAU,EAAE,MAAM,UAAU;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7991, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/readable-stream/lib/ours/index.js"], "sourcesContent": ["'use strict'\n\nconst Stream = require('stream')\nif (Stream && process.env.READABLE_STREAM === 'disable') {\n  const promises = Stream.promises\n\n  // Explicit export naming is needed for ESM\n  module.exports._uint8ArrayToBuffer = Stream._uint8ArrayToBuffer\n  module.exports._isUint8Array = Stream._isUint8Array\n  module.exports.isDisturbed = Stream.isDisturbed\n  module.exports.isErrored = Stream.isErrored\n  module.exports.isReadable = Stream.isReadable\n  module.exports.Readable = Stream.Readable\n  module.exports.Writable = Stream.Writable\n  module.exports.Duplex = Stream.Duplex\n  module.exports.Transform = Stream.Transform\n  module.exports.PassThrough = Stream.PassThrough\n  module.exports.addAbortSignal = Stream.addAbortSignal\n  module.exports.finished = Stream.finished\n  module.exports.destroy = Stream.destroy\n  module.exports.pipeline = Stream.pipeline\n  module.exports.compose = Stream.compose\n  Object.defineProperty(Stream, 'promises', {\n    configurable: true,\n    enumerable: true,\n    get() {\n      return promises\n    }\n  })\n  module.exports.Stream = Stream.Stream\n} else {\n  const CustomStream = require('../stream')\n  const promises = require('../stream/promises')\n  const originalDestroy = CustomStream.Readable.destroy\n  module.exports = CustomStream.Readable\n\n  // Explicit export naming is needed for ESM\n  module.exports._uint8ArrayToBuffer = CustomStream._uint8ArrayToBuffer\n  module.exports._isUint8Array = CustomStream._isUint8Array\n  module.exports.isDisturbed = CustomStream.isDisturbed\n  module.exports.isErrored = CustomStream.isErrored\n  module.exports.isReadable = CustomStream.isReadable\n  module.exports.Readable = CustomStream.Readable\n  module.exports.Writable = CustomStream.Writable\n  module.exports.Duplex = CustomStream.Duplex\n  module.exports.Transform = CustomStream.Transform\n  module.exports.PassThrough = CustomStream.PassThrough\n  module.exports.addAbortSignal = CustomStream.addAbortSignal\n  module.exports.finished = CustomStream.finished\n  module.exports.destroy = CustomStream.destroy\n  module.exports.destroy = originalDestroy\n  module.exports.pipeline = CustomStream.pipeline\n  module.exports.compose = CustomStream.compose\n  Object.defineProperty(CustomStream, 'promises', {\n    configurable: true,\n    enumerable: true,\n    get() {\n      return promises\n    }\n  })\n  module.exports.Stream = CustomStream.Stream\n}\n\n// Allow default importing\nmodule.exports.default = module.exports\n"], "names": [], "mappings": "AAEA,MAAM;AACN,IAAI,UAAU,QAAQ,GAAG,CAAC,eAAe,KAAK,WAAW;IACvD,MAAM,WAAW,OAAO,QAAQ;IAEhC,2CAA2C;IAC3C,OAAO,OAAO,CAAC,mBAAmB,GAAG,OAAO,mBAAmB;IAC/D,OAAO,OAAO,CAAC,aAAa,GAAG,OAAO,aAAa;IACnD,OAAO,OAAO,CAAC,WAAW,GAAG,OAAO,WAAW;IAC/C,OAAO,OAAO,CAAC,SAAS,GAAG,OAAO,SAAS;IAC3C,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,UAAU;IAC7C,OAAO,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;IACzC,OAAO,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;IACzC,OAAO,OAAO,CAAC,MAAM,GAAG,OAAO,MAAM;IACrC,OAAO,OAAO,CAAC,SAAS,GAAG,OAAO,SAAS;IAC3C,OAAO,OAAO,CAAC,WAAW,GAAG,OAAO,WAAW;IAC/C,OAAO,OAAO,CAAC,cAAc,GAAG,OAAO,cAAc;IACrD,OAAO,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;IACzC,OAAO,OAAO,CAAC,OAAO,GAAG,OAAO,OAAO;IACvC,OAAO,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;IACzC,OAAO,OAAO,CAAC,OAAO,GAAG,OAAO,OAAO;IACvC,OAAO,cAAc,CAAC,QAAQ,YAAY;QACxC,cAAc;QACd,YAAY;QACZ;YACE,OAAO;QACT;IACF;IACA,OAAO,OAAO,CAAC,MAAM,GAAG,OAAO,MAAM;AACvC,OAAO;IACL,MAAM;IACN,MAAM;IACN,MAAM,kBAAkB,aAAa,QAAQ,CAAC,OAAO;IACrD,OAAO,OAAO,GAAG,aAAa,QAAQ;IAEtC,2CAA2C;IAC3C,OAAO,OAAO,CAAC,mBAAmB,GAAG,aAAa,mBAAmB;IACrE,OAAO,OAAO,CAAC,aAAa,GAAG,aAAa,aAAa;IACzD,OAAO,OAAO,CAAC,WAAW,GAAG,aAAa,WAAW;IACrD,OAAO,OAAO,CAAC,SAAS,GAAG,aAAa,SAAS;IACjD,OAAO,OAAO,CAAC,UAAU,GAAG,aAAa,UAAU;IACnD,OAAO,OAAO,CAAC,QAAQ,GAAG,aAAa,QAAQ;IAC/C,OAAO,OAAO,CAAC,QAAQ,GAAG,aAAa,QAAQ;IAC/C,OAAO,OAAO,CAAC,MAAM,GAAG,aAAa,MAAM;IAC3C,OAAO,OAAO,CAAC,SAAS,GAAG,aAAa,SAAS;IACjD,OAAO,OAAO,CAAC,WAAW,GAAG,aAAa,WAAW;IACrD,OAAO,OAAO,CAAC,cAAc,GAAG,aAAa,cAAc;IAC3D,OAAO,OAAO,CAAC,QAAQ,GAAG,aAAa,QAAQ;IAC/C,OAAO,OAAO,CAAC,OAAO,GAAG,aAAa,OAAO;IAC7C,OAAO,OAAO,CAAC,OAAO,GAAG;IACzB,OAAO,OAAO,CAAC,QAAQ,GAAG,aAAa,QAAQ;IAC/C,OAAO,OAAO,CAAC,OAAO,GAAG,aAAa,OAAO;IAC7C,OAAO,cAAc,CAAC,cAAc,YAAY;QAC9C,cAAc;QACd,YAAY;QACZ;YACE,OAAO;QACT;IACF;IACA,OAAO,OAAO,CAAC,MAAM,GAAG,aAAa,MAAM;AAC7C;AAEA,0BAA0B;AAC1B,OAAO,OAAO,CAAC,OAAO,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}