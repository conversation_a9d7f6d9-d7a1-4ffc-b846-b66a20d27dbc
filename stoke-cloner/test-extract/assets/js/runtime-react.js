var ly=Object.defineProperty;var sy=(e,t,n)=>t in e?ly(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var J=(e,t,n)=>(sy(e,typeof t!="symbol"?t+"":t,n),n);function uy(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function ql(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var lm={exports:{}},Zl={},sm={exports:{}},X={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hi=Symbol.for("react.element"),cy=Symbol.for("react.portal"),dy=Symbol.for("react.fragment"),fy=Symbol.for("react.strict_mode"),py=Symbol.for("react.profiler"),hy=Symbol.for("react.provider"),my=Symbol.for("react.context"),gy=Symbol.for("react.forward_ref"),vy=Symbol.for("react.suspense"),yy=Symbol.for("react.memo"),xy=Symbol.for("react.lazy"),Mf=Symbol.iterator;function _y(e){return e===null||typeof e!="object"?null:(e=Mf&&e[Mf]||e["@@iterator"],typeof e=="function"?e:null)}var um={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cm=Object.assign,dm={};function Io(e,t,n){this.props=e,this.context=t,this.refs=dm,this.updater=n||um}Io.prototype.isReactComponent={};Io.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Io.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function fm(){}fm.prototype=Io.prototype;function Xc(e,t,n){this.props=e,this.context=t,this.refs=dm,this.updater=n||um}var qc=Xc.prototype=new fm;qc.constructor=Xc;cm(qc,Io.prototype);qc.isPureReactComponent=!0;var Af=Array.isArray,pm=Object.prototype.hasOwnProperty,Zc={current:null},hm={key:!0,ref:!0,__self:!0,__source:!0};function mm(e,t,n){var r,o={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)pm.call(t,r)&&!hm.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var s=Array(l),c=0;c<l;c++)s[c]=arguments[c+2];o.children=s}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:Hi,type:e,key:i,ref:a,props:o,_owner:Zc.current}}function wy(e,t){return{$$typeof:Hi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Jc(e){return typeof e=="object"&&e!==null&&e.$$typeof===Hi}function Cy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Tf=/\/+/g;function Ks(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Cy(""+e.key):t.toString(36)}function Ka(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case Hi:case cy:a=!0}}if(a)return a=e,o=o(a),e=r===""?"."+Ks(a,0):r,Af(o)?(n="",e!=null&&(n=e.replace(Tf,"$&/")+"/"),Ka(o,t,n,"",function(c){return c})):o!=null&&(Jc(o)&&(o=wy(o,n+(!o.key||a&&a.key===o.key?"":(""+o.key).replace(Tf,"$&/")+"/")+e)),t.push(o)),1;if(a=0,r=r===""?".":r+":",Af(e))for(var l=0;l<e.length;l++){i=e[l];var s=r+Ks(i,l);a+=Ka(i,t,n,s,o)}else if(s=_y(e),typeof s=="function")for(e=s.call(e),l=0;!(i=e.next()).done;)i=i.value,s=r+Ks(i,l++),a+=Ka(i,t,n,s,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function va(e,t,n){if(e==null)return e;var r=[],o=0;return Ka(e,r,"","",function(i){return t.call(n,i,o++)}),r}function by(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var at={current:null},Ya={transition:null},Sy={ReactCurrentDispatcher:at,ReactCurrentBatchConfig:Ya,ReactCurrentOwner:Zc};function gm(){throw Error("act(...) is not supported in production builds of React.")}X.Children={map:va,forEach:function(e,t,n){va(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return va(e,function(){t++}),t},toArray:function(e){return va(e,function(t){return t})||[]},only:function(e){if(!Jc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};X.Component=Io;X.Fragment=dy;X.Profiler=py;X.PureComponent=Xc;X.StrictMode=fy;X.Suspense=vy;X.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Sy;X.act=gm;X.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cm({},e.props),o=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=Zc.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(s in t)pm.call(t,s)&&!hm.hasOwnProperty(s)&&(r[s]=t[s]===void 0&&l!==void 0?l[s]:t[s])}var s=arguments.length-2;if(s===1)r.children=n;else if(1<s){l=Array(s);for(var c=0;c<s;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:Hi,type:e.type,key:o,ref:i,props:r,_owner:a}};X.createContext=function(e){return e={$$typeof:my,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:hy,_context:e},e.Consumer=e};X.createElement=mm;X.createFactory=function(e){var t=mm.bind(null,e);return t.type=e,t};X.createRef=function(){return{current:null}};X.forwardRef=function(e){return{$$typeof:gy,render:e}};X.isValidElement=Jc;X.lazy=function(e){return{$$typeof:xy,_payload:{_status:-1,_result:e},_init:by}};X.memo=function(e,t){return{$$typeof:yy,type:e,compare:t===void 0?null:t}};X.startTransition=function(e){var t=Ya.transition;Ya.transition={};try{e()}finally{Ya.transition=t}};X.unstable_act=gm;X.useCallback=function(e,t){return at.current.useCallback(e,t)};X.useContext=function(e){return at.current.useContext(e)};X.useDebugValue=function(){};X.useDeferredValue=function(e){return at.current.useDeferredValue(e)};X.useEffect=function(e,t){return at.current.useEffect(e,t)};X.useId=function(){return at.current.useId()};X.useImperativeHandle=function(e,t,n){return at.current.useImperativeHandle(e,t,n)};X.useInsertionEffect=function(e,t){return at.current.useInsertionEffect(e,t)};X.useLayoutEffect=function(e,t){return at.current.useLayoutEffect(e,t)};X.useMemo=function(e,t){return at.current.useMemo(e,t)};X.useReducer=function(e,t,n){return at.current.useReducer(e,t,n)};X.useRef=function(e){return at.current.useRef(e)};X.useState=function(e){return at.current.useState(e)};X.useSyncExternalStore=function(e,t,n){return at.current.useSyncExternalStore(e,t,n)};X.useTransition=function(){return at.current.useTransition()};X.version="18.3.1";sm.exports=X;var C=sm.exports;const Ce=ql(C),uj=uy({__proto__:null,default:Ce},[C]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oy=C,Ey=Symbol.for("react.element"),ky=Symbol.for("react.fragment"),My=Object.prototype.hasOwnProperty,Ay=Oy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ty={key:!0,ref:!0,__self:!0,__source:!0};function vm(e,t,n){var r,o={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)My.call(t,r)&&!Ty.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Ey,type:e,key:i,ref:a,props:o,_owner:Ay.current}}Zl.Fragment=ky;Zl.jsx=vm;Zl.jsxs=vm;lm.exports=Zl;var u=lm.exports;const cj=ql(u);var ym={exports:{}},Ct={},xm={exports:{}},_m={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(j,F){var U=j.length;j.push(F);e:for(;0<U;){var ne=U-1>>>1,I=j[ne];if(0<o(I,F))j[ne]=F,j[U]=I,U=ne;else break e}}function n(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var F=j[0],U=j.pop();if(U!==F){j[0]=U;e:for(var ne=0,I=j.length,L=I>>>1;ne<L;){var R=2*(ne+1)-1,W=j[R],b=R+1,Y=j[b];if(0>o(W,U))b<I&&0>o(Y,W)?(j[ne]=Y,j[b]=U,ne=b):(j[ne]=W,j[R]=U,ne=R);else if(b<I&&0>o(Y,U))j[ne]=Y,j[b]=U,ne=b;else break e}}return F}function o(j,F){var U=j.sortIndex-F.sortIndex;return U!==0?U:j.id-F.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,l=a.now();e.unstable_now=function(){return a.now()-l}}var s=[],c=[],p=1,f=null,d=3,y=!1,w=!1,x=!1,O=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(j){for(var F=n(c);F!==null;){if(F.callback===null)r(c);else if(F.startTime<=j)r(c),F.sortIndex=F.expirationTime,t(s,F);else break;F=n(c)}}function _(j){if(x=!1,g(j),!w)if(n(s)!==null)w=!0,tt(S);else{var F=n(c);F!==null&&Ne(_,F.startTime-j)}}function S(j,F){w=!1,x&&(x=!1,v(M),M=-1),y=!0;var U=d;try{for(g(F),f=n(s);f!==null&&(!(f.expirationTime>F)||j&&!Z());){var ne=f.callback;if(typeof ne=="function"){f.callback=null,d=f.priorityLevel;var I=ne(f.expirationTime<=F);F=e.unstable_now(),typeof I=="function"?f.callback=I:f===n(s)&&r(s),g(F)}else r(s);f=n(s)}if(f!==null)var L=!0;else{var R=n(c);R!==null&&Ne(_,R.startTime-F),L=!1}return L}finally{f=null,d=U,y=!1}}var E=!1,T=null,M=-1,B=5,z=-1;function Z(){return!(e.unstable_now()-z<B)}function ee(){if(T!==null){var j=e.unstable_now();z=j;var F=!0;try{F=T(!0,j)}finally{F?ce():(E=!1,T=null)}}else E=!1}var ce;if(typeof m=="function")ce=function(){m(ee)};else if(typeof MessageChannel<"u"){var Me=new MessageChannel,Oe=Me.port2;Me.port1.onmessage=ee,ce=function(){Oe.postMessage(null)}}else ce=function(){O(ee,0)};function tt(j){T=j,E||(E=!0,ce())}function Ne(j,F){M=O(function(){j(e.unstable_now())},F)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(j){j.callback=null},e.unstable_continueExecution=function(){w||y||(w=!0,tt(S))},e.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):B=0<j?Math.floor(1e3/j):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(j){switch(d){case 1:case 2:case 3:var F=3;break;default:F=d}var U=d;d=F;try{return j()}finally{d=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(j,F){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var U=d;d=j;try{return F()}finally{d=U}},e.unstable_scheduleCallback=function(j,F,U){var ne=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?ne+U:ne):U=ne,j){case 1:var I=-1;break;case 2:I=250;break;case 5:I=**********;break;case 4:I=1e4;break;default:I=5e3}return I=U+I,j={id:p++,callback:F,priorityLevel:j,startTime:U,expirationTime:I,sortIndex:-1},U>ne?(j.sortIndex=U,t(c,j),n(s)===null&&j===n(c)&&(x?(v(M),M=-1):x=!0,Ne(_,U-ne))):(j.sortIndex=I,t(s,j),w||y||(w=!0,tt(S))),j},e.unstable_shouldYield=Z,e.unstable_wrapCallback=function(j){var F=d;return function(){var U=d;d=F;try{return j.apply(this,arguments)}finally{d=U}}}})(_m);xm.exports=_m;var jy=xm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ny=C,wt=jy;function A(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var wm=new Set,hi={};function Ir(e,t){xo(e,t),xo(e+"Capture",t)}function xo(e,t){for(hi[e]=t,e=0;e<t.length;e++)wm.add(t[e])}var bn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Pu=Object.prototype.hasOwnProperty,Py=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,jf={},Nf={};function Iy(e){return Pu.call(Nf,e)?!0:Pu.call(jf,e)?!1:Py.test(e)?Nf[e]=!0:(jf[e]=!0,!1)}function Ly(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ry(e,t,n,r){if(t===null||typeof t>"u"||Ly(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function lt(e,t,n,r,o,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var Ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ye[e]=new lt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ye[t]=new lt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ye[e]=new lt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ye[e]=new lt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ye[e]=new lt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ye[e]=new lt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ye[e]=new lt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ye[e]=new lt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ye[e]=new lt(e,5,!1,e.toLowerCase(),null,!1,!1)});var ed=/[\-:]([a-z])/g;function td(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ed,td);Ye[t]=new lt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ed,td);Ye[t]=new lt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ed,td);Ye[t]=new lt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ye[e]=new lt(e,1,!1,e.toLowerCase(),null,!1,!1)});Ye.xlinkHref=new lt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ye[e]=new lt(e,1,!1,e.toLowerCase(),null,!0,!0)});function nd(e,t,n,r){var o=Ye.hasOwnProperty(t)?Ye[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ry(t,n,o,r)&&(n=null),r||o===null?Iy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Mn=Ny.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ya=Symbol.for("react.element"),Gr=Symbol.for("react.portal"),Kr=Symbol.for("react.fragment"),rd=Symbol.for("react.strict_mode"),Iu=Symbol.for("react.profiler"),Cm=Symbol.for("react.provider"),bm=Symbol.for("react.context"),od=Symbol.for("react.forward_ref"),Lu=Symbol.for("react.suspense"),Ru=Symbol.for("react.suspense_list"),id=Symbol.for("react.memo"),In=Symbol.for("react.lazy"),Sm=Symbol.for("react.offscreen"),Pf=Symbol.iterator;function Fo(e){return e===null||typeof e!="object"?null:(e=Pf&&e[Pf]||e["@@iterator"],typeof e=="function"?e:null)}var Se=Object.assign,Ys;function Xo(e){if(Ys===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ys=t&&t[1]||""}return`
`+Ys+e}var Qs=!1;function Xs(e,t){if(!e||Qs)return"";Qs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),i=r.stack.split(`
`),a=o.length-1,l=i.length-1;1<=a&&0<=l&&o[a]!==i[l];)l--;for(;1<=a&&0<=l;a--,l--)if(o[a]!==i[l]){if(a!==1||l!==1)do if(a--,l--,0>l||o[a]!==i[l]){var s=`
`+o[a].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=a&&0<=l);break}}}finally{Qs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Xo(e):""}function Dy(e){switch(e.tag){case 5:return Xo(e.type);case 16:return Xo("Lazy");case 13:return Xo("Suspense");case 19:return Xo("SuspenseList");case 0:case 2:case 15:return e=Xs(e.type,!1),e;case 11:return e=Xs(e.type.render,!1),e;case 1:return e=Xs(e.type,!0),e;default:return""}}function Du(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Kr:return"Fragment";case Gr:return"Portal";case Iu:return"Profiler";case rd:return"StrictMode";case Lu:return"Suspense";case Ru:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case bm:return(e.displayName||"Context")+".Consumer";case Cm:return(e._context.displayName||"Context")+".Provider";case od:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case id:return t=e.displayName||null,t!==null?t:Du(e.type)||"Memo";case In:t=e._payload,e=e._init;try{return Du(e(t))}catch{}}return null}function $y(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Du(t);case 8:return t===rd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function er(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Om(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function By(e){var t=Om(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xa(e){e._valueTracker||(e._valueTracker=By(e))}function Em(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Om(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function dl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function $u(e,t){var n=t.checked;return Se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function If(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=er(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function km(e,t){t=t.checked,t!=null&&nd(e,"checked",t,!1)}function Bu(e,t){km(e,t);var n=er(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?zu(e,t.type,n):t.hasOwnProperty("defaultValue")&&zu(e,t.type,er(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Lf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function zu(e,t,n){(t!=="number"||dl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var qo=Array.isArray;function uo(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+er(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Fu(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(A(91));return Se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Rf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(A(92));if(qo(n)){if(1<n.length)throw Error(A(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:er(n)}}function Mm(e,t){var n=er(t.value),r=er(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Df(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Am(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Uu(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Am(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var _a,Tm=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(_a=_a||document.createElement("div"),_a.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=_a.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function mi(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ni={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},zy=["Webkit","ms","Moz","O"];Object.keys(ni).forEach(function(e){zy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ni[t]=ni[e]})});function jm(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ni.hasOwnProperty(e)&&ni[e]?(""+t).trim():t+"px"}function Nm(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=jm(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Fy=Se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wu(e,t){if(t){if(Fy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(A(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(A(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(A(61))}if(t.style!=null&&typeof t.style!="object")throw Error(A(62))}}function Vu(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Hu=null;function ad(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Gu=null,co=null,fo=null;function $f(e){if(e=Yi(e)){if(typeof Gu!="function")throw Error(A(280));var t=e.stateNode;t&&(t=rs(t),Gu(e.stateNode,e.type,t))}}function Pm(e){co?fo?fo.push(e):fo=[e]:co=e}function Im(){if(co){var e=co,t=fo;if(fo=co=null,$f(e),t)for(e=0;e<t.length;e++)$f(t[e])}}function Lm(e,t){return e(t)}function Rm(){}var qs=!1;function Dm(e,t,n){if(qs)return e(t,n);qs=!0;try{return Lm(e,t,n)}finally{qs=!1,(co!==null||fo!==null)&&(Rm(),Im())}}function gi(e,t){var n=e.stateNode;if(n===null)return null;var r=rs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(A(231,t,typeof n));return n}var Ku=!1;if(bn)try{var Uo={};Object.defineProperty(Uo,"passive",{get:function(){Ku=!0}}),window.addEventListener("test",Uo,Uo),window.removeEventListener("test",Uo,Uo)}catch{Ku=!1}function Uy(e,t,n,r,o,i,a,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(p){this.onError(p)}}var ri=!1,fl=null,pl=!1,Yu=null,Wy={onError:function(e){ri=!0,fl=e}};function Vy(e,t,n,r,o,i,a,l,s){ri=!1,fl=null,Uy.apply(Wy,arguments)}function Hy(e,t,n,r,o,i,a,l,s){if(Vy.apply(this,arguments),ri){if(ri){var c=fl;ri=!1,fl=null}else throw Error(A(198));pl||(pl=!0,Yu=c)}}function Lr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function $m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Bf(e){if(Lr(e)!==e)throw Error(A(188))}function Gy(e){var t=e.alternate;if(!t){if(t=Lr(e),t===null)throw Error(A(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Bf(o),e;if(i===r)return Bf(o),t;i=i.sibling}throw Error(A(188))}if(n.return!==r.return)n=o,r=i;else{for(var a=!1,l=o.child;l;){if(l===n){a=!0,n=o,r=i;break}if(l===r){a=!0,r=o,n=i;break}l=l.sibling}if(!a){for(l=i.child;l;){if(l===n){a=!0,n=i,r=o;break}if(l===r){a=!0,r=i,n=o;break}l=l.sibling}if(!a)throw Error(A(189))}}if(n.alternate!==r)throw Error(A(190))}if(n.tag!==3)throw Error(A(188));return n.stateNode.current===n?e:t}function Bm(e){return e=Gy(e),e!==null?zm(e):null}function zm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=zm(e);if(t!==null)return t;e=e.sibling}return null}var Fm=wt.unstable_scheduleCallback,zf=wt.unstable_cancelCallback,Ky=wt.unstable_shouldYield,Yy=wt.unstable_requestPaint,Te=wt.unstable_now,Qy=wt.unstable_getCurrentPriorityLevel,ld=wt.unstable_ImmediatePriority,Um=wt.unstable_UserBlockingPriority,hl=wt.unstable_NormalPriority,Xy=wt.unstable_LowPriority,Wm=wt.unstable_IdlePriority,Jl=null,an=null;function qy(e){if(an&&typeof an.onCommitFiberRoot=="function")try{an.onCommitFiberRoot(Jl,e,void 0,(e.current.flags&128)===128)}catch{}}var Vt=Math.clz32?Math.clz32:ex,Zy=Math.log,Jy=Math.LN2;function ex(e){return e>>>=0,e===0?32:31-(Zy(e)/Jy|0)|0}var wa=64,Ca=4194304;function Zo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ml(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var l=a&~o;l!==0?r=Zo(l):(i&=a,i!==0&&(r=Zo(i)))}else a=n&~o,a!==0?r=Zo(a):i!==0&&(r=Zo(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Vt(t),o=1<<n,r|=e[n],t&=~o;return r}function tx(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function nx(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-Vt(i),l=1<<a,s=o[a];s===-1?(!(l&n)||l&r)&&(o[a]=tx(l,t)):s<=t&&(e.expiredLanes|=l),i&=~l}}function Qu(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Vm(){var e=wa;return wa<<=1,!(wa&4194240)&&(wa=64),e}function Zs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Gi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Vt(t),e[t]=n}function rx(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Vt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function sd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Vt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var fe=0;function Hm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Gm,ud,Km,Ym,Qm,Xu=!1,ba=[],Fn=null,Un=null,Wn=null,vi=new Map,yi=new Map,Rn=[],ox="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ff(e,t){switch(e){case"focusin":case"focusout":Fn=null;break;case"dragenter":case"dragleave":Un=null;break;case"mouseover":case"mouseout":Wn=null;break;case"pointerover":case"pointerout":vi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":yi.delete(t.pointerId)}}function Wo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Yi(t),t!==null&&ud(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function ix(e,t,n,r,o){switch(t){case"focusin":return Fn=Wo(Fn,e,t,n,r,o),!0;case"dragenter":return Un=Wo(Un,e,t,n,r,o),!0;case"mouseover":return Wn=Wo(Wn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return vi.set(i,Wo(vi.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,yi.set(i,Wo(yi.get(i)||null,e,t,n,r,o)),!0}return!1}function Xm(e){var t=vr(e.target);if(t!==null){var n=Lr(t);if(n!==null){if(t=n.tag,t===13){if(t=$m(n),t!==null){e.blockedOn=t,Qm(e.priority,function(){Km(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qa(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=qu(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Hu=r,n.target.dispatchEvent(r),Hu=null}else return t=Yi(n),t!==null&&ud(t),e.blockedOn=n,!1;t.shift()}return!0}function Uf(e,t,n){Qa(e)&&n.delete(t)}function ax(){Xu=!1,Fn!==null&&Qa(Fn)&&(Fn=null),Un!==null&&Qa(Un)&&(Un=null),Wn!==null&&Qa(Wn)&&(Wn=null),vi.forEach(Uf),yi.forEach(Uf)}function Vo(e,t){e.blockedOn===t&&(e.blockedOn=null,Xu||(Xu=!0,wt.unstable_scheduleCallback(wt.unstable_NormalPriority,ax)))}function xi(e){function t(o){return Vo(o,e)}if(0<ba.length){Vo(ba[0],e);for(var n=1;n<ba.length;n++){var r=ba[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Fn!==null&&Vo(Fn,e),Un!==null&&Vo(Un,e),Wn!==null&&Vo(Wn,e),vi.forEach(t),yi.forEach(t),n=0;n<Rn.length;n++)r=Rn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Rn.length&&(n=Rn[0],n.blockedOn===null);)Xm(n),n.blockedOn===null&&Rn.shift()}var po=Mn.ReactCurrentBatchConfig,gl=!0;function lx(e,t,n,r){var o=fe,i=po.transition;po.transition=null;try{fe=1,cd(e,t,n,r)}finally{fe=o,po.transition=i}}function sx(e,t,n,r){var o=fe,i=po.transition;po.transition=null;try{fe=4,cd(e,t,n,r)}finally{fe=o,po.transition=i}}function cd(e,t,n,r){if(gl){var o=qu(e,t,n,r);if(o===null)su(e,t,r,vl,n),Ff(e,r);else if(ix(o,e,t,n,r))r.stopPropagation();else if(Ff(e,r),t&4&&-1<ox.indexOf(e)){for(;o!==null;){var i=Yi(o);if(i!==null&&Gm(i),i=qu(e,t,n,r),i===null&&su(e,t,r,vl,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else su(e,t,r,null,n)}}var vl=null;function qu(e,t,n,r){if(vl=null,e=ad(r),e=vr(e),e!==null)if(t=Lr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=$m(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return vl=e,null}function qm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qy()){case ld:return 1;case Um:return 4;case hl:case Xy:return 16;case Wm:return 536870912;default:return 16}default:return 16}}var $n=null,dd=null,Xa=null;function Zm(){if(Xa)return Xa;var e,t=dd,n=t.length,r,o="value"in $n?$n.value:$n.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===o[i-r];r++);return Xa=o.slice(e,1<r?1-r:void 0)}function qa(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Sa(){return!0}function Wf(){return!1}function bt(e){function t(n,r,o,i,a){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Sa:Wf,this.isPropagationStopped=Wf,this}return Se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Sa)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Sa)},persist:function(){},isPersistent:Sa}),t}var Lo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fd=bt(Lo),Ki=Se({},Lo,{view:0,detail:0}),ux=bt(Ki),Js,eu,Ho,es=Se({},Ki,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ho&&(Ho&&e.type==="mousemove"?(Js=e.screenX-Ho.screenX,eu=e.screenY-Ho.screenY):eu=Js=0,Ho=e),Js)},movementY:function(e){return"movementY"in e?e.movementY:eu}}),Vf=bt(es),cx=Se({},es,{dataTransfer:0}),dx=bt(cx),fx=Se({},Ki,{relatedTarget:0}),tu=bt(fx),px=Se({},Lo,{animationName:0,elapsedTime:0,pseudoElement:0}),hx=bt(px),mx=Se({},Lo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),gx=bt(mx),vx=Se({},Lo,{data:0}),Hf=bt(vx),yx={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},xx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},_x={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function wx(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=_x[e])?!!t[e]:!1}function pd(){return wx}var Cx=Se({},Ki,{key:function(e){if(e.key){var t=yx[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qa(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?xx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pd,charCode:function(e){return e.type==="keypress"?qa(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qa(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),bx=bt(Cx),Sx=Se({},es,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gf=bt(Sx),Ox=Se({},Ki,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pd}),Ex=bt(Ox),kx=Se({},Lo,{propertyName:0,elapsedTime:0,pseudoElement:0}),Mx=bt(kx),Ax=Se({},es,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Tx=bt(Ax),jx=[9,13,27,32],hd=bn&&"CompositionEvent"in window,oi=null;bn&&"documentMode"in document&&(oi=document.documentMode);var Nx=bn&&"TextEvent"in window&&!oi,Jm=bn&&(!hd||oi&&8<oi&&11>=oi),Kf=" ",Yf=!1;function eg(e,t){switch(e){case"keyup":return jx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tg(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Yr=!1;function Px(e,t){switch(e){case"compositionend":return tg(t);case"keypress":return t.which!==32?null:(Yf=!0,Kf);case"textInput":return e=t.data,e===Kf&&Yf?null:e;default:return null}}function Ix(e,t){if(Yr)return e==="compositionend"||!hd&&eg(e,t)?(e=Zm(),Xa=dd=$n=null,Yr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jm&&t.locale!=="ko"?null:t.data;default:return null}}var Lx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Lx[e.type]:t==="textarea"}function ng(e,t,n,r){Pm(r),t=yl(t,"onChange"),0<t.length&&(n=new fd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ii=null,_i=null;function Rx(e){pg(e,0)}function ts(e){var t=qr(e);if(Em(t))return e}function Dx(e,t){if(e==="change")return t}var rg=!1;if(bn){var nu;if(bn){var ru="oninput"in document;if(!ru){var Xf=document.createElement("div");Xf.setAttribute("oninput","return;"),ru=typeof Xf.oninput=="function"}nu=ru}else nu=!1;rg=nu&&(!document.documentMode||9<document.documentMode)}function qf(){ii&&(ii.detachEvent("onpropertychange",og),_i=ii=null)}function og(e){if(e.propertyName==="value"&&ts(_i)){var t=[];ng(t,_i,e,ad(e)),Dm(Rx,t)}}function $x(e,t,n){e==="focusin"?(qf(),ii=t,_i=n,ii.attachEvent("onpropertychange",og)):e==="focusout"&&qf()}function Bx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ts(_i)}function zx(e,t){if(e==="click")return ts(t)}function Fx(e,t){if(e==="input"||e==="change")return ts(t)}function Ux(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qt=typeof Object.is=="function"?Object.is:Ux;function wi(e,t){if(Qt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Pu.call(t,o)||!Qt(e[o],t[o]))return!1}return!0}function Zf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jf(e,t){var n=Zf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Zf(n)}}function ig(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ig(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ag(){for(var e=window,t=dl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=dl(e.document)}return t}function md(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Wx(e){var t=ag(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ig(n.ownerDocument.documentElement,n)){if(r!==null&&md(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Jf(n,i);var a=Jf(n,r);o&&a&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Vx=bn&&"documentMode"in document&&11>=document.documentMode,Qr=null,Zu=null,ai=null,Ju=!1;function ep(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ju||Qr==null||Qr!==dl(r)||(r=Qr,"selectionStart"in r&&md(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ai&&wi(ai,r)||(ai=r,r=yl(Zu,"onSelect"),0<r.length&&(t=new fd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Qr)))}function Oa(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Xr={animationend:Oa("Animation","AnimationEnd"),animationiteration:Oa("Animation","AnimationIteration"),animationstart:Oa("Animation","AnimationStart"),transitionend:Oa("Transition","TransitionEnd")},ou={},lg={};bn&&(lg=document.createElement("div").style,"AnimationEvent"in window||(delete Xr.animationend.animation,delete Xr.animationiteration.animation,delete Xr.animationstart.animation),"TransitionEvent"in window||delete Xr.transitionend.transition);function ns(e){if(ou[e])return ou[e];if(!Xr[e])return e;var t=Xr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in lg)return ou[e]=t[n];return e}var sg=ns("animationend"),ug=ns("animationiteration"),cg=ns("animationstart"),dg=ns("transitionend"),fg=new Map,tp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function rr(e,t){fg.set(e,t),Ir(t,[e])}for(var iu=0;iu<tp.length;iu++){var au=tp[iu],Hx=au.toLowerCase(),Gx=au[0].toUpperCase()+au.slice(1);rr(Hx,"on"+Gx)}rr(sg,"onAnimationEnd");rr(ug,"onAnimationIteration");rr(cg,"onAnimationStart");rr("dblclick","onDoubleClick");rr("focusin","onFocus");rr("focusout","onBlur");rr(dg,"onTransitionEnd");xo("onMouseEnter",["mouseout","mouseover"]);xo("onMouseLeave",["mouseout","mouseover"]);xo("onPointerEnter",["pointerout","pointerover"]);xo("onPointerLeave",["pointerout","pointerover"]);Ir("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ir("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ir("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ir("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ir("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ir("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Jo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Kx=new Set("cancel close invalid load scroll toggle".split(" ").concat(Jo));function np(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Hy(r,t,void 0,e),e.currentTarget=null}function pg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var l=r[a],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==i&&o.isPropagationStopped())break e;np(o,l,c),i=s}else for(a=0;a<r.length;a++){if(l=r[a],s=l.instance,c=l.currentTarget,l=l.listener,s!==i&&o.isPropagationStopped())break e;np(o,l,c),i=s}}}if(pl)throw e=Yu,pl=!1,Yu=null,e}function ve(e,t){var n=t[oc];n===void 0&&(n=t[oc]=new Set);var r=e+"__bubble";n.has(r)||(hg(t,e,2,!1),n.add(r))}function lu(e,t,n){var r=0;t&&(r|=4),hg(n,e,r,t)}var Ea="_reactListening"+Math.random().toString(36).slice(2);function Ci(e){if(!e[Ea]){e[Ea]=!0,wm.forEach(function(n){n!=="selectionchange"&&(Kx.has(n)||lu(n,!1,e),lu(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ea]||(t[Ea]=!0,lu("selectionchange",!1,t))}}function hg(e,t,n,r){switch(qm(t)){case 1:var o=lx;break;case 4:o=sx;break;default:o=cd}n=o.bind(null,t,n,e),o=void 0,!Ku||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function su(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(a===4)for(a=r.return;a!==null;){var s=a.tag;if((s===3||s===4)&&(s=a.stateNode.containerInfo,s===o||s.nodeType===8&&s.parentNode===o))return;a=a.return}for(;l!==null;){if(a=vr(l),a===null)return;if(s=a.tag,s===5||s===6){r=i=a;continue e}l=l.parentNode}}r=r.return}Dm(function(){var c=i,p=ad(n),f=[];e:{var d=fg.get(e);if(d!==void 0){var y=fd,w=e;switch(e){case"keypress":if(qa(n)===0)break e;case"keydown":case"keyup":y=bx;break;case"focusin":w="focus",y=tu;break;case"focusout":w="blur",y=tu;break;case"beforeblur":case"afterblur":y=tu;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Vf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=dx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Ex;break;case sg:case ug:case cg:y=hx;break;case dg:y=Mx;break;case"scroll":y=ux;break;case"wheel":y=Tx;break;case"copy":case"cut":case"paste":y=gx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Gf}var x=(t&4)!==0,O=!x&&e==="scroll",v=x?d!==null?d+"Capture":null:d;x=[];for(var m=c,g;m!==null;){g=m;var _=g.stateNode;if(g.tag===5&&_!==null&&(g=_,v!==null&&(_=gi(m,v),_!=null&&x.push(bi(m,_,g)))),O)break;m=m.return}0<x.length&&(d=new y(d,w,null,n,p),f.push({event:d,listeners:x}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",d&&n!==Hu&&(w=n.relatedTarget||n.fromElement)&&(vr(w)||w[Sn]))break e;if((y||d)&&(d=p.window===p?p:(d=p.ownerDocument)?d.defaultView||d.parentWindow:window,y?(w=n.relatedTarget||n.toElement,y=c,w=w?vr(w):null,w!==null&&(O=Lr(w),w!==O||w.tag!==5&&w.tag!==6)&&(w=null)):(y=null,w=c),y!==w)){if(x=Vf,_="onMouseLeave",v="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(x=Gf,_="onPointerLeave",v="onPointerEnter",m="pointer"),O=y==null?d:qr(y),g=w==null?d:qr(w),d=new x(_,m+"leave",y,n,p),d.target=O,d.relatedTarget=g,_=null,vr(p)===c&&(x=new x(v,m+"enter",w,n,p),x.target=g,x.relatedTarget=O,_=x),O=_,y&&w)t:{for(x=y,v=w,m=0,g=x;g;g=$r(g))m++;for(g=0,_=v;_;_=$r(_))g++;for(;0<m-g;)x=$r(x),m--;for(;0<g-m;)v=$r(v),g--;for(;m--;){if(x===v||v!==null&&x===v.alternate)break t;x=$r(x),v=$r(v)}x=null}else x=null;y!==null&&rp(f,d,y,x,!1),w!==null&&O!==null&&rp(f,O,w,x,!0)}}e:{if(d=c?qr(c):window,y=d.nodeName&&d.nodeName.toLowerCase(),y==="select"||y==="input"&&d.type==="file")var S=Dx;else if(Qf(d))if(rg)S=Fx;else{S=Bx;var E=$x}else(y=d.nodeName)&&y.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(S=zx);if(S&&(S=S(e,c))){ng(f,S,n,p);break e}E&&E(e,d,c),e==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&zu(d,"number",d.value)}switch(E=c?qr(c):window,e){case"focusin":(Qf(E)||E.contentEditable==="true")&&(Qr=E,Zu=c,ai=null);break;case"focusout":ai=Zu=Qr=null;break;case"mousedown":Ju=!0;break;case"contextmenu":case"mouseup":case"dragend":Ju=!1,ep(f,n,p);break;case"selectionchange":if(Vx)break;case"keydown":case"keyup":ep(f,n,p)}var T;if(hd)e:{switch(e){case"compositionstart":var M="onCompositionStart";break e;case"compositionend":M="onCompositionEnd";break e;case"compositionupdate":M="onCompositionUpdate";break e}M=void 0}else Yr?eg(e,n)&&(M="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(M="onCompositionStart");M&&(Jm&&n.locale!=="ko"&&(Yr||M!=="onCompositionStart"?M==="onCompositionEnd"&&Yr&&(T=Zm()):($n=p,dd="value"in $n?$n.value:$n.textContent,Yr=!0)),E=yl(c,M),0<E.length&&(M=new Hf(M,e,null,n,p),f.push({event:M,listeners:E}),T?M.data=T:(T=tg(n),T!==null&&(M.data=T)))),(T=Nx?Px(e,n):Ix(e,n))&&(c=yl(c,"onBeforeInput"),0<c.length&&(p=new Hf("onBeforeInput","beforeinput",null,n,p),f.push({event:p,listeners:c}),p.data=T))}pg(f,t)})}function bi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function yl(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=gi(e,n),i!=null&&r.unshift(bi(e,i,o)),i=gi(e,t),i!=null&&r.push(bi(e,i,o))),e=e.return}return r}function $r(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function rp(e,t,n,r,o){for(var i=t._reactName,a=[];n!==null&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(s!==null&&s===r)break;l.tag===5&&c!==null&&(l=c,o?(s=gi(n,i),s!=null&&a.unshift(bi(n,s,l))):o||(s=gi(n,i),s!=null&&a.push(bi(n,s,l)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Yx=/\r\n?/g,Qx=/\u0000|\uFFFD/g;function op(e){return(typeof e=="string"?e:""+e).replace(Yx,`
`).replace(Qx,"")}function ka(e,t,n){if(t=op(t),op(e)!==t&&n)throw Error(A(425))}function xl(){}var ec=null,tc=null;function nc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var rc=typeof setTimeout=="function"?setTimeout:void 0,Xx=typeof clearTimeout=="function"?clearTimeout:void 0,ip=typeof Promise=="function"?Promise:void 0,qx=typeof queueMicrotask=="function"?queueMicrotask:typeof ip<"u"?function(e){return ip.resolve(null).then(e).catch(Zx)}:rc;function Zx(e){setTimeout(function(){throw e})}function uu(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),xi(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);xi(t)}function Vn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ap(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Ro=Math.random().toString(36).slice(2),tn="__reactFiber$"+Ro,Si="__reactProps$"+Ro,Sn="__reactContainer$"+Ro,oc="__reactEvents$"+Ro,Jx="__reactListeners$"+Ro,e_="__reactHandles$"+Ro;function vr(e){var t=e[tn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Sn]||n[tn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ap(e);e!==null;){if(n=e[tn])return n;e=ap(e)}return t}e=n,n=e.parentNode}return null}function Yi(e){return e=e[tn]||e[Sn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function qr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(A(33))}function rs(e){return e[Si]||null}var ic=[],Zr=-1;function or(e){return{current:e}}function ye(e){0>Zr||(e.current=ic[Zr],ic[Zr]=null,Zr--)}function ge(e,t){Zr++,ic[Zr]=e.current,e.current=t}var tr={},Je=or(tr),pt=or(!1),kr=tr;function _o(e,t){var n=e.type.contextTypes;if(!n)return tr;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function ht(e){return e=e.childContextTypes,e!=null}function _l(){ye(pt),ye(Je)}function lp(e,t,n){if(Je.current!==tr)throw Error(A(168));ge(Je,t),ge(pt,n)}function mg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(A(108,$y(e)||"Unknown",o));return Se({},n,r)}function wl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tr,kr=Je.current,ge(Je,e),ge(pt,pt.current),!0}function sp(e,t,n){var r=e.stateNode;if(!r)throw Error(A(169));n?(e=mg(e,t,kr),r.__reactInternalMemoizedMergedChildContext=e,ye(pt),ye(Je),ge(Je,e)):ye(pt),ge(pt,n)}var hn=null,os=!1,cu=!1;function gg(e){hn===null?hn=[e]:hn.push(e)}function t_(e){os=!0,gg(e)}function ir(){if(!cu&&hn!==null){cu=!0;var e=0,t=fe;try{var n=hn;for(fe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}hn=null,os=!1}catch(o){throw hn!==null&&(hn=hn.slice(e+1)),Fm(ld,ir),o}finally{fe=t,cu=!1}}return null}var Jr=[],eo=0,Cl=null,bl=0,kt=[],Mt=0,Mr=null,xn=1,_n="";function fr(e,t){Jr[eo++]=bl,Jr[eo++]=Cl,Cl=e,bl=t}function vg(e,t,n){kt[Mt++]=xn,kt[Mt++]=_n,kt[Mt++]=Mr,Mr=e;var r=xn;e=_n;var o=32-Vt(r)-1;r&=~(1<<o),n+=1;var i=32-Vt(t)+o;if(30<i){var a=o-o%5;i=(r&(1<<a)-1).toString(32),r>>=a,o-=a,xn=1<<32-Vt(t)+o|n<<o|r,_n=i+e}else xn=1<<i|n<<o|r,_n=e}function gd(e){e.return!==null&&(fr(e,1),vg(e,1,0))}function vd(e){for(;e===Cl;)Cl=Jr[--eo],Jr[eo]=null,bl=Jr[--eo],Jr[eo]=null;for(;e===Mr;)Mr=kt[--Mt],kt[Mt]=null,_n=kt[--Mt],kt[Mt]=null,xn=kt[--Mt],kt[Mt]=null}var _t=null,xt=null,xe=!1,Ut=null;function yg(e,t){var n=jt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function up(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,_t=e,xt=Vn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,_t=e,xt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Mr!==null?{id:xn,overflow:_n}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=jt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,_t=e,xt=null,!0):!1;default:return!1}}function ac(e){return(e.mode&1)!==0&&(e.flags&128)===0}function lc(e){if(xe){var t=xt;if(t){var n=t;if(!up(e,t)){if(ac(e))throw Error(A(418));t=Vn(n.nextSibling);var r=_t;t&&up(e,t)?yg(r,n):(e.flags=e.flags&-4097|2,xe=!1,_t=e)}}else{if(ac(e))throw Error(A(418));e.flags=e.flags&-4097|2,xe=!1,_t=e}}}function cp(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;_t=e}function Ma(e){if(e!==_t)return!1;if(!xe)return cp(e),xe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!nc(e.type,e.memoizedProps)),t&&(t=xt)){if(ac(e))throw xg(),Error(A(418));for(;t;)yg(e,t),t=Vn(t.nextSibling)}if(cp(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(A(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){xt=Vn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}xt=null}}else xt=_t?Vn(e.stateNode.nextSibling):null;return!0}function xg(){for(var e=xt;e;)e=Vn(e.nextSibling)}function wo(){xt=_t=null,xe=!1}function yd(e){Ut===null?Ut=[e]:Ut.push(e)}var n_=Mn.ReactCurrentBatchConfig;function Go(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(A(309));var r=n.stateNode}if(!r)throw Error(A(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var l=o.refs;a===null?delete l[i]:l[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(A(284));if(!n._owner)throw Error(A(290,e))}return e}function Aa(e,t){throw e=Object.prototype.toString.call(t),Error(A(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function dp(e){var t=e._init;return t(e._payload)}function _g(e){function t(v,m){if(e){var g=v.deletions;g===null?(v.deletions=[m],v.flags|=16):g.push(m)}}function n(v,m){if(!e)return null;for(;m!==null;)t(v,m),m=m.sibling;return null}function r(v,m){for(v=new Map;m!==null;)m.key!==null?v.set(m.key,m):v.set(m.index,m),m=m.sibling;return v}function o(v,m){return v=Yn(v,m),v.index=0,v.sibling=null,v}function i(v,m,g){return v.index=g,e?(g=v.alternate,g!==null?(g=g.index,g<m?(v.flags|=2,m):g):(v.flags|=2,m)):(v.flags|=1048576,m)}function a(v){return e&&v.alternate===null&&(v.flags|=2),v}function l(v,m,g,_){return m===null||m.tag!==6?(m=vu(g,v.mode,_),m.return=v,m):(m=o(m,g),m.return=v,m)}function s(v,m,g,_){var S=g.type;return S===Kr?p(v,m,g.props.children,_,g.key):m!==null&&(m.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===In&&dp(S)===m.type)?(_=o(m,g.props),_.ref=Go(v,m,g),_.return=v,_):(_=ol(g.type,g.key,g.props,null,v.mode,_),_.ref=Go(v,m,g),_.return=v,_)}function c(v,m,g,_){return m===null||m.tag!==4||m.stateNode.containerInfo!==g.containerInfo||m.stateNode.implementation!==g.implementation?(m=yu(g,v.mode,_),m.return=v,m):(m=o(m,g.children||[]),m.return=v,m)}function p(v,m,g,_,S){return m===null||m.tag!==7?(m=br(g,v.mode,_,S),m.return=v,m):(m=o(m,g),m.return=v,m)}function f(v,m,g){if(typeof m=="string"&&m!==""||typeof m=="number")return m=vu(""+m,v.mode,g),m.return=v,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ya:return g=ol(m.type,m.key,m.props,null,v.mode,g),g.ref=Go(v,null,m),g.return=v,g;case Gr:return m=yu(m,v.mode,g),m.return=v,m;case In:var _=m._init;return f(v,_(m._payload),g)}if(qo(m)||Fo(m))return m=br(m,v.mode,g,null),m.return=v,m;Aa(v,m)}return null}function d(v,m,g,_){var S=m!==null?m.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return S!==null?null:l(v,m,""+g,_);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case ya:return g.key===S?s(v,m,g,_):null;case Gr:return g.key===S?c(v,m,g,_):null;case In:return S=g._init,d(v,m,S(g._payload),_)}if(qo(g)||Fo(g))return S!==null?null:p(v,m,g,_,null);Aa(v,g)}return null}function y(v,m,g,_,S){if(typeof _=="string"&&_!==""||typeof _=="number")return v=v.get(g)||null,l(m,v,""+_,S);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case ya:return v=v.get(_.key===null?g:_.key)||null,s(m,v,_,S);case Gr:return v=v.get(_.key===null?g:_.key)||null,c(m,v,_,S);case In:var E=_._init;return y(v,m,g,E(_._payload),S)}if(qo(_)||Fo(_))return v=v.get(g)||null,p(m,v,_,S,null);Aa(m,_)}return null}function w(v,m,g,_){for(var S=null,E=null,T=m,M=m=0,B=null;T!==null&&M<g.length;M++){T.index>M?(B=T,T=null):B=T.sibling;var z=d(v,T,g[M],_);if(z===null){T===null&&(T=B);break}e&&T&&z.alternate===null&&t(v,T),m=i(z,m,M),E===null?S=z:E.sibling=z,E=z,T=B}if(M===g.length)return n(v,T),xe&&fr(v,M),S;if(T===null){for(;M<g.length;M++)T=f(v,g[M],_),T!==null&&(m=i(T,m,M),E===null?S=T:E.sibling=T,E=T);return xe&&fr(v,M),S}for(T=r(v,T);M<g.length;M++)B=y(T,v,M,g[M],_),B!==null&&(e&&B.alternate!==null&&T.delete(B.key===null?M:B.key),m=i(B,m,M),E===null?S=B:E.sibling=B,E=B);return e&&T.forEach(function(Z){return t(v,Z)}),xe&&fr(v,M),S}function x(v,m,g,_){var S=Fo(g);if(typeof S!="function")throw Error(A(150));if(g=S.call(g),g==null)throw Error(A(151));for(var E=S=null,T=m,M=m=0,B=null,z=g.next();T!==null&&!z.done;M++,z=g.next()){T.index>M?(B=T,T=null):B=T.sibling;var Z=d(v,T,z.value,_);if(Z===null){T===null&&(T=B);break}e&&T&&Z.alternate===null&&t(v,T),m=i(Z,m,M),E===null?S=Z:E.sibling=Z,E=Z,T=B}if(z.done)return n(v,T),xe&&fr(v,M),S;if(T===null){for(;!z.done;M++,z=g.next())z=f(v,z.value,_),z!==null&&(m=i(z,m,M),E===null?S=z:E.sibling=z,E=z);return xe&&fr(v,M),S}for(T=r(v,T);!z.done;M++,z=g.next())z=y(T,v,M,z.value,_),z!==null&&(e&&z.alternate!==null&&T.delete(z.key===null?M:z.key),m=i(z,m,M),E===null?S=z:E.sibling=z,E=z);return e&&T.forEach(function(ee){return t(v,ee)}),xe&&fr(v,M),S}function O(v,m,g,_){if(typeof g=="object"&&g!==null&&g.type===Kr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case ya:e:{for(var S=g.key,E=m;E!==null;){if(E.key===S){if(S=g.type,S===Kr){if(E.tag===7){n(v,E.sibling),m=o(E,g.props.children),m.return=v,v=m;break e}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===In&&dp(S)===E.type){n(v,E.sibling),m=o(E,g.props),m.ref=Go(v,E,g),m.return=v,v=m;break e}n(v,E);break}else t(v,E);E=E.sibling}g.type===Kr?(m=br(g.props.children,v.mode,_,g.key),m.return=v,v=m):(_=ol(g.type,g.key,g.props,null,v.mode,_),_.ref=Go(v,m,g),_.return=v,v=_)}return a(v);case Gr:e:{for(E=g.key;m!==null;){if(m.key===E)if(m.tag===4&&m.stateNode.containerInfo===g.containerInfo&&m.stateNode.implementation===g.implementation){n(v,m.sibling),m=o(m,g.children||[]),m.return=v,v=m;break e}else{n(v,m);break}else t(v,m);m=m.sibling}m=yu(g,v.mode,_),m.return=v,v=m}return a(v);case In:return E=g._init,O(v,m,E(g._payload),_)}if(qo(g))return w(v,m,g,_);if(Fo(g))return x(v,m,g,_);Aa(v,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,m!==null&&m.tag===6?(n(v,m.sibling),m=o(m,g),m.return=v,v=m):(n(v,m),m=vu(g,v.mode,_),m.return=v,v=m),a(v)):n(v,m)}return O}var Co=_g(!0),wg=_g(!1),Sl=or(null),Ol=null,to=null,xd=null;function _d(){xd=to=Ol=null}function wd(e){var t=Sl.current;ye(Sl),e._currentValue=t}function sc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ho(e,t){Ol=e,xd=to=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ct=!0),e.firstContext=null)}function Pt(e){var t=e._currentValue;if(xd!==e)if(e={context:e,memoizedValue:t,next:null},to===null){if(Ol===null)throw Error(A(308));to=e,Ol.dependencies={lanes:0,firstContext:e}}else to=to.next=e;return t}var yr=null;function Cd(e){yr===null?yr=[e]:yr.push(e)}function Cg(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Cd(t)):(n.next=o.next,o.next=n),t.interleaved=n,On(e,r)}function On(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ln=!1;function bd(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function bg(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function wn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Hn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,te&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,On(e,n)}return o=r.interleaved,o===null?(t.next=t,Cd(r)):(t.next=o.next,o.next=t),r.interleaved=t,On(e,n)}function Za(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sd(e,n)}}function fp(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function El(e,t,n,r){var o=e.updateQueue;Ln=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var s=l,c=s.next;s.next=null,a===null?i=c:a.next=c,a=s;var p=e.alternate;p!==null&&(p=p.updateQueue,l=p.lastBaseUpdate,l!==a&&(l===null?p.firstBaseUpdate=c:l.next=c,p.lastBaseUpdate=s))}if(i!==null){var f=o.baseState;a=0,p=c=s=null,l=i;do{var d=l.lane,y=l.eventTime;if((r&d)===d){p!==null&&(p=p.next={eventTime:y,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,x=l;switch(d=t,y=n,x.tag){case 1:if(w=x.payload,typeof w=="function"){f=w.call(y,f,d);break e}f=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=x.payload,d=typeof w=="function"?w.call(y,f,d):w,d==null)break e;f=Se({},f,d);break e;case 2:Ln=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[l]:d.push(l))}else y={eventTime:y,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},p===null?(c=p=y,s=f):p=p.next=y,a|=d;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;d=l,l=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(p===null&&(s=f),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=p,t=o.shared.interleaved,t!==null){o=t;do a|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Tr|=a,e.lanes=a,e.memoizedState=f}}function pp(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(A(191,o));o.call(r)}}}var Qi={},ln=or(Qi),Oi=or(Qi),Ei=or(Qi);function xr(e){if(e===Qi)throw Error(A(174));return e}function Sd(e,t){switch(ge(Ei,t),ge(Oi,e),ge(ln,Qi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Uu(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Uu(t,e)}ye(ln),ge(ln,t)}function bo(){ye(ln),ye(Oi),ye(Ei)}function Sg(e){xr(Ei.current);var t=xr(ln.current),n=Uu(t,e.type);t!==n&&(ge(Oi,e),ge(ln,n))}function Od(e){Oi.current===e&&(ye(ln),ye(Oi))}var we=or(0);function kl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var du=[];function Ed(){for(var e=0;e<du.length;e++)du[e]._workInProgressVersionPrimary=null;du.length=0}var Ja=Mn.ReactCurrentDispatcher,fu=Mn.ReactCurrentBatchConfig,Ar=0,be=null,Le=null,$e=null,Ml=!1,li=!1,ki=0,r_=0;function Xe(){throw Error(A(321))}function kd(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qt(e[n],t[n]))return!1;return!0}function Md(e,t,n,r,o,i){if(Ar=i,be=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ja.current=e===null||e.memoizedState===null?l_:s_,e=n(r,o),li){i=0;do{if(li=!1,ki=0,25<=i)throw Error(A(301));i+=1,$e=Le=null,t.updateQueue=null,Ja.current=u_,e=n(r,o)}while(li)}if(Ja.current=Al,t=Le!==null&&Le.next!==null,Ar=0,$e=Le=be=null,Ml=!1,t)throw Error(A(300));return e}function Ad(){var e=ki!==0;return ki=0,e}function en(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $e===null?be.memoizedState=$e=e:$e=$e.next=e,$e}function It(){if(Le===null){var e=be.alternate;e=e!==null?e.memoizedState:null}else e=Le.next;var t=$e===null?be.memoizedState:$e.next;if(t!==null)$e=t,Le=e;else{if(e===null)throw Error(A(310));Le=e,e={memoizedState:Le.memoizedState,baseState:Le.baseState,baseQueue:Le.baseQueue,queue:Le.queue,next:null},$e===null?be.memoizedState=$e=e:$e=$e.next=e}return $e}function Mi(e,t){return typeof t=="function"?t(e):t}function pu(e){var t=It(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=Le,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var a=o.next;o.next=i.next,i.next=a}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=a=null,s=null,c=i;do{var p=c.lane;if((Ar&p)===p)s!==null&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:p,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};s===null?(l=s=f,a=r):s=s.next=f,be.lanes|=p,Tr|=p}c=c.next}while(c!==null&&c!==i);s===null?a=r:s.next=l,Qt(r,t.memoizedState)||(ct=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=s,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,be.lanes|=i,Tr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function hu(e){var t=It(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var a=o=o.next;do i=e(i,a.action),a=a.next;while(a!==o);Qt(i,t.memoizedState)||(ct=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Og(){}function Eg(e,t){var n=be,r=It(),o=t(),i=!Qt(r.memoizedState,o);if(i&&(r.memoizedState=o,ct=!0),r=r.queue,Td(Ag.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||$e!==null&&$e.memoizedState.tag&1){if(n.flags|=2048,Ai(9,Mg.bind(null,n,r,o,t),void 0,null),ze===null)throw Error(A(349));Ar&30||kg(n,t,o)}return o}function kg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=be.updateQueue,t===null?(t={lastEffect:null,stores:null},be.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Mg(e,t,n,r){t.value=n,t.getSnapshot=r,Tg(t)&&jg(e)}function Ag(e,t,n){return n(function(){Tg(t)&&jg(e)})}function Tg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qt(e,n)}catch{return!0}}function jg(e){var t=On(e,1);t!==null&&Ht(t,e,1,-1)}function hp(e){var t=en();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Mi,lastRenderedState:e},t.queue=e,e=e.dispatch=a_.bind(null,be,e),[t.memoizedState,e]}function Ai(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=be.updateQueue,t===null?(t={lastEffect:null,stores:null},be.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ng(){return It().memoizedState}function el(e,t,n,r){var o=en();be.flags|=e,o.memoizedState=Ai(1|t,n,void 0,r===void 0?null:r)}function is(e,t,n,r){var o=It();r=r===void 0?null:r;var i=void 0;if(Le!==null){var a=Le.memoizedState;if(i=a.destroy,r!==null&&kd(r,a.deps)){o.memoizedState=Ai(t,n,i,r);return}}be.flags|=e,o.memoizedState=Ai(1|t,n,i,r)}function mp(e,t){return el(8390656,8,e,t)}function Td(e,t){return is(2048,8,e,t)}function Pg(e,t){return is(4,2,e,t)}function Ig(e,t){return is(4,4,e,t)}function Lg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Rg(e,t,n){return n=n!=null?n.concat([e]):null,is(4,4,Lg.bind(null,t,e),n)}function jd(){}function Dg(e,t){var n=It();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&kd(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function $g(e,t){var n=It();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&kd(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Bg(e,t,n){return Ar&21?(Qt(n,t)||(n=Vm(),be.lanes|=n,Tr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ct=!0),e.memoizedState=n)}function o_(e,t){var n=fe;fe=n!==0&&4>n?n:4,e(!0);var r=fu.transition;fu.transition={};try{e(!1),t()}finally{fe=n,fu.transition=r}}function zg(){return It().memoizedState}function i_(e,t,n){var r=Kn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Fg(e))Ug(t,n);else if(n=Cg(e,t,n,r),n!==null){var o=it();Ht(n,e,r,o),Wg(n,t,r)}}function a_(e,t,n){var r=Kn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Fg(e))Ug(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,l=i(a,n);if(o.hasEagerState=!0,o.eagerState=l,Qt(l,a)){var s=t.interleaved;s===null?(o.next=o,Cd(t)):(o.next=s.next,s.next=o),t.interleaved=o;return}}catch{}finally{}n=Cg(e,t,o,r),n!==null&&(o=it(),Ht(n,e,r,o),Wg(n,t,r))}}function Fg(e){var t=e.alternate;return e===be||t!==null&&t===be}function Ug(e,t){li=Ml=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sd(e,n)}}var Al={readContext:Pt,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useInsertionEffect:Xe,useLayoutEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useMutableSource:Xe,useSyncExternalStore:Xe,useId:Xe,unstable_isNewReconciler:!1},l_={readContext:Pt,useCallback:function(e,t){return en().memoizedState=[e,t===void 0?null:t],e},useContext:Pt,useEffect:mp,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,el(4194308,4,Lg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return el(4194308,4,e,t)},useInsertionEffect:function(e,t){return el(4,2,e,t)},useMemo:function(e,t){var n=en();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=en();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=i_.bind(null,be,e),[r.memoizedState,e]},useRef:function(e){var t=en();return e={current:e},t.memoizedState=e},useState:hp,useDebugValue:jd,useDeferredValue:function(e){return en().memoizedState=e},useTransition:function(){var e=hp(!1),t=e[0];return e=o_.bind(null,e[1]),en().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=be,o=en();if(xe){if(n===void 0)throw Error(A(407));n=n()}else{if(n=t(),ze===null)throw Error(A(349));Ar&30||kg(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,mp(Ag.bind(null,r,i,e),[e]),r.flags|=2048,Ai(9,Mg.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=en(),t=ze.identifierPrefix;if(xe){var n=_n,r=xn;n=(r&~(1<<32-Vt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ki++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=r_++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},s_={readContext:Pt,useCallback:Dg,useContext:Pt,useEffect:Td,useImperativeHandle:Rg,useInsertionEffect:Pg,useLayoutEffect:Ig,useMemo:$g,useReducer:pu,useRef:Ng,useState:function(){return pu(Mi)},useDebugValue:jd,useDeferredValue:function(e){var t=It();return Bg(t,Le.memoizedState,e)},useTransition:function(){var e=pu(Mi)[0],t=It().memoizedState;return[e,t]},useMutableSource:Og,useSyncExternalStore:Eg,useId:zg,unstable_isNewReconciler:!1},u_={readContext:Pt,useCallback:Dg,useContext:Pt,useEffect:Td,useImperativeHandle:Rg,useInsertionEffect:Pg,useLayoutEffect:Ig,useMemo:$g,useReducer:hu,useRef:Ng,useState:function(){return hu(Mi)},useDebugValue:jd,useDeferredValue:function(e){var t=It();return Le===null?t.memoizedState=e:Bg(t,Le.memoizedState,e)},useTransition:function(){var e=hu(Mi)[0],t=It().memoizedState;return[e,t]},useMutableSource:Og,useSyncExternalStore:Eg,useId:zg,unstable_isNewReconciler:!1};function zt(e,t){if(e&&e.defaultProps){t=Se({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function uc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Se({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return(e=e._reactInternals)?Lr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=it(),o=Kn(e),i=wn(r,o);i.payload=t,n!=null&&(i.callback=n),t=Hn(e,i,o),t!==null&&(Ht(t,e,o,r),Za(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=it(),o=Kn(e),i=wn(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Hn(e,i,o),t!==null&&(Ht(t,e,o,r),Za(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=it(),r=Kn(e),o=wn(n,r);o.tag=2,t!=null&&(o.callback=t),t=Hn(e,o,r),t!==null&&(Ht(t,e,r,n),Za(t,e,r))}};function gp(e,t,n,r,o,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!wi(n,r)||!wi(o,i):!0}function Vg(e,t,n){var r=!1,o=tr,i=t.contextType;return typeof i=="object"&&i!==null?i=Pt(i):(o=ht(t)?kr:Je.current,r=t.contextTypes,i=(r=r!=null)?_o(e,o):tr),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function vp(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function cc(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},bd(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Pt(i):(i=ht(t)?kr:Je.current,o.context=_o(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(uc(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&as.enqueueReplaceState(o,o.state,null),El(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function So(e,t){try{var n="",r=t;do n+=Dy(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function mu(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function dc(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var c_=typeof WeakMap=="function"?WeakMap:Map;function Hg(e,t,n){n=wn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){jl||(jl=!0,wc=r),dc(e,t)},n}function Gg(e,t,n){n=wn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){dc(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){dc(e,t),typeof r!="function"&&(Gn===null?Gn=new Set([this]):Gn.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function yp(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new c_;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=S_.bind(null,e,t,n),t.then(e,e))}function xp(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function _p(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=wn(-1,1),t.tag=2,Hn(n,t,1))),n.lanes|=1),e)}var d_=Mn.ReactCurrentOwner,ct=!1;function ot(e,t,n,r){t.child=e===null?wg(t,null,n,r):Co(t,e.child,n,r)}function wp(e,t,n,r,o){n=n.render;var i=t.ref;return ho(t,o),r=Md(e,t,n,r,i,o),n=Ad(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,En(e,t,o)):(xe&&n&&gd(t),t.flags|=1,ot(e,t,r,o),t.child)}function Cp(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Bd(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Kg(e,t,i,r,o)):(e=ol(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:wi,n(a,r)&&e.ref===t.ref)return En(e,t,o)}return t.flags|=1,e=Yn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Kg(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(wi(i,r)&&e.ref===t.ref)if(ct=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(ct=!0);else return t.lanes=e.lanes,En(e,t,o)}return fc(e,t,n,r,o)}function Yg(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ge(ro,vt),vt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ge(ro,vt),vt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ge(ro,vt),vt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ge(ro,vt),vt|=r;return ot(e,t,o,n),t.child}function Qg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function fc(e,t,n,r,o){var i=ht(n)?kr:Je.current;return i=_o(t,i),ho(t,o),n=Md(e,t,n,r,i,o),r=Ad(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,En(e,t,o)):(xe&&r&&gd(t),t.flags|=1,ot(e,t,n,o),t.child)}function bp(e,t,n,r,o){if(ht(n)){var i=!0;wl(t)}else i=!1;if(ho(t,o),t.stateNode===null)tl(e,t),Vg(t,n,r),cc(t,n,r,o),r=!0;else if(e===null){var a=t.stateNode,l=t.memoizedProps;a.props=l;var s=a.context,c=n.contextType;typeof c=="object"&&c!==null?c=Pt(c):(c=ht(n)?kr:Je.current,c=_o(t,c));var p=n.getDerivedStateFromProps,f=typeof p=="function"||typeof a.getSnapshotBeforeUpdate=="function";f||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(l!==r||s!==c)&&vp(t,a,r,c),Ln=!1;var d=t.memoizedState;a.state=d,El(t,r,a,o),s=t.memoizedState,l!==r||d!==s||pt.current||Ln?(typeof p=="function"&&(uc(t,n,p,r),s=t.memoizedState),(l=Ln||gp(t,n,l,r,d,s,c))?(f||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),a.props=r,a.state=s,a.context=c,r=l):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,bg(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:zt(t.type,l),a.props=c,f=t.pendingProps,d=a.context,s=n.contextType,typeof s=="object"&&s!==null?s=Pt(s):(s=ht(n)?kr:Je.current,s=_o(t,s));var y=n.getDerivedStateFromProps;(p=typeof y=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(l!==f||d!==s)&&vp(t,a,r,s),Ln=!1,d=t.memoizedState,a.state=d,El(t,r,a,o);var w=t.memoizedState;l!==f||d!==w||pt.current||Ln?(typeof y=="function"&&(uc(t,n,y,r),w=t.memoizedState),(c=Ln||gp(t,n,c,r,d,w,s)||!1)?(p||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,w,s),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,w,s)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),a.props=r,a.state=w,a.context=s,r=c):(typeof a.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return pc(e,t,n,r,i,o)}function pc(e,t,n,r,o,i){Qg(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return o&&sp(t,n,!1),En(e,t,i);r=t.stateNode,d_.current=t;var l=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=Co(t,e.child,null,i),t.child=Co(t,null,l,i)):ot(e,t,l,i),t.memoizedState=r.state,o&&sp(t,n,!0),t.child}function Xg(e){var t=e.stateNode;t.pendingContext?lp(e,t.pendingContext,t.pendingContext!==t.context):t.context&&lp(e,t.context,!1),Sd(e,t.containerInfo)}function Sp(e,t,n,r,o){return wo(),yd(o),t.flags|=256,ot(e,t,n,r),t.child}var hc={dehydrated:null,treeContext:null,retryLane:0};function mc(e){return{baseLanes:e,cachePool:null,transitions:null}}function qg(e,t,n){var r=t.pendingProps,o=we.current,i=!1,a=(t.flags&128)!==0,l;if((l=a)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ge(we,o&1),e===null)return lc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=us(a,r,0,null),e=br(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=mc(n),t.memoizedState=hc,e):Nd(t,a));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return f_(e,t,a,r,l,o,n);if(i){i=r.fallback,a=t.mode,o=e.child,l=o.sibling;var s={mode:"hidden",children:r.children};return!(a&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=s,t.deletions=null):(r=Yn(o,s),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=Yn(l,i):(i=br(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?mc(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=hc,r}return i=e.child,e=i.sibling,r=Yn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Nd(e,t){return t=us({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ta(e,t,n,r){return r!==null&&yd(r),Co(t,e.child,null,n),e=Nd(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function f_(e,t,n,r,o,i,a){if(n)return t.flags&256?(t.flags&=-257,r=mu(Error(A(422))),Ta(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=us({mode:"visible",children:r.children},o,0,null),i=br(i,o,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Co(t,e.child,null,a),t.child.memoizedState=mc(a),t.memoizedState=hc,i);if(!(t.mode&1))return Ta(e,t,a,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(A(419)),r=mu(i,r,void 0),Ta(e,t,a,r)}if(l=(a&e.childLanes)!==0,ct||l){if(r=ze,r!==null){switch(a&-a){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|a)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,On(e,o),Ht(r,e,o,-1))}return $d(),r=mu(Error(A(421))),Ta(e,t,a,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=O_.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,xt=Vn(o.nextSibling),_t=t,xe=!0,Ut=null,e!==null&&(kt[Mt++]=xn,kt[Mt++]=_n,kt[Mt++]=Mr,xn=e.id,_n=e.overflow,Mr=t),t=Nd(t,r.children),t.flags|=4096,t)}function Op(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),sc(e.return,t,n)}function gu(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Zg(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ot(e,t,r.children,n),r=we.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Op(e,n,t);else if(e.tag===19)Op(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ge(we,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&kl(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),gu(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&kl(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}gu(t,!0,n,null,i);break;case"together":gu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function tl(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function En(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(A(153));if(t.child!==null){for(e=t.child,n=Yn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Yn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function p_(e,t,n){switch(t.tag){case 3:Xg(t),wo();break;case 5:Sg(t);break;case 1:ht(t.type)&&wl(t);break;case 4:Sd(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ge(Sl,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ge(we,we.current&1),t.flags|=128,null):n&t.child.childLanes?qg(e,t,n):(ge(we,we.current&1),e=En(e,t,n),e!==null?e.sibling:null);ge(we,we.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Zg(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ge(we,we.current),r)break;return null;case 22:case 23:return t.lanes=0,Yg(e,t,n)}return En(e,t,n)}var Jg,gc,ev,tv;Jg=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};gc=function(){};ev=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,xr(ln.current);var i=null;switch(n){case"input":o=$u(e,o),r=$u(e,r),i=[];break;case"select":o=Se({},o,{value:void 0}),r=Se({},r,{value:void 0}),i=[];break;case"textarea":o=Fu(e,o),r=Fu(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=xl)}Wu(n,r);var a;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var l=o[c];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(hi.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var s=r[c];if(l=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&s!==l&&(s!=null||l!=null))if(c==="style")if(l){for(a in l)!l.hasOwnProperty(a)||s&&s.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in s)s.hasOwnProperty(a)&&l[a]!==s[a]&&(n||(n={}),n[a]=s[a])}else n||(i||(i=[]),i.push(c,n)),n=s;else c==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,l=l?l.__html:void 0,s!=null&&l!==s&&(i=i||[]).push(c,s)):c==="children"?typeof s!="string"&&typeof s!="number"||(i=i||[]).push(c,""+s):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(hi.hasOwnProperty(c)?(s!=null&&c==="onScroll"&&ve("scroll",e),i||l===s||(i=[])):(i=i||[]).push(c,s))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};tv=function(e,t,n,r){n!==r&&(t.flags|=4)};function Ko(e,t){if(!xe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function h_(e,t,n){var r=t.pendingProps;switch(vd(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qe(t),null;case 1:return ht(t.type)&&_l(),qe(t),null;case 3:return r=t.stateNode,bo(),ye(pt),ye(Je),Ed(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ma(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ut!==null&&(Sc(Ut),Ut=null))),gc(e,t),qe(t),null;case 5:Od(t);var o=xr(Ei.current);if(n=t.type,e!==null&&t.stateNode!=null)ev(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(A(166));return qe(t),null}if(e=xr(ln.current),Ma(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[tn]=t,r[Si]=i,e=(t.mode&1)!==0,n){case"dialog":ve("cancel",r),ve("close",r);break;case"iframe":case"object":case"embed":ve("load",r);break;case"video":case"audio":for(o=0;o<Jo.length;o++)ve(Jo[o],r);break;case"source":ve("error",r);break;case"img":case"image":case"link":ve("error",r),ve("load",r);break;case"details":ve("toggle",r);break;case"input":If(r,i),ve("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ve("invalid",r);break;case"textarea":Rf(r,i),ve("invalid",r)}Wu(n,i),o=null;for(var a in i)if(i.hasOwnProperty(a)){var l=i[a];a==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&ka(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&ka(r.textContent,l,e),o=["children",""+l]):hi.hasOwnProperty(a)&&l!=null&&a==="onScroll"&&ve("scroll",r)}switch(n){case"input":xa(r),Lf(r,i,!0);break;case"textarea":xa(r),Df(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=xl)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Am(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[tn]=t,e[Si]=r,Jg(e,t,!1,!1),t.stateNode=e;e:{switch(a=Vu(n,r),n){case"dialog":ve("cancel",e),ve("close",e),o=r;break;case"iframe":case"object":case"embed":ve("load",e),o=r;break;case"video":case"audio":for(o=0;o<Jo.length;o++)ve(Jo[o],e);o=r;break;case"source":ve("error",e),o=r;break;case"img":case"image":case"link":ve("error",e),ve("load",e),o=r;break;case"details":ve("toggle",e),o=r;break;case"input":If(e,r),o=$u(e,r),ve("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Se({},r,{value:void 0}),ve("invalid",e);break;case"textarea":Rf(e,r),o=Fu(e,r),ve("invalid",e);break;default:o=r}Wu(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var s=l[i];i==="style"?Nm(e,s):i==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,s!=null&&Tm(e,s)):i==="children"?typeof s=="string"?(n!=="textarea"||s!=="")&&mi(e,s):typeof s=="number"&&mi(e,""+s):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(hi.hasOwnProperty(i)?s!=null&&i==="onScroll"&&ve("scroll",e):s!=null&&nd(e,i,s,a))}switch(n){case"input":xa(e),Lf(e,r,!1);break;case"textarea":xa(e),Df(e);break;case"option":r.value!=null&&e.setAttribute("value",""+er(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?uo(e,!!r.multiple,i,!1):r.defaultValue!=null&&uo(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=xl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return qe(t),null;case 6:if(e&&t.stateNode!=null)tv(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(A(166));if(n=xr(Ei.current),xr(ln.current),Ma(t)){if(r=t.stateNode,n=t.memoizedProps,r[tn]=t,(i=r.nodeValue!==n)&&(e=_t,e!==null))switch(e.tag){case 3:ka(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ka(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tn]=t,t.stateNode=r}return qe(t),null;case 13:if(ye(we),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(xe&&xt!==null&&t.mode&1&&!(t.flags&128))xg(),wo(),t.flags|=98560,i=!1;else if(i=Ma(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(A(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(A(317));i[tn]=t}else wo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;qe(t),i=!1}else Ut!==null&&(Sc(Ut),Ut=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||we.current&1?Re===0&&(Re=3):$d())),t.updateQueue!==null&&(t.flags|=4),qe(t),null);case 4:return bo(),gc(e,t),e===null&&Ci(t.stateNode.containerInfo),qe(t),null;case 10:return wd(t.type._context),qe(t),null;case 17:return ht(t.type)&&_l(),qe(t),null;case 19:if(ye(we),i=t.memoizedState,i===null)return qe(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)Ko(i,!1);else{if(Re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=kl(e),a!==null){for(t.flags|=128,Ko(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ge(we,we.current&1|2),t.child}e=e.sibling}i.tail!==null&&Te()>Oo&&(t.flags|=128,r=!0,Ko(i,!1),t.lanes=4194304)}else{if(!r)if(e=kl(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ko(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!xe)return qe(t),null}else 2*Te()-i.renderingStartTime>Oo&&n!==1073741824&&(t.flags|=128,r=!0,Ko(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Te(),t.sibling=null,n=we.current,ge(we,r?n&1|2:n&1),t):(qe(t),null);case 22:case 23:return Dd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?vt&1073741824&&(qe(t),t.subtreeFlags&6&&(t.flags|=8192)):qe(t),null;case 24:return null;case 25:return null}throw Error(A(156,t.tag))}function m_(e,t){switch(vd(t),t.tag){case 1:return ht(t.type)&&_l(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return bo(),ye(pt),ye(Je),Ed(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Od(t),null;case 13:if(ye(we),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(A(340));wo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ye(we),null;case 4:return bo(),null;case 10:return wd(t.type._context),null;case 22:case 23:return Dd(),null;case 24:return null;default:return null}}var ja=!1,Ze=!1,g_=typeof WeakSet=="function"?WeakSet:Set,$=null;function no(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ee(e,t,r)}else n.current=null}function vc(e,t,n){try{n()}catch(r){Ee(e,t,r)}}var Ep=!1;function v_(e,t){if(ec=gl,e=ag(),md(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,l=-1,s=-1,c=0,p=0,f=e,d=null;t:for(;;){for(var y;f!==n||o!==0&&f.nodeType!==3||(l=a+o),f!==i||r!==0&&f.nodeType!==3||(s=a+r),f.nodeType===3&&(a+=f.nodeValue.length),(y=f.firstChild)!==null;)d=f,f=y;for(;;){if(f===e)break t;if(d===n&&++c===o&&(l=a),d===i&&++p===r&&(s=a),(y=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=y}n=l===-1||s===-1?null:{start:l,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(tc={focusedElem:e,selectionRange:n},gl=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var x=w.memoizedProps,O=w.memoizedState,v=t.stateNode,m=v.getSnapshotBeforeUpdate(t.elementType===t.type?x:zt(t.type,x),O);v.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(A(163))}}catch(_){Ee(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return w=Ep,Ep=!1,w}function si(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&vc(t,n,i)}o=o.next}while(o!==r)}}function ls(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function yc(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function nv(e){var t=e.alternate;t!==null&&(e.alternate=null,nv(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tn],delete t[Si],delete t[oc],delete t[Jx],delete t[e_])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function rv(e){return e.tag===5||e.tag===3||e.tag===4}function kp(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||rv(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function xc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=xl));else if(r!==4&&(e=e.child,e!==null))for(xc(e,t,n),e=e.sibling;e!==null;)xc(e,t,n),e=e.sibling}function _c(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(_c(e,t,n),e=e.sibling;e!==null;)_c(e,t,n),e=e.sibling}var Ge=null,Ft=!1;function Pn(e,t,n){for(n=n.child;n!==null;)ov(e,t,n),n=n.sibling}function ov(e,t,n){if(an&&typeof an.onCommitFiberUnmount=="function")try{an.onCommitFiberUnmount(Jl,n)}catch{}switch(n.tag){case 5:Ze||no(n,t);case 6:var r=Ge,o=Ft;Ge=null,Pn(e,t,n),Ge=r,Ft=o,Ge!==null&&(Ft?(e=Ge,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ge.removeChild(n.stateNode));break;case 18:Ge!==null&&(Ft?(e=Ge,n=n.stateNode,e.nodeType===8?uu(e.parentNode,n):e.nodeType===1&&uu(e,n),xi(e)):uu(Ge,n.stateNode));break;case 4:r=Ge,o=Ft,Ge=n.stateNode.containerInfo,Ft=!0,Pn(e,t,n),Ge=r,Ft=o;break;case 0:case 11:case 14:case 15:if(!Ze&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&vc(n,t,a),o=o.next}while(o!==r)}Pn(e,t,n);break;case 1:if(!Ze&&(no(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Ee(n,t,l)}Pn(e,t,n);break;case 21:Pn(e,t,n);break;case 22:n.mode&1?(Ze=(r=Ze)||n.memoizedState!==null,Pn(e,t,n),Ze=r):Pn(e,t,n);break;default:Pn(e,t,n)}}function Mp(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new g_),t.forEach(function(r){var o=E_.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,a=t,l=a;e:for(;l!==null;){switch(l.tag){case 5:Ge=l.stateNode,Ft=!1;break e;case 3:Ge=l.stateNode.containerInfo,Ft=!0;break e;case 4:Ge=l.stateNode.containerInfo,Ft=!0;break e}l=l.return}if(Ge===null)throw Error(A(160));ov(i,a,o),Ge=null,Ft=!1;var s=o.alternate;s!==null&&(s.return=null),o.return=null}catch(c){Ee(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)iv(t,e),t=t.sibling}function iv(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),Zt(e),r&4){try{si(3,e,e.return),ls(3,e)}catch(x){Ee(e,e.return,x)}try{si(5,e,e.return)}catch(x){Ee(e,e.return,x)}}break;case 1:Bt(t,e),Zt(e),r&512&&n!==null&&no(n,n.return);break;case 5:if(Bt(t,e),Zt(e),r&512&&n!==null&&no(n,n.return),e.flags&32){var o=e.stateNode;try{mi(o,"")}catch(x){Ee(e,e.return,x)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,l=e.type,s=e.updateQueue;if(e.updateQueue=null,s!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&km(o,i),Vu(l,a);var c=Vu(l,i);for(a=0;a<s.length;a+=2){var p=s[a],f=s[a+1];p==="style"?Nm(o,f):p==="dangerouslySetInnerHTML"?Tm(o,f):p==="children"?mi(o,f):nd(o,p,f,c)}switch(l){case"input":Bu(o,i);break;case"textarea":Mm(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?uo(o,!!i.multiple,y,!1):d!==!!i.multiple&&(i.defaultValue!=null?uo(o,!!i.multiple,i.defaultValue,!0):uo(o,!!i.multiple,i.multiple?[]:"",!1))}o[Si]=i}catch(x){Ee(e,e.return,x)}}break;case 6:if(Bt(t,e),Zt(e),r&4){if(e.stateNode===null)throw Error(A(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(x){Ee(e,e.return,x)}}break;case 3:if(Bt(t,e),Zt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{xi(t.containerInfo)}catch(x){Ee(e,e.return,x)}break;case 4:Bt(t,e),Zt(e);break;case 13:Bt(t,e),Zt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Ld=Te())),r&4&&Mp(e);break;case 22:if(p=n!==null&&n.memoizedState!==null,e.mode&1?(Ze=(c=Ze)||p,Bt(t,e),Ze=c):Bt(t,e),Zt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!p&&e.mode&1)for($=e,p=e.child;p!==null;){for(f=$=p;$!==null;){switch(d=$,y=d.child,d.tag){case 0:case 11:case 14:case 15:si(4,d,d.return);break;case 1:no(d,d.return);var w=d.stateNode;if(typeof w.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(x){Ee(r,n,x)}}break;case 5:no(d,d.return);break;case 22:if(d.memoizedState!==null){Tp(f);continue}}y!==null?(y.return=d,$=y):Tp(f)}p=p.sibling}e:for(p=null,f=e;;){if(f.tag===5){if(p===null){p=f;try{o=f.stateNode,c?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=f.stateNode,s=f.memoizedProps.style,a=s!=null&&s.hasOwnProperty("display")?s.display:null,l.style.display=jm("display",a))}catch(x){Ee(e,e.return,x)}}}else if(f.tag===6){if(p===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(x){Ee(e,e.return,x)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;p===f&&(p=null),f=f.return}p===f&&(p=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Bt(t,e),Zt(e),r&4&&Mp(e);break;case 21:break;default:Bt(t,e),Zt(e)}}function Zt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(rv(n)){var r=n;break e}n=n.return}throw Error(A(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(mi(o,""),r.flags&=-33);var i=kp(e);_c(e,i,o);break;case 3:case 4:var a=r.stateNode.containerInfo,l=kp(e);xc(e,l,a);break;default:throw Error(A(161))}}catch(s){Ee(e,e.return,s)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function y_(e,t,n){$=e,av(e)}function av(e,t,n){for(var r=(e.mode&1)!==0;$!==null;){var o=$,i=o.child;if(o.tag===22&&r){var a=o.memoizedState!==null||ja;if(!a){var l=o.alternate,s=l!==null&&l.memoizedState!==null||Ze;l=ja;var c=Ze;if(ja=a,(Ze=s)&&!c)for($=o;$!==null;)a=$,s=a.child,a.tag===22&&a.memoizedState!==null?jp(o):s!==null?(s.return=a,$=s):jp(o);for(;i!==null;)$=i,av(i),i=i.sibling;$=o,ja=l,Ze=c}Ap(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,$=i):Ap(e)}}function Ap(e){for(;$!==null;){var t=$;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ze||ls(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ze)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:zt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&pp(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}pp(t,a,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var p=c.memoizedState;if(p!==null){var f=p.dehydrated;f!==null&&xi(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(A(163))}Ze||t.flags&512&&yc(t)}catch(d){Ee(t,t.return,d)}}if(t===e){$=null;break}if(n=t.sibling,n!==null){n.return=t.return,$=n;break}$=t.return}}function Tp(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var n=t.sibling;if(n!==null){n.return=t.return,$=n;break}$=t.return}}function jp(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ls(4,t)}catch(s){Ee(t,n,s)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(s){Ee(t,o,s)}}var i=t.return;try{yc(t)}catch(s){Ee(t,i,s)}break;case 5:var a=t.return;try{yc(t)}catch(s){Ee(t,a,s)}}}catch(s){Ee(t,t.return,s)}if(t===e){$=null;break}var l=t.sibling;if(l!==null){l.return=t.return,$=l;break}$=t.return}}var x_=Math.ceil,Tl=Mn.ReactCurrentDispatcher,Pd=Mn.ReactCurrentOwner,Nt=Mn.ReactCurrentBatchConfig,te=0,ze=null,Pe=null,Ke=0,vt=0,ro=or(0),Re=0,Ti=null,Tr=0,ss=0,Id=0,ui=null,ut=null,Ld=0,Oo=1/0,pn=null,jl=!1,wc=null,Gn=null,Na=!1,Bn=null,Nl=0,ci=0,Cc=null,nl=-1,rl=0;function it(){return te&6?Te():nl!==-1?nl:nl=Te()}function Kn(e){return e.mode&1?te&2&&Ke!==0?Ke&-Ke:n_.transition!==null?(rl===0&&(rl=Vm()),rl):(e=fe,e!==0||(e=window.event,e=e===void 0?16:qm(e.type)),e):1}function Ht(e,t,n,r){if(50<ci)throw ci=0,Cc=null,Error(A(185));Gi(e,n,r),(!(te&2)||e!==ze)&&(e===ze&&(!(te&2)&&(ss|=n),Re===4&&Dn(e,Ke)),mt(e,r),n===1&&te===0&&!(t.mode&1)&&(Oo=Te()+500,os&&ir()))}function mt(e,t){var n=e.callbackNode;nx(e,t);var r=ml(e,e===ze?Ke:0);if(r===0)n!==null&&zf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&zf(n),t===1)e.tag===0?t_(Np.bind(null,e)):gg(Np.bind(null,e)),qx(function(){!(te&6)&&ir()}),n=null;else{switch(Hm(r)){case 1:n=ld;break;case 4:n=Um;break;case 16:n=hl;break;case 536870912:n=Wm;break;default:n=hl}n=hv(n,lv.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function lv(e,t){if(nl=-1,rl=0,te&6)throw Error(A(327));var n=e.callbackNode;if(mo()&&e.callbackNode!==n)return null;var r=ml(e,e===ze?Ke:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Pl(e,r);else{t=r;var o=te;te|=2;var i=uv();(ze!==e||Ke!==t)&&(pn=null,Oo=Te()+500,Cr(e,t));do try{C_();break}catch(l){sv(e,l)}while(!0);_d(),Tl.current=i,te=o,Pe!==null?t=0:(ze=null,Ke=0,t=Re)}if(t!==0){if(t===2&&(o=Qu(e),o!==0&&(r=o,t=bc(e,o))),t===1)throw n=Ti,Cr(e,0),Dn(e,r),mt(e,Te()),n;if(t===6)Dn(e,r);else{if(o=e.current.alternate,!(r&30)&&!__(o)&&(t=Pl(e,r),t===2&&(i=Qu(e),i!==0&&(r=i,t=bc(e,i))),t===1))throw n=Ti,Cr(e,0),Dn(e,r),mt(e,Te()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(A(345));case 2:pr(e,ut,pn);break;case 3:if(Dn(e,r),(r&130023424)===r&&(t=Ld+500-Te(),10<t)){if(ml(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){it(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=rc(pr.bind(null,e,ut,pn),t);break}pr(e,ut,pn);break;case 4:if(Dn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var a=31-Vt(r);i=1<<a,a=t[a],a>o&&(o=a),r&=~i}if(r=o,r=Te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*x_(r/1960))-r,10<r){e.timeoutHandle=rc(pr.bind(null,e,ut,pn),r);break}pr(e,ut,pn);break;case 5:pr(e,ut,pn);break;default:throw Error(A(329))}}}return mt(e,Te()),e.callbackNode===n?lv.bind(null,e):null}function bc(e,t){var n=ui;return e.current.memoizedState.isDehydrated&&(Cr(e,t).flags|=256),e=Pl(e,t),e!==2&&(t=ut,ut=n,t!==null&&Sc(t)),e}function Sc(e){ut===null?ut=e:ut.push.apply(ut,e)}function __(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Qt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Dn(e,t){for(t&=~Id,t&=~ss,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Vt(t),r=1<<n;e[n]=-1,t&=~r}}function Np(e){if(te&6)throw Error(A(327));mo();var t=ml(e,0);if(!(t&1))return mt(e,Te()),null;var n=Pl(e,t);if(e.tag!==0&&n===2){var r=Qu(e);r!==0&&(t=r,n=bc(e,r))}if(n===1)throw n=Ti,Cr(e,0),Dn(e,t),mt(e,Te()),n;if(n===6)throw Error(A(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,pr(e,ut,pn),mt(e,Te()),null}function Rd(e,t){var n=te;te|=1;try{return e(t)}finally{te=n,te===0&&(Oo=Te()+500,os&&ir())}}function jr(e){Bn!==null&&Bn.tag===0&&!(te&6)&&mo();var t=te;te|=1;var n=Nt.transition,r=fe;try{if(Nt.transition=null,fe=1,e)return e()}finally{fe=r,Nt.transition=n,te=t,!(te&6)&&ir()}}function Dd(){vt=ro.current,ye(ro)}function Cr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Xx(n)),Pe!==null)for(n=Pe.return;n!==null;){var r=n;switch(vd(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&_l();break;case 3:bo(),ye(pt),ye(Je),Ed();break;case 5:Od(r);break;case 4:bo();break;case 13:ye(we);break;case 19:ye(we);break;case 10:wd(r.type._context);break;case 22:case 23:Dd()}n=n.return}if(ze=e,Pe=e=Yn(e.current,null),Ke=vt=t,Re=0,Ti=null,Id=ss=Tr=0,ut=ui=null,yr!==null){for(t=0;t<yr.length;t++)if(n=yr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=o,r.next=a}n.pending=r}yr=null}return e}function sv(e,t){do{var n=Pe;try{if(_d(),Ja.current=Al,Ml){for(var r=be.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ml=!1}if(Ar=0,$e=Le=be=null,li=!1,ki=0,Pd.current=null,n===null||n.return===null){Re=1,Ti=t,Pe=null;break}e:{var i=e,a=n.return,l=n,s=t;if(t=Ke,l.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){var c=s,p=l,f=p.tag;if(!(p.mode&1)&&(f===0||f===11||f===15)){var d=p.alternate;d?(p.updateQueue=d.updateQueue,p.memoizedState=d.memoizedState,p.lanes=d.lanes):(p.updateQueue=null,p.memoizedState=null)}var y=xp(a);if(y!==null){y.flags&=-257,_p(y,a,l,i,t),y.mode&1&&yp(i,c,t),t=y,s=c;var w=t.updateQueue;if(w===null){var x=new Set;x.add(s),t.updateQueue=x}else w.add(s);break e}else{if(!(t&1)){yp(i,c,t),$d();break e}s=Error(A(426))}}else if(xe&&l.mode&1){var O=xp(a);if(O!==null){!(O.flags&65536)&&(O.flags|=256),_p(O,a,l,i,t),yd(So(s,l));break e}}i=s=So(s,l),Re!==4&&(Re=2),ui===null?ui=[i]:ui.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var v=Hg(i,s,t);fp(i,v);break e;case 1:l=s;var m=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Gn===null||!Gn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var _=Gg(i,l,t);fp(i,_);break e}}i=i.return}while(i!==null)}dv(n)}catch(S){t=S,Pe===n&&n!==null&&(Pe=n=n.return);continue}break}while(!0)}function uv(){var e=Tl.current;return Tl.current=Al,e===null?Al:e}function $d(){(Re===0||Re===3||Re===2)&&(Re=4),ze===null||!(Tr&268435455)&&!(ss&268435455)||Dn(ze,Ke)}function Pl(e,t){var n=te;te|=2;var r=uv();(ze!==e||Ke!==t)&&(pn=null,Cr(e,t));do try{w_();break}catch(o){sv(e,o)}while(!0);if(_d(),te=n,Tl.current=r,Pe!==null)throw Error(A(261));return ze=null,Ke=0,Re}function w_(){for(;Pe!==null;)cv(Pe)}function C_(){for(;Pe!==null&&!Ky();)cv(Pe)}function cv(e){var t=pv(e.alternate,e,vt);e.memoizedProps=e.pendingProps,t===null?dv(e):Pe=t,Pd.current=null}function dv(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=m_(n,t),n!==null){n.flags&=32767,Pe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Re=6,Pe=null;return}}else if(n=h_(n,t,vt),n!==null){Pe=n;return}if(t=t.sibling,t!==null){Pe=t;return}Pe=t=e}while(t!==null);Re===0&&(Re=5)}function pr(e,t,n){var r=fe,o=Nt.transition;try{Nt.transition=null,fe=1,b_(e,t,n,r)}finally{Nt.transition=o,fe=r}return null}function b_(e,t,n,r){do mo();while(Bn!==null);if(te&6)throw Error(A(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(A(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(rx(e,i),e===ze&&(Pe=ze=null,Ke=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Na||(Na=!0,hv(hl,function(){return mo(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Nt.transition,Nt.transition=null;var a=fe;fe=1;var l=te;te|=4,Pd.current=null,v_(e,n),iv(n,e),Wx(tc),gl=!!ec,tc=ec=null,e.current=n,y_(n),Yy(),te=l,fe=a,Nt.transition=i}else e.current=n;if(Na&&(Na=!1,Bn=e,Nl=o),i=e.pendingLanes,i===0&&(Gn=null),qy(n.stateNode),mt(e,Te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(jl)throw jl=!1,e=wc,wc=null,e;return Nl&1&&e.tag!==0&&mo(),i=e.pendingLanes,i&1?e===Cc?ci++:(ci=0,Cc=e):ci=0,ir(),null}function mo(){if(Bn!==null){var e=Hm(Nl),t=Nt.transition,n=fe;try{if(Nt.transition=null,fe=16>e?16:e,Bn===null)var r=!1;else{if(e=Bn,Bn=null,Nl=0,te&6)throw Error(A(331));var o=te;for(te|=4,$=e.current;$!==null;){var i=$,a=i.child;if($.flags&16){var l=i.deletions;if(l!==null){for(var s=0;s<l.length;s++){var c=l[s];for($=c;$!==null;){var p=$;switch(p.tag){case 0:case 11:case 15:si(8,p,i)}var f=p.child;if(f!==null)f.return=p,$=f;else for(;$!==null;){p=$;var d=p.sibling,y=p.return;if(nv(p),p===c){$=null;break}if(d!==null){d.return=y,$=d;break}$=y}}}var w=i.alternate;if(w!==null){var x=w.child;if(x!==null){w.child=null;do{var O=x.sibling;x.sibling=null,x=O}while(x!==null)}}$=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,$=a;else e:for(;$!==null;){if(i=$,i.flags&2048)switch(i.tag){case 0:case 11:case 15:si(9,i,i.return)}var v=i.sibling;if(v!==null){v.return=i.return,$=v;break e}$=i.return}}var m=e.current;for($=m;$!==null;){a=$;var g=a.child;if(a.subtreeFlags&2064&&g!==null)g.return=a,$=g;else e:for(a=m;$!==null;){if(l=$,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:ls(9,l)}}catch(S){Ee(l,l.return,S)}if(l===a){$=null;break e}var _=l.sibling;if(_!==null){_.return=l.return,$=_;break e}$=l.return}}if(te=o,ir(),an&&typeof an.onPostCommitFiberRoot=="function")try{an.onPostCommitFiberRoot(Jl,e)}catch{}r=!0}return r}finally{fe=n,Nt.transition=t}}return!1}function Pp(e,t,n){t=So(n,t),t=Hg(e,t,1),e=Hn(e,t,1),t=it(),e!==null&&(Gi(e,1,t),mt(e,t))}function Ee(e,t,n){if(e.tag===3)Pp(e,e,n);else for(;t!==null;){if(t.tag===3){Pp(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Gn===null||!Gn.has(r))){e=So(n,e),e=Gg(t,e,1),t=Hn(t,e,1),e=it(),t!==null&&(Gi(t,1,e),mt(t,e));break}}t=t.return}}function S_(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=it(),e.pingedLanes|=e.suspendedLanes&n,ze===e&&(Ke&n)===n&&(Re===4||Re===3&&(Ke&130023424)===Ke&&500>Te()-Ld?Cr(e,0):Id|=n),mt(e,t)}function fv(e,t){t===0&&(e.mode&1?(t=Ca,Ca<<=1,!(Ca&130023424)&&(Ca=4194304)):t=1);var n=it();e=On(e,t),e!==null&&(Gi(e,t,n),mt(e,n))}function O_(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),fv(e,n)}function E_(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(A(314))}r!==null&&r.delete(t),fv(e,n)}var pv;pv=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||pt.current)ct=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ct=!1,p_(e,t,n);ct=!!(e.flags&131072)}else ct=!1,xe&&t.flags&1048576&&vg(t,bl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;tl(e,t),e=t.pendingProps;var o=_o(t,Je.current);ho(t,n),o=Md(null,t,r,e,o,n);var i=Ad();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ht(r)?(i=!0,wl(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,bd(t),o.updater=as,t.stateNode=o,o._reactInternals=t,cc(t,r,e,n),t=pc(null,t,r,!0,i,n)):(t.tag=0,xe&&i&&gd(t),ot(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(tl(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=M_(r),e=zt(r,e),o){case 0:t=fc(null,t,r,e,n);break e;case 1:t=bp(null,t,r,e,n);break e;case 11:t=wp(null,t,r,e,n);break e;case 14:t=Cp(null,t,r,zt(r.type,e),n);break e}throw Error(A(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:zt(r,o),fc(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:zt(r,o),bp(e,t,r,o,n);case 3:e:{if(Xg(t),e===null)throw Error(A(387));r=t.pendingProps,i=t.memoizedState,o=i.element,bg(e,t),El(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=So(Error(A(423)),t),t=Sp(e,t,r,n,o);break e}else if(r!==o){o=So(Error(A(424)),t),t=Sp(e,t,r,n,o);break e}else for(xt=Vn(t.stateNode.containerInfo.firstChild),_t=t,xe=!0,Ut=null,n=wg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(wo(),r===o){t=En(e,t,n);break e}ot(e,t,r,n)}t=t.child}return t;case 5:return Sg(t),e===null&&lc(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,a=o.children,nc(r,o)?a=null:i!==null&&nc(r,i)&&(t.flags|=32),Qg(e,t),ot(e,t,a,n),t.child;case 6:return e===null&&lc(t),null;case 13:return qg(e,t,n);case 4:return Sd(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Co(t,null,r,n):ot(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:zt(r,o),wp(e,t,r,o,n);case 7:return ot(e,t,t.pendingProps,n),t.child;case 8:return ot(e,t,t.pendingProps.children,n),t.child;case 12:return ot(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,a=o.value,ge(Sl,r._currentValue),r._currentValue=a,i!==null)if(Qt(i.value,a)){if(i.children===o.children&&!pt.current){t=En(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){a=i.child;for(var s=l.firstContext;s!==null;){if(s.context===r){if(i.tag===1){s=wn(-1,n&-n),s.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var p=c.pending;p===null?s.next=s:(s.next=p.next,p.next=s),c.pending=s}}i.lanes|=n,s=i.alternate,s!==null&&(s.lanes|=n),sc(i.return,n,t),l.lanes|=n;break}s=s.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(A(341));a.lanes|=n,l=a.alternate,l!==null&&(l.lanes|=n),sc(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}ot(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,ho(t,n),o=Pt(o),r=r(o),t.flags|=1,ot(e,t,r,n),t.child;case 14:return r=t.type,o=zt(r,t.pendingProps),o=zt(r.type,o),Cp(e,t,r,o,n);case 15:return Kg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:zt(r,o),tl(e,t),t.tag=1,ht(r)?(e=!0,wl(t)):e=!1,ho(t,n),Vg(t,r,o),cc(t,r,o,n),pc(null,t,r,!0,e,n);case 19:return Zg(e,t,n);case 22:return Yg(e,t,n)}throw Error(A(156,t.tag))};function hv(e,t){return Fm(e,t)}function k_(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,n,r){return new k_(e,t,n,r)}function Bd(e){return e=e.prototype,!(!e||!e.isReactComponent)}function M_(e){if(typeof e=="function")return Bd(e)?1:0;if(e!=null){if(e=e.$$typeof,e===od)return 11;if(e===id)return 14}return 2}function Yn(e,t){var n=e.alternate;return n===null?(n=jt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ol(e,t,n,r,o,i){var a=2;if(r=e,typeof e=="function")Bd(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Kr:return br(n.children,o,i,t);case rd:a=8,o|=8;break;case Iu:return e=jt(12,n,t,o|2),e.elementType=Iu,e.lanes=i,e;case Lu:return e=jt(13,n,t,o),e.elementType=Lu,e.lanes=i,e;case Ru:return e=jt(19,n,t,o),e.elementType=Ru,e.lanes=i,e;case Sm:return us(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cm:a=10;break e;case bm:a=9;break e;case od:a=11;break e;case id:a=14;break e;case In:a=16,r=null;break e}throw Error(A(130,e==null?e:typeof e,""))}return t=jt(a,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function br(e,t,n,r){return e=jt(7,e,r,t),e.lanes=n,e}function us(e,t,n,r){return e=jt(22,e,r,t),e.elementType=Sm,e.lanes=n,e.stateNode={isHidden:!1},e}function vu(e,t,n){return e=jt(6,e,null,t),e.lanes=n,e}function yu(e,t,n){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function A_(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Zs(0),this.expirationTimes=Zs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function zd(e,t,n,r,o,i,a,l,s){return e=new A_(e,t,n,l,s),t===1?(t=1,i===!0&&(t|=8)):t=0,i=jt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},bd(i),e}function T_(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Gr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function mv(e){if(!e)return tr;e=e._reactInternals;e:{if(Lr(e)!==e||e.tag!==1)throw Error(A(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ht(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(A(171))}if(e.tag===1){var n=e.type;if(ht(n))return mg(e,n,t)}return t}function gv(e,t,n,r,o,i,a,l,s){return e=zd(n,r,!0,e,o,i,a,l,s),e.context=mv(null),n=e.current,r=it(),o=Kn(n),i=wn(r,o),i.callback=t??null,Hn(n,i,o),e.current.lanes=o,Gi(e,o,r),mt(e,r),e}function cs(e,t,n,r){var o=t.current,i=it(),a=Kn(o);return n=mv(n),t.context===null?t.context=n:t.pendingContext=n,t=wn(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Hn(o,t,a),e!==null&&(Ht(e,o,a,i),Za(e,o,a)),a}function Il(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ip(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Fd(e,t){Ip(e,t),(e=e.alternate)&&Ip(e,t)}function j_(){return null}var vv=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ud(e){this._internalRoot=e}ds.prototype.render=Ud.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(A(409));cs(e,t,null,null)};ds.prototype.unmount=Ud.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;jr(function(){cs(null,e,null,null)}),t[Sn]=null}};function ds(e){this._internalRoot=e}ds.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ym();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rn.length&&t!==0&&t<Rn[n].priority;n++);Rn.splice(n,0,e),n===0&&Xm(e)}};function Wd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function fs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Lp(){}function N_(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var c=Il(a);i.call(c)}}var a=gv(t,r,e,0,null,!1,!1,"",Lp);return e._reactRootContainer=a,e[Sn]=a.current,Ci(e.nodeType===8?e.parentNode:e),jr(),a}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var c=Il(s);l.call(c)}}var s=zd(e,0,!1,null,null,!1,!1,"",Lp);return e._reactRootContainer=s,e[Sn]=s.current,Ci(e.nodeType===8?e.parentNode:e),jr(function(){cs(t,s,n,r)}),s}function ps(e,t,n,r,o){var i=n._reactRootContainer;if(i){var a=i;if(typeof o=="function"){var l=o;o=function(){var s=Il(a);l.call(s)}}cs(t,a,e,o)}else a=N_(n,t,e,o,r);return Il(a)}Gm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Zo(t.pendingLanes);n!==0&&(sd(t,n|1),mt(t,Te()),!(te&6)&&(Oo=Te()+500,ir()))}break;case 13:jr(function(){var r=On(e,1);if(r!==null){var o=it();Ht(r,e,1,o)}}),Fd(e,1)}};ud=function(e){if(e.tag===13){var t=On(e,134217728);if(t!==null){var n=it();Ht(t,e,134217728,n)}Fd(e,134217728)}};Km=function(e){if(e.tag===13){var t=Kn(e),n=On(e,t);if(n!==null){var r=it();Ht(n,e,t,r)}Fd(e,t)}};Ym=function(){return fe};Qm=function(e,t){var n=fe;try{return fe=e,t()}finally{fe=n}};Gu=function(e,t,n){switch(t){case"input":if(Bu(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=rs(r);if(!o)throw Error(A(90));Em(r),Bu(r,o)}}}break;case"textarea":Mm(e,n);break;case"select":t=n.value,t!=null&&uo(e,!!n.multiple,t,!1)}};Lm=Rd;Rm=jr;var P_={usingClientEntryPoint:!1,Events:[Yi,qr,rs,Pm,Im,Rd]},Yo={findFiberByHostInstance:vr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},I_={bundleType:Yo.bundleType,version:Yo.version,rendererPackageName:Yo.rendererPackageName,rendererConfig:Yo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Mn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Bm(e),e===null?null:e.stateNode},findFiberByHostInstance:Yo.findFiberByHostInstance||j_,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Pa=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Pa.isDisabled&&Pa.supportsFiber)try{Jl=Pa.inject(I_),an=Pa}catch{}}Ct.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=P_;Ct.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Wd(t))throw Error(A(200));return T_(e,t,null,n)};Ct.createRoot=function(e,t){if(!Wd(e))throw Error(A(299));var n=!1,r="",o=vv;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=zd(e,1,!1,null,null,n,!1,r,o),e[Sn]=t.current,Ci(e.nodeType===8?e.parentNode:e),new Ud(t)};Ct.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(A(188)):(e=Object.keys(e).join(","),Error(A(268,e)));return e=Bm(t),e=e===null?null:e.stateNode,e};Ct.flushSync=function(e){return jr(e)};Ct.hydrate=function(e,t,n){if(!fs(t))throw Error(A(200));return ps(null,e,t,!0,n)};Ct.hydrateRoot=function(e,t,n){if(!Wd(e))throw Error(A(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",a=vv;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=gv(t,null,e,1,n??null,o,!1,i,a),e[Sn]=t.current,Ci(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ds(t)};Ct.render=function(e,t,n){if(!fs(t))throw Error(A(200));return ps(null,e,t,!1,n)};Ct.unmountComponentAtNode=function(e){if(!fs(e))throw Error(A(40));return e._reactRootContainer?(jr(function(){ps(null,null,e,!1,function(){e._reactRootContainer=null,e[Sn]=null})}),!0):!1};Ct.unstable_batchedUpdates=Rd;Ct.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!fs(n))throw Error(A(200));if(e==null||e._reactInternals===void 0)throw Error(A(38));return ps(e,t,n,!1,r)};Ct.version="18.3.1-next-f1338f8080-20240426";function yv(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(yv)}catch(e){console.error(e)}}yv(),ym.exports=Ct;var Xi=ym.exports;const Ia=ql(Xi);var xv,_v,Rp=Xi;_v=Rp.createRoot,xv=Rp.hydrateRoot;function Ie(){if(!window)throw new Error("Window object isn't available");return window}function wv(e){const t={...e,type:`ssr-${e.type}`};return Ie().parent.postMessage(t,"*")}function ie(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error(typeof e=="number"?"[MobX] minified error nr: "+e+(n.length?" "+n.map(String).join(","):"")+". Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.ts":"[MobX] "+e)}var L_={};function Vd(){return typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:L_}var Cv=Object.assign,Ll=Object.getOwnPropertyDescriptor,sn=Object.defineProperty,qi=Object.prototype,Oc=[];Object.freeze(Oc);var bv={};Object.freeze(bv);var R_=typeof Proxy<"u",D_=Object.toString();function Sv(){R_||ie("Proxy not available")}function Ov(e){var t=!1;return function(){if(!t)return t=!0,e.apply(this,arguments)}}var oo=function(){};function Xt(e){return typeof e=="function"}function Nr(e){var t=typeof e;switch(t){case"string":case"symbol":case"number":return!0}return!1}function hs(e){return e!==null&&typeof e=="object"}function kn(e){if(!hs(e))return!1;var t=Object.getPrototypeOf(e);if(t==null)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n.toString()===D_}function Ev(e){var t=e==null?void 0:e.constructor;return t?t.name==="GeneratorFunction"||t.displayName==="GeneratorFunction":!1}function Zi(e,t,n){sn(e,t,{enumerable:!1,writable:!0,configurable:!0,value:n})}function kv(e,t,n){sn(e,t,{enumerable:!1,writable:!1,configurable:!0,value:n})}function ar(e,t){var n="isMobX"+e;return t.prototype[n]=!0,function(r){return hs(r)&&r[n]===!0}}function Do(e){return e instanceof Map}function Ji(e){return e instanceof Set}var Mv=typeof Object.getOwnPropertySymbols<"u";function $_(e){var t=Object.keys(e);if(!Mv)return t;var n=Object.getOwnPropertySymbols(e);return n.length?[].concat(t,n.filter(function(r){return qi.propertyIsEnumerable.call(e,r)})):t}var Eo=typeof Reflect<"u"&&Reflect.ownKeys?Reflect.ownKeys:Mv?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames;function Av(e){return e===null?null:typeof e=="object"?""+e:e}function Cn(e,t){return qi.hasOwnProperty.call(e,t)}var B_=Object.getOwnPropertyDescriptors||function(t){var n={};return Eo(t).forEach(function(r){n[r]=Ll(t,r)}),n};function z_(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,W_(r.key),r)}}function Hd(e,t,n){return t&&z_(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ko(){return ko=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ko.apply(this,arguments)}function Tv(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Ec(e,t)}function Ec(e,t){return Ec=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},Ec(e,t)}function xu(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function F_(e,t){if(e){if(typeof e=="string")return Dp(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Dp(e,t)}}function Dp(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function io(e,t){var n=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=F_(e))||t){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function U_(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function W_(e){var t=U_(e,"string");return typeof t=="symbol"?t:String(t)}var nn=Symbol("mobx-stored-annotations");function un(e){function t(n,r){ea(n,r,e)}return Object.assign(t,e)}function ea(e,t,n){Cn(e,nn)||Zi(e,nn,ko({},e[nn])),Z_(n)||(e[nn][t]=n)}function V_(e){return Cn(e,nn)||Zi(e,nn,ko({},e[nn])),e[nn]}var H=Symbol("mobx administration"),ta=function(){function e(n){n===void 0&&(n="Atom"),this.name_=void 0,this.isPendingUnobservation_=!1,this.isBeingObserved_=!1,this.observers_=new Set,this.diffValue_=0,this.lastAccessedBy_=0,this.lowestObserverState_=oe.NOT_TRACKING_,this.onBOL=void 0,this.onBUOL=void 0,this.name_=n}var t=e.prototype;return t.onBO=function(){this.onBOL&&this.onBOL.forEach(function(r){return r()})},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach(function(r){return r()})},t.reportObserved=function(){return Xv(this)},t.reportChanged=function(){dt(),qv(this),P.stateVersion=P.stateVersion<Number.MAX_SAFE_INTEGER?P.stateVersion+1:Number.MIN_SAFE_INTEGER,ft()},t.toString=function(){return this.name_},e}(),Gd=ar("Atom",ta);function Kd(e,t,n){t===void 0&&(t=oo),n===void 0&&(n=oo);var r=new ta(e);return t!==oo&&qw(r,t),n!==oo&&i0(r,n),r}function H_(e,t){return e===t}function G_(e,t){return Jd(e,t)}function K_(e,t){return Jd(e,t,1)}function Y_(e,t){return Object.is?Object.is(e,t):e===t?e!==0||1/e===1/t:e!==e&&t!==t}var Rl={identity:H_,structural:G_,default:Y_,shallow:K_};function Mo(e,t,n){return Xd(e)?e:Array.isArray(e)?Be.array(e,{name:n}):kn(e)?Be.object(e,void 0,{name:n}):Do(e)?Be.map(e,{name:n}):Ji(e)?Be.set(e,{name:n}):typeof e=="function"&&!Qd(e)&&!Fl(e)?Ev(e)?Pi(e):Ni(n,e):e}function Q_(e,t,n){if(e==null||ca(e)||sa(e)||sr(e)||Rr(e))return e;if(Array.isArray(e))return Be.array(e,{name:n,deep:!1});if(kn(e))return Be.object(e,void 0,{name:n,deep:!1});if(Do(e))return Be.map(e,{name:n,deep:!1});if(Ji(e))return Be.set(e,{name:n,deep:!1})}function ms(e){return e}function X_(e,t){return Jd(e,t)?t:e}var q_="override";function Z_(e){return e.annotationType_===q_}function na(e,t){return{annotationType_:e,options_:t,make_:J_,extend_:ew}}function J_(e,t,n,r){var o;if((o=this.options_)!=null&&o.bound)return this.extend_(e,t,n,!1)===null?0:1;if(r===e.target_)return this.extend_(e,t,n,!1)===null?0:2;if(Qd(n.value))return 1;var i=jv(e,this,t,n,!1);return sn(r,t,i),2}function ew(e,t,n,r){var o=jv(e,this,t,n);return e.defineProperty_(t,o,r)}function tw(e,t,n,r){t.annotationType_,r.value}function jv(e,t,n,r,o){var i,a,l,s,c,p,f;o===void 0&&(o=P.safeDescriptors),tw(e,t,n,r);var d=r.value;if((i=t.options_)!=null&&i.bound){var y;d=d.bind((y=e.proxy_)!=null?y:e.target_)}return{value:To((a=(l=t.options_)==null?void 0:l.name)!=null?a:n.toString(),d,(s=(c=t.options_)==null?void 0:c.autoAction)!=null?s:!1,(p=t.options_)!=null&&p.bound?(f=e.proxy_)!=null?f:e.target_:void 0),configurable:o?e.isPlainObject_:!0,enumerable:!1,writable:!o}}function Nv(e,t){return{annotationType_:e,options_:t,make_:nw,extend_:rw}}function nw(e,t,n,r){var o;if(r===e.target_)return this.extend_(e,t,n,!1)===null?0:2;if((o=this.options_)!=null&&o.bound&&(!Cn(e.target_,t)||!Fl(e.target_[t]))&&this.extend_(e,t,n,!1)===null)return 0;if(Fl(n.value))return 1;var i=Pv(e,this,t,n,!1,!1);return sn(r,t,i),2}function rw(e,t,n,r){var o,i=Pv(e,this,t,n,(o=this.options_)==null?void 0:o.bound);return e.defineProperty_(t,i,r)}function ow(e,t,n,r){t.annotationType_,r.value}function Pv(e,t,n,r,o,i){i===void 0&&(i=P.safeDescriptors),ow(e,t,n,r);var a=r.value;if(Fl(a)||(a=Pi(a)),o){var l;a=a.bind((l=e.proxy_)!=null?l:e.target_),a.isMobXFlow=!0}return{value:a,configurable:i?e.isPlainObject_:!0,enumerable:!1,writable:!i}}function Yd(e,t){return{annotationType_:e,options_:t,make_:iw,extend_:aw}}function iw(e,t,n){return this.extend_(e,t,n,!1)===null?0:1}function aw(e,t,n,r){return lw(e,this,t,n),e.defineComputedProperty_(t,ko({},this.options_,{get:n.get,set:n.set}),r)}function lw(e,t,n,r){t.annotationType_,r.get}function gs(e,t){return{annotationType_:e,options_:t,make_:sw,extend_:uw}}function sw(e,t,n){return this.extend_(e,t,n,!1)===null?0:1}function uw(e,t,n,r){var o,i;return cw(e,this),e.defineObservableProperty_(t,n.value,(o=(i=this.options_)==null?void 0:i.enhancer)!=null?o:Mo,r)}function cw(e,t,n,r){t.annotationType_}var dw="true",fw=Iv();function Iv(e){return{annotationType_:dw,options_:e,make_:pw,extend_:hw}}function pw(e,t,n,r){var o,i;if(n.get)return Ao.make_(e,t,n,r);if(n.set){var a=To(t.toString(),n.set);return r===e.target_?e.defineProperty_(t,{configurable:P.safeDescriptors?e.isPlainObject_:!0,set:a})===null?0:2:(sn(r,t,{configurable:!0,set:a}),2)}if(r!==e.target_&&typeof n.value=="function"){var l;if(Ev(n.value)){var s,c=(s=this.options_)!=null&&s.autoBind?Pi.bound:Pi;return c.make_(e,t,n,r)}var p=(l=this.options_)!=null&&l.autoBind?Ni.bound:Ni;return p.make_(e,t,n,r)}var f=((o=this.options_)==null?void 0:o.deep)===!1?Be.ref:Be;if(typeof n.value=="function"&&(i=this.options_)!=null&&i.autoBind){var d;n.value=n.value.bind((d=e.proxy_)!=null?d:e.target_)}return f.make_(e,t,n,r)}function hw(e,t,n,r){var o,i;if(n.get)return Ao.extend_(e,t,n,r);if(n.set)return e.defineProperty_(t,{configurable:P.safeDescriptors?e.isPlainObject_:!0,set:To(t.toString(),n.set)},r);if(typeof n.value=="function"&&(o=this.options_)!=null&&o.autoBind){var a;n.value=n.value.bind((a=e.proxy_)!=null?a:e.target_)}var l=((i=this.options_)==null?void 0:i.deep)===!1?Be.ref:Be;return l.extend_(e,t,n,r)}var mw="observable",gw="observable.ref",vw="observable.shallow",yw="observable.struct",Lv={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};Object.freeze(Lv);function La(e){return e||Lv}var Rv=gs(mw),xw=gs(gw,{enhancer:ms}),_w=gs(vw,{enhancer:Q_}),ww=gs(yw,{enhancer:X_}),Dv=un(Rv);function Ra(e){return e.deep===!0?Mo:e.deep===!1?ms:bw(e.defaultDecorator)}function Cw(e){var t;return e?(t=e.defaultDecorator)!=null?t:Iv(e):void 0}function bw(e){var t,n;return e&&(t=(n=e.options_)==null?void 0:n.enhancer)!=null?t:Mo}function $v(e,t,n){if(Nr(t)){ea(e,t,Rv);return}return Xd(e)?e:kn(e)?Be.object(e,t,n):Array.isArray(e)?Be.array(e,t):Do(e)?Be.map(e,t):Ji(e)?Be.set(e,t):typeof e=="object"&&e!==null?e:Be.box(e,t)}Cv($v,Dv);var Sw={box:function(t,n){var r=La(n);return new Sr(t,Ra(r),r.name,!0,r.equals)},array:function(t,n){var r=La(n);return(P.useProxies===!1||r.proxy===!1?_C:dC)(t,Ra(r),r.name)},map:function(t,n){var r=La(n);return new p0(t,Ra(r),r.name)},set:function(t,n){var r=La(n);return new g0(t,Ra(r),r.name)},object:function(t,n,r){return l0(P.useProxies===!1||(r==null?void 0:r.proxy)===!1?ua({},r):lC({},r),t,n)},ref:un(xw),shallow:un(_w),deep:Dv,struct:un(ww)},Be=Cv($v,Sw),Bv="computed",Ow="computed.struct",zv=Yd(Bv),Ew=Yd(Ow,{equals:Rl.structural}),Ao=function(t,n){if(Nr(n))return ea(t,n,zv);if(kn(t))return un(Yd(Bv,t));var r=kn(n)?n:{};return r.get=t,r.name||(r.name=t.name||""),new ji(r)};Object.assign(Ao,zv);Ao.struct=un(Ew);var $p,Bp,Dl=0,kw=1,Mw=($p=(Bp=Ll(function(){},"name"))==null?void 0:Bp.configurable)!=null?$p:!1,zp={value:"action",configurable:!0,writable:!1,enumerable:!1};function To(e,t,n,r){n===void 0&&(n=!1);function o(){return Aw(e,n,t,r||this,arguments)}return o.isMobxAction=!0,Mw&&(zp.value=e,sn(o,"name",zp)),o}function Aw(e,t,n,r,o){var i=Tw(e,t);try{return n.apply(r,o)}catch(a){throw i.error_=a,a}finally{jw(i)}}function Tw(e,t,n,r){var o=!1,i=0,a=P.trackingDerivation,l=!t||!a;dt();var s=P.allowStateChanges;l&&($o(),s=ra(!0));var c=go(!0),p={runAsAction_:l,prevDerivation_:a,prevAllowStateChanges_:s,prevAllowStateReads_:c,notifySpy_:o,startTime_:i,actionId_:kw++,parentActionId_:Dl};return Dl=p.actionId_,p}function jw(e){Dl!==e.actionId_&&ie(30),Dl=e.parentActionId_,e.error_!==void 0&&(P.suppressReactionErrors=!0),oa(e.prevAllowStateChanges_),Xn(e.prevAllowStateReads_),ft(),e.runAsAction_&&Qn(e.prevDerivation_),P.suppressReactionErrors=!1}function Fv(e,t){var n=ra(e);try{return t()}finally{oa(n)}}function ra(e){var t=P.allowStateChanges;return P.allowStateChanges=e,t}function oa(e){P.allowStateChanges=e}var Uv;Uv=Symbol.toPrimitive;var Sr=function(e){Tv(t,e);function t(r,o,i,a,l){var s;return i===void 0&&(i="ObservableValue"),l===void 0&&(l=Rl.default),s=e.call(this,i)||this,s.enhancer=void 0,s.name_=void 0,s.equals=void 0,s.hasUnreportedChange_=!1,s.interceptors_=void 0,s.changeListeners_=void 0,s.value_=void 0,s.dehancer=void 0,s.enhancer=o,s.name_=i,s.equals=l,s.value_=o(r,void 0,i),s}var n=t.prototype;return n.dehanceValue=function(o){return this.dehancer!==void 0?this.dehancer(o):o},n.set=function(o){this.value_,o=this.prepareNewValue_(o),o!==P.UNCHANGED&&this.setNewValue_(o)},n.prepareNewValue_=function(o){if(At(this)){var i=Tt(this,{object:this,type:cn,newValue:o});if(!i)return P.UNCHANGED;o=i.newValue}return o=this.enhancer(o,this.value_,this.name_),this.equals(this.value_,o)?P.UNCHANGED:o},n.setNewValue_=function(o){var i=this.value_;this.value_=o,this.reportChanged(),Gt(this)&&Kt(this,{type:cn,object:this,newValue:o,oldValue:i})},n.get=function(){return this.reportObserved(),this.dehanceValue(this.value_)},n.intercept_=function(o){return aa(this,o)},n.observe_=function(o,i){return i&&o({observableKind:"value",debugObjectName:this.name_,object:this,type:cn,newValue:this.value_,oldValue:void 0}),la(this,o)},n.raw=function(){return this.value_},n.toJSON=function(){return this.get()},n.toString=function(){return this.name_+"["+this.value_+"]"},n.valueOf=function(){return Av(this.get())},n[Uv]=function(){return this.valueOf()},t}(ta),Nw=ar("ObservableValue",Sr),Wv;Wv=Symbol.toPrimitive;var ji=function(){function e(n){this.dependenciesState_=oe.NOT_TRACKING_,this.observing_=[],this.newObserving_=null,this.isBeingObserved_=!1,this.isPendingUnobservation_=!1,this.observers_=new Set,this.diffValue_=0,this.runId_=0,this.lastAccessedBy_=0,this.lowestObserverState_=oe.UP_TO_DATE_,this.unboundDepsCount_=0,this.value_=new Bl(null),this.name_=void 0,this.triggeredBy_=void 0,this.isComputing_=!1,this.isRunningSetter_=!1,this.derivation=void 0,this.setter_=void 0,this.isTracing_=$l.NONE,this.scope_=void 0,this.equals_=void 0,this.requiresReaction_=void 0,this.keepAlive_=void 0,this.onBOL=void 0,this.onBUOL=void 0,n.get||ie(31),this.derivation=n.get,this.name_=n.name||"ComputedValue",n.set&&(this.setter_=To("ComputedValue-setter",n.set)),this.equals_=n.equals||(n.compareStructural||n.struct?Rl.structural:Rl.default),this.scope_=n.context,this.requiresReaction_=n.requiresReaction,this.keepAlive_=!!n.keepAlive}var t=e.prototype;return t.onBecomeStale_=function(){Dw(this)},t.onBO=function(){this.onBOL&&this.onBOL.forEach(function(r){return r()})},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach(function(r){return r()})},t.get=function(){if(this.isComputing_&&ie(32,this.name_,this.derivation),P.inBatch===0&&this.observers_.size===0&&!this.keepAlive_)kc(this)&&(this.warnAboutUntrackedRead_(),dt(),this.value_=this.computeValue_(!1),ft());else if(Xv(this),kc(this)){var r=P.trackingContext;this.keepAlive_&&!r&&(P.trackingContext=this),this.trackAndCompute()&&Rw(this),P.trackingContext=r}var o=this.value_;if(il(o))throw o.cause;return o},t.set=function(r){if(this.setter_){this.isRunningSetter_&&ie(33,this.name_),this.isRunningSetter_=!0;try{this.setter_.call(this.scope_,r)}finally{this.isRunningSetter_=!1}}else ie(34,this.name_)},t.trackAndCompute=function(){var r=this.value_,o=this.dependenciesState_===oe.NOT_TRACKING_,i=this.computeValue_(!0),a=o||il(r)||il(i)||!this.equals_(r,i);return a&&(this.value_=i),a},t.computeValue_=function(r){this.isComputing_=!0;var o=ra(!1),i;if(r)i=Vv(this,this.derivation,this.scope_);else if(P.disableErrorBoundaries===!0)i=this.derivation.call(this.scope_);else try{i=this.derivation.call(this.scope_)}catch(a){i=new Bl(a)}return oa(o),this.isComputing_=!1,i},t.suspend_=function(){this.keepAlive_||(Mc(this),this.value_=void 0)},t.observe_=function(r,o){var i=this,a=!0,l=void 0;return o0(function(){var s=i.get();if(!a||o){var c=$o();r({observableKind:"computed",debugObjectName:i.name_,type:cn,object:i,newValue:s,oldValue:l}),Qn(c)}a=!1,l=s})},t.warnAboutUntrackedRead_=function(){},t.toString=function(){return this.name_+"["+this.derivation.toString()+"]"},t.valueOf=function(){return Av(this.get())},t[Wv]=function(){return this.valueOf()},e}(),ia=ar("ComputedValue",ji),oe;(function(e){e[e.NOT_TRACKING_=-1]="NOT_TRACKING_",e[e.UP_TO_DATE_=0]="UP_TO_DATE_",e[e.POSSIBLY_STALE_=1]="POSSIBLY_STALE_",e[e.STALE_=2]="STALE_"})(oe||(oe={}));var $l;(function(e){e[e.NONE=0]="NONE",e[e.LOG=1]="LOG",e[e.BREAK=2]="BREAK"})($l||($l={}));var Bl=function(t){this.cause=void 0,this.cause=t};function il(e){return e instanceof Bl}function kc(e){switch(e.dependenciesState_){case oe.UP_TO_DATE_:return!1;case oe.NOT_TRACKING_:case oe.STALE_:return!0;case oe.POSSIBLY_STALE_:{for(var t=go(!0),n=$o(),r=e.observing_,o=r.length,i=0;i<o;i++){var a=r[i];if(ia(a)){if(P.disableErrorBoundaries)a.get();else try{a.get()}catch{return Qn(n),Xn(t),!0}if(e.dependenciesState_===oe.STALE_)return Qn(n),Xn(t),!0}}return Gv(e),Qn(n),Xn(t),!1}}}function Vv(e,t,n){var r=go(!0);Gv(e),e.newObserving_=new Array(e.observing_.length+100),e.unboundDepsCount_=0,e.runId_=++P.runId;var o=P.trackingDerivation;P.trackingDerivation=e,P.inBatch++;var i;if(P.disableErrorBoundaries===!0)i=t.call(n);else try{i=t.call(n)}catch(a){i=new Bl(a)}return P.inBatch--,P.trackingDerivation=o,Pw(e),Xn(r),i}function Pw(e){for(var t=e.observing_,n=e.observing_=e.newObserving_,r=oe.UP_TO_DATE_,o=0,i=e.unboundDepsCount_,a=0;a<i;a++){var l=n[a];l.diffValue_===0&&(l.diffValue_=1,o!==a&&(n[o]=l),o++),l.dependenciesState_>r&&(r=l.dependenciesState_)}for(n.length=o,e.newObserving_=null,i=t.length;i--;){var s=t[i];s.diffValue_===0&&Yv(s,e),s.diffValue_=0}for(;o--;){var c=n[o];c.diffValue_===1&&(c.diffValue_=0,Lw(c,e))}r!==oe.UP_TO_DATE_&&(e.dependenciesState_=r,e.onBecomeStale_())}function Mc(e){var t=e.observing_;e.observing_=[];for(var n=t.length;n--;)Yv(t[n],e);e.dependenciesState_=oe.NOT_TRACKING_}function Hv(e){var t=$o();try{return e()}finally{Qn(t)}}function $o(){var e=P.trackingDerivation;return P.trackingDerivation=null,e}function Qn(e){P.trackingDerivation=e}function go(e){var t=P.allowStateReads;return P.allowStateReads=e,t}function Xn(e){P.allowStateReads=e}function Gv(e){if(e.dependenciesState_!==oe.UP_TO_DATE_){e.dependenciesState_=oe.UP_TO_DATE_;for(var t=e.observing_,n=t.length;n--;)t[n].lowestObserverState_=oe.UP_TO_DATE_}}var al=function(){this.version=6,this.UNCHANGED={},this.trackingDerivation=null,this.trackingContext=null,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!1,this.allowStateReads=!0,this.enforceActions=!0,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1,this.useProxies=!0,this.verifyProxies=!1,this.safeDescriptors=!0,this.stateVersion=Number.MIN_SAFE_INTEGER},ll=!0,Kv=!1,P=function(){var e=Vd();return e.__mobxInstanceCount>0&&!e.__mobxGlobals&&(ll=!1),e.__mobxGlobals&&e.__mobxGlobals.version!==new al().version&&(ll=!1),ll?e.__mobxGlobals?(e.__mobxInstanceCount+=1,e.__mobxGlobals.UNCHANGED||(e.__mobxGlobals.UNCHANGED={}),e.__mobxGlobals):(e.__mobxInstanceCount=1,e.__mobxGlobals=new al):(setTimeout(function(){Kv||ie(35)},1),new al)}();function Iw(){if((P.pendingReactions.length||P.inBatch||P.isRunningReactions)&&ie(36),Kv=!0,ll){var e=Vd();--e.__mobxInstanceCount===0&&(e.__mobxGlobals=void 0),P=new al}}function Lw(e,t){e.observers_.add(t),e.lowestObserverState_>t.dependenciesState_&&(e.lowestObserverState_=t.dependenciesState_)}function Yv(e,t){e.observers_.delete(t),e.observers_.size===0&&Qv(e)}function Qv(e){e.isPendingUnobservation_===!1&&(e.isPendingUnobservation_=!0,P.pendingUnobservations.push(e))}function dt(){P.inBatch++}function ft(){if(--P.inBatch===0){Zv();for(var e=P.pendingUnobservations,t=0;t<e.length;t++){var n=e[t];n.isPendingUnobservation_=!1,n.observers_.size===0&&(n.isBeingObserved_&&(n.isBeingObserved_=!1,n.onBUO()),n instanceof ji&&n.suspend_())}P.pendingUnobservations=[]}}function Xv(e){var t=P.trackingDerivation;return t!==null?(t.runId_!==e.lastAccessedBy_&&(e.lastAccessedBy_=t.runId_,t.newObserving_[t.unboundDepsCount_++]=e,!e.isBeingObserved_&&P.trackingContext&&(e.isBeingObserved_=!0,e.onBO())),e.isBeingObserved_):(e.observers_.size===0&&P.inBatch>0&&Qv(e),!1)}function qv(e){e.lowestObserverState_!==oe.STALE_&&(e.lowestObserverState_=oe.STALE_,e.observers_.forEach(function(t){t.dependenciesState_===oe.UP_TO_DATE_&&t.onBecomeStale_(),t.dependenciesState_=oe.STALE_}))}function Rw(e){e.lowestObserverState_!==oe.STALE_&&(e.lowestObserverState_=oe.STALE_,e.observers_.forEach(function(t){t.dependenciesState_===oe.POSSIBLY_STALE_?t.dependenciesState_=oe.STALE_:t.dependenciesState_===oe.UP_TO_DATE_&&(e.lowestObserverState_=oe.UP_TO_DATE_)}))}function Dw(e){e.lowestObserverState_===oe.UP_TO_DATE_&&(e.lowestObserverState_=oe.POSSIBLY_STALE_,e.observers_.forEach(function(t){t.dependenciesState_===oe.UP_TO_DATE_&&(t.dependenciesState_=oe.POSSIBLY_STALE_,t.onBecomeStale_())}))}var jo=function(){function e(n,r,o,i){n===void 0&&(n="Reaction"),this.name_=void 0,this.onInvalidate_=void 0,this.errorHandler_=void 0,this.requiresObservable_=void 0,this.observing_=[],this.newObserving_=[],this.dependenciesState_=oe.NOT_TRACKING_,this.diffValue_=0,this.runId_=0,this.unboundDepsCount_=0,this.isDisposed_=!1,this.isScheduled_=!1,this.isTrackPending_=!1,this.isRunning_=!1,this.isTracing_=$l.NONE,this.name_=n,this.onInvalidate_=r,this.errorHandler_=o,this.requiresObservable_=i}var t=e.prototype;return t.onBecomeStale_=function(){this.schedule_()},t.schedule_=function(){this.isScheduled_||(this.isScheduled_=!0,P.pendingReactions.push(this),Zv())},t.isScheduled=function(){return this.isScheduled_},t.runReaction_=function(){if(!this.isDisposed_){dt(),this.isScheduled_=!1;var r=P.trackingContext;if(P.trackingContext=this,kc(this)){this.isTrackPending_=!0;try{this.onInvalidate_()}catch(o){this.reportExceptionInDerivation_(o)}}P.trackingContext=r,ft()}},t.track=function(r){if(!this.isDisposed_){dt(),this.isRunning_=!0;var o=P.trackingContext;P.trackingContext=this;var i=Vv(this,r,void 0);P.trackingContext=o,this.isRunning_=!1,this.isTrackPending_=!1,this.isDisposed_&&Mc(this),il(i)&&this.reportExceptionInDerivation_(i.cause),ft()}},t.reportExceptionInDerivation_=function(r){var o=this;if(this.errorHandler_){this.errorHandler_(r,this);return}if(P.disableErrorBoundaries)throw r;var i="[mobx] uncaught error in '"+this+"'";P.suppressReactionErrors||console.error(i,r),P.globalReactionErrorHandlers.forEach(function(a){return a(r,o)})},t.dispose=function(){this.isDisposed_||(this.isDisposed_=!0,this.isRunning_||(dt(),Mc(this),ft()))},t.getDisposer_=function(r){var o=this,i=function a(){o.dispose(),r==null||r.removeEventListener==null||r.removeEventListener("abort",a)};return r==null||r.addEventListener==null||r.addEventListener("abort",i),i[H]=this,i},t.toString=function(){return"Reaction["+this.name_+"]"},t.trace=function(r){},e}(),$w=100,Ac=function(t){return t()};function Zv(){P.inBatch>0||P.isRunningReactions||Ac(Bw)}function Bw(){P.isRunningReactions=!0;for(var e=P.pendingReactions,t=0;e.length>0;){++t===$w&&(console.error("[mobx] cycle in reaction: "+e[0]),e.splice(0));for(var n=e.splice(0),r=0,o=n.length;r<o;r++)n[r].runReaction_()}P.isRunningReactions=!1}var zl=ar("Reaction",jo);function zw(e){var t=Ac;Ac=function(r){return e(function(){return t(r)})}}function di(){return!1}function Fw(e){return console.warn("[mobx.spy] Is a no-op in production builds"),function(){}}var Jv="action",Uw="action.bound",e0="autoAction",Ww="autoAction.bound",Vw="<unnamed action>",t0=na(Jv),Hw=na(Uw,{bound:!0}),n0=na(e0,{autoAction:!0}),Gw=na(Ww,{autoAction:!0,bound:!0});function r0(e){var t=function(r,o){if(Xt(r))return To(r.name||Vw,r,e);if(Xt(o))return To(r,o,e);if(Nr(o))return ea(r,o,e?n0:t0);if(Nr(r))return un(na(e?e0:Jv,{name:r,autoAction:e}))};return t}var ao=r0(!1);Object.assign(ao,t0);var Ni=r0(!0);Object.assign(Ni,n0);ao.bound=un(Hw);Ni.bound=un(Gw);function Qd(e){return Xt(e)&&e.isMobxAction===!0}function o0(e,t){var n,r,o,i,a;t===void 0&&(t=bv);var l=(n=(r=t)==null?void 0:r.name)!=null?n:"Autorun",s=!t.scheduler&&!t.delay,c;if(s)c=new jo(l,function(){this.track(d)},t.onError,t.requiresObservable);else{var p=Yw(t),f=!1;c=new jo(l,function(){f||(f=!0,p(function(){f=!1,c.isDisposed_||c.track(d)}))},t.onError,t.requiresObservable)}function d(){e(c)}return(o=t)!=null&&(i=o.signal)!=null&&i.aborted||c.schedule_(),c.getDisposer_((a=t)==null?void 0:a.signal)}var Kw=function(t){return t()};function Yw(e){return e.scheduler?e.scheduler:e.delay?function(t){return setTimeout(t,e.delay)}:Kw}var Qw="onBO",Xw="onBUO";function qw(e,t,n){return a0(Qw,e,t,n)}function i0(e,t,n){return a0(Xw,e,t,n)}function a0(e,t,n,r){var o=Vl(t),i=Xt(r)?r:n,a=e+"L";return o[a]?o[a].add(i):o[a]=new Set([i]),function(){var l=o[a];l&&(l.delete(i),l.size===0&&delete o[a])}}var Zw="never",Da="always",Jw="observed";function vs(e){e.isolateGlobalState===!0&&Iw();var t=e.useProxies,n=e.enforceActions;if(t!==void 0&&(P.useProxies=t===Da?!0:t===Zw?!1:typeof Proxy<"u"),t==="ifavailable"&&(P.verifyProxies=!0),n!==void 0){var r=n===Da?Da:n===Jw;P.enforceActions=r,P.allowStateChanges=!(r===!0||r===Da)}["computedRequiresReaction","reactionRequiresObservable","observableRequiresReaction","disableErrorBoundaries","safeDescriptors"].forEach(function(o){o in e&&(P[o]=!!e[o])}),P.allowStateReads=!P.observableRequiresReaction,e.reactionScheduler&&zw(e.reactionScheduler)}function l0(e,t,n,r){var o=B_(t),i=ua(e,r)[H];dt();try{Eo(o).forEach(function(a){i.extend_(a,o[a],n&&a in n?n[a]:!0)})}finally{ft()}return e}function eC(e,t){return s0(Vl(e,t))}function s0(e){var t={name:e.name_};return e.observing_&&e.observing_.length>0&&(t.dependencies=tC(e.observing_).map(s0)),t}function tC(e){return Array.from(new Set(e))}var nC=0;function u0(){this.message="FLOW_CANCELLED"}u0.prototype=Object.create(Error.prototype);var Fp=Nv("flow"),rC=Nv("flow.bound",{bound:!0}),Pi=Object.assign(function(t,n){if(Nr(n))return ea(t,n,Fp);var r=t,o=r.name||"<unnamed flow>",i=function(){var l=this,s=arguments,c=++nC,p=ao(o+" - runid: "+c+" - init",r).apply(l,s),f,d=void 0,y=new Promise(function(w,x){var O=0;f=x;function v(_){d=void 0;var S;try{S=ao(o+" - runid: "+c+" - yield "+O++,p.next).call(p,_)}catch(E){return x(E)}g(S)}function m(_){d=void 0;var S;try{S=ao(o+" - runid: "+c+" - yield "+O++,p.throw).call(p,_)}catch(E){return x(E)}g(S)}function g(_){if(Xt(_==null?void 0:_.then)){_.then(g,x);return}return _.done?w(_.value):(d=Promise.resolve(_.value),d.then(v,m))}v(void 0)});return y.cancel=ao(o+" - runid: "+c+" - cancel",function(){try{d&&Up(d);var w=p.return(void 0),x=Promise.resolve(w.value);x.then(oo,oo),Up(x),f(new u0)}catch(O){f(O)}}),y};return i.isMobXFlow=!0,i},Fp);Pi.bound=un(rC);function Up(e){Xt(e.cancel)&&e.cancel()}function Fl(e){return(e==null?void 0:e.isMobXFlow)===!0}function oC(e,t){return e?ca(e)||!!e[H]||Gd(e)||zl(e)||ia(e):!1}function Xd(e){return oC(e)}function iC(e){if(ca(e))return e[H].ownKeys_();ie(38)}function $a(e,t,n){return e.set(t,n),n}function Vr(e,t){if(e==null||typeof e!="object"||e instanceof Date||!Xd(e))return e;if(Nw(e)||ia(e))return Vr(e.get(),t);if(t.has(e))return t.get(e);if(sa(e)){var n=$a(t,e,new Array(e.length));return e.forEach(function(a,l){n[l]=Vr(a,t)}),n}if(Rr(e)){var r=$a(t,e,new Set);return e.forEach(function(a){r.add(Vr(a,t))}),r}if(sr(e)){var o=$a(t,e,new Map);return e.forEach(function(a,l){o.set(l,Vr(a,t))}),o}else{var i=$a(t,e,{});return iC(e).forEach(function(a){qi.propertyIsEnumerable.call(e,a)&&(i[a]=Vr(e[a],t))}),i}}function lo(e,t){return Vr(e,new Map)}function mn(e,t){t===void 0&&(t=void 0),dt();try{return e.apply(t)}finally{ft()}}function Br(e){return e[H]}var aC={has:function(t,n){return Br(t).has_(n)},get:function(t,n){return Br(t).get_(n)},set:function(t,n,r){var o;return Nr(n)?(o=Br(t).set_(n,r,!0))!=null?o:!0:!1},deleteProperty:function(t,n){var r;return Nr(n)?(r=Br(t).delete_(n,!0))!=null?r:!0:!1},defineProperty:function(t,n,r){var o;return(o=Br(t).defineProperty_(n,r))!=null?o:!0},ownKeys:function(t){return Br(t).ownKeys_()},preventExtensions:function(t){ie(13)}};function lC(e,t){var n,r;return Sv(),e=ua(e,t),(r=(n=e[H]).proxy_)!=null?r:n.proxy_=new Proxy(e,aC)}function At(e){return e.interceptors_!==void 0&&e.interceptors_.length>0}function aa(e,t){var n=e.interceptors_||(e.interceptors_=[]);return n.push(t),Ov(function(){var r=n.indexOf(t);r!==-1&&n.splice(r,1)})}function Tt(e,t){var n=$o();try{for(var r=[].concat(e.interceptors_||[]),o=0,i=r.length;o<i&&(t=r[o](t),t&&!t.type&&ie(14),!!t);o++);return t}finally{Qn(n)}}function Gt(e){return e.changeListeners_!==void 0&&e.changeListeners_.length>0}function la(e,t){var n=e.changeListeners_||(e.changeListeners_=[]);return n.push(t),Ov(function(){var r=n.indexOf(t);r!==-1&&n.splice(r,1)})}function Kt(e,t){var n=$o(),r=e.changeListeners_;if(r){r=r.slice();for(var o=0,i=r.length;o<i;o++)r[o](t);Qn(n)}}function sC(e,t,n){var r=ua(e,n)[H];dt();try{var o;(o=t)!=null||(t=V_(e)),Eo(t).forEach(function(i){return r.make_(i,t[i])})}finally{ft()}return e}var _u=Symbol("mobx-keys");function ys(e,t,n){if(kn(e))return l0(e,e,t,n);var r=ua(e,n)[H];if(!e[_u]){var o=Object.getPrototypeOf(e),i=new Set([].concat(Eo(e),Eo(o)));i.delete("constructor"),i.delete(H),Zi(o,_u,i)}dt();try{e[_u].forEach(function(a){return r.make_(a,t&&a in t?t[a]:!0)})}finally{ft()}return e}var Wp="splice",cn="update",uC=1e4,cC={get:function(t,n){var r=t[H];return n===H?r:n==="length"?r.getArrayLength_():typeof n=="string"&&!isNaN(n)?r.get_(parseInt(n)):Cn(Ul,n)?Ul[n]:t[n]},set:function(t,n,r){var o=t[H];return n==="length"&&o.setArrayLength_(r),typeof n=="symbol"||isNaN(n)?t[n]=r:o.set_(parseInt(n),r),!0},preventExtensions:function(){ie(15)}},qd=function(){function e(n,r,o,i){n===void 0&&(n="ObservableArray"),this.owned_=void 0,this.legacyMode_=void 0,this.atom_=void 0,this.values_=[],this.interceptors_=void 0,this.changeListeners_=void 0,this.enhancer_=void 0,this.dehancer=void 0,this.proxy_=void 0,this.lastKnownLength_=0,this.owned_=o,this.legacyMode_=i,this.atom_=new ta(n),this.enhancer_=function(a,l){return r(a,l,"ObservableArray[..]")}}var t=e.prototype;return t.dehanceValue_=function(r){return this.dehancer!==void 0?this.dehancer(r):r},t.dehanceValues_=function(r){return this.dehancer!==void 0&&r.length>0?r.map(this.dehancer):r},t.intercept_=function(r){return aa(this,r)},t.observe_=function(r,o){return o===void 0&&(o=!1),o&&r({observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:"splice",index:0,added:this.values_.slice(),addedCount:this.values_.length,removed:[],removedCount:0}),la(this,r)},t.getArrayLength_=function(){return this.atom_.reportObserved(),this.values_.length},t.setArrayLength_=function(r){(typeof r!="number"||isNaN(r)||r<0)&&ie("Out of range: "+r);var o=this.values_.length;if(r!==o)if(r>o){for(var i=new Array(r-o),a=0;a<r-o;a++)i[a]=void 0;this.spliceWithArray_(o,0,i)}else this.spliceWithArray_(r,o-r)},t.updateArrayLength_=function(r,o){r!==this.lastKnownLength_&&ie(16),this.lastKnownLength_+=o,this.legacyMode_&&o>0&&_0(r+o+1)},t.spliceWithArray_=function(r,o,i){var a=this;this.atom_;var l=this.values_.length;if(r===void 0?r=0:r>l?r=l:r<0&&(r=Math.max(0,l+r)),arguments.length===1?o=l-r:o==null?o=0:o=Math.max(0,Math.min(o,l-r)),i===void 0&&(i=Oc),At(this)){var s=Tt(this,{object:this.proxy_,type:Wp,index:r,removedCount:o,added:i});if(!s)return Oc;o=s.removedCount,i=s.added}if(i=i.length===0?i:i.map(function(f){return a.enhancer_(f,void 0)}),this.legacyMode_){var c=i.length-o;this.updateArrayLength_(l,c)}var p=this.spliceItemsIntoValues_(r,o,i);return(o!==0||i.length!==0)&&this.notifyArraySplice_(r,i,p),this.dehanceValues_(p)},t.spliceItemsIntoValues_=function(r,o,i){if(i.length<uC){var a;return(a=this.values_).splice.apply(a,[r,o].concat(i))}else{var l=this.values_.slice(r,r+o),s=this.values_.slice(r+o);this.values_.length+=i.length-o;for(var c=0;c<i.length;c++)this.values_[r+c]=i[c];for(var p=0;p<s.length;p++)this.values_[r+i.length+p]=s[p];return l}},t.notifyArrayChildUpdate_=function(r,o,i){var a=!this.owned_&&di(),l=Gt(this),s=l||a?{observableKind:"array",object:this.proxy_,type:cn,debugObjectName:this.atom_.name_,index:r,newValue:o,oldValue:i}:null;this.atom_.reportChanged(),l&&Kt(this,s)},t.notifyArraySplice_=function(r,o,i){var a=!this.owned_&&di(),l=Gt(this),s=l||a?{observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:Wp,index:r,removed:i,added:o,removedCount:i.length,addedCount:o.length}:null;this.atom_.reportChanged(),l&&Kt(this,s)},t.get_=function(r){if(this.legacyMode_&&r>=this.values_.length){console.warn("[mobx] Out of bounds read: "+r);return}return this.atom_.reportObserved(),this.dehanceValue_(this.values_[r])},t.set_=function(r,o){var i=this.values_;if(this.legacyMode_&&r>i.length&&ie(17,r,i.length),r<i.length){this.atom_;var a=i[r];if(At(this)){var l=Tt(this,{type:cn,object:this.proxy_,index:r,newValue:o});if(!l)return;o=l.newValue}o=this.enhancer_(o,a);var s=o!==a;s&&(i[r]=o,this.notifyArrayChildUpdate_(r,o,a))}else{for(var c=new Array(r+1-i.length),p=0;p<c.length-1;p++)c[p]=void 0;c[c.length-1]=o,this.spliceWithArray_(i.length,0,c)}},e}();function dC(e,t,n,r){n===void 0&&(n="ObservableArray"),r===void 0&&(r=!1),Sv();var o=new qd(n,t,r,!1);kv(o.values_,H,o);var i=new Proxy(o.values_,cC);if(o.proxy_=i,e&&e.length){var a=ra(!0);o.spliceWithArray_(0,0,e),oa(a)}return i}var Ul={clear:function(){return this.splice(0)},replace:function(t){var n=this[H];return n.spliceWithArray_(0,n.values_.length,t)},toJSON:function(){return this.slice()},splice:function(t,n){for(var r=arguments.length,o=new Array(r>2?r-2:0),i=2;i<r;i++)o[i-2]=arguments[i];var a=this[H];switch(arguments.length){case 0:return[];case 1:return a.spliceWithArray_(t);case 2:return a.spliceWithArray_(t,n)}return a.spliceWithArray_(t,n,o)},spliceWithArray:function(t,n,r){return this[H].spliceWithArray_(t,n,r)},push:function(){for(var t=this[H],n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return t.spliceWithArray_(t.values_.length,0,r),t.values_.length},pop:function(){return this.splice(Math.max(this[H].values_.length-1,0),1)[0]},shift:function(){return this.splice(0,1)[0]},unshift:function(){for(var t=this[H],n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return t.spliceWithArray_(0,0,r),t.values_.length},reverse:function(){return P.trackingDerivation&&ie(37,"reverse"),this.replace(this.slice().reverse()),this},sort:function(){P.trackingDerivation&&ie(37,"sort");var t=this.slice();return t.sort.apply(t,arguments),this.replace(t),this},remove:function(t){var n=this[H],r=n.dehanceValues_(n.values_).indexOf(t);return r>-1?(this.splice(r,1),!0):!1}};De("concat",An);De("flat",An);De("includes",An);De("indexOf",An);De("join",An);De("lastIndexOf",An);De("slice",An);De("toString",An);De("toLocaleString",An);De("every",lr);De("filter",lr);De("find",lr);De("findIndex",lr);De("flatMap",lr);De("forEach",lr);De("map",lr);De("some",lr);De("reduce",c0);De("reduceRight",c0);function De(e,t){typeof Array.prototype[e]=="function"&&(Ul[e]=t(e))}function An(e){return function(){var t=this[H];t.atom_.reportObserved();var n=t.dehanceValues_(t.values_);return n[e].apply(n,arguments)}}function lr(e){return function(t,n){var r=this,o=this[H];o.atom_.reportObserved();var i=o.dehanceValues_(o.values_);return i[e](function(a,l){return t.call(n,a,l,r)})}}function c0(e){return function(){var t=this,n=this[H];n.atom_.reportObserved();var r=n.dehanceValues_(n.values_),o=arguments[0];return arguments[0]=function(i,a,l){return o(i,a,l,t)},r[e].apply(r,arguments)}}var fC=ar("ObservableArrayAdministration",qd);function sa(e){return hs(e)&&fC(e[H])}var d0,f0,pC={},zn="add",Wl="delete";d0=Symbol.iterator;f0=Symbol.toStringTag;var p0=function(){function e(n,r,o){var i=this;r===void 0&&(r=Mo),o===void 0&&(o="ObservableMap"),this.enhancer_=void 0,this.name_=void 0,this[H]=pC,this.data_=void 0,this.hasMap_=void 0,this.keysAtom_=void 0,this.interceptors_=void 0,this.changeListeners_=void 0,this.dehancer=void 0,this.enhancer_=r,this.name_=o,Xt(Map)||ie(18),this.keysAtom_=Kd("ObservableMap.keys()"),this.data_=new Map,this.hasMap_=new Map,Fv(!0,function(){i.merge(n)})}var t=e.prototype;return t.has_=function(r){return this.data_.has(r)},t.has=function(r){var o=this;if(!P.trackingDerivation)return this.has_(r);var i=this.hasMap_.get(r);if(!i){var a=i=new Sr(this.has_(r),ms,"ObservableMap.key?",!1);this.hasMap_.set(r,a),i0(a,function(){return o.hasMap_.delete(r)})}return i.get()},t.set=function(r,o){var i=this.has_(r);if(At(this)){var a=Tt(this,{type:i?cn:zn,object:this,newValue:o,name:r});if(!a)return this;o=a.newValue}return i?this.updateValue_(r,o):this.addValue_(r,o),this},t.delete=function(r){var o=this;if(this.keysAtom_,At(this)){var i=Tt(this,{type:Wl,object:this,name:r});if(!i)return!1}if(this.has_(r)){var a=di(),l=Gt(this),s=l||a?{observableKind:"map",debugObjectName:this.name_,type:Wl,object:this,oldValue:this.data_.get(r).value_,name:r}:null;return mn(function(){var c;o.keysAtom_.reportChanged(),(c=o.hasMap_.get(r))==null||c.setNewValue_(!1);var p=o.data_.get(r);p.setNewValue_(void 0),o.data_.delete(r)}),l&&Kt(this,s),!0}return!1},t.updateValue_=function(r,o){var i=this.data_.get(r);if(o=i.prepareNewValue_(o),o!==P.UNCHANGED){var a=di(),l=Gt(this),s=l||a?{observableKind:"map",debugObjectName:this.name_,type:cn,object:this,oldValue:i.value_,name:r,newValue:o}:null;i.setNewValue_(o),l&&Kt(this,s)}},t.addValue_=function(r,o){var i=this;this.keysAtom_,mn(function(){var c,p=new Sr(o,i.enhancer_,"ObservableMap.key",!1);i.data_.set(r,p),o=p.value_,(c=i.hasMap_.get(r))==null||c.setNewValue_(!0),i.keysAtom_.reportChanged()});var a=di(),l=Gt(this),s=l||a?{observableKind:"map",debugObjectName:this.name_,type:zn,object:this,name:r,newValue:o}:null;l&&Kt(this,s)},t.get=function(r){return this.has(r)?this.dehanceValue_(this.data_.get(r).get()):this.dehanceValue_(void 0)},t.dehanceValue_=function(r){return this.dehancer!==void 0?this.dehancer(r):r},t.keys=function(){return this.keysAtom_.reportObserved(),this.data_.keys()},t.values=function(){var r=this,o=this.keys();return Ii({next:function(){var a=o.next(),l=a.done,s=a.value;return{done:l,value:l?void 0:r.get(s)}}})},t.entries=function(){var r=this,o=this.keys();return Ii({next:function(){var a=o.next(),l=a.done,s=a.value;return{done:l,value:l?void 0:[s,r.get(s)]}}})},t[d0]=function(){return this.entries()},t.forEach=function(r,o){for(var i=io(this),a;!(a=i()).done;){var l=a.value,s=l[0],c=l[1];r.call(o,c,s,this)}},t.merge=function(r){var o=this;return sr(r)&&(r=new Map(r)),mn(function(){kn(r)?$_(r).forEach(function(i){return o.set(i,r[i])}):Array.isArray(r)?r.forEach(function(i){var a=i[0],l=i[1];return o.set(a,l)}):Do(r)?(r.constructor!==Map&&ie(19,r),r.forEach(function(i,a){return o.set(a,i)})):r!=null&&ie(20,r)}),this},t.clear=function(){var r=this;mn(function(){Hv(function(){for(var o=io(r.keys()),i;!(i=o()).done;){var a=i.value;r.delete(a)}})})},t.replace=function(r){var o=this;return mn(function(){for(var i=hC(r),a=new Map,l=!1,s=io(o.data_.keys()),c;!(c=s()).done;){var p=c.value;if(!i.has(p)){var f=o.delete(p);if(f)l=!0;else{var d=o.data_.get(p);a.set(p,d)}}}for(var y=io(i.entries()),w;!(w=y()).done;){var x=w.value,O=x[0],v=x[1],m=o.data_.has(O);if(o.set(O,v),o.data_.has(O)){var g=o.data_.get(O);a.set(O,g),m||(l=!0)}}if(!l)if(o.data_.size!==a.size)o.keysAtom_.reportChanged();else for(var _=o.data_.keys(),S=a.keys(),E=_.next(),T=S.next();!E.done;){if(E.value!==T.value){o.keysAtom_.reportChanged();break}E=_.next(),T=S.next()}o.data_=a}),this},t.toString=function(){return"[object ObservableMap]"},t.toJSON=function(){return Array.from(this)},t.observe_=function(r,o){return la(this,r)},t.intercept_=function(r){return aa(this,r)},Hd(e,[{key:"size",get:function(){return this.keysAtom_.reportObserved(),this.data_.size}},{key:f0,get:function(){return"Map"}}]),e}(),sr=ar("ObservableMap",p0);function hC(e){if(Do(e)||sr(e))return e;if(Array.isArray(e))return new Map(e);if(kn(e)){var t=new Map;for(var n in e)t.set(n,e[n]);return t}else return ie(21,e)}var h0,m0,mC={};h0=Symbol.iterator;m0=Symbol.toStringTag;var g0=function(){function e(n,r,o){r===void 0&&(r=Mo),o===void 0&&(o="ObservableSet"),this.name_=void 0,this[H]=mC,this.data_=new Set,this.atom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.dehancer=void 0,this.enhancer_=void 0,this.name_=o,Xt(Set)||ie(22),this.atom_=Kd(this.name_),this.enhancer_=function(i,a){return r(i,a,o)},n&&this.replace(n)}var t=e.prototype;return t.dehanceValue_=function(r){return this.dehancer!==void 0?this.dehancer(r):r},t.clear=function(){var r=this;mn(function(){Hv(function(){for(var o=io(r.data_.values()),i;!(i=o()).done;){var a=i.value;r.delete(a)}})})},t.forEach=function(r,o){for(var i=io(this),a;!(a=i()).done;){var l=a.value;r.call(o,l,l,this)}},t.add=function(r){var o=this;if(this.atom_,At(this)){var i=Tt(this,{type:zn,object:this,newValue:r});if(!i)return this}if(!this.has(r)){mn(function(){o.data_.add(o.enhancer_(r,void 0)),o.atom_.reportChanged()});var a=!1,l=Gt(this),s=l||a?{observableKind:"set",debugObjectName:this.name_,type:zn,object:this,newValue:r}:null;l&&Kt(this,s)}return this},t.delete=function(r){var o=this;if(At(this)){var i=Tt(this,{type:Wl,object:this,oldValue:r});if(!i)return!1}if(this.has(r)){var a=!1,l=Gt(this),s=l||a?{observableKind:"set",debugObjectName:this.name_,type:Wl,object:this,oldValue:r}:null;return mn(function(){o.atom_.reportChanged(),o.data_.delete(r)}),l&&Kt(this,s),!0}return!1},t.has=function(r){return this.atom_.reportObserved(),this.data_.has(this.dehanceValue_(r))},t.entries=function(){var r=0,o=Array.from(this.keys()),i=Array.from(this.values());return Ii({next:function(){var l=r;return r+=1,l<i.length?{value:[o[l],i[l]],done:!1}:{done:!0}}})},t.keys=function(){return this.values()},t.values=function(){this.atom_.reportObserved();var r=this,o=0,i=Array.from(this.data_.values());return Ii({next:function(){return o<i.length?{value:r.dehanceValue_(i[o++]),done:!1}:{done:!0}}})},t.replace=function(r){var o=this;return Rr(r)&&(r=new Set(r)),mn(function(){Array.isArray(r)?(o.clear(),r.forEach(function(i){return o.add(i)})):Ji(r)?(o.clear(),r.forEach(function(i){return o.add(i)})):r!=null&&ie("Cannot initialize set from "+r)}),this},t.observe_=function(r,o){return la(this,r)},t.intercept_=function(r){return aa(this,r)},t.toJSON=function(){return Array.from(this)},t.toString=function(){return"[object ObservableSet]"},t[h0]=function(){return this.values()},Hd(e,[{key:"size",get:function(){return this.atom_.reportObserved(),this.data_.size}},{key:m0,get:function(){return"Set"}}]),e}(),Rr=ar("ObservableSet",g0),Vp=Object.create(null),Hp="remove",v0=function(){function e(n,r,o,i){r===void 0&&(r=new Map),i===void 0&&(i=fw),this.target_=void 0,this.values_=void 0,this.name_=void 0,this.defaultAnnotation_=void 0,this.keysAtom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.proxy_=void 0,this.isPlainObject_=void 0,this.appliedAnnotations_=void 0,this.pendingKeys_=void 0,this.target_=n,this.values_=r,this.name_=o,this.defaultAnnotation_=i,this.keysAtom_=new ta("ObservableObject.keys"),this.isPlainObject_=kn(this.target_)}var t=e.prototype;return t.getObservablePropValue_=function(r){return this.values_.get(r).get()},t.setObservablePropValue_=function(r,o){var i=this.values_.get(r);if(i instanceof ji)return i.set(o),!0;if(At(this)){var a=Tt(this,{type:cn,object:this.proxy_||this.target_,name:r,newValue:o});if(!a)return null;o=a.newValue}if(o=i.prepareNewValue_(o),o!==P.UNCHANGED){var l=Gt(this),s=!1,c=l||s?{type:cn,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,oldValue:i.value_,name:r,newValue:o}:null;i.setNewValue_(o),l&&Kt(this,c)}return!0},t.get_=function(r){return P.trackingDerivation&&!Cn(this.target_,r)&&this.has_(r),this.target_[r]},t.set_=function(r,o,i){return i===void 0&&(i=!1),Cn(this.target_,r)?this.values_.has(r)?this.setObservablePropValue_(r,o):i?Reflect.set(this.target_,r,o):(this.target_[r]=o,!0):this.extend_(r,{value:o,enumerable:!0,writable:!0,configurable:!0},this.defaultAnnotation_,i)},t.has_=function(r){if(!P.trackingDerivation)return r in this.target_;this.pendingKeys_||(this.pendingKeys_=new Map);var o=this.pendingKeys_.get(r);return o||(o=new Sr(r in this.target_,ms,"ObservableObject.key?",!1),this.pendingKeys_.set(r,o)),o.get()},t.make_=function(r,o){if(o===!0&&(o=this.defaultAnnotation_),o!==!1){if(!(r in this.target_)){var i;if((i=this.target_[nn])!=null&&i[r])return;ie(1,o.annotationType_,this.name_+"."+r.toString())}for(var a=this.target_;a&&a!==qi;){var l=Ll(a,r);if(l){var s=o.make_(this,r,l,a);if(s===0)return;if(s===1)break}a=Object.getPrototypeOf(a)}Kp(this,o,r)}},t.extend_=function(r,o,i,a){if(a===void 0&&(a=!1),i===!0&&(i=this.defaultAnnotation_),i===!1)return this.defineProperty_(r,o,a);var l=i.extend_(this,r,o,a);return l&&Kp(this,i,r),l},t.defineProperty_=function(r,o,i){i===void 0&&(i=!1);try{dt();var a=this.delete_(r);if(!a)return a;if(At(this)){var l=Tt(this,{object:this.proxy_||this.target_,name:r,type:zn,newValue:o.value});if(!l)return null;var s=l.newValue;o.value!==s&&(o=ko({},o,{value:s}))}if(i){if(!Reflect.defineProperty(this.target_,r,o))return!1}else sn(this.target_,r,o);this.notifyPropertyAddition_(r,o.value)}finally{ft()}return!0},t.defineObservableProperty_=function(r,o,i,a){a===void 0&&(a=!1);try{dt();var l=this.delete_(r);if(!l)return l;if(At(this)){var s=Tt(this,{object:this.proxy_||this.target_,name:r,type:zn,newValue:o});if(!s)return null;o=s.newValue}var c=Gp(r),p={configurable:P.safeDescriptors?this.isPlainObject_:!0,enumerable:!0,get:c.get,set:c.set};if(a){if(!Reflect.defineProperty(this.target_,r,p))return!1}else sn(this.target_,r,p);var f=new Sr(o,i,"ObservableObject.key",!1);this.values_.set(r,f),this.notifyPropertyAddition_(r,f.value_)}finally{ft()}return!0},t.defineComputedProperty_=function(r,o,i){i===void 0&&(i=!1);try{dt();var a=this.delete_(r);if(!a)return a;if(At(this)){var l=Tt(this,{object:this.proxy_||this.target_,name:r,type:zn,newValue:void 0});if(!l)return null}o.name||(o.name="ObservableObject.key"),o.context=this.proxy_||this.target_;var s=Gp(r),c={configurable:P.safeDescriptors?this.isPlainObject_:!0,enumerable:!1,get:s.get,set:s.set};if(i){if(!Reflect.defineProperty(this.target_,r,c))return!1}else sn(this.target_,r,c);this.values_.set(r,new ji(o)),this.notifyPropertyAddition_(r,void 0)}finally{ft()}return!0},t.delete_=function(r,o){if(o===void 0&&(o=!1),!Cn(this.target_,r))return!0;if(At(this)){var i=Tt(this,{object:this.proxy_||this.target_,name:r,type:Hp});if(!i)return null}try{var a,l;dt();var s=Gt(this),c=!1,p=this.values_.get(r),f=void 0;if(!p&&(s||c)){var d;f=(d=Ll(this.target_,r))==null?void 0:d.value}if(o){if(!Reflect.deleteProperty(this.target_,r))return!1}else delete this.target_[r];if(p&&(this.values_.delete(r),p instanceof Sr&&(f=p.value_),qv(p)),this.keysAtom_.reportChanged(),(a=this.pendingKeys_)==null||(l=a.get(r))==null||l.set(r in this.target_),s||c){var y={type:Hp,observableKind:"object",object:this.proxy_||this.target_,debugObjectName:this.name_,oldValue:f,name:r};s&&Kt(this,y)}}finally{ft()}return!0},t.observe_=function(r,o){return la(this,r)},t.intercept_=function(r){return aa(this,r)},t.notifyPropertyAddition_=function(r,o){var i,a,l=Gt(this),s=!1;if(l||s){var c=l||s?{type:zn,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,name:r,newValue:o}:null;l&&Kt(this,c)}(i=this.pendingKeys_)==null||(a=i.get(r))==null||a.set(!0),this.keysAtom_.reportChanged()},t.ownKeys_=function(){return this.keysAtom_.reportObserved(),Eo(this.target_)},t.keys_=function(){return this.keysAtom_.reportObserved(),Object.keys(this.target_)},e}();function ua(e,t){var n;if(Cn(e,H))return e;var r=(n=t==null?void 0:t.name)!=null?n:"ObservableObject",o=new v0(e,new Map,String(r),Cw(t));return Zi(e,H,o),e}var gC=ar("ObservableObjectAdministration",v0);function Gp(e){return Vp[e]||(Vp[e]={get:function(){return this[H].getObservablePropValue_(e)},set:function(n){return this[H].setObservablePropValue_(e,n)}})}function ca(e){return hs(e)?gC(e[H]):!1}function Kp(e,t,n){var r;(r=e.target_[nn])==null||delete r[n]}var vC=x0(0),wu=0,y0=function(){};function yC(e,t){Object.setPrototypeOf?Object.setPrototypeOf(e.prototype,t):e.prototype.__proto__!==void 0?e.prototype.__proto__=t:e.prototype=t}yC(y0,Array.prototype);var Zd=function(e,t,n){Tv(r,e);function r(i,a,l,s){var c;l===void 0&&(l="ObservableArray"),s===void 0&&(s=!1),c=e.call(this)||this;var p=new qd(l,a,s,!0);if(p.proxy_=xu(c),kv(xu(c),H,p),i&&i.length){var f=ra(!0);c.spliceWithArray(0,0,i),oa(f)}return Object.defineProperty(xu(c),"0",vC),c}var o=r.prototype;return o.concat=function(){this[H].atom_.reportObserved();for(var a=arguments.length,l=new Array(a),s=0;s<a;s++)l[s]=arguments[s];return Array.prototype.concat.apply(this.slice(),l.map(function(c){return sa(c)?c.slice():c}))},o[n]=function(){var i=this,a=0;return Ii({next:function(){return a<i.length?{value:i[a++],done:!1}:{done:!0,value:void 0}}})},Hd(r,[{key:"length",get:function(){return this[H].getArrayLength_()},set:function(a){this[H].setArrayLength_(a)}},{key:t,get:function(){return"Array"}}]),r}(y0,Symbol.toStringTag,Symbol.iterator);Object.entries(Ul).forEach(function(e){var t=e[0],n=e[1];t!=="concat"&&Zi(Zd.prototype,t,n)});function x0(e){return{enumerable:!1,configurable:!0,get:function(){return this[H].get_(e)},set:function(n){this[H].set_(e,n)}}}function xC(e){sn(Zd.prototype,""+e,x0(e))}function _0(e){if(e>wu){for(var t=wu;t<e+100;t++)xC(t);wu=e}}_0(1e3);function _C(e,t,n){return new Zd(e,t,n)}function Vl(e,t){if(typeof e=="object"&&e!==null){if(sa(e))return t!==void 0&&ie(23),e[H].atom_;if(Rr(e))return e.atom_;if(sr(e)){if(t===void 0)return e.keysAtom_;var n=e.data_.get(t)||e.hasMap_.get(t);return n||ie(25,t,Tc(e)),n}if(ca(e)){if(!t)return ie(26);var r=e[H].values_.get(t);return r||ie(27,t,Tc(e)),r}if(Gd(e)||ia(e)||zl(e))return e}else if(Xt(e)&&zl(e[H]))return e[H];ie(28)}function wC(e,t){if(e||ie(29),Gd(e)||ia(e)||zl(e)||sr(e)||Rr(e))return e;if(e[H])return e[H];ie(24,e)}function Tc(e,t){var n;if(t!==void 0)n=Vl(e,t);else{if(Qd(e))return e.name;ca(e)||sr(e)||Rr(e)?n=wC(e):n=Vl(e)}return n.name_}var Yp=qi.toString;function Jd(e,t,n){return n===void 0&&(n=-1),jc(e,t,n)}function jc(e,t,n,r,o){if(e===t)return e!==0||1/e===1/t;if(e==null||t==null)return!1;if(e!==e)return t!==t;var i=typeof e;if(i!=="function"&&i!=="object"&&typeof t!="object")return!1;var a=Yp.call(e);if(a!==Yp.call(t))return!1;switch(a){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!=+e?+t!=+t:+e==0?1/+e===1/t:+e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object Symbol]":return typeof Symbol<"u"&&Symbol.valueOf.call(e)===Symbol.valueOf.call(t);case"[object Map]":case"[object Set]":n>=0&&n++;break}e=Qp(e),t=Qp(t);var l=a==="[object Array]";if(!l){if(typeof e!="object"||typeof t!="object")return!1;var s=e.constructor,c=t.constructor;if(s!==c&&!(Xt(s)&&s instanceof s&&Xt(c)&&c instanceof c)&&"constructor"in e&&"constructor"in t)return!1}if(n===0)return!1;n<0&&(n=-1),r=r||[],o=o||[];for(var p=r.length;p--;)if(r[p]===e)return o[p]===t;if(r.push(e),o.push(t),l){if(p=e.length,p!==t.length)return!1;for(;p--;)if(!jc(e[p],t[p],n-1,r,o))return!1}else{var f=Object.keys(e),d;if(p=f.length,Object.keys(t).length!==p)return!1;for(;p--;)if(d=f[p],!(Cn(t,d)&&jc(e[d],t[d],n-1,r,o)))return!1}return r.pop(),o.pop(),!0}function Qp(e){return sa(e)?e.slice():Do(e)||sr(e)||Ji(e)||Rr(e)?Array.from(e.entries()):e}function Ii(e){return e[Symbol.iterator]=CC,e}function CC(){return this}["Symbol","Map","Set"].forEach(function(e){var t=Vd();typeof t[e]>"u"&&ie("MobX requires global '"+e+"' to be available or polyfilled")});typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__=="object"&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:Fw,extras:{getDebugName:Tc},$mobx:H});if(!C.useState)throw new Error("mobx-react-lite requires React with Hooks support");if(!sC)throw new Error("mobx-react-lite@3 requires mobx at least version 6 to be available");function bC(e){e()}function SC(e){e||(e=bC),vs({reactionScheduler:e})}function OC(e){return eC(e)}var EC=1e4,kC=1e4,MC=function(){function e(t){var n=this;Object.defineProperty(this,"finalize",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"registrations",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"sweepTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sweep",{enumerable:!0,configurable:!0,writable:!0,value:function(r){r===void 0&&(r=EC),clearTimeout(n.sweepTimeout),n.sweepTimeout=void 0;var o=Date.now();n.registrations.forEach(function(i,a){o-i.registeredAt>=r&&(n.finalize(i.value),n.registrations.delete(a))}),n.registrations.size>0&&n.scheduleSweep()}}),Object.defineProperty(this,"finalizeAllImmediately",{enumerable:!0,configurable:!0,writable:!0,value:function(){n.sweep(0)}})}return Object.defineProperty(e.prototype,"register",{enumerable:!1,configurable:!0,writable:!0,value:function(t,n,r){this.registrations.set(r,{value:n,registeredAt:Date.now()}),this.scheduleSweep()}}),Object.defineProperty(e.prototype,"unregister",{enumerable:!1,configurable:!0,writable:!0,value:function(t){this.registrations.delete(t)}}),Object.defineProperty(e.prototype,"scheduleSweep",{enumerable:!1,configurable:!0,writable:!0,value:function(){this.sweepTimeout===void 0&&(this.sweepTimeout=setTimeout(this.sweep,kC))}}),e}(),AC=typeof FinalizationRegistry<"u"?FinalizationRegistry:MC,Nc=new AC(function(e){var t;(t=e.reaction)===null||t===void 0||t.dispose(),e.reaction=null}),Xp=function(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),o,i=[],a;try{for(;(t===void 0||t-- >0)&&!(o=r.next()).done;)i.push(o.value)}catch(l){a={error:l}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(a)throw a.error}}return i};function qp(e){return"observer".concat(e)}var TC=function(){function e(){}return e}();function jC(){return new TC}function NC(e,t){t===void 0&&(t="observed");var n=Xp(Ce.useState(jC),1),r=n[0],o=Xp(Ce.useState(),2),i=o[1],a=function(){return i([])},l=Ce.useRef(null);l.current||(l.current={reaction:null,mounted:!1,changedBeforeMount:!1});var s=l.current;s.reaction||(s.reaction=new jo(qp(t),function(){s.mounted?a():s.changedBeforeMount=!0}),Nc.register(r,s,s)),Ce.useDebugValue(s.reaction,OC),Ce.useEffect(function(){return Nc.unregister(s),s.mounted=!0,s.reaction?s.changedBeforeMount&&(s.changedBeforeMount=!1,a()):(s.reaction=new jo(qp(t),function(){a()}),a()),function(){s.reaction.dispose(),s.reaction=null,s.mounted=!1,s.changedBeforeMount=!1}},[]);var c,p;if(s.reaction.track(function(){try{c=e()}catch(f){p=f}}),p)throw p;return c}var w0=typeof Symbol=="function"&&Symbol.for,Zp=w0?Symbol.for("react.forward_ref"):typeof C.forwardRef=="function"&&C.forwardRef(function(e){return null}).$$typeof,Jp=w0?Symbol.for("react.memo"):typeof C.memo=="function"&&C.memo(function(e){return null}).$$typeof;function PC(e,t){var n;if(Jp&&e.$$typeof===Jp)throw new Error("[mobx-react-lite] You are trying to use `observer` on a function component wrapped in either another `observer` or `React.memo`. The observer already applies 'React.memo' for you.");var r=(n=void 0)!==null&&n!==void 0?n:!1,o=e,i=e.displayName||e.name;if(Zp&&e.$$typeof===Zp&&(r=!0,o=e.render,typeof o!="function"))throw new Error("[mobx-react-lite] `render` property of ForwardRef was not a function");var a=function(l,s){return NC(function(){return o(l,s)},i)};return i!==""&&(a.displayName=i),e.contextTypes&&(a.contextTypes=e.contextTypes),r&&(a=C.forwardRef(a)),a=C.memo(a),LC(e,a),a}var IC={$$typeof:!0,render:!0,compare:!0,type:!0,displayName:!0};function LC(e,t){Object.keys(e).forEach(function(n){IC[n]||Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}var Cu;SC(Xi.unstable_batchedUpdates);Cu=Nc.finalizeAllImmediately;var eh=0;function RC(e){if(typeof Symbol=="function")return Symbol(e);var t="__$mobx-react "+e+" ("+eh+")";return eh++,t}var bu={};function nr(e){return bu[e]||(bu[e]=RC(e)),bu[e]}function C0(e,t){if(th(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=0;o<n.length;o++)if(!Object.hasOwnProperty.call(t,n[o])||!th(e[n[o]],t[n[o]]))return!1;return!0}function th(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function gn(e,t,n){Object.hasOwnProperty.call(e,t)?e[t]=n:Object.defineProperty(e,t,{enumerable:!1,configurable:!0,writable:!0,value:n})}var nh=nr("patchMixins"),b0=nr("patchedDefinition");function DC(e,t){var n=e[nh]=e[nh]||{},r=n[t]=n[t]||{};return r.locks=r.locks||0,r.methods=r.methods||[],r}function rh(e,t){for(var n=this,r=arguments.length,o=new Array(r>2?r-2:0),i=2;i<r;i++)o[i-2]=arguments[i];t.locks++;try{var a;return e!=null&&(a=e.apply(this,o)),a}finally{t.locks--,t.locks===0&&t.methods.forEach(function(l){l.apply(n,o)})}}function oh(e,t){var n=function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];rh.call.apply(rh,[this,e,t].concat(i))};return n}function ih(e,t,n){var r=DC(e,t);r.methods.indexOf(n)<0&&r.methods.push(n);var o=Object.getOwnPropertyDescriptor(e,t);if(!(o&&o[b0])){var i=e[t],a=S0(e,t,o?o.enumerable:void 0,r,i);Object.defineProperty(e,t,a)}}function S0(e,t,n,r,o){var i,a=oh(o,r);return i={},i[b0]=!0,i.get=function(){return a},i.set=function(s){if(this===e)a=oh(s,r);else{var c=S0(this,t,n,r,s);Object.defineProperty(this,t,c)}},i.configurable=!0,i.enumerable=n,i}var vo=H||"$mobx",ah=nr("isMobXReactObserver"),Pc=nr("isUnmounted"),Hl=nr("skipRender"),sl=nr("isForcingUpdate");function $C(e){var t=e.prototype;if(e[ah]){var n=ul(t);console.warn("The provided component class ("+n+`)
                has already been declared as an observer component.`)}else e[ah]=!0;if(t.componentWillReact)throw new Error("The componentWillReact life-cycle event is no longer supported");if(e.__proto__!==C.PureComponent){if(!t.shouldComponentUpdate)t.shouldComponentUpdate=lh;else if(t.shouldComponentUpdate!==lh)throw new Error("It is not allowed to use shouldComponentUpdate in observer based components.")}Su(t,"props"),Su(t,"state"),e.contextType&&Su(t,"context");var r=t.render;if(typeof r!="function"){var o=ul(t);throw new Error("[mobx-react] class component ("+o+") is missing `render` method.\n`observer` requires `render` being a function defined on prototype.\n`render = () => {}` or `render = function() {}` is not supported.")}return t.render=function(){return this.render=BC.call(this,r),this.render()},ih(t,"componentDidMount",function(){this[Pc]=!1,this.render[vo]||C.Component.prototype.forceUpdate.call(this)}),ih(t,"componentWillUnmount",function(){var i=this.render[vo];if(i)i.dispose(),this.render[vo]=null;else{var a=ul(this);console.warn("The reactive render of an observer class component ("+a+`)
                was overridden after MobX attached. This may result in a memory leak if the
                overridden reactive render was not properly disposed.`)}this[Pc]=!0}),e}function ul(e){return e.displayName||e.name||e.constructor&&(e.constructor.displayName||e.constructor.name)||"<component>"}function BC(e){var t=this;gn(this,Hl,!1),gn(this,sl,!1);var n=ul(this),r=e.bind(this),o=!1,i=function(){var s=new jo(n+".render()",function(){if(!o&&(o=!0,t[Pc]!==!0)){var c=!0;try{gn(t,sl,!0),t[Hl]||C.Component.prototype.forceUpdate.call(t),c=!1}finally{gn(t,sl,!1),c&&(s.dispose(),t.render[vo]=null)}}});return s.reactComponent=t,s};function a(){var l;o=!1;var s=(l=a[vo])!=null?l:a[vo]=i(),c=void 0,p=void 0;if(s.track(function(){try{p=Fv(!1,r)}catch(f){c=f}}),c)throw c;return p}return a}function lh(e,t){return this.state!==t?!0:!C0(this.props,e)}function Su(e,t){var n=nr("reactProp_"+t+"_valueHolder"),r=nr("reactProp_"+t+"_atomHolder");function o(){return this[r]||gn(this,r,Kd("reactive "+t)),this[r]}Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){var a=!1;return go&&Xn&&(a=go(!0)),o.call(this).reportObserved(),go&&Xn&&Xn(a),this[n]},set:function(a){!this[sl]&&!C0(this[n],a)?(gn(this,n,a),gn(this,Hl,!0),o.call(this).reportChanged(),gn(this,Hl,!1)):gn(this,n,a)}})}function O0(e){return e.isMobxInjector===!0&&console.warn("Mobx observer: You are trying to use `observer` on a component that already has `inject`. Please apply `observer` before applying `inject`"),Object.prototype.isPrototypeOf.call(C.Component,e)||Object.prototype.isPrototypeOf.call(C.PureComponent,e)?$C(e):PC(e)}if(!C.Component)throw new Error("mobx-react requires React to be available");if(!Be)throw new Error("mobx-react requires mobx to be available");const zC=e=>(e=e||{},Object.entries(e).reduce((n,r)=>{let[o,i]=r;i=i||{};const a=Object.entries(i).reduce((l,s)=>({...l,...UC(s)}),{});return{...n,[o]:a}},{})),FC=e=>e.replace(/-(\w|$)/g,(t,n)=>n.toUpperCase()),UC=e=>{const[t,n]=e;return{[FC(t)]:n}};function WC(e,t){if(!t)return e;const n=e.indexOf("?")>-1?"&":"?",r=new URLSearchParams(t);return r?`${e}${n}${r}`:e}let E0=function(e){return e.GET="GET",e.POST="POST",e.PUT="PUT",e.DELETE="DELETE",e.PATCH="PATCH",e.HEAD="HEAD",e}({});const Ba="Something went wrong.",VC={"Content-Type":"application/json"};async function HC(e){var d;let{url:t,method:n=E0.GET,authToken:r,headers:o,toJson:i=!0,throwError:a=!1,useDefaultHeaders:l=!0,queryParams:s,...c}=e;const p={method:n,...r&&{credentials:"include"},headers:{...l&&VC,...r&&{Authorization:r},...o},...c};let f;try{if(f=await(globalThis==null?void 0:globalThis.fetch(WC(t,s),{...p})),f.ok&&f.status>=200&&f.status<300){let y;return i&&f.status!==204&&((d=f.headers.get("Content-type"))!=null&&d.includes("json"))&&(y=await f.json()),{data:y,error:void 0,response:f}}}catch(y){if(a)throw y||new Error(Ba);return{data:void 0,error:y||Ba,response:f}}if(a)throw new Error(Ba);return{data:void 0,error:Ba,response:f}}const k0=1e3,GC=2*k0,KC=30*k0,YC=5,Ou=e=>M0(...e.reverse())(),QC=e=>()=>t=>e.forEach(n=>n(t)),XC=function(e){let{debounceTime:t=GC,timeout:n=KC,maxBufferCount:r=YC}=e===void 0?{}:e;return function(o){let i,a=null;const l=[],s=function(){a=null,o(l.splice(0))};return c=>{clearInterval(i),l.push(c),a=a||Date.now(),(l.length>=r?s:()=>i=setTimeout(s,Math.max(Math.min(t,a+n-Date.now()))))()}}};function M0(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((r,o)=>i=>o(r(i)))}function sh(e,t){return n=>(n[e]=t,n)}function qC(e){return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];try{return e(...n)}catch{var o;(o=console)==null||o.error==null||o.error("Exception occurred while processing log message",n)}}}function ZC(){let e=!0;return(globalThis.parentFlags||globalThis.commonProps&&!globalThis.rtCommonProps)&&(e=!1),e}function JC(e,t,n){let{leading:r}=n===void 0?{leading:!1}:n,o;return function(){for(var i=arguments.length,a=new Array(i),l=0;l<i;l++)a[l]=arguments[l];o&&clearTimeout(o),r&&e(...a),o=setTimeout(()=>{e(...a),o=null},t)}}globalThis!=null&&globalThis.notifyServerThrottled||(globalThis.notifyServerThrottled=JC(tb,3e4,{leading:!1}));globalThis!=null&&globalThis.evaluatedFlags||(globalThis.evaluatedFlags=new Set);function uh(e,t){return parseInt(String(ef(e,t)),10)}function ef(e,t){return T0(e,t,globalThis.commonProps)}function A0(e,t){return t===void 0&&(t=!1),ef("featureFlag.fromCommonProps.enabled",!1)||(globalThis.evaluatedFlags.add(e),globalThis.notifyServerThrottled()),T0(e,t,eb())}function T0(e,t,n){n===void 0&&(n=globalThis.commonProps);const r=n||globalThis.commonProps||globalThis.rtCommonProps||{};return r[e]===void 0?t:r[e]}function eb(){let e;if(window._flags)e=window._flags;else try{e=window.parent._flags}catch{e={}}return e}async function tb(){if(ZC())return;const e=Array.from(globalThis.evaluatedFlags);if(globalThis.evaluatedFlags.clear(),!!e.length)try{await HC({url:"/api/uis/flags/notify",method:E0.POST,body:JSON.stringify(e),keepalive:!0})}catch(t){console.warn(`Couldn't send flags evaluation (flags: ${e}):`,t)}}const j0=()=>{},za=(e,t)=>t?{[e]:t}:{},ch="application/json",nb="version",dh="/_dm/s/rt/actions/logs",rb=A0("sentry.report.on.client.logs.enabled"),ob=function(e){return["getHours","getMinutes","getSeconds"].map(t=>new Date(e)[t]().toString().padStart(2,"0")).join(":")},N0=e=>typeof e=="string"?e:["message","msg","text","error","desc","description","dataString"].reduce((t,n)=>{const r=e[n];return t||(typeof r=="string"?r:null)},null),ib=e=>t=>{let{logLevel:n,dataString:r}=t;return e({data:r,level:n})},ab=e=>t=>{var n,r;return e(Object.assign(t,{userAgent:(n=window==null?void 0:window.navigator)==null?void 0:n.userAgent,url:(r=window==null?void 0:window.location)==null?void 0:r.href,timestamp:Date.now()}))},fh=e=>{const t=n=>[ke.TRACE,ke.DEBUG,ke.INFO,ke.WARN,ke.ERROR].indexOf(n);return n=>r=>{let{level:o,...i}=r;return t(o)>=t(e)&&n({level:o,...i})}},lb=e=>t=>{let{data:n,data:{_skipJournal:r=!1}={},...o}=t;(r?j0:e)({data:n,...o})},sb=function(){const e=[{test:t=>t instanceof Error,format:t=>["message","stack","code","cause"].reduce((n,r)=>({...za(r,t[r]),...n}),{})},{test:t=>["string","number"].includes(typeof t),format:t=>({message:t})},{test:()=>!0,format:t=>Object.entries(t).reduce((n,r)=>{let[o,i]=r;return{[o]:["number","string"].includes(typeof i)?i:JSON.stringify(i),...n}},{})}];return t=>n=>{t({logs:n.map(r=>{let{data:o,level:i,timestamp:a,userAgent:l,url:s,version:c,environment:p}=r;const f=N0(o);return{priority:i.toUpperCase(),...za("log",f),fields:{_ts:a,_url:s,_userAgent:l,...za("_ver",c),...za("_env",p),...e.find(d=>{let{test:y}=d;return y(o)}).format(o)}}})})}}(),ub=function(e){e===void 0&&(e="");const[t,n]=(e.match(/^(\w+)_([0-9]+)$/)||[]).slice(1);return r=>M0(...[t&&sh("environment",t),n&&sh("version",Number(n)),r].filter(Boolean))}((e=>e(e,window))((e,t)=>t&&(t[nb]||(t.parent!==t?e(e,t.parent):"")))),cb=()=>e=>{let{text:t,level:n}=e;((console==null?void 0:console[n])??console.log)(t)},db=e=>t=>{let{data:n,level:r,timestamp:o}=t;return e({level:r,text:`[${ob(o)}] ${N0(n)||'Cannot find a description for the message sent. See "fields" for more information.'}`})},fb=e=>t=>{var n;return navigator.sendBeacon?navigator.sendBeacon(dh,new Blob([JSON.stringify(t)],{type:ch})):(n=globalThis==null?void 0:globalThis.fetch)==null||n.call(globalThis,dh,{headers:{"content-type":ch},credentials:"omit",cache:"no-store",method:"POST",body:JSON.stringify(t)}),e(t)},pb=e=>t=>{if(rb&&(window!=null&&window.Sentry)){const{logs:n}=t;n.length&&n.forEach(r=>{if(r.priority==="ERROR"){const{log:o}=r,i=new Error(`[FROM LOGGER] - ${o}`);window.Sentry.captureException(i)}})}return e==null?void 0:e(t)},hb=e=>t=>{var n;return((n=ef("server.for.resources"))!=null&&n.includes("localhost")?j0:e)(t)},mb=1e3,gb=.5*mb,vb=5,yb="common.log.debounceDelay",xb="common.log.batchLogLimit",ke={TRACE:"trace",DEBUG:"debug",INFO:"info",WARN:"warn",ERROR:"error"},P0=qC(Ou([ib,ab,ub,QC([Ou([fh(ke.TRACE),db,cb]),Ou([hb,lb,fh(ke.TRACE),XC({debounceTime:uh(yb)||gb,maxBufferCount:uh(xb)||vb}),sb,fb,pb])])])),_b=e=>t=>P0({logLevel:e,dataString:t}),[I0,L0,R0,D0,$0]=[ke.TRACE,ke.DEBUG,ke.INFO,ke.WARN,ke.ERROR].map(_b);Object.assign(P0,{[ke.TRACE]:I0,[ke.DEBUG]:L0,[ke.INFO]:R0,[ke.WARN]:D0,[ke.ERROR]:$0});const wb={[ke.TRACE]:I0,[ke.DEBUG]:L0,[ke.INFO]:R0,[ke.WARN]:D0,[ke.ERROR]:$0};function Cb(e,t,n){if(!e)return"";const r=e,o=bb();o&&(o[r]||o[e]);let i=o&&(o[r]||o[e])||e;return t&&Object.keys(t).forEach(a=>{i=i.replace(new RegExp(`{${a}}`,"g"),t[a])}),i}function bb(){var e;return window.dmStr||((e=window.parent)==null?void 0:e.dmStr)}globalThis._abtests=globalThis._abtests||{};const de={str:(e,t)=>Cb(e,t),getAllStringsForCurrentLang:()=>{throw new Error("not implemented")}};var B0=(e=>(e.ONE_TIME="ONE_TIME",e.SUBSCRIPTION="SUBSCRIPTION",e))(B0||{});function Sb(e){return typeof e=="string"?e:{url:window.location.href,...e}}const je=new Proxy(wb,{get:(e,t)=>n=>{e[t](Sb(n))}}),Li="ONE_TIME_ONLY";var tf=(e=>(e.SNIPCART_READY="snipcart.ready",e.SNIPCART_INITIALIZED="snipcart.initialized",e.ITEM_ADDED="item.added",e.ITEM_UPDATED="item.updated",e.ITEM_REMOVED="item.removed",e.CART_CONFIRMED="cart.confirmed",e))(tf||{});const Ob=["snipcart.initialized","item.added","item.updated","item.removed","cart.confirmed"],Eb=()=>{const[e,t]=C.useState(0);C.useEffect(()=>{const r=[];if(typeof window>"u")return;window.Snipcart?(o(),i()):window.document.addEventListener("snipcart.ready",o);function o(){try{Ob.forEach(a=>{var s;const l=(s=window.Snipcart)==null?void 0:s.events.on(a,i);l&&r.push(l)})}catch(a){je.error("cannot bind snipcart listeners: "+a)}}function i(){var a;try{const l=((a=window.Snipcart)==null?void 0:a.store.getState().cart.items.count)??0;t(l)}catch(l){je.error("cannot update cart count: "+l),t(-1)}}return()=>{r.forEach(a=>{a&&typeof a=="function"&&a()})}},[]);const n=C.useCallback(()=>{var r;(r=window.Snipcart)==null||r.api.theme.cart.open()},[]);return{itemsCount:e,openSnipcart:n}};function Eu(e){try{const t=typeof e=="string"?e:JSON.stringify(e);if(window.TextEncoder){const n=new TextEncoder().encode(t),r=String.fromCharCode(...n);return btoa(r)}return btoa(t)}catch(t){return je.warn({err:t,variation:e,tags:["encodeUniqueKey"]}),"{}"}}function z0(e){try{return JSON.parse(new TextDecoder().decode(Uint8Array.from(atob(e),t=>t.charCodeAt(0)))||"{}")}catch{return{}}}function kb(e){window.document.addEventListener(tf.SNIPCART_READY,e)}function Mb(e){const{purchase_options:t=[],payment_plans:n=[]}=e;switch(!0){case t.length===2:return[{id:Li,name:de.str("ui.runtimessr.priceOptions.option.oneTimeOnly"),discount_percentage:0,plan_price:e.price,plan_displayed_price:e.displayed_price,tax_percentage:e.tax_percentage},...n];case t.includes(B0.SUBSCRIPTION):return n;default:return[]}}function Ab(e){return{...e||{},options:((e==null?void 0:e.options)||[]).reduce((t,n)=>({...t,[n.name]:n.choices.split(",").map(r=>({label:r,value:r}))}),{})}}vs({isolateGlobalState:!0});function ph(e){return typeof e=="object"&&!Array.isArray(e)&&e!==null}function Ic(e,t){const n=new Set(Object.keys({...e,...t})),r={};return n.forEach(o=>{const i=e[o],a=t[o];ph(i)&&ph(a)?r[o]=Ic(i,a):r[o]=a??i}),r}const Tb=e=>{var r,o;const t=(r=globalThis==null?void 0:globalThis.document)==null?void 0:r.getElementById(e),n=(o=t==null?void 0:t.dataset)==null?void 0:o.model;return z0(n)};class jb{constructor(t={},n={}){J(this,"_legacyWidget",!0);J(this,"_model");J(this,"_props");J(this,"_initialProps");J(this,"getInitialJSProps",()=>lo(this._initialProps));J(this,"getJSProps",()=>lo(this._props));this._model=t,this._props=n,ys(this)}get model(){if(!this._legacyWidget){const t="model accessed in non legacy widget";console.error(t),je.debug({msg:t})}return this._model}get modelForPersistance(){return lo(this.model)}get props(){if(this._legacyWidget){const t=this._props;return t._styles=zC(this.model._styles),t}return this._props}setProps(t){this._props=t}jsModel(){return lo(this._model)}mergeProps(t){this._props=Ic({...this.props},{...t})}clearProps(){this._props={}}mergeModel(t){this._model=Ic({...this._model},{...t})}clearModel(){this._model={}}clearModelStyle(){this._model={...this._model,_styles:{}}}set legacyWidget(t){this._legacyWidget=t}setInitialProps(t,n){this._initialProps=t,this.setProps(t),wv({type:"widget-props-store-initial-props-updated",widgetId:n})}}class Nb{constructor(){J(this,"widgetsStoresMap",new Map);J(this,"getWidgetData",this.getWidgetStore)}getWidgetStore(t){let n=this.widgetsStoresMap.get(t);if(!n){const r=Tb(t);n=new jb(r),this.widgetsStoresMap.set(t,n)}return n}}globalThis.propsStore=globalThis.propsStore||new Nb;const F0=globalThis.propsStore;var om;const U0={isInEditor:(om=globalThis==null?void 0:globalThis.Parameters)==null?void 0:om.isInEditor},W0=Ce.createContext(U0);function nf(){return Ce.useContext(W0)}function Pb({children:e}){const t=Ib();return u.jsx(W0.Provider,{value:t,children:e})}function Ib(){const[e,t]=Ce.useState(U0);return Ce.useEffect(()=>{var i;if(!((i=window==null?void 0:window.Parameters)==null?void 0:i.isInEditor))return;let r=null;const o=setInterval(()=>{var l,s,c;const a=(c=(s=(l=window.top)==null?void 0:l.$)==null?void 0:s.Editor)==null?void 0:c.uiStateListener;a&&(clearInterval(o),r=a(t))},1e3);return()=>{r==null||r()}},[]),e}vs({isolateGlobalState:!0});function Lb({Component:e,widgetId:t}){const n=F0.getWidgetStore(t);C.useEffect(()=>{wv({type:"widget-rendered-following-props-change",widgetId:t})},[n.props]);const{key:r,...o}=lo(n.props);return u.jsx(Pb,{children:u.jsx(e,{widgetId:t,...o},r)})}const V0=O0(Lb),hh="runtime.ssr.",Rt={getBoolean:(e,t=!1)=>{if(!e.startsWith(hh))throw new Error(`Flag must contains ${hh}`);return A0(e,t)},getServiceFlags:()=>{throw new Error("not implemented")},initServiceFlags:async()=>{throw new Error("not implemented")}};var rt=(e=>(e.Slider="SSR_IMAGE_SLIDER",e.Cart="SSR_CART",e.AddToCart="SSR_ADD_TO_CART",e.OptionsVariations="SSR_OPTIONS_VARIATIONS",e.Breadcrumbs="SSR_BREADCRUMBS",e.CollectionSearch="SSR_COLLECTION_SEARCH",e.RuntimeFilterSort="SSR_FILTER_SORT",e.ProductPrice="SSR_PRODUCT_PRICE",e.ProductPriceOptions="SSR_PRODUCT_PRICE_OPTIONS",e.Accordion="SSR_ACCORDION",e.CalBooking="SSR_CAL_BOOKING",e.ProductCustomizations="SSR_PRODUCT_CUSTOMIZATIONS",e))(rt||{}),H0={exports:{}},pe={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rf=Symbol.for("react.element"),of=Symbol.for("react.portal"),xs=Symbol.for("react.fragment"),_s=Symbol.for("react.strict_mode"),ws=Symbol.for("react.profiler"),Cs=Symbol.for("react.provider"),bs=Symbol.for("react.context"),Rb=Symbol.for("react.server_context"),Ss=Symbol.for("react.forward_ref"),Os=Symbol.for("react.suspense"),Es=Symbol.for("react.suspense_list"),ks=Symbol.for("react.memo"),Ms=Symbol.for("react.lazy"),Db=Symbol.for("react.offscreen"),G0;G0=Symbol.for("react.module.reference");function Dt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case rf:switch(e=e.type,e){case xs:case ws:case _s:case Os:case Es:return e;default:switch(e=e&&e.$$typeof,e){case Rb:case bs:case Ss:case Ms:case ks:case Cs:return e;default:return t}}case of:return t}}}pe.ContextConsumer=bs;pe.ContextProvider=Cs;pe.Element=rf;pe.ForwardRef=Ss;pe.Fragment=xs;pe.Lazy=Ms;pe.Memo=ks;pe.Portal=of;pe.Profiler=ws;pe.StrictMode=_s;pe.Suspense=Os;pe.SuspenseList=Es;pe.isAsyncMode=function(){return!1};pe.isConcurrentMode=function(){return!1};pe.isContextConsumer=function(e){return Dt(e)===bs};pe.isContextProvider=function(e){return Dt(e)===Cs};pe.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===rf};pe.isForwardRef=function(e){return Dt(e)===Ss};pe.isFragment=function(e){return Dt(e)===xs};pe.isLazy=function(e){return Dt(e)===Ms};pe.isMemo=function(e){return Dt(e)===ks};pe.isPortal=function(e){return Dt(e)===of};pe.isProfiler=function(e){return Dt(e)===ws};pe.isStrictMode=function(e){return Dt(e)===_s};pe.isSuspense=function(e){return Dt(e)===Os};pe.isSuspenseList=function(e){return Dt(e)===Es};pe.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===xs||e===ws||e===_s||e===Os||e===Es||e===Db||typeof e=="object"&&e!==null&&(e.$$typeof===Ms||e.$$typeof===ks||e.$$typeof===Cs||e.$$typeof===bs||e.$$typeof===Ss||e.$$typeof===G0||e.getModuleId!==void 0)};pe.typeOf=Dt;H0.exports=pe;var K0=H0.exports;function $b(e){function t(I,L,R,W,b){for(var Y=0,k=0,me=0,le=0,se,G,We=0,st=0,re,Qe=re=se=0,ue=0,Ve=0,Bo=0,He=0,ga=R.length,zo=ga-1,$t,K="",Ae="",Hs="",Gs="",Nn;ue<ga;){if(G=R.charCodeAt(ue),ue===zo&&k+le+me+Y!==0&&(k!==0&&(G=k===47?10:47),le=me=Y=0,ga++,zo++),k+le+me+Y===0){if(ue===zo&&(0<Ve&&(K=K.replace(d,"")),0<K.trim().length)){switch(G){case 32:case 9:case 59:case 13:case 10:break;default:K+=R.charAt(ue)}G=59}switch(G){case 123:for(K=K.trim(),se=K.charCodeAt(0),re=1,He=++ue;ue<ga;){switch(G=R.charCodeAt(ue)){case 123:re++;break;case 125:re--;break;case 47:switch(G=R.charCodeAt(ue+1)){case 42:case 47:e:{for(Qe=ue+1;Qe<zo;++Qe)switch(R.charCodeAt(Qe)){case 47:if(G===42&&R.charCodeAt(Qe-1)===42&&ue+2!==Qe){ue=Qe+1;break e}break;case 10:if(G===47){ue=Qe+1;break e}}ue=Qe}}break;case 91:G++;case 40:G++;case 34:case 39:for(;ue++<zo&&R.charCodeAt(ue)!==G;);}if(re===0)break;ue++}switch(re=R.substring(He,ue),se===0&&(se=(K=K.replace(f,"").trim()).charCodeAt(0)),se){case 64:switch(0<Ve&&(K=K.replace(d,"")),G=K.charCodeAt(1),G){case 100:case 109:case 115:case 45:Ve=L;break;default:Ve=tt}if(re=t(L,Ve,re,G,b+1),He=re.length,0<j&&(Ve=n(tt,K,Bo),Nn=l(3,re,Ve,L,ce,ee,He,G,b,W),K=Ve.join(""),Nn!==void 0&&(He=(re=Nn.trim()).length)===0&&(G=0,re="")),0<He)switch(G){case 115:K=K.replace(E,a);case 100:case 109:case 45:re=K+"{"+re+"}";break;case 107:K=K.replace(m,"$1 $2"),re=K+"{"+re+"}",re=Oe===1||Oe===2&&i("@"+re,3)?"@-webkit-"+re+"@"+re:"@"+re;break;default:re=K+re,W===112&&(re=(Ae+=re,""))}else re="";break;default:re=t(L,n(L,K,Bo),re,W,b+1)}Hs+=re,re=Bo=Ve=Qe=se=0,K="",G=R.charCodeAt(++ue);break;case 125:case 59:if(K=(0<Ve?K.replace(d,""):K).trim(),1<(He=K.length))switch(Qe===0&&(se=K.charCodeAt(0),se===45||96<se&&123>se)&&(He=(K=K.replace(" ",":")).length),0<j&&(Nn=l(1,K,L,I,ce,ee,Ae.length,W,b,W))!==void 0&&(He=(K=Nn.trim()).length)===0&&(K="\0\0"),se=K.charCodeAt(0),G=K.charCodeAt(1),se){case 0:break;case 64:if(G===105||G===99){Gs+=K+R.charAt(ue);break}default:K.charCodeAt(He-1)!==58&&(Ae+=o(K,se,G,K.charCodeAt(2)))}Bo=Ve=Qe=se=0,K="",G=R.charCodeAt(++ue)}}switch(G){case 13:case 10:k===47?k=0:1+se===0&&W!==107&&0<K.length&&(Ve=1,K+="\0"),0<j*U&&l(0,K,L,I,ce,ee,Ae.length,W,b,W),ee=1,ce++;break;case 59:case 125:if(k+le+me+Y===0){ee++;break}default:switch(ee++,$t=R.charAt(ue),G){case 9:case 32:if(le+Y+k===0)switch(We){case 44:case 58:case 9:case 32:$t="";break;default:G!==32&&($t=" ")}break;case 0:$t="\\0";break;case 12:$t="\\f";break;case 11:$t="\\v";break;case 38:le+k+Y===0&&(Ve=Bo=1,$t="\f"+$t);break;case 108:if(le+k+Y+Me===0&&0<Qe)switch(ue-Qe){case 2:We===112&&R.charCodeAt(ue-3)===58&&(Me=We);case 8:st===111&&(Me=st)}break;case 58:le+k+Y===0&&(Qe=ue);break;case 44:k+me+le+Y===0&&(Ve=1,$t+="\r");break;case 34:case 39:k===0&&(le=le===G?0:le===0?G:le);break;case 91:le+k+me===0&&Y++;break;case 93:le+k+me===0&&Y--;break;case 41:le+k+Y===0&&me--;break;case 40:if(le+k+Y===0){if(se===0)switch(2*We+3*st){case 533:break;default:se=1}me++}break;case 64:k+me+le+Y+Qe+re===0&&(re=1);break;case 42:case 47:if(!(0<le+Y+me))switch(k){case 0:switch(2*G+3*R.charCodeAt(ue+1)){case 235:k=47;break;case 220:He=ue,k=42}break;case 42:G===47&&We===42&&He+2!==ue&&(R.charCodeAt(He+2)===33&&(Ae+=R.substring(He,ue+1)),$t="",k=0)}}k===0&&(K+=$t)}st=We,We=G,ue++}if(He=Ae.length,0<He){if(Ve=L,0<j&&(Nn=l(2,Ae,Ve,I,ce,ee,He,W,b,W),Nn!==void 0&&(Ae=Nn).length===0))return Gs+Ae+Hs;if(Ae=Ve.join(",")+"{"+Ae+"}",Oe*Me!==0){switch(Oe!==2||i(Ae,2)||(Me=0),Me){case 111:Ae=Ae.replace(_,":-moz-$1")+Ae;break;case 112:Ae=Ae.replace(g,"::-webkit-input-$1")+Ae.replace(g,"::-moz-$1")+Ae.replace(g,":-ms-input-$1")+Ae}Me=0}}return Gs+Ae+Hs}function n(I,L,R){var W=L.trim().split(O);L=W;var b=W.length,Y=I.length;switch(Y){case 0:case 1:var k=0;for(I=Y===0?"":I[0]+" ";k<b;++k)L[k]=r(I,L[k],R).trim();break;default:var me=k=0;for(L=[];k<b;++k)for(var le=0;le<Y;++le)L[me++]=r(I[le]+" ",W[k],R).trim()}return L}function r(I,L,R){var W=L.charCodeAt(0);switch(33>W&&(W=(L=L.trim()).charCodeAt(0)),W){case 38:return L.replace(v,"$1"+I.trim());case 58:return I.trim()+L.replace(v,"$1"+I.trim());default:if(0<1*R&&0<L.indexOf("\f"))return L.replace(v,(I.charCodeAt(0)===58?"":"$1")+I.trim())}return I+L}function o(I,L,R,W){var b=I+";",Y=2*L+3*R+4*W;if(Y===944){I=b.indexOf(":",9)+1;var k=b.substring(I,b.length-1).trim();return k=b.substring(0,I).trim()+k+";",Oe===1||Oe===2&&i(k,1)?"-webkit-"+k+k:k}if(Oe===0||Oe===2&&!i(b,1))return b;switch(Y){case 1015:return b.charCodeAt(10)===97?"-webkit-"+b+b:b;case 951:return b.charCodeAt(3)===116?"-webkit-"+b+b:b;case 963:return b.charCodeAt(5)===110?"-webkit-"+b+b:b;case 1009:if(b.charCodeAt(4)!==100)break;case 969:case 942:return"-webkit-"+b+b;case 978:return"-webkit-"+b+"-moz-"+b+b;case 1019:case 983:return"-webkit-"+b+"-moz-"+b+"-ms-"+b+b;case 883:if(b.charCodeAt(8)===45)return"-webkit-"+b+b;if(0<b.indexOf("image-set(",11))return b.replace(Z,"$1-webkit-$2")+b;break;case 932:if(b.charCodeAt(4)===45)switch(b.charCodeAt(5)){case 103:return"-webkit-box-"+b.replace("-grow","")+"-webkit-"+b+"-ms-"+b.replace("grow","positive")+b;case 115:return"-webkit-"+b+"-ms-"+b.replace("shrink","negative")+b;case 98:return"-webkit-"+b+"-ms-"+b.replace("basis","preferred-size")+b}return"-webkit-"+b+"-ms-"+b+b;case 964:return"-webkit-"+b+"-ms-flex-"+b+b;case 1023:if(b.charCodeAt(8)!==99)break;return k=b.substring(b.indexOf(":",15)).replace("flex-","").replace("space-between","justify"),"-webkit-box-pack"+k+"-webkit-"+b+"-ms-flex-pack"+k+b;case 1005:return w.test(b)?b.replace(y,":-webkit-")+b.replace(y,":-moz-")+b:b;case 1e3:switch(k=b.substring(13).trim(),L=k.indexOf("-")+1,k.charCodeAt(0)+k.charCodeAt(L)){case 226:k=b.replace(S,"tb");break;case 232:k=b.replace(S,"tb-rl");break;case 220:k=b.replace(S,"lr");break;default:return b}return"-webkit-"+b+"-ms-"+k+b;case 1017:if(b.indexOf("sticky",9)===-1)break;case 975:switch(L=(b=I).length-10,k=(b.charCodeAt(L)===33?b.substring(0,L):b).substring(I.indexOf(":",7)+1).trim(),Y=k.charCodeAt(0)+(k.charCodeAt(7)|0)){case 203:if(111>k.charCodeAt(8))break;case 115:b=b.replace(k,"-webkit-"+k)+";"+b;break;case 207:case 102:b=b.replace(k,"-webkit-"+(102<Y?"inline-":"")+"box")+";"+b.replace(k,"-webkit-"+k)+";"+b.replace(k,"-ms-"+k+"box")+";"+b}return b+";";case 938:if(b.charCodeAt(5)===45)switch(b.charCodeAt(6)){case 105:return k=b.replace("-items",""),"-webkit-"+b+"-webkit-box-"+k+"-ms-flex-"+k+b;case 115:return"-webkit-"+b+"-ms-flex-item-"+b.replace(M,"")+b;default:return"-webkit-"+b+"-ms-flex-line-pack"+b.replace("align-content","").replace(M,"")+b}break;case 973:case 989:if(b.charCodeAt(3)!==45||b.charCodeAt(4)===122)break;case 931:case 953:if(z.test(I)===!0)return(k=I.substring(I.indexOf(":")+1)).charCodeAt(0)===115?o(I.replace("stretch","fill-available"),L,R,W).replace(":fill-available",":stretch"):b.replace(k,"-webkit-"+k)+b.replace(k,"-moz-"+k.replace("fill-",""))+b;break;case 962:if(b="-webkit-"+b+(b.charCodeAt(5)===102?"-ms-"+b:"")+b,R+W===211&&b.charCodeAt(13)===105&&0<b.indexOf("transform",10))return b.substring(0,b.indexOf(";",27)+1).replace(x,"$1-webkit-$2")+b}return b}function i(I,L){var R=I.indexOf(L===1?":":"{"),W=I.substring(0,L!==3?R:10);return R=I.substring(R+1,I.length-1),F(L!==2?W:W.replace(B,"$1"),R,L)}function a(I,L){var R=o(L,L.charCodeAt(0),L.charCodeAt(1),L.charCodeAt(2));return R!==L+";"?R.replace(T," or ($1)").substring(4):"("+L+")"}function l(I,L,R,W,b,Y,k,me,le,se){for(var G=0,We=L,st;G<j;++G)switch(st=Ne[G].call(p,I,We,R,W,b,Y,k,me,le,se)){case void 0:case!1:case!0:case null:break;default:We=st}if(We!==L)return We}function s(I){switch(I){case void 0:case null:j=Ne.length=0;break;default:if(typeof I=="function")Ne[j++]=I;else if(typeof I=="object")for(var L=0,R=I.length;L<R;++L)s(I[L]);else U=!!I|0}return s}function c(I){return I=I.prefix,I!==void 0&&(F=null,I?typeof I!="function"?Oe=1:(Oe=2,F=I):Oe=0),c}function p(I,L){var R=I;if(33>R.charCodeAt(0)&&(R=R.trim()),ne=R,R=[ne],0<j){var W=l(-1,L,R,R,ce,ee,0,0,0,0);W!==void 0&&typeof W=="string"&&(L=W)}var b=t(tt,R,L,0,0);return 0<j&&(W=l(-2,b,R,R,ce,ee,b.length,0,0,0),W!==void 0&&(b=W)),ne="",Me=0,ee=ce=1,b}var f=/^\0+/g,d=/[\0\r\f]/g,y=/: */g,w=/zoo|gra/,x=/([,: ])(transform)/g,O=/,\r+?/g,v=/([\t\r\n ])*\f?&/g,m=/@(k\w+)\s*(\S*)\s*/,g=/::(place)/g,_=/:(read-only)/g,S=/[svh]\w+-[tblr]{2}/,E=/\(\s*(.*)\s*\)/g,T=/([\s\S]*?);/g,M=/-self|flex-/g,B=/[^]*?(:[rp][el]a[\w-]+)[^]*/,z=/stretch|:\s*\w+\-(?:conte|avail)/,Z=/([^-])(image-set\()/,ee=1,ce=1,Me=0,Oe=1,tt=[],Ne=[],j=0,F=null,U=0,ne="";return p.use=s,p.set=c,e!==void 0&&c(e),p}var Bb={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function zb(e){var t=Object.create(null);return function(n){return t[n]===void 0&&(t[n]=e(n)),t[n]}}var Fb=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,mh=zb(function(e){return Fb.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91}),Y0={exports:{}},he={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ue=typeof Symbol=="function"&&Symbol.for,af=Ue?Symbol.for("react.element"):60103,lf=Ue?Symbol.for("react.portal"):60106,As=Ue?Symbol.for("react.fragment"):60107,Ts=Ue?Symbol.for("react.strict_mode"):60108,js=Ue?Symbol.for("react.profiler"):60114,Ns=Ue?Symbol.for("react.provider"):60109,Ps=Ue?Symbol.for("react.context"):60110,sf=Ue?Symbol.for("react.async_mode"):60111,Is=Ue?Symbol.for("react.concurrent_mode"):60111,Ls=Ue?Symbol.for("react.forward_ref"):60112,Rs=Ue?Symbol.for("react.suspense"):60113,Ub=Ue?Symbol.for("react.suspense_list"):60120,Ds=Ue?Symbol.for("react.memo"):60115,$s=Ue?Symbol.for("react.lazy"):60116,Wb=Ue?Symbol.for("react.block"):60121,Vb=Ue?Symbol.for("react.fundamental"):60117,Hb=Ue?Symbol.for("react.responder"):60118,Gb=Ue?Symbol.for("react.scope"):60119;function St(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case af:switch(e=e.type,e){case sf:case Is:case As:case js:case Ts:case Rs:return e;default:switch(e=e&&e.$$typeof,e){case Ps:case Ls:case $s:case Ds:case Ns:return e;default:return t}}case lf:return t}}}function Q0(e){return St(e)===Is}he.AsyncMode=sf;he.ConcurrentMode=Is;he.ContextConsumer=Ps;he.ContextProvider=Ns;he.Element=af;he.ForwardRef=Ls;he.Fragment=As;he.Lazy=$s;he.Memo=Ds;he.Portal=lf;he.Profiler=js;he.StrictMode=Ts;he.Suspense=Rs;he.isAsyncMode=function(e){return Q0(e)||St(e)===sf};he.isConcurrentMode=Q0;he.isContextConsumer=function(e){return St(e)===Ps};he.isContextProvider=function(e){return St(e)===Ns};he.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===af};he.isForwardRef=function(e){return St(e)===Ls};he.isFragment=function(e){return St(e)===As};he.isLazy=function(e){return St(e)===$s};he.isMemo=function(e){return St(e)===Ds};he.isPortal=function(e){return St(e)===lf};he.isProfiler=function(e){return St(e)===js};he.isStrictMode=function(e){return St(e)===Ts};he.isSuspense=function(e){return St(e)===Rs};he.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===As||e===Is||e===js||e===Ts||e===Rs||e===Ub||typeof e=="object"&&e!==null&&(e.$$typeof===$s||e.$$typeof===Ds||e.$$typeof===Ns||e.$$typeof===Ps||e.$$typeof===Ls||e.$$typeof===Vb||e.$$typeof===Hb||e.$$typeof===Gb||e.$$typeof===Wb)};he.typeOf=St;Y0.exports=he;var Kb=Y0.exports,uf=Kb,Yb={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Qb={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Xb={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},X0={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},cf={};cf[uf.ForwardRef]=Xb;cf[uf.Memo]=X0;function gh(e){return uf.isMemo(e)?X0:cf[e.$$typeof]||Yb}var qb=Object.defineProperty,Zb=Object.getOwnPropertyNames,vh=Object.getOwnPropertySymbols,Jb=Object.getOwnPropertyDescriptor,eS=Object.getPrototypeOf,yh=Object.prototype;function q0(e,t,n){if(typeof t!="string"){if(yh){var r=eS(t);r&&r!==yh&&q0(e,r,n)}var o=Zb(t);vh&&(o=o.concat(vh(t)));for(var i=gh(e),a=gh(t),l=0;l<o.length;++l){var s=o[l];if(!Qb[s]&&!(n&&n[s])&&!(a&&a[s])&&!(i&&i[s])){var c=Jb(t,s);try{qb(e,s,c)}catch{}}}}return e}var tS=q0;const nS=ql(tS);var Et={};function rn(){return(rn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var xh=function(e,t){for(var n=[e[0]],r=0,o=t.length;r<o;r+=1)n.push(t[r],e[r+1]);return n},Lc=function(e){return e!==null&&typeof e=="object"&&(e.toString?e.toString():Object.prototype.toString.call(e))==="[object Object]"&&!K0.typeOf(e)},Gl=Object.freeze([]),qn=Object.freeze({});function Ri(e){return typeof e=="function"}function _h(e){return e.displayName||e.name||"Component"}function df(e){return e&&typeof e.styledComponentId=="string"}var No=typeof process<"u"&&Et!==void 0&&(Et.REACT_APP_SC_ATTR||Et.SC_ATTR)||"data-styled",ff=typeof window<"u"&&"HTMLElement"in window,rS=!!(typeof SC_DISABLE_SPEEDY=="boolean"?SC_DISABLE_SPEEDY:typeof process<"u"&&Et!==void 0&&(Et.REACT_APP_SC_DISABLE_SPEEDY!==void 0&&Et.REACT_APP_SC_DISABLE_SPEEDY!==""?Et.REACT_APP_SC_DISABLE_SPEEDY!=="false"&&Et.REACT_APP_SC_DISABLE_SPEEDY:Et.SC_DISABLE_SPEEDY!==void 0&&Et.SC_DISABLE_SPEEDY!==""&&Et.SC_DISABLE_SPEEDY!=="false"&&Et.SC_DISABLE_SPEEDY)),oS={};function da(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("An error occurred. See https://git.io/JUIaE#"+e+" for more information."+(n.length>0?" Args: "+n.join(", "):""))}var iS=function(){function e(n){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=n}var t=e.prototype;return t.indexOfGroup=function(n){for(var r=0,o=0;o<n;o++)r+=this.groupSizes[o];return r},t.insertRules=function(n,r){if(n>=this.groupSizes.length){for(var o=this.groupSizes,i=o.length,a=i;n>=a;)(a<<=1)<0&&da(16,""+n);this.groupSizes=new Uint32Array(a),this.groupSizes.set(o),this.length=a;for(var l=i;l<a;l++)this.groupSizes[l]=0}for(var s=this.indexOfGroup(n+1),c=0,p=r.length;c<p;c++)this.tag.insertRule(s,r[c])&&(this.groupSizes[n]++,s++)},t.clearGroup=function(n){if(n<this.length){var r=this.groupSizes[n],o=this.indexOfGroup(n),i=o+r;this.groupSizes[n]=0;for(var a=o;a<i;a++)this.tag.deleteRule(o)}},t.getGroup=function(n){var r="";if(n>=this.length||this.groupSizes[n]===0)return r;for(var o=this.groupSizes[n],i=this.indexOfGroup(n),a=i+o,l=i;l<a;l++)r+=this.tag.getRule(l)+`/*!sc*/
`;return r},e}(),cl=new Map,Kl=new Map,fi=1,Fa=function(e){if(cl.has(e))return cl.get(e);for(;Kl.has(fi);)fi++;var t=fi++;return cl.set(e,t),Kl.set(t,e),t},aS=function(e){return Kl.get(e)},lS=function(e,t){t>=fi&&(fi=t+1),cl.set(e,t),Kl.set(t,e)},sS="style["+No+'][data-styled-version="5.3.11"]',uS=new RegExp("^"+No+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),cS=function(e,t,n){for(var r,o=n.split(","),i=0,a=o.length;i<a;i++)(r=o[i])&&e.registerName(t,r)},dS=function(e,t){for(var n=(t.textContent||"").split(`/*!sc*/
`),r=[],o=0,i=n.length;o<i;o++){var a=n[o].trim();if(a){var l=a.match(uS);if(l){var s=0|parseInt(l[1],10),c=l[2];s!==0&&(lS(c,s),cS(e,c,l[3]),e.getTag().insertRules(s,r)),r.length=0}else r.push(a)}}},fS=function(){return typeof __webpack_nonce__<"u"?__webpack_nonce__:null},Z0=function(e){var t=document.head,n=e||t,r=document.createElement("style"),o=function(l){for(var s=l.childNodes,c=s.length;c>=0;c--){var p=s[c];if(p&&p.nodeType===1&&p.hasAttribute(No))return p}}(n),i=o!==void 0?o.nextSibling:null;r.setAttribute(No,"active"),r.setAttribute("data-styled-version","5.3.11");var a=fS();return a&&r.setAttribute("nonce",a),n.insertBefore(r,i),r},pS=function(){function e(n){var r=this.element=Z0(n);r.appendChild(document.createTextNode("")),this.sheet=function(o){if(o.sheet)return o.sheet;for(var i=document.styleSheets,a=0,l=i.length;a<l;a++){var s=i[a];if(s.ownerNode===o)return s}da(17)}(r),this.length=0}var t=e.prototype;return t.insertRule=function(n,r){try{return this.sheet.insertRule(r,n),this.length++,!0}catch{return!1}},t.deleteRule=function(n){this.sheet.deleteRule(n),this.length--},t.getRule=function(n){var r=this.sheet.cssRules[n];return r!==void 0&&typeof r.cssText=="string"?r.cssText:""},e}(),hS=function(){function e(n){var r=this.element=Z0(n);this.nodes=r.childNodes,this.length=0}var t=e.prototype;return t.insertRule=function(n,r){if(n<=this.length&&n>=0){var o=document.createTextNode(r),i=this.nodes[n];return this.element.insertBefore(o,i||null),this.length++,!0}return!1},t.deleteRule=function(n){this.element.removeChild(this.nodes[n]),this.length--},t.getRule=function(n){return n<this.length?this.nodes[n].textContent:""},e}(),mS=function(){function e(n){this.rules=[],this.length=0}var t=e.prototype;return t.insertRule=function(n,r){return n<=this.length&&(this.rules.splice(n,0,r),this.length++,!0)},t.deleteRule=function(n){this.rules.splice(n,1),this.length--},t.getRule=function(n){return n<this.length?this.rules[n]:""},e}(),wh=ff,gS={isServer:!ff,useCSSOMInjection:!rS},Yl=function(){function e(n,r,o){n===void 0&&(n=qn),r===void 0&&(r={}),this.options=rn({},gS,{},n),this.gs=r,this.names=new Map(o),this.server=!!n.isServer,!this.server&&ff&&wh&&(wh=!1,function(i){for(var a=document.querySelectorAll(sS),l=0,s=a.length;l<s;l++){var c=a[l];c&&c.getAttribute(No)!=="active"&&(dS(i,c),c.parentNode&&c.parentNode.removeChild(c))}}(this))}e.registerId=function(n){return Fa(n)};var t=e.prototype;return t.reconstructWithOptions=function(n,r){return r===void 0&&(r=!0),new e(rn({},this.options,{},n),this.gs,r&&this.names||void 0)},t.allocateGSInstance=function(n){return this.gs[n]=(this.gs[n]||0)+1},t.getTag=function(){return this.tag||(this.tag=(o=(r=this.options).isServer,i=r.useCSSOMInjection,a=r.target,n=o?new mS(a):i?new pS(a):new hS(a),new iS(n)));var n,r,o,i,a},t.hasNameForId=function(n,r){return this.names.has(n)&&this.names.get(n).has(r)},t.registerName=function(n,r){if(Fa(n),this.names.has(n))this.names.get(n).add(r);else{var o=new Set;o.add(r),this.names.set(n,o)}},t.insertRules=function(n,r,o){this.registerName(n,r),this.getTag().insertRules(Fa(n),o)},t.clearNames=function(n){this.names.has(n)&&this.names.get(n).clear()},t.clearRules=function(n){this.getTag().clearGroup(Fa(n)),this.clearNames(n)},t.clearTag=function(){this.tag=void 0},t.toString=function(){return function(n){for(var r=n.getTag(),o=r.length,i="",a=0;a<o;a++){var l=aS(a);if(l!==void 0){var s=n.names.get(l),c=r.getGroup(a);if(s&&c&&s.size){var p=No+".g"+a+'[id="'+l+'"]',f="";s!==void 0&&s.forEach(function(d){d.length>0&&(f+=d+",")}),i+=""+c+p+'{content:"'+f+`"}/*!sc*/
`}}}return i}(this)},e}(),vS=/(a)(d)/gi,Ch=function(e){return String.fromCharCode(e+(e>25?39:97))};function Rc(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=Ch(t%52)+n;return(Ch(t%52)+n).replace(vS,"$1-$2")}var so=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},J0=function(e){return so(5381,e)};function e1(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Ri(n)&&!df(n))return!1}return!0}var yS=J0("5.3.11"),xS=function(){function e(t,n,r){this.rules=t,this.staticRulesId="",this.isStatic=(r===void 0||r.isStatic)&&e1(t),this.componentId=n,this.baseHash=so(yS,n),this.baseStyle=r,Yl.registerId(n)}return e.prototype.generateAndInjectStyles=function(t,n,r){var o=this.componentId,i=[];if(this.baseStyle&&i.push(this.baseStyle.generateAndInjectStyles(t,n,r)),this.isStatic&&!r.hash)if(this.staticRulesId&&n.hasNameForId(o,this.staticRulesId))i.push(this.staticRulesId);else{var a=Pr(this.rules,t,n,r).join(""),l=Rc(so(this.baseHash,a)>>>0);if(!n.hasNameForId(o,l)){var s=r(a,"."+l,void 0,o);n.insertRules(o,l,s)}i.push(l),this.staticRulesId=l}else{for(var c=this.rules.length,p=so(this.baseHash,r.hash),f="",d=0;d<c;d++){var y=this.rules[d];if(typeof y=="string")f+=y;else if(y){var w=Pr(y,t,n,r),x=Array.isArray(w)?w.join(""):w;p=so(p,x+d),f+=x}}if(f){var O=Rc(p>>>0);if(!n.hasNameForId(o,O)){var v=r(f,"."+O,void 0,o);n.insertRules(o,O,v)}i.push(O)}}return i.join(" ")},e}(),_S=/^\s*\/\/.*$/gm,wS=[":","[",".","#"];function CS(e){var t,n,r,o,i=qn,a=i.options,l=a===void 0?qn:a,s=i.plugins,c=s===void 0?Gl:s,p=new $b(l),f=[],d=function(x){function O(v){if(v)try{x(v+"}")}catch{}}return function(v,m,g,_,S,E,T,M,B,z){switch(v){case 1:if(B===0&&m.charCodeAt(0)===64)return x(m+";"),"";break;case 2:if(M===0)return m+"/*|*/";break;case 3:switch(M){case 102:case 112:return x(g[0]+m),"";default:return m+(z===0?"/*|*/":"")}case-2:m.split("/*|*/}").forEach(O)}}}(function(x){f.push(x)}),y=function(x,O,v){return O===0&&wS.indexOf(v[n.length])!==-1||v.match(o)?x:"."+t};function w(x,O,v,m){m===void 0&&(m="&");var g=x.replace(_S,""),_=O&&v?v+" "+O+" { "+g+" }":g;return t=m,n=O,r=new RegExp("\\"+n+"\\b","g"),o=new RegExp("(\\"+n+"\\b){2,}"),p(v||!O?"":O,_)}return p.use([].concat(c,[function(x,O,v){x===2&&v.length&&v[0].lastIndexOf(n)>0&&(v[0]=v[0].replace(r,y))},d,function(x){if(x===-2){var O=f;return f=[],O}}])),w.hash=c.length?c.reduce(function(x,O){return O.name||da(15),so(x,O.name)},5381).toString():"",w}var t1=Ce.createContext();t1.Consumer;var n1=Ce.createContext(),bS=(n1.Consumer,new Yl),Dc=CS();function r1(){return C.useContext(t1)||bS}function o1(){return C.useContext(n1)||Dc}var SS=function(){function e(t,n){var r=this;this.inject=function(o,i){i===void 0&&(i=Dc);var a=r.name+i.hash;o.hasNameForId(r.id,a)||o.insertRules(r.id,a,i(r.rules,a,"@keyframes"))},this.toString=function(){return da(12,String(r.name))},this.name=t,this.id="sc-keyframes-"+t,this.rules=n}return e.prototype.getName=function(t){return t===void 0&&(t=Dc),this.name+t.hash},e}(),OS=/([A-Z])/,ES=/([A-Z])/g,kS=/^ms-/,MS=function(e){return"-"+e.toLowerCase()};function bh(e){return OS.test(e)?e.replace(ES,MS).replace(kS,"-ms-"):e}var Sh=function(e){return e==null||e===!1||e===""};function Pr(e,t,n,r){if(Array.isArray(e)){for(var o,i=[],a=0,l=e.length;a<l;a+=1)(o=Pr(e[a],t,n,r))!==""&&(Array.isArray(o)?i.push.apply(i,o):i.push(o));return i}if(Sh(e))return"";if(df(e))return"."+e.styledComponentId;if(Ri(e)){if(typeof(c=e)!="function"||c.prototype&&c.prototype.isReactComponent||!t)return e;var s=e(t);return Pr(s,t,n,r)}var c;return e instanceof SS?n?(e.inject(n,r),e.getName(r)):e:Lc(e)?function p(f,d){var y,w,x=[];for(var O in f)f.hasOwnProperty(O)&&!Sh(f[O])&&(Array.isArray(f[O])&&f[O].isCss||Ri(f[O])?x.push(bh(O)+":",f[O],";"):Lc(f[O])?x.push.apply(x,p(f[O],O)):x.push(bh(O)+": "+(y=O,(w=f[O])==null||typeof w=="boolean"||w===""?"":typeof w!="number"||w===0||y in Bb||y.startsWith("--")?String(w).trim():w+"px")+";"));return d?[d+" {"].concat(x,["}"]):x}(e):e.toString()}var Oh=function(e){return Array.isArray(e)&&(e.isCss=!0),e};function qt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Ri(e)||Lc(e)?Oh(Pr(xh(Gl,[e].concat(n)))):n.length===0&&e.length===1&&typeof e[0]=="string"?e:Oh(Pr(xh(e,n)))}var i1=function(e,t,n){return n===void 0&&(n=qn),e.theme!==n.theme&&e.theme||t||n.theme},AS=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,TS=/(^-|-$)/g;function ku(e){return e.replace(AS,"-").replace(TS,"")}var a1=function(e){return Rc(J0(e)>>>0)};function Ua(e){return typeof e=="string"&&!0}var $c=function(e){return typeof e=="function"||typeof e=="object"&&e!==null&&!Array.isArray(e)},jS=function(e){return e!=="__proto__"&&e!=="constructor"&&e!=="prototype"};function NS(e,t,n){var r=e[n];$c(t)&&$c(r)?l1(r,t):e[n]=t}function l1(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var o=0,i=n;o<i.length;o++){var a=i[o];if($c(a))for(var l in a)jS(l)&&NS(e,a[l],l)}return e}var pf=Ce.createContext();pf.Consumer;var Mu={};function s1(e,t,n){var r=df(e),o=!Ua(e),i=t.attrs,a=i===void 0?Gl:i,l=t.componentId,s=l===void 0?function(m,g){var _=typeof m!="string"?"sc":ku(m);Mu[_]=(Mu[_]||0)+1;var S=_+"-"+a1("5.3.11"+_+Mu[_]);return g?g+"-"+S:S}(t.displayName,t.parentComponentId):l,c=t.displayName,p=c===void 0?function(m){return Ua(m)?"styled."+m:"Styled("+_h(m)+")"}(e):c,f=t.displayName&&t.componentId?ku(t.displayName)+"-"+t.componentId:t.componentId||s,d=r&&e.attrs?Array.prototype.concat(e.attrs,a).filter(Boolean):a,y=t.shouldForwardProp;r&&e.shouldForwardProp&&(y=t.shouldForwardProp?function(m,g,_){return e.shouldForwardProp(m,g,_)&&t.shouldForwardProp(m,g,_)}:e.shouldForwardProp);var w,x=new xS(n,f,r?e.componentStyle:void 0),O=x.isStatic&&a.length===0,v=function(m,g){return function(_,S,E,T){var M=_.attrs,B=_.componentStyle,z=_.defaultProps,Z=_.foldedComponentIds,ee=_.shouldForwardProp,ce=_.styledComponentId,Me=_.target,Oe=function(W,b,Y){W===void 0&&(W=qn);var k=rn({},b,{theme:W}),me={};return Y.forEach(function(le){var se,G,We,st=le;for(se in Ri(st)&&(st=st(k)),st)k[se]=me[se]=se==="className"?(G=me[se],We=st[se],G&&We?G+" "+We:G||We):st[se]}),[k,me]}(i1(S,C.useContext(pf),z)||qn,S,M),tt=Oe[0],Ne=Oe[1],j=function(W,b,Y,k){var me=r1(),le=o1(),se=b?W.generateAndInjectStyles(qn,me,le):W.generateAndInjectStyles(Y,me,le);return se}(B,T,tt),F=E,U=Ne.$as||S.$as||Ne.as||S.as||Me,ne=Ua(U),I=Ne!==S?rn({},S,{},Ne):S,L={};for(var R in I)R[0]!=="$"&&R!=="as"&&(R==="forwardedAs"?L.as=I[R]:(ee?ee(R,mh,U):!ne||mh(R))&&(L[R]=I[R]));return S.style&&Ne.style!==S.style&&(L.style=rn({},S.style,{},Ne.style)),L.className=Array.prototype.concat(Z,ce,j!==ce?j:null,S.className,Ne.className).filter(Boolean).join(" "),L.ref=F,C.createElement(U,L)}(w,m,g,O)};return v.displayName=p,(w=Ce.forwardRef(v)).attrs=d,w.componentStyle=x,w.displayName=p,w.shouldForwardProp=y,w.foldedComponentIds=r?Array.prototype.concat(e.foldedComponentIds,e.styledComponentId):Gl,w.styledComponentId=f,w.target=r?e.target:e,w.withComponent=function(m){var g=t.componentId,_=function(E,T){if(E==null)return{};var M,B,z={},Z=Object.keys(E);for(B=0;B<Z.length;B++)M=Z[B],T.indexOf(M)>=0||(z[M]=E[M]);return z}(t,["componentId"]),S=g&&g+"-"+(Ua(m)?m:ku(_h(m)));return s1(m,rn({},_,{attrs:d,componentId:S}),n)},Object.defineProperty(w,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(m){this._foldedDefaultProps=r?l1({},e.defaultProps,m):m}}),Object.defineProperty(w,"toString",{value:function(){return"."+w.styledComponentId}}),o&&nS(w,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),w}var D=function(e){return function t(n,r,o){if(o===void 0&&(o=qn),!K0.isValidElementType(r))return da(1,String(r));var i=function(){return n(r,o,qt.apply(void 0,arguments))};return i.withConfig=function(a){return t(n,r,rn({},o,{},a))},i.attrs=function(a){return t(n,r,rn({},o,{attrs:Array.prototype.concat(o.attrs,a).filter(Boolean)}))},i}(s1,e)};["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach(function(e){D[e]=D(e)});var PS=function(){function e(n,r){this.rules=n,this.componentId=r,this.isStatic=e1(n),Yl.registerId(this.componentId+1)}var t=e.prototype;return t.createStyles=function(n,r,o,i){var a=i(Pr(this.rules,r,o,i).join(""),""),l=this.componentId+n;o.insertRules(l,l,a)},t.removeStyles=function(n,r){r.clearRules(this.componentId+n)},t.renderStyles=function(n,r,o,i){n>2&&Yl.registerId(this.componentId+n),this.removeStyles(n,o),this.createStyles(n,r,o,i)},e}();function IS(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=qt.apply(void 0,[e].concat(n)),i="sc-global-"+a1(JSON.stringify(o)),a=new PS(o,i);function l(c){var p=r1(),f=o1(),d=C.useContext(pf),y=C.useRef(p.allocateGSInstance(i)).current;return p.server&&s(y,c,p,d,f),C.useLayoutEffect(function(){if(!p.server)return s(y,c,p,d,f),function(){return a.removeStyles(y,p)}},[y,c,p,d,f]),null}function s(c,p,f,d,y){if(a.isStatic)a.renderStyles(c,oS,f,y);else{var w=rn({},p,{theme:i1(p,d,l.defaultProps)});a.renderStyles(c,w,f,y)}}return Ce.memo(l)}let h=function(e){return e.COMMON="common",e.DESKTOP="desktop",e.TABLET="tablet",e.TABLET_IMPLICIT="tablet_implicit",e.MOBILE="mobile",e.MOBILE_IMPLICIT="mobile_implicit",e}({});const LS={[h.COMMON]:{},[h.DESKTOP]:{minWidth:1025},[h.TABLET]:{maxWidth:1024,minWidth:768},[h.MOBILE]:{maxWidth:767}};function Eh(e){return e&&typeof e=="object"&&!Array.isArray(e)}function u1(e,t){const n={...e};return Object.keys(t).forEach(r=>{Eh(e[r])&&Eh(t[r])?n[r]=u1(e[r],t[r]):n[r]=t[r]}),n}const hf=(e,t)=>{let n;return(...r)=>{clearTimeout(n),n=setTimeout(()=>{e.apply(void 0,r)},t)}};function c1(e){return e==null?!0:Object.keys(e).length===0}function RS(e,t){return t.reduce((n,r)=>(e.hasOwnProperty(r)&&(n[r]=e[r]),n),{})}const et={get isEditor(){var e,t,n;try{return(n=(t=(e=window.$)==null?void 0:e.DM)==null?void 0:t.insideEditor)==null?void 0:n.call(t)}catch{return!1}},get isServer(){return!1},get isBrowser(){return!0}};function DS(e){var t={};return n=>n in t?t[n]:t[n]=e(n)}function $S(e){return e.replace(/-./g,t=>t[1].toUpperCase())}const BS=DS($S);function d1(e){const t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="object"&&r!=null?t[n]=d1(r):t[BS(n)]=r}),t}const zS=d1;function FS(e,t){if(!t)return e;const n=zS(t);return u1(e,n)}function Yt(e){return(Array.isArray(e)?e:[e]).flat(1/0).reduce(FS,{})}function Fe(e={},t){const{pseudoSelector:n,innerSelector:r}=t,o={};return Object.entries(e).forEach(([i,a])=>{let l;n?(l={[`&:${n} ${r||""}`]:a},et.isEditor&&(l[`&.${n} ${r||""}`]=a)):r?l={[r]:a}:l={},o[i]=l}),o}function US(e,t){const n={};return Object.entries(e).forEach(([r,o])=>{n[r]=Object.entries(o).reduce((i,[a,l])=>(t.has(a)&&(i[a]=l),i),{})}),n}const WS="#dm#dm#dm &&";function VS(e,t){return`${e==="maxWidth"?"max-width":"min-width"}: ${t}px`}function HS(e,t){const n=LS[e];let r;return!n.maxWidth&&!n.minWidth?r="all":r=Object.entries(n).map(([o,i])=>`(${VS(o,i)})`).join(" and "),{[`@media ${r}`]:t}}const GS={main:h.MOBILE,implicit:h.MOBILE_IMPLICIT},KS={main:h.TABLET,implicit:h.TABLET_IMPLICIT};function kh(e={},t){const n=e[t.implicit],r=e[t.main];return(n||r)&&{...n,...r}}const YS=(e,t)=>Object.fromEntries(t.filter(n=>n in e).map(n=>[n,e[n]]));function QS(e={},t=!1){const n={...YS(e,[h.COMMON,h.DESKTOP]),[h.TABLET]:kh(e,KS),[h.MOBILE]:kh(e,GS)};let r={};return Object.entries(n).forEach(([o,i])=>{if(i){const a=HS(o,i);Object.assign(r,a)}}),t?{[WS]:r}:r}function Tn(e,t){const n=Yt(e||[]);return QS(n,t)}const XS=D.video(({styles:e})=>Tn([qS,e],!0)),qS={[h.COMMON]:{margin:"revert"}},Dr=D.div(({styles:e})=>Tn(e||[])),N=Dr,Lt=Dr.withComponent("span"),Di=Dr.withComponent("img"),mf=Dr.withComponent("label"),ZS=Dr.withComponent("li"),JS=Dr.withComponent("ul"),$i=Dr.withComponent("svg");function f1(e,t=!1){return D.div(({styles:r})=>Tn(r,t)).withComponent(e)}var ae=(e=>(e.h1="h1",e.h2="h2",e.h3="h3",e.h4="h4",e.h5="h5",e.h6="h6",e.paragraph="p",e.span="span",e.div="div",e))(ae||{});function eO(e){return ae[e]}const p1=D.div(e=>Tn(e.styles,!0)),Q=({tag:e,domAttrs:t,grabId:n,...r})=>u.jsx(p1,{as:e,"data-auto":n,"data-grab":n,...t,...r});Q.selector=p1;const gf=({containerStyles:e})=>et.isEditor?u.jsxs(N,{styles:[tO,e],children:[u.jsx(Di,{src:"https://static-cdn.multiscreensite.com/react-widgets/placeholder.svg",alt:"placeholder"}),u.jsxs(N,{styles:nO,children:[u.jsx(Di,{src:"https://static-cdn.multiscreensite.com/react-widgets/i_Plus.svg",alt:"plus"}),u.jsx(Q,{styles:rO,children:de.str("placeholder.add-content")})]})]}):null,tO={[h.COMMON]:{width:"100%",height:"100%",position:"relative",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",backgroundColor:"#F5F5F5"}},nO={[h.COMMON]:{display:"flex",alignItems:"center",color:"#616C79"}},rO={[h.COMMON]:{fontFamily:"Source Sans Pro",fontWeight:400,fontSize:"14px",lineHeight:"22px",color:"#616C79",paddingInlineStart:"4px"}},h1=({linkFunctionalityDomAttributes:e,grabId:t,classNames:n,...r})=>u.jsx(oO,{...e,"data-auto":t,"data-grab":t,className:n==null?void 0:n.join(" "),...r}),oO=D.a.withConfig({shouldForwardProp:e=>e!=="styles"})(({styles:e})=>Tn(e,!0)),iO=f1("span",!0),Bc=({buttonContent:e,styles:t,linkFunctionalityDomAttributes:n,normalizeStyles:r=!0,iconClass:o,showHoverEffect:i,rootDataAttributes:a,dataGrab:l,isDisabled:s=!1,className:c})=>{const{text:p}=e,f=r?sO:{};return u.jsxs(h1,{styles:[f.root,t.root,s?t.rootDisabled:[Fe(Yt(t.rootHover),{pseudoSelector:"hover"}),Fe(Yt(t.rootHoverText),{pseudoSelector:"hover",innerSelector:Q.selector}),Fe(Yt(t.rootHoverIcon),{pseudoSelector:"hover",innerSelector:'[data-grab="button-icon"]'})]],linkFunctionalityDomAttributes:n,classNames:["dmWidget",t.buttonTypeCSSClass??"",i?"hover":"",s?"data-disabled":"",c||""],grabId:l||"button-root","data-disabled":s,...a,children:[o&&u.jsx(iO,{styles:[t.icon,s&&t.iconDisabled],"data-grab":"button-icon",className:`icon ${o}`}),u.jsx(Q,{styles:[f.text,uO,t.text,s&&t.disabledText],grabId:"button-text",domAttrs:{className:"text"},tag:ae.span,children:p})]})},aO={common:{maxWidth:"revert",whiteSpace:"revert",display:"inline-block"}},lO={common:{"&:after":{display:"none"},display:"flex",alignItems:"center",justifyContent:"center"}},sO={text:aO,root:lO},uO={common:{flexGrow:"1"}},cO=e=>{const t={...e.styles,root:[e.styles.root,{common:{display:"none"}}]};return u.jsxs(u.Fragment,{children:[u.jsx(Bc,{...e}),u.jsx(Bc,{...e,isDisabled:!e.isDisabled,styles:t})]})};function Bs(e){return et.isEditor?u.jsx(cO,{...e}):u.jsx(Bc,{...e})}const dO=({styles:e,isOpen:t,arrowType:n})=>{const r=a=>{switch(a){case"arrow":default:return u.jsx($i,{styles:[hO,e==null?void 0:e.itemArrowSvg,t?mO:{}],xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1152 1792",fill:"currentColor",children:u.jsx("path",{fill:"inherit",d:"M1075 736q0 13-10 23l-466 466q-10 10-23 10t-23-10l-466-466q-10-10-10-23t10-23l50-50q10-10 23-10t23 10l393 393 393-393q10-10 23-10t23 10l50 50q10 10 10 23z"})});case"leftArrow":return u.jsx($i,{styles:[gO,e==null?void 0:e.itemArrowSvg,t?vO:{}],xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 640 1792",children:u.jsx("path",{fill:"inherit",d:"M595 960q0 13-10 23l-466 466q-10 10-23 10t-23-10l-50-50q-10-10-10-23t10-23l393-393-393-393q-10-10-10-23t10-23l50-50q10-10 23-10t23 10l466 466q10 10 10 23z"})});case"plus":return u.jsxs(N,{children:[u.jsx(N,{styles:[yO,t&&_O]}),u.jsx(N,{styles:[xO,t?wO:{}]})]})}},o=i();return u.jsx(N,{styles:[fO,e==null?void 0:e.itemArrowWrapper,o],"data-grab":"accordion-item-arrow-wrapper",children:u.jsx(N,{styles:[pO,e==null?void 0:e.itemArrow],"data-grab":"accordion-item-arrow",children:r(n)})});function i(){var l,s,c,p;const a={[h.COMMON]:{},[h.DESKTOP]:{},[h.TABLET]:{},[h.MOBILE]:{}};if(e!=null&&e.itemArrowWrapper&&Array.isArray(e.itemArrowWrapper)&&e.itemArrowWrapper.length>0){const f=e.itemArrowWrapper[0];if(f&&"common"in f){const d=f.common;d&&"width"in d&&(a[h.COMMON]={height:((l=d==null?void 0:d.width)==null?void 0:l.toString())+"px"})}}if(e!=null&&e.itemArrowWrapper&&Array.isArray(e.itemArrowWrapper)&&e.itemArrowWrapper.length>1){const f=e.itemArrowWrapper[1];if(f&&"desktop"in f){const d=f.desktop;d&&"width"in d&&(a[h.DESKTOP]={height:(s=d==null?void 0:d.width)==null?void 0:s.toString()})}if(f&&"tablet"in f){const d=f.tablet;d&&"width"in d&&(a[h.TABLET]={height:(c=d==null?void 0:d.width)==null?void 0:c.toString()})}if(f&&"mobile"in f){const d=f.mobile;d&&"width"in d&&(a[h.MOBILE]={height:(p=d==null?void 0:d.width)==null?void 0:p.toString()})}}return a}},fO={[h.COMMON]:{aspectRatio:"1 / 1",width:20}},pO={[h.COMMON]:{width:"100%",height:"100%",transition:"transform 0.3s ease-in-out",display:"flex",flexDirection:"column",position:"relative"}},hO={[h.COMMON]:{transition:"all 0.5s ease-in-out",fill:"currentColor",height:"100%",width:"100%"}},mO={[h.COMMON]:{transform:"rotate(180deg)"}},gO={[h.COMMON]:{transition:"all 0.5s ease-in-out",fill:"currentColor",height:"100%",width:"100%"}},vO={[h.COMMON]:{transform:"rotate(90deg)"}},yO={[h.COMMON]:{transition:"all 0.5s ease-in-out",transform:"rotate(-90deg)",position:"absolute",backgroundColor:"currentColor",width:"10%",height:"100%",left:" 50%",marginLeft:"-5%",top:"50%",marginTop:"-50%"}},xO={[h.COMMON]:{transition:"all 0.5s ease-in-out",transform:"rotate(-90deg)",backgroundColor:"currentColor",position:"absolute",width:"100%",height:"10%",left:"50%",marginLeft:"-50%",top:"50%",marginTop:"-5%",opacity:"1"}},_O={[h.COMMON]:{transform:"rotate(90deg)"}},wO={[h.COMMON]:{transform:"rotate(90deg)",opacity:0}},m1=()=>{const[e,t]=C.useState(!1);return C.useEffect(()=>t(!0),[]),e},g1=({vidSrc:e,imgSrc:t,alt:n,type:r,styles:o,lazy:i,dataGrab:a,isHidden:l=!1})=>{const s=m1();return l&&!s?u.jsx(N,{styles:o,"data-grab":a}):r==="VIDEO"?u.jsx(XS,{"data-grab":a,src:e,poster:t,controls:!1,autoPlay:!0,loop:!0,muted:!0,playsInline:!0,styles:o}):u.jsx(Di,{"data-grab":a,src:t,alt:n,styles:o,loading:i?"lazy":void 0})},CO=({styles:e,...t})=>u.jsx(g1,{styles:[bO,e],dataGrab:"accordion-item-media",...t}),bO={[h.COMMON]:{width:123,borderRadius:0,borderStyle:"solid",maxWidth:"unset"},[h.MOBILE]:{maxWidth:"100%"}},v1=e=>{var E;const{onItemToggle:t,index:n,isOpen:r,styles:o,title:i,titleTag:a="h3",titleIcon:l='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1792 1792"> <path fill="inherit" d="M1671 566q0 40-28 68l-724 724-136 136q-28 28-68 28t-68-28l-136-136-362-362q-28-28-28-68t28-68l136-136q28-28 68-28t68 28l294 295 656-657q28-28 68-28t68 28l136 136q28 28 28 68z"></path></svg>',desc:s,button:c,media:p,linkDomAttributes:f,showButton:d,arrowDirection:y,arrowType:w,hasTitleIcons:x,disableTransition:O}=e,v=C.useRef(null),m=()=>{t==null||t(n)},g=eO(a||"h3"),_=u.jsx(N,{"data-grab":"accordion-item-media-container",styles:[IO,o==null?void 0:o.mediaContainer],children:p&&u.jsx(CO,{styles:o==null?void 0:o.media,...p})});let S={};return v.current&&(S=jO(O?3e5:v.current.scrollHeight)),u.jsxs(ZS,{styles:[SO,o==null?void 0:o.itemContainer],"data-grab":"accordion-item-container",children:[u.jsxs(N,{styles:[EO,y==="left"&&kO,o==null?void 0:o.itemTitleWrapper],onClick:m,tabIndex:0,onKeyDown:T=>{T.key==="Enter"&&m()},"data-grab":"accordion-item-title-wrapper",children:[i&&u.jsxs(Q,{grabId:"accordion-item-title",styles:[MO,o==null?void 0:o.itemTitle],tag:g,children:[x?u.jsx(Lt,{styles:[OO,o==null?void 0:o.itemTitleIcon],"data-grab":"accordion-item-title-icon",dangerouslySetInnerHTML:{__html:l}}):null,u.jsx(Lt,{styles:AO,children:i})]}),u.jsx(dO,{styles:o,isOpen:r,arrowType:w})]}),u.jsx(N,{children:u.jsx(N,{ref:v,styles:[TO,r?S:{}],children:u.jsxs(N,{styles:[NO,o==null?void 0:o.itemDesc],"data-grab":"accordion-item-desc",children:[p&&_,u.jsxs(N,{styles:[PO],children:[s&&u.jsx(Q,{grabId:"accordion-item-desc-text","data-auto":"desc",styles:[Fe(LO,{innerSelector:"p"}),o==null?void 0:o.desc],tag:ae.div,domAttrs:{dangerouslySetInnerHTML:{__html:s}}}),d&&c&&u.jsx(Bs,{styles:{...(o==null?void 0:o.button)??{},root:[RO,(E=o==null?void 0:o.button)==null?void 0:E.root]},linkFunctionalityDomAttributes:f,buttonContent:c})]})]})})})]})},SO={[h.COMMON]:{cursor:"pointer"}},OO={[h.COMMON]:{width:"20px",aspectRatio:"1 / 1",display:"flex","& svg":{fill:"currentColor"}}},EO={[h.COMMON]:{display:"flex",gap:8,alignItems:"center",justifyContent:"space-between",padding:"16px",paddingTop:16,paddingBottom:16}},kO={[h.COMMON]:{flexDirection:"row-reverse",justifyContent:"flex-end",gap:"12px"}},MO={[h.COMMON]:{margin:0,display:"flex",gap:"8px",alignItems:"center",flex:1,textAlign:"start"}},AO={[h.COMMON]:{flex:1}},TO={[h.COMMON]:{overflow:"hidden",transition:"max-height 0.3s ease-out",height:"auto",maxHeight:0}};function jO(e){return{[h.COMMON]:{maxHeight:e}}}const NO={[h.COMMON]:{padding:"16px",paddingTop:16,paddingBottom:16,margin:0,display:"flex",flexDirection:"row",justifyContent:"flex-start",gap:"24px",alignItems:"flex-start",flexWrap:"wrap"},[h.MOBILE]:{flexDirection:"column"}},PO={[h.COMMON]:{display:"flex",flexDirection:"column",gap:16,textAlign:"start",flex:1}},IO={[h.COMMON]:{}},LO={[h.COMMON]:{marginBlock:0,overflowWrap:"anywhere"}},RO={[h.COMMON]:{width:200,paddingInlineStart:20,paddingInlineEnd:20,margin:0}},DO={title:"Title or Question",desc:"Describe the item or answer the question so that site visitors who are interested get more information. You can emphasize this text with bullets, italics or bold, and add links.",button:{text:"Button"},showButton:!0,showMedia:!0,media:{lazy:!1,type:"IMAGE",imgSrc:"https://du-cdn.cdn-website.com/duda_website/images/home/<USER>"}},$O=({styles:e,arrowDirection:t,arrowType:n,hasTitleIcons:r})=>u.jsx(u.Fragment,{children:et.isEditor&&u.jsx(v1,{...DO,styles:{...e,itemContainer:[e==null?void 0:e.itemContainer,{[h.COMMON]:{display:"none"}}]},arrowDirection:t,arrowType:n,hasTitleIcons:r,index:0,isOpen:!0,disableTransition:!0})}),BO={LAYOUT_1:{arrowDirection:"right",hasTitleIcons:!1,styles:{container:{[h.COMMON]:{gap:24}},itemTitleWrapper:{[h.COMMON]:{background:"#f2f2f2"}},itemArrowWrapper:{[h.COMMON]:{borderRadius:"50%",width:32,padding:4,backgroundColor:"#000",color:"#e3e3e3"}}}},LAYOUT_2:{arrowDirection:"right",arrowType:"plus",hasTitleIcons:!1,styles:{container:{[h.COMMON]:{gap:0}},itemTitleWrapper:{[h.COMMON]:{borderBottom:"1px solid #e3e3e3"}},itemArrowWrapper:{[h.COMMON]:{padding:8,width:30}}}},LAYOUT_3:{arrowDirection:"right",arrowType:"arrow",hasTitleIcons:!1,styles:{container:{[h.COMMON]:{border:"solid 1px #e1e1e1",borderBottom:"none"}},itemTitleWrapper:{[h.COMMON]:{padding:"18px 20px",borderBottom:"solid 1px #e1e1e1"}},itemDesc:{[h.COMMON]:{borderBottom:"solid 1px #e1e1e1",background:"#f2f2f2"}}}},LAYOUT_4:{arrowDirection:"right",hasTitleIcons:!0,styles:{container:{[h.COMMON]:{gap:10}},itemTitleWrapper:{[h.COMMON]:{background:"#f2f2f2"}},itemArrowWrapper:{[h.COMMON]:{padding:8,width:40}}}},LAYOUT_5:{arrowDirection:"left",arrowType:"leftArrow",hasTitleIcons:!1,styles:{}}},zO=({_styles:e,layout:t="LAYOUT_1",itemsData:n,firstExpanded:r,closeOthers:o,openedIndex:i,addSchemaMarkup:a,titleTag:l="h3"})=>{var x,O,v,m,g,_,S,E,T;const[s,c]=C.useState(i!==void 0?[i]:r?[0]:[]);C.useEffect(()=>{c(i!==void 0?[i]:r?[0]:[])},[i,r]);const p=M=>{let B=[...s];const z=B.indexOf(M);z===-1?B.push(M):B.splice(z,1),o&&(B=B.filter(Z=>Z===M)),c(B)},f=BO[t],{styles:d}=f,y={container:[d==null?void 0:d.container,e==null?void 0:e.container],itemContainer:[d==null?void 0:d.itemContainer,e==null?void 0:e.itemContainer],itemTitleWrapper:[d==null?void 0:d.itemTitleWrapper,e==null?void 0:e.itemTitleWrapper],itemTitle:[d==null?void 0:d.itemTitle,e==null?void 0:e.itemTitle],itemTitleIcon:[d==null?void 0:d.itemTitleIcon,e==null?void 0:e.itemTitleIcon],itemArrowWrapper:[d==null?void 0:d.itemArrowWrapper,e==null?void 0:e.itemArrowWrapper],itemArrow:[d==null?void 0:d.itemArrow,e==null?void 0:e.itemArrow],itemArrowSvg:[d==null?void 0:d.itemArrowSvg,e==null?void 0:e.itemArrowSvg],itemDesc:[d==null?void 0:d.itemDesc,e==null?void 0:e.itemDesc],desc:[d==null?void 0:d.desc,e==null?void 0:e.desc],button:{root:[(x=d==null?void 0:d.button)==null?void 0:x.root,(O=e==null?void 0:e.button)==null?void 0:O.root],text:[(v=d==null?void 0:d.button)==null?void 0:v.text,(m=e==null?void 0:e.button)==null?void 0:m.text],rootHover:[(g=d==null?void 0:d.button)==null?void 0:g.rootHover,(_=e==null?void 0:e.button)==null?void 0:_.rootHover],rootHoverText:[(S=d==null?void 0:d.button)==null?void 0:S.rootHoverText,(E=e==null?void 0:e.button)==null?void 0:E.rootHoverText],buttonTypeCSSClass:(T=e==null?void 0:e.button)==null?void 0:T.buttonTypeCSSClass},mediaContainer:[d==null?void 0:d.mediaContainer,e==null?void 0:e.mediaContainer],media:[d==null?void 0:d.media,e==null?void 0:e.media]},w=C.useMemo(()=>{const M={"@context":"http://schema.org/","@type":"FAQPage",mainEntity:n.map(B=>({"@type":"Question",name:B.title,acceptedAnswer:{"@type":"Answer",text:B.desc}}))};return JSON.stringify(M)},[n]);return u.jsxs(N,{"data-auto":"runtime-accordion-widget",children:[a&&n.length>0?u.jsx("script",{"data-auto":"schema",type:"application/ld+json",dangerouslySetInnerHTML:{__html:w}}):null,u.jsx(JS,{styles:[FO,y==null?void 0:y.container],"data-grab":"accordion-container",children:n.length===0?u.jsx(gf,{}):n.map((M,B)=>C.createElement(v1,{...M,key:`item-${B}`,styles:y,arrowDirection:f.arrowDirection,arrowType:f.arrowType,hasTitleIcons:f.hasTitleIcons,onItemToggle:p,index:B,isOpen:s.includes(B),titleTag:l,disableTransition:i!==void 0}))}),u.jsx($O,{styles:y,arrowDirection:f.arrowDirection,arrowType:f.arrowType,hasTitleIcons:f.hasTitleIcons})]})},FO={[h.COMMON]:{listStyleType:"none",overflow:"hidden",margin:0,padding:0,display:"flex",flexDirection:"column",gap:0}},fa=()=>{const e=()=>{var r,o,i;return(i=(o=(r=globalThis.$)==null?void 0:r.DM)==null?void 0:o.insideEditor)==null?void 0:i.call(o)},t=()=>{var o,i;const r=(i=(o=globalThis.dmAPI)==null?void 0:o.getCurrentEnvironment)==null?void 0:i.call(o);return r==="preview"||r==="editor"};return{insideEditor:e,isInPreview:t,isInPreviewOrEditor:()=>e()||t()}},{isInPreviewOrEditor:UO}=fa();function WO(e){var n;if(!(window!=null&&window.runtime)||UO())return;const t=()=>{GO("view_item",e.price,HO(e))};(n=window.Snipcart)!=null&&n.store.getState().session.storeSettings.defaultCurrency?t():kb(()=>{var r;(r=window.Snipcart)==null||r.events.on(tf.SNIPCART_INITIALIZED,()=>{t()})})}function VO(e){try{const t=e.selected_options_values?JSON.parse(e.selected_options_values):{};if(Object.keys(t).length==0)return e.name;const n=`${Object.values(t).join(" ")}`;return`${e.name} ${n}`}catch(t){return je.error({message:"Error getting name from product",error:t}),e.name}}function HO(e){let t=VO(e);return{currency:window.Snipcart.store.getState().session.storeSettings.defaultCurrency.toUpperCase(),items:[{item_id:e.itemId,item_name:t,id:e.itemId,name:t,price:e.price,description:e.description}]}}function GO(e,t,n){var r,o,i;(i=window==null?void 0:window.runtime)==null||i.tagManagerAPI.sendGAEvent({category:e,action:e,value:t,siteAlias:(o=(r=Ie())==null?void 0:r.Parameters)==null?void 0:o.SiteAlias,payload:n})}const y1={dispatch:(e,t,n)=>{const r=Ie(),o=new CustomEvent(`${e}-${t}`,{detail:n});return r.document.dispatchEvent(o)},listen(e,t,n){return Ie().document.addEventListener(`${e}-${t}`,n),()=>Ie().document.removeEventListener(`${e}-${t}`,n)}},{str:KO}=de;class YO{constructor(t=[]){J(this,"list",[]);J(this,"values",{});J(this,"errors",{});J(this,"validationSchema",{});J(this,"updateValue",(t,n)=>{this.values[t]=n,this.errors[t]&&delete this.errors[t]});J(this,"validate",t=>{const n=this.values[t];for(const r of this.validationSchema[t]){const o=r(n);if(typeof o=="string"){this.errors[t]=o;return}}});J(this,"validateAll",()=>{this.list.forEach(t=>{this.validate(t.id)})});J(this,"isEmpty",()=>this.list.length===0);J(this,"isValid",()=>Object.keys(this.errors).length===0);ys(this,{validationSchema:!1,isEmpty:!1,isValid:!1}),this.validationSchema=XO(t),this.list=t}}const QO={required:e=>!!e||KO("ui.runtimessr.productCustomizations.errors.required")};function XO(e){return e.reduce((t,{id:n,mandatory:r})=>(t[n]||(t[n]=[]),r&&t[n].push(QO.required),t),{})}vs({isolateGlobalState:!0});const x1="defvar12";class qO{constructor(t){J(this,"product");J(this,"dropdownOptions",{});J(this,"paymentPlanOptions",[]);J(this,"variations",new Map);J(this,"selectedVariationKey","");J(this,"selectedPaymentPlan",null);J(this,"customizations");J(this,"getDiscountPrice",({price:t,displayed_price:n})=>this.selectedPaymentPlan?{price:this.selectedPaymentPlan.plan_price,displayed_price:this.selectedPaymentPlan.plan_displayed_price}:{price:t,displayed_price:n});J(this,"getSelectedVariationDiscountPrice",({price:t,displayed_price:n,plans_prices:r})=>{const o=r.find(i=>{var a;return i.id===((a=this.selectedPaymentPlan)==null?void 0:a.id)});return o?{price:o.price,displayed_price:o.displayed_price}:{price:t,displayed_price:n}});J(this,"selectPaymentPlanOption",t=>{this.selectedPaymentPlan=t});J(this,"updatePaymentPlanOptions",()=>{if(!this.selectedVariation)return;const t=this.selectedVariation;this.paymentPlanOptions=this.paymentPlanOptions.map(n=>{const r=t.plans_prices.find(o=>o.id===n.id);return{...n,plan_price:(r==null?void 0:r.price)||t.price,plan_displayed_price:(r==null?void 0:r.displayed_price)||t.displayed_price}})});ys(this,{selectedVariation:Ao,productData:Ao}),this.product=t,this.dropdownOptions=t.options,this.paymentPlanOptions=Mb(t),this.selectedPaymentPlan=this.paymentPlanOptions[0],this.customizations=new YO(t.customizations||[]),this.initVariations(t.variations),this.prepareAndSendGMEvent()}prepareAndSendGMEvent(){this.productData&&WO(this.productData)}initVariations(t=[]){var o,i;const n=!!t.find(a=>a.price!==this.product.price),r=((o=Object.keys(this.dropdownOptions))==null?void 0:o.length)===1&&n;if(t.forEach(a=>{const l=a.selected_options_values;if(l&&(this.variations.set(Eu(l),a),r)){const s=Object.entries(JSON.parse(l)),[c,p]=s[0],f=this.dropdownOptions[c].find(d=>d.value===p);f&&(f.label=`${f.label} - ${a.displayed_price}`)}}),!this.selectedVariationKey){const a=(i=t.find(l=>l.selected_options_values))==null?void 0:i.selected_options_values;if(a){const l=JSON.parse(a);this.selectedVariationKey=Eu(l)}}}getSelectedField(t){const n=z0(this.selectedVariationKey)[t];if(n&&this.dropdownOptions[t])return this.dropdownOptions[t].find(r=>r.value===n)}get selectedVariation(){return this.variations.get(this.selectedVariationKey)}get productData(){var r;const t=lo(this.selectedVariation),n=this.getItemId(t);if(t){const o=((r=t.images[0])==null?void 0:r.image)||this.product.image;return{...this.product,...t,...this.getSelectedVariationDiscountPrice(t),image:o,itemId:n,productId:this.product.identifier}}return{...this.product,...this.getDiscountPrice(this.product),itemId:n,productId:this.product.identifier}}updateVariation(t,n){var r;if((r=this.selectedVariation)!=null&&r.selected_options_values){const o=JSON.parse(this.selectedVariation.selected_options_values);this.selectedVariationKey=Eu({...o,[t]:n}),this.updatePaymentPlanOptions(),this.prepareAndSendGMEvent()}}onSelectedValuesChange(t){const n=this.product.images.reduce((o,i,a)=>({...o,[i.image]:a}),{});let r=this.productData.image;o0(()=>{const o=this.productData.image;if(o!==r){const i=n[o];y1.dispatch("selected-image-changed","dynamic_page_collection.images",{newIndex:i,newSrc:o}),r=o}t(this.productData)})}getItemId(t){const n=this.product.identifier||this.product.sku,r=this.product.external_id||"",{identifier:o=x1,external_id:i=""}=t||this.defaultVariation||{};return`${n}_${o}:${r}_${i}`}get defaultVariation(){return this.product.variations.find(t=>!t.selected_options_values)}}const ZO=({product:e,productState:t})=>{if(c1(e))return{elementAttributes:{},componentProps:{}};const{itemId:n,productId:r,price:o,internal_quantity:i}=e,{customizations:a}=t,s=(!!i||i===0)&&i<=0,c=JO(t.selectedPaymentPlan);return{elementAttributes:{"data-item-id":n,"data-item-price":o,"data-auto":"addToCart",className:"snipcart-add-item",onClick:async y=>{var x,O,v,m,g,_;if(et.isEditor||!window.dmAPI||!window._ecomSDK||s)return;const w=Rt.getBoolean("runtime.ssr.productCustomizations");if(y.stopPropagation(),a.validateAll(),!(!a.isValid()&&w))try{const S={product_id:r,variation_id:((x=t.selectedVariation)==null?void 0:x.identifier)||x1,quantity:1,product_customizations:w?eE(a):[]};c?await((m=(O=window._ecomSDK)==null?void 0:O.buyProduct)==null?void 0:m.call(O,{...S,plan_id:((v=t.selectedPaymentPlan)==null?void 0:v.id)||""})):await((_=(g=window._ecomSDK)==null?void 0:g.addProduct)==null?void 0:_.call(g,S))}catch(S){const E={message:`Error in window._ecomSDK?.${c?"buyProduct":"addProduct"}`,tags:["__new-runtime__","native-ecom",c?"subscribe-to-product":"add-items-to-cart"],errorMessage:S};je.error(E)}}},componentProps:{isDisabled:s,isSubscribe:c}}},JO=e=>!!e&&e.id!=Li,eE=e=>e.list.map(t=>({id:t.id,type:"TEXT",value:e.values[t.id]}));class tE{constructor(){J(this,"storeProducts",new Map);J(this,"currentProduct");ys(this)}getProductById(t){return this.storeProducts.get(t)}addProduct(t){var r;if(c1(t))return;const n=t.identifier;return n&&!this.storeProducts.has(n)&&(this.currentProduct=new qO(t),this.storeProducts.set(n,this.currentProduct),Rt.getBoolean("runtime.ssr.productStore.internal.observer",!1)&&((r=window==null?void 0:window.runtime)!=null&&r.updateConnectedProductWidgets)&&this.currentProduct.onSelectedValuesChange(window.runtime.updateConnectedProductWidgets)),this.storeProducts.get(n)}}globalThis.productsStore=globalThis.productsStore||new tE;const nE=globalThis.productsStore,pa=e=>function(t){const n=Ab(t.productView),r=nE.addProduct(n);if(!r)return null;const o=O0(e);return u.jsx(o,{...t,productState:r})},rE=()=>{const{insideEditor:e}=fa();return u.jsx("div",{"data-auto":"widgets-error-fallback",children:e()&&u.jsx("h4",{style:{fontSize:24},children:"Something went wrong. We are on it"})})};class oE extends Ce.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(t){const{additionalInfoToLog:n,componentName:r}=this.props,o={message:"Rendering error caught in error boundary. ",tags:["__new-runtime__","ErrorBoundary"],errorMessage:t==null?void 0:t.message,isEditor:et.isEditor,additionalInfoToLog:n,componentName:r};je.warn(o)}render(){return this.state.hasError?u.jsx(rE,{}):this.props.children}}function Ot({Comp:e,additionalInfoToLog:t,logProps:n,componentName:r}){return o=>{const i={...t};return r&&(i.componentName=r),n&&(i.childComponentProps=o),u.jsx(oE,{additionalInfoToLog:i,componentName:r,children:u.jsx(e,{...o})})}}const iE=D.div`
    cursor: auto;
    width: 100%;
    height: 100%;
`;function aE(e){const t=Ot({Comp:e,componentName:e.name||"withAddToCart",additionalInfoToLog:{tag:"native-ecom"},logProps:!0});return pa(n=>{const{productState:r,...o}=n,{productData:i}=r,{elementAttributes:a,componentProps:l}=ZO({product:i,productState:r});return i?u.jsx(iE,{...a,children:u.jsx(t,{...o,...l})}):u.jsx("div",{})})}function Bi(e){"@babel/helpers - typeof";return Bi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bi(e)}function lE(e,t){if(Bi(e)!=="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(Bi(r)!=="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function sE(e){var t=lE(e,"string");return Bi(t)==="symbol"?t:String(t)}function vn(e,t,n){return t=sE(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ur,Qo;function Mh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function uE(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Mh(Object(n),!0).forEach(function(r){vn(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mh(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var yt;(function(e){e.DEFAULT="default",e.DESKTOP="desktop",e.LARGE_DESKTOP="large_desktop",e.TABLET="tablet",e.MOBILE_PORTRAIT="mobile_portrait"})(yt||(yt={}));var cE=(ur={},vn(ur,yt.DEFAULT,{}),vn(ur,yt.LARGE_DESKTOP,{minWidth:1440}),vn(ur,yt.DESKTOP,{minWidth:768}),vn(ur,yt.TABLET,{minWidth:768,maxWidth:1024}),vn(ur,yt.MOBILE_PORTRAIT,{maxWidth:767}),ur),yo;(function(e){e.DESKTOP="new_desktop",e.TABLET="new_tablet",e.MOBILE="new_mobile"})(yo||(yo={}));var _1=uE((Qo={},vn(Qo,yo.MOBILE,{maxWidth:767}),vn(Qo,yo.TABLET,{maxWidth:1024,minWidth:768}),vn(Qo,yo.DESKTOP,{}),Qo),cE);const zs="#dm &&",dE=[...Object.values(yo),...Object.values(yt)];function Po(e={}){let t="";if(Object.keys(e).some(n=>dE.includes(n)))for(const[n,r]of Object.entries(e))t+=hE(n,r);else t=e;return t}function fE(e,t){const n=_1[e];return!n.maxWidth&&!n.minWidth?t:{[`@media ${Object.entries(n).map(([o,i])=>`(${w1(o,i)})`).join(" and ")}`]:t}}function pE(e={},t=!1){let n={};return Object.entries(e).forEach(([r,o])=>{const i=fE(r,o);Object.assign(n,i)}),t?{[zs]:n}:n}function w1(e,t){return`${e==="maxWidth"?"max-width":"min-width"}: ${t}px`}function hE(e,t){const n=_1[e],r=gE(t);return!n.maxWidth&&!n.minWidth?`${r}
`:`@media ${Object.entries(n).map(([i,a])=>`(${w1(i,a)})`).join(" and ")} {
	${r}}
`}function mE(e){return e.replace(/[A-Z]/g,t=>"-"+t.toLowerCase())}function gE(e){let t="";for(const[n,r]of Object.entries(e))t+=`	${mE(n)}: ${r};
`;return t}function vf(e={}){let t=e;if(vE(e)){const r={};for(const[o,i]of Object.entries(e))r[o]={default:i};t={rules:r}}return t}function vE(e){let t=!0;return["rules"].some(n=>n in e)?!1:t}const yE={[yt.DEFAULT]:h.COMMON,[yt.DESKTOP]:h.DESKTOP,[yt.MOBILE_PORTRAIT]:h.MOBILE,[yt.TABLET]:h.TABLET,[yt.LARGE_DESKTOP]:h.DESKTOP};function Ah(e,t={}){try{const n=e.rules||{};return Object.entries(n).reduce((r,[o,i])=>(Object.entries(yE).forEach(([a,l])=>{const s=t[o]||o,c=i[a]||i[l];c&&(r[s]={...r[s]||{},[l]:c})}),r),{})}catch(n){throw je.error({message:"Failed to migrate old BP styles structure",errorMessage:n==null?void 0:n.toString(),tags:["widget migration","style migration","old breakpoints to new"]}),n}}var Or=(e=>(e.NO_BP="no-breakpoints",e.OLD_BP="old-breakpoints",e.NEW_BP="new-styles",e))(Or||{});function C1({stylesInput:e,migrationStyleKeys:t,monitorKey:n}){var r,o;if(!e)return e;try{const{newStyles:i,inputStylesType:a}=xE({stylesInput:e,migrationStyleKeys:t});return n&&a!==Or.NEW_BP&&Rt.getBoolean(`runtime.ssr.widget.migration.${n}`,!1)&&je.info({widget:n,key:"breakpoints style migration",type:a,stylesInput:e,newStyles:i,tags:["style-migration"],siteAlias:(o=(r=Ie())==null?void 0:r.Parameters)==null?void 0:o.SiteAlias}),i}catch(i){je.error({message:"Failed to migrate old styles structure",errorMessage:i==null?void 0:i.toString(),tags:["style-migration","style-migration-error",n]})}}function xE({stylesInput:e={},migrationStyleKeys:t}){const n=_E(e);let r;switch(n){case Or.NO_BP:r=Ah(vf(e)||{},t);break;case Or.OLD_BP:r=Ah(e||{},t);break;default:r=e;break}return{newStyles:r,inputStylesType:n}}function _E(e){if(e&&typeof e=="object"){if("rules"in e)return Or.OLD_BP;const t=Object.values(e);return t.length===1&&e.buttonTypeCSSClass||t.length===0||t.some(n=>n[h.COMMON]||Object.values(n||{}).some(r=>r==null?void 0:r[h.COMMON]))?Or.NEW_BP:Or.NO_BP}return null}const wE={button:"root",buttonHover:"rootHover",disabledButton:"rootDisabled",textHover:"rootHoverText",iconHover:"rootHoverIcon",disabledIcon:"iconDisabled"};function CE(e){try{return C1({monitorKey:"addtocart",stylesInput:e,migrationStyleKeys:wE})}catch(t){throw je.error({message:"Failed to migrate old styles structure",errorMessage:t==null?void 0:t.toString(),tags:["AddToCart style migration"]}),t}}function bE(e){var t;return e?"show-icon"in e?{showIcon:e["show-icon"],iconName:(t=e["selected-icon"])==null?void 0:t.classname}:{showIcon:e.showIcon,iconName:e.iconName}:{showIcon:!1,iconName:""}}function SE(){return u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"10",height:"5",viewBox:"0 0 10 5",fill:"none",children:u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.005 8.66907e-07L9.94934 5L0.0468741 5L5.005 8.66907e-07Z",fill:"#313131"})})}const OE=({children:e,text:t})=>u.jsxs(Lt,{"data-auto":"tooltip-container",styles:[EE,Fe(Yt(TE),{pseudoSelector:"hover",innerSelector:'[data-auto="tooltip-content"]'})],children:[e,u.jsxs(N,{styles:kE,"data-auto":"tooltip-content",children:[u.jsx(SE,{}),u.jsx(N,{styles:AE,"data-auto":"tooltip-content-box",children:u.jsx(Q,{styles:ME,grabId:"tooltip-content-text",children:t})})]})]}),EE={common:{height:"100%"}},kE={common:{visibility:"hidden",marginLeft:"25%",marginRight:"25%",display:"inline-flex",flexDirection:"column",justifyContent:"center",alignItems:"center",position:"relative",zIndex:"100"}},ME={common:{fontSize:"12px",fontStyle:"normal",fontWeight:"550",lineHeight:"18px",color:"#fff",flex:"1 0 0"}},AE={common:{display:"flex",maxWidth:"200px",alignItems:"flex-start",gap:"4px",padding:"10px 16px",borderRadius:"4px",background:"#313131"}},TE={common:{visibility:"visible"}},jE=({text:e,disabledText:t,_styles:n,iconProps:r,isDisabled:o,showButtonHoverEffect:i,showButtonDisabledEffect:a,isSubscribe:l})=>{const{showIcon:s,iconName:c}=bE(r),p=NE(n),{isInPreview:f}=fa(),d=(o?t:l?de.str("ui.runtimessr.addtocart.subscribe.title"):e)||de.str("widget.addtocart.title"),y=()=>u.jsx(Bs,{styles:p,buttonContent:{text:d},dataGrab:"add-to-cart-button",iconClass:s&&c,isDisabled:a||o,className:"ssr-button",showHoverEffect:i});return l&&f()?u.jsx(OE,{text:de.str("ui.runtimessr.addtocart.subscribe.tooltip.text"),children:u.jsx(y,{})}):u.jsx(y,{})};function NE(e){const t=CE(e);return{root:[PE,t==null?void 0:t.root],rootHover:t==null?void 0:t.rootHover,rootDisabled:[IE,t==null?void 0:t.rootDisabled],text:t==null?void 0:t.text,rootHoverText:t==null?void 0:t.rootHoverText,disabledText:t==null?void 0:t.disabledText,icon:t==null?void 0:t.icon,rootHoverIcon:t==null?void 0:t.rootHoverIcon,iconDisabled:t==null?void 0:t.iconDisabled,buttonTypeCSSClass:t==null?void 0:t.buttonTypeCSSClass}}const PE={[h.COMMON]:{display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",minHeight:"32px",padding:"8px 14px",minWidth:"fit-content",textAlign:"center",boxSizing:"border-box",margin:0}},IE={[h.COMMON]:{opacity:.6}},LE=aE(jE),zi=D.button(({styles:e})=>Tn([RE,e],!0)),RE={[h.COMMON]:{padding:"unset",backgroundColor:"unset",border:"unset",cursor:"pointer"}},zc=({name:e,dataGrab:t,styles:n})=>u.jsx(N,{styles:n==null?void 0:n.wrapper,children:u.jsx(N,{styles:[n==null?void 0:n.icon],"data-grab":t,className:e,"aria-hidden":"true"})}),Th=({styles:e,arrowStyle:t,goNext:n,goPrevious:r})=>u.jsxs(N,{styles:[DE,e==null?void 0:e.container],"data-auto":"pagination-arrows","data-grab":"pagination-container",children:[u.jsx(zi,{styles:[Ih,e==null?void 0:e.buttonArrow],"data-grab":"pagination-button-arrow",onClick:r,"aria-label":"back","data-auto":jh("back"),children:u.jsx(zc,{styles:{wrapper:Nh},...Ph({pos:"left",arrowStyle:t})})}),u.jsx(zi,{styles:[Ih,e==null?void 0:e.buttonArrow],"data-grab":"pagination-button-arrow","aria-label":"next","data-auto":jh("next"),onClick:n,children:u.jsx(zc,{styles:{wrapper:Nh},...Ph({pos:"right",arrowStyle:t})})})]});function jh(e){return`RuntimeSlider-navigation-${e}`}const Nh={common:{lineHeight:1,'[class*=" icon-"]::before':{cursor:"pointer"}}};function Ph({arrowStyle:e,pos:t}){return e==="arrow_thin"?{name:t==="left"?"icon-angle-left":"icon-angle-right"}:e==="arrow_double"?{name:t==="left"?"icon-double-angle-left":"icon-double-angle-right"}:{name:t==="left"?"icon-chevron-left":"icon-chevron-right"}}const DE={[h.COMMON]:{display:"flex",alignItems:"center",justifyContent:"space-between",pointerEvents:"none"}},Ih={[h.COMMON]:{backgroundColor:"transparent",pointerEvents:"auto",fontSize:40,padding:32},[h.MOBILE]:{padding:20}},b1=({goToCursor:e,styles:t,dataGrab:n="pagination-container",bulletsList:r})=>u.jsx(N,{styles:[$E,t==null?void 0:t.container],"data-auto":"pagination-bullets-base-container","data-grab":n,children:r.map(({styles:o,domAttributes:i={}},a)=>{const{"data-grab":l="pagination-button-bullet",...s}=i;return C.createElement(zi,{"data-grab":l,...s,styles:[BE,t==null?void 0:t.buttonBullet,o],key:a,onClick:()=>{e(a)},"aria-label":`go to slide ${a+1}`})})}),$E={[h.COMMON]:{display:"flex",justifyContent:"center",alignItems:"center"}},BE={[h.COMMON]:{backgroundColor:"transparent"}},zE=({cursor:e,totalPages:t,styles:n,...r})=>u.jsx(b1,{bulletsList:new Array(t).fill(null).map((o,i)=>{const a=i===e?" active":"";return{styles:[e===i?n==null?void 0:n.buttonBulletActive:void 0],domAttributes:{"data-auto":`pagination-button-bullet ${i}${a}`,"data-grab":`pagination-button-bullet${a}`}}}),styles:{container:[UE,n==null?void 0:n.container],buttonBullet:[FE,n==null?void 0:n.buttonBullet]},...r}),FE={[h.COMMON]:{borderRadius:"50%",width:8,height:8,boxShadow:"0px 0px 2px rgba(24, 39, 75, 0.3), 0px 0px 3px rgba(24, 39, 75, 0.04)"}},UE={[h.COMMON]:{gap:8}},Lh=({thumbList:e,styles:t,cursor:n,...r})=>u.jsx(N,{styles:t==null?void 0:t.container,children:u.jsx(b1,{bulletsList:e.map((o,i)=>{const a=i===n?" active":"";return{styles:[VE(o==null?void 0:o.imgSrc),i===n?[KE,t==null?void 0:t.buttonThumbActive]:void 0],domAttributes:{"data-auto":`pagination-button-thumb ${i}${a}`,"data-grab":`pagination-button-thumb${a}`}}}),dataGrab:"pagination-container thumbs-container",...r,styles:{buttonBullet:[GE,t==null?void 0:t.buttonThumb],container:[WE,t==null?void 0:t.thumbsContainer,e.length>3?void 0:HE]}})}),WE={[h.COMMON]:{gap:20}};function VE(e){return{[h.COMMON]:{backgroundImage:`url(${e})`}}}const HE={[h.MOBILE]:{justifyContent:"center"}},GE={[h.COMMON]:{aspectRatio:"1 / 1",pointerEvents:"auto",backgroundSize:"cover",backgroundPosition:"center",borderWidth:3,borderColor:"transparent",borderStyle:"solid"}},KE={[h.COMMON]:{borderColor:"black"}};var Wt=(e=>(e.BULLETS="bullets",e.THUMBS="thumbs",e.ARROWS="arrows",e.ARROWS_AND_THUMBS="arrows_and_thumbs",e))(Wt||{});const YE=e=>{switch(e.type){case"arrows":return u.jsx(Th,{...e});case"thumbs":return u.jsx(Lh,{...e});case"arrows_and_thumbs":return u.jsxs(u.Fragment,{children:[u.jsx(Th,{...e}),u.jsx(Lh,{...e})]});default:return u.jsx(zE,{...e})}},QE=Ot({Comp:YE,logProps:!0,componentName:"Pagination"});var S1=(e=>(e.slideFromRight="slideFromRight",e.slideFromLeft="slideFromLeft",e.slideFromTop="slideFromTop",e.slideFromBottom="slideFromBottom",e.fade="fade",e))(S1||{});D.div`
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
`;const XE={slideFromBottom:"top",slideFromTop:"bottom",slideFromRight:"left",slideFromLeft:"right",fade:"opacity"},qE=({distanceFromIndex:e,transitionDuration:t,transitionType:n})=>{const r=XE[n||"slideFromRight"];switch(n){case"fade":return{left:0,opacity:`${e===0?1:0}`,transition:`${r} ${t}s`};default:return{[r]:`${e>0?"100%":e===0?"0":"-100%"}`,transition:`${r} ${t}s`}}};D.div`
    width: 100%;
    height: 100%;
    bottom: 0;
    position: absolute;
    ${e=>({...qE(e)})}
`;const ZE={href:"#"},JE=D.a`
    ${zs} {
        ${e=>Po(e==null?void 0:e.styles)}
        &:hover {
            ${e=>Po(e==null?void 0:e.hoverStyles)}
        }
    }
`,ek=(e,t=ZE)=>n=>u.jsx(JE,{...t,children:u.jsx(e,{...n})}),tk=D.div(e=>pE(e.styles)),Au=({tag:e,domAttrs:t,...n})=>u.jsx(tk,{as:e,...t,...n}),Wa={links:[],ariaLabel:"Breadcrumbs",emptyMessage:"No breadcrumbs"},nk=D.nav`
    ${zs} {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        gap: 10px;
        ${e=>Po(e.styles)}
    }
`,rk=D.span`
    ${zs} {
        ${e=>Po(e==null?void 0:e.styles)}
        transform: ${e=>(e==null?void 0:e.direction)==="rtl"?"rotate(180deg)":"rotate(0)"};
        display: flex;
        width: 16px;

        svg {
            width: 100%;
            height: 100%;
            fill: currentColor;
        }
    }
`,ok=e=>{switch(e){case"arrow":return u.jsx("svg",{"data-auto":"arrow",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 3.46967C5.76256 3.17678 6.23744 3.17678 6.53033 3.46967L10.5303 7.46967C10.8232 7.76256 10.8232 8.23744 10.5303 8.53033L6.53033 12.5303C6.23744 12.8232 5.76256 12.8232 5.46967 12.5303C5.17678 12.2374 5.17678 11.7626 5.46967 11.4697L8.93934 8L5.46967 4.53033C5.17678 4.23744 5.17678 3.76256 5.46967 3.46967Z",fill:"currentColor"})});case"double_arrow":return u.jsxs("svg",{"data-auto":"double_arrow",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.21967 3.46967C3.51256 3.17678 3.98744 3.17678 4.28033 3.46967L8.28033 7.46967C8.57322 7.76256 8.57322 8.23744 8.28033 8.53033L4.28033 12.5303C3.98744 12.8232 3.51256 12.8232 3.21967 12.5303C2.92678 12.2374 2.92678 11.7626 3.21967 11.4697L6.68934 8L3.21967 4.53033C2.92678 4.23744 2.92678 3.76256 3.21967 3.46967Z",fill:"currentColor"}),u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.71967 3.46967C8.01256 3.17678 8.48744 3.17678 8.78033 3.46967L12.7803 7.46967C13.0732 7.76256 13.0732 8.23744 12.7803 8.53033L8.78033 12.5303C8.48744 12.8232 8.01256 12.8232 7.71967 12.5303C7.42678 12.2374 7.42678 11.7626 7.71967 11.4697L11.1893 8L7.71967 4.53033C7.42678 4.23744 7.42678 3.76256 7.71967 3.46967Z",fill:"currentColor"})]});case"divider":return u.jsx("svg",{"data-auto":"divider",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 2.58333C8.41421 2.58333 8.75 2.91912 8.75 3.33333V12.6667C8.75 13.0809 8.41421 13.4167 8 13.4167C7.58579 13.4167 7.25 13.0809 7.25 12.6667V3.33333C7.25 2.91912 7.58579 2.58333 8 2.58333Z",fill:"currentColor"})});case"slash":return u.jsx("svg",{"data-auto":"slash",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.59807 13.2321C6.19797 13.1249 5.96053 12.7136 6.06774 12.3135L8.48338 3.29821C8.59059 2.89811 9.00184 2.66068 9.40194 2.76788C9.80204 2.87509 10.0395 3.28634 9.93227 3.68644L7.51662 12.7017C7.40942 13.1018 6.99817 13.3393 6.59807 13.2321Z",fill:"currentColor"})});case"horizontal":return u.jsx("svg",{"data-auto":"horizontal",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.4167 7.99998C13.4167 8.41419 13.0809 8.74998 12.6667 8.74998L3.33333 8.74998C2.91912 8.74998 2.58333 8.41419 2.58333 7.99998C2.58333 7.58576 2.91912 7.24998 3.33333 7.24998L12.6667 7.24998C13.0809 7.24998 13.4167 7.58576 13.4167 7.99998Z",fill:"currentColor"})})}},ik=(e,t,n,r)=>n?u.jsx(rk,{styles:e,direction:t,className:"bc-separator","aria-hidden":"true",children:ok(n)},r):null,ak=(e,t)=>e?u.jsx("span",{style:{color:"#C1C9CC",padding:"0 4px"},children:t}):null,lk=({links:e=Wa.links,styles:t,linksStyles:n,linksHoverStyles:r,separatorStyles:o,unlinkableItemsStyles:i,currentItemStyles:a,separatorType:l,ariaLabel:s=Wa.ariaLabel,showEmptyMessage:c,emptyMessage:p=Wa.emptyMessage}=Wa)=>u.jsx(nk,{links:e,styles:t,className:"bc-root dmBlockElement","aria-label":s,children:e.length?e.map((f,d)=>{var g,_,S,E,T;const w=ek(()=>u.jsx(Au,{children:f.title},`text-${d}`),{...f.linkProps,styles:n,hoverStyles:r,className:"bc-item"}),x=(g=f==null?void 0:f.linkProps)!=null&&g.currentPage?u.jsx(Au,{styles:a,domAttrs:{className:"bc-current-item"},"data-auto":"bc-current-item",children:f.title},`current-${d}`):null,O=((_=f==null?void 0:f.linkProps)==null?void 0:_.href)=="#"?u.jsx(Au,{styles:i,domAttrs:{className:"bc-unlinkable-item"},"data-auto":"bc-unlinkable-item",children:f.title},`unlinkable-${d}`):null,v=((S=f==null?void 0:f.linkProps)==null?void 0:S.href)!="#"&&!((E=f==null?void 0:f.linkProps)!=null&&E.currentPage)?u.jsx(w,{},`Linkable-${d}`):null,m=d<e.length-1?ik({...o},((T=t==null?void 0:t.default)==null?void 0:T.direction)||"ltr",l,`separator-${d}`):null;return[v,O,x,m]}):ak(c,p)});function sk(){return u.jsx("svg",{width:"14",height:"7",viewBox:"0 0 15 9",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{d:"M0.604736 1L7.60474 8L14.6047 1",stroke:"currentColor"})})}function Rh({styles:e,size:t=15,onClick:n,dataGrab:r}){return u.jsx(Lt,{styles:e,"data-grab":r,onClick:n,children:u.jsxs($i,{width:t,height:t,viewBox:"0 0 16 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[u.jsx("path",{d:"M0.604736 0.5L14.6047 14.5",stroke:"currentColor",strokeWidth:"1.33333"}),u.jsx("path",{d:"M14.6047 0.5L0.604736 14.5",stroke:"currentColor",strokeWidth:"1.33333"})]})})}function uk({size:e}){return u.jsx(N,{styles:ck,"data-auto":"warning-icon",children:u.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:`0 0 ${e} ${e}`,fill:"none",children:[u.jsxs("g",{clipPath:"url(#clip0_2202_45766)",children:[u.jsx("path",{d:"M5.00019 20.0005H19.0002C19.3265 19.9982 19.6473 19.9161 19.9346 19.7614C20.2219 19.6066 20.4669 19.3839 20.6484 19.1127C20.8299 18.8415 20.9422 18.53 20.9756 18.2054C21.0091 17.8809 20.9626 17.553 20.8402 17.2505L13.7402 5.0005C13.5672 4.6879 13.3137 4.42733 13.0059 4.24589C12.6982 4.06445 12.3474 3.96875 11.9902 3.96875C11.6329 3.96875 11.2822 4.06445 10.9744 4.24589C10.6667 4.42733 10.4131 4.6879 10.2402 5.0005L3.14019 17.2505C3.02013 17.5461 2.97252 17.8661 3.00134 18.1838C3.03016 18.5015 3.13456 18.8078 3.30584 19.0769C3.47712 19.3461 3.7103 19.5703 3.98592 19.731C4.26153 19.8917 4.57158 19.9841 4.89019 20.0005",fill:"currentColor"}),u.jsx("path",{d:"M11.9902 9L11.9902 13",stroke:"white",strokeWidth:"1.8",strokeLinecap:"round",strokeLinejoin:"round"}),u.jsx("path",{d:"M11.9902 17L12.0002 17",stroke:"white",strokeWidth:"1.8",strokeLinecap:"round",strokeLinejoin:"round"})]}),u.jsx("defs",{children:u.jsx("clipPath",{id:"clip0_2202_45766",children:u.jsx("rect",{width:e,height:e,fill:"white"})})})]})})}const ck={[h.COMMON]:{position:"absolute",left:"-12px",top:"-12px",color:"#f4a622"}},dk=({size:e,...t})=>u.jsx($i,{xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:`0 0 ${e} ${e}`,fill:"none",...t,children:u.jsx("path",{d:"M7.99563 12C8.20688 12 8.38542 11.9285 8.53125 11.7856C8.67708 11.6427 8.75 11.4656 8.75 11.2544C8.75 11.0431 8.67854 10.8646 8.53563 10.7188C8.39271 10.5729 8.21562 10.5 8.00438 10.5C7.79313 10.5 7.61458 10.5715 7.46875 10.7144C7.32292 10.8573 7.25 11.0344 7.25 11.2456C7.25 11.4569 7.32146 11.6354 7.46438 11.7812C7.60729 11.9271 7.78438 12 7.99563 12ZM7.25 9H8.75V4H7.25V9ZM8.00583 16C6.90472 16 5.86806 15.7917 4.89583 15.375C3.92361 14.9583 3.07292 14.3854 2.34375 13.6562C1.61458 12.9271 1.04167 12.0767 0.625 11.105C0.208333 10.1333 0 9.09514 0 7.99042C0 6.88569 0.208333 5.85069 0.625 4.88542C1.04167 3.92014 1.61458 3.07292 2.34375 2.34375C3.07292 1.61458 3.92333 1.04167 4.895 0.625C5.86667 0.208333 6.90486 0 8.00958 0C9.11431 0 10.1493 0.208333 11.1146 0.625C12.0799 1.04167 12.9271 1.61458 13.6562 2.34375C14.3854 3.07292 14.9583 3.92167 15.375 4.89C15.7917 5.85847 16 6.89319 16 7.99417C16 9.09528 15.7917 10.1319 15.375 11.1042C14.9583 12.0764 14.3854 12.9271 13.6562 13.6562C12.9271 14.3854 12.0783 14.9583 11.11 15.375C10.1415 15.7917 9.10681 16 8.00583 16Z",fill:"currentColor"})});function fk({title:e,children:t,styles:n,isAccordionOpen:r=!1,dataGrabs:o,...i}){const[a,l]=C.useState(r);C.useEffect(()=>{l(r)},[r]);const s=()=>{l(c=>!c)};return u.jsxs(N,{styles:[hk,n==null?void 0:n.container],...i,children:[u.jsxs(N,{styles:[pk,n==null?void 0:n.headerWrapper],onClick:s,"data-auto":"accordion-trigger","data-grab":o==null?void 0:o.textWrapperDataGrab,children:[u.jsx(Q,{styles:[mk,n==null?void 0:n.title],"data-grab":o==null?void 0:o.titleDataGrab,tag:ae.h4,children:e}),u.jsx(Q,{styles:[vk(a),n==null?void 0:n.title],tag:ae.h4,children:u.jsx(sk,{})})]}),u.jsx(N,{styles:gk,style:{display:a?"flex":"none"},"data-auto":"accordion-list",children:t})]})}const pk={[h.COMMON]:{width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between",cursor:"pointer",textTransform:"uppercase",fontWeight:400,fontSize:"16px",lineHeight:"19px",gap:"8px",lineBreak:"anywhere"}},hk={[h.COMMON]:{width:"100%"}},mk={[h.COMMON]:{maxWidth:"90%",cursor:"pointer",flex:1,textDecoration:"inherit",margin:0}},gk={[h.COMMON]:{marginBlockStart:"16px",flexDirection:"column",gap:"8px",display:"flex"}};function vk(e){return{[h.COMMON]:{display:"grid",alignItems:"center",transform:`rotate(${e?"0":"180deg"})`,aspectRatio:"1/1",margin:0}}}const Fs=D.input(({styles:e,increaseSpecificity:t})=>Tn([yk,e],t)),yk={[h.COMMON]:{padding:"unset",backgroundColor:"unset",border:"0 solid #000000"}};function xk({label:e,checked:t,onChange:n,labelWrapperStyles:r,labelStyles:o,inputStyles:i,inputIconStyles:a,dataGrabs:l,...s}){return u.jsxs(mf,{styles:[_k,r],...s,children:[u.jsx(Fs,{type:"checkbox",checked:t,onChange:c=>{c.stopPropagation(),n(c.target.checked)},styles:{[h.COMMON]:{display:"none"}}}),u.jsx(N,{styles:[wk,i],"data-grab":l==null?void 0:l.iconWrapperDataGrab,children:u.jsx($i,{width:"8",height:"7",viewBox:"0 0 8 7",fill:"none",xmlns:"http://www.w3.org/2000/svg","data-grab":(l==null?void 0:l.iconDataGrab)||"input-icon",styles:[a,{[h.COMMON]:{display:t?"unset":"none",background:"none"}}],children:u.jsx("path",{d:"M0.760254 3.57145L2.6191 5.5L6.47454 1.5",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),u.jsx(Q,{styles:[Ck,o],"data-grab":l==null?void 0:l.labelDataGrab,tag:ae.paragraph,children:e})]})}const _k={[h.COMMON]:{position:"relative",display:"flex",alignItems:"center",gap:"8px",cursor:"pointer",color:"#313131"}},wk={[h.COMMON]:{display:"grid",alignItems:"center",justifyContent:"center",width:"14px",height:"14px",backgroundColor:"#fff",border:"1px solid #ced6d9"}},Ck={[h.COMMON]:{textTransform:"capitalize",flex:1,margin:0,display:"grid",alignItems:"center"}},yf=({label:e,labelTag:t=ae.paragraph,onChange:n,value:r,selectedValue:o,labelStyles:i,labelWrapperStyles:a,inputStyles:l,inputIconStyles:s,dataGrabs:c,...p})=>{const f=y=>{n(y.target.value)},d=o===r;return u.jsxs(mf,{styles:[bk,a],...p,children:[u.jsx(Fs,{type:"radio",onChange:f,value:r,checked:d,styles:{[h.COMMON]:{display:"none"}}}),u.jsx(Lt,{"data-grab":(c==null?void 0:c.outerCircleDataGrab)||"radio-outer-circle",styles:[Ok,l],children:u.jsx(Lt,{"data-grab":(c==null?void 0:c.innerCircleDataGrab)||"radio-inner-circle",styles:[Ek(d),s]})}),u.jsx(Q,{styles:[Sk,i],tag:t,"data-grab":(c==null?void 0:c.labelDataGrab)||"radio-input-label",children:e||r})]})},bk={[h.COMMON]:{display:"flex",alignItems:"center",cursor:"pointer",gap:"8px",color:"#313131"}},Sk={[h.COMMON]:{textTransform:"capitalize",flex:1,margin:0,display:"grid",alignItems:"center"}},Ok={[h.COMMON]:{display:"flex",alignItems:"center",justifyContent:"center",width:"18px",height:"18px",border:"1px solid #ced6d9",borderRadius:"50%",boxSizing:"border-box",backgroundColor:"#fff",flexShrink:0}};function Ek(e){return{[h.COMMON]:{display:"block",opacity:e?1:0,width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"#30373a",flexShrink:0}}}const kk=({title:e,options:t,selectedValue:n,onChange:r,styles:o,...i})=>{var a;return u.jsxs(N,{"data-auto":"radio-buttons-group",children:[u.jsx(Q,{tag:ae.h4,styles:[Tk,o==null?void 0:o.title],"data-grab":"radiogroup-title",children:e}),u.jsx(N,{styles:[Mk,(a=o==null?void 0:o.fields)==null?void 0:a.wrapper],"data-grab":"radiogroup-fields",children:t.map(l=>{var s,c,p;return u.jsx(yf,{label:l.label,value:l.value,disabled:l.disabled,selectedValue:n==null?void 0:n.value,onChange:()=>r(l),labelWrapperStyles:Ak,inputStyles:(s=o==null?void 0:o.fields)==null?void 0:s.outerCircle,inputIconStyles:(c=o==null?void 0:o.fields)==null?void 0:c.innerCircle,labelStyles:(p=o==null?void 0:o.fields)==null?void 0:p.label,dataGrabs:{labelDataGrab:"radio-label"},...i},l.value)})})]})},Mk={[h.COMMON]:{display:"flex",flexDirection:"column",gap:"4px",flexWrap:"wrap"}},Ak={[h.COMMON]:{color:"inherit"}},Tk={[h.COMMON]:{textAlign:"start",marginBlockEnd:"6px",lineHeight:"100%",marginTop:"0",fontSize:"16px",direction:"inherit"}};function jk(e){return u.jsxs(N,{styles:[Nk,e.labelsWrapperStyles],children:[u.jsxs(Q,{styles:[Dh,e.labelStyles],"data-auto":"min-input-label",tag:ae.paragraph,children:[u.jsx("span",{children:e.rangeValues.start}),e.sign&&u.jsx("span",{children:e.sign})]}),u.jsxs(Q,{styles:[Dh,e.labelStyles],"data-auto":"max-input-label",tag:ae.paragraph,children:[u.jsx("span",{children:e.rangeValues.end}),e.sign&&u.jsx("span",{children:e.sign})]})]})}const Dh={[h.COMMON]:{margin:0}},Nk={[h.COMMON]:{width:"100%",display:"flex",justifyContent:"space-between",marginBlockEnd:"12px"}},Pk=C.forwardRef(({trackStyles:e,trackDataGrab:t="range-slider-track",rangeValues:n,...r},o)=>{const i=[Dk,e];return u.jsxs(N,{"data-auto":"slider-track-wrapper",styles:Lk,ref:o,children:[u.jsx(N,{styles:[Rk,i],"data-grab":t}),!r.hideRangeTrack&&u.jsx(N,{styles:[Ik({...n,min:r.min,max:r.max}),i]})]})});function Ik({start:e,end:t,min:n,max:r}){const o=r-n,i=(e-n)/o*100,a=(t-e)/o*100;return{[h.COMMON]:{height:"100%",position:"absolute",borderRadius:"24px",marginInlineStart:`${i}%`,width:`${a}%`,top:0}}}const Lk={[h.COMMON]:{backgroundColor:"transparent",position:"relative",width:"100%"}},Rk={[h.COMMON]:{opacity:.5,width:"100%"}},Dk={[h.COMMON]:{height:"2px",backgroundColor:"#000",borderRadius:"6px"}};function $k(e){const t=Yt([zk,e.trackStyles,e.thumbStyles,e.isActive&&$h,Fk]),n=[Bk,e.isActive&&$h,Fe({[h.COMMON]:{height:"100%"}},{innerSelector:"&::-webkit-slider-runnable-track"}),Fe(t,{innerSelector:"&&::-webkit-slider-thumb"}),Fe(t,{innerSelector:"&::-moz-range-thumb"})];return u.jsx(Fs,{type:"range",styles:n,...e})}const Bk={[h.COMMON]:{margin:0,height:"200%",width:"100%",position:"absolute",WebkitAppearance:"none",MozAppearance:"none",pointerEvents:"none",cursor:"pointer",minHeight:"8px"},[h.MOBILE]:{minHeight:"20px"}},zk={[h.COMMON]:{position:"relative",WebkitAppearance:"none",backgroundColor:"#000",pointerEvents:"all",borderRadius:"50%",aspectRatio:"1/1",width:"unset",border:"0",boxShadow:"0 0 1px 1px rgba(0,0,0,0.4)",zIndex:2}},$h={[h.COMMON]:{zIndex:3,pointerEvents:"all"}},Fk={[h.COMMON]:{height:"100%"},[h.TABLET]:{height:"100%"},[h.DESKTOP]:{height:"100%"},[h.MOBILE]:{height:"100%"}},Bh=C.memo($k);var Fi=(e=>(e.START="start",e.END="end",e))(Fi||{});const Uk=({callback:e,msToWait:t=300})=>{const n=C.useRef(e);return C.useEffect(()=>{n.current=e},[e]),C.useCallback(hf((...r)=>{n.current(...r)},t),[])};function Wk({rangeValues:e,minRange:t=1,...n}){const[r,o]=C.useState(null),[i,a]=C.useState(null),[l,s]=C.useState(!1),[c,p]=C.useState("ltr"),f=C.useCallback(g=>{if(g){a(g==null?void 0:g.getBoundingClientRect());const _=Ie().getComputedStyle(g);p(_.getPropertyValue("direction"))}},[]),d=C.useCallback(g=>{if(i){const{width:_,left:S,right:E}=i,T=c==="rtl"?E-g.clientX:g.clientX-S,M=n.max-n.min;return Math.round(T/_*M+n.min)}return null},[i,c]),{start:y,end:w}=e,x=g=>{const _=Math.min(g,w-t),S=Math.max(_,n.min);n.onChange({start:S,end:w})},O=g=>{const _=Math.max(g,y+t),S=Math.min(_,n.max);n.onChange({start:y,end:S})},m=Uk({callback:g=>{const _=d(g);if(_){const E=Math.abs(e.start-_)<Math.abs(e.end-_)?Fi.START:Fi.END;r!==E&&o(E)}},msToWait:10});return{trackRefCB:f,activeSlider:r,wrapperEvents:{onMouseMove:g=>{!l&&m(g)},onMouseDown:()=>{s(!0)},onMouseUp:()=>{o(null),s(!1)}},onStartInputChange:g=>{x(+g.target.value)},onEndInputChange:g=>{O(+g.target.value)}}}function Vk(e){const{rangeValues:t}=e,{wrapperEvents:n,trackRefCB:r,onStartInputChange:o,onEndInputChange:i,activeSlider:a}=Wk(e);return u.jsxs(N,{styles:Hk,...n,children:[u.jsx(Pk,{trackStyles:e.trackStyles,min:e.min,max:e.max,rangeValues:t,hideRangeTrack:e.hideRangeTrack,trackDataGrab:e.trackDataGrab,ref:r}),u.jsx(Bh,{min:e.min,max:e.max,value:t.start,"data-auto":"range-slider-start-input",thumbStyles:e.thumbStyles,trackStyles:e.trackStyles,onChange:o,isActive:a===Fi.START}),u.jsx(Bh,{min:e.min,max:e.max,value:t.end,"data-auto":"range-slider-end-input",thumbStyles:e.thumbStyles,trackStyles:e.trackStyles,onChange:i,isActive:a===Fi.END})]})}const Hk={[h.COMMON]:{display:"flex",alignItems:"center",position:"relative",marginBlock:"12px",cursor:"pointer","::before":{content:'""',position:"absolute",top:"-8px",right:"0",bottom:"-8px",left:"0"}}},Gk=e=>{const{currentValues:t}=e,[n,r]=C.useState({start:(t==null?void 0:t.start)||e.min,end:(t==null?void 0:t.end)||e.max}),o=C.useCallback(hf(e.onChange,300),[]);return C.useEffect(()=>{(n.start!==(t==null?void 0:t.start)||n.end!==t.end)&&r({start:(t==null?void 0:t.start)||e.min,end:(t==null?void 0:t.end)||e.max})},[t]),u.jsxs(N,{"data-auto":"range-slider",styles:Kk,children:[u.jsx(jk,{labelsWrapperStyles:e.labelsWrapperStyles,labelStyles:e.labelStyles,sign:e.sign,rangeValues:n}),u.jsx(Vk,{onChange:i=>{r(i),o(i)},trackStyles:e.trackStyles,thumbStyles:e.thumbStyles,min:e.min,max:e.max,rangeValues:n,hideRangeTrack:e.hideRangeTrack,trackDataGrab:e.trackDataGrab,minRange:e.minRange})]})},Kk={[h.COMMON]:{padding:"2px"}},Yk=e=>{var r,o,i,a,l,s;const t=C.useMemo(()=>{const c={"@context":"http://schema.org/","@type":"BreadcrumbList",itemListElement:e.links.filter(p=>(p==null?void 0:p.linkProps)&&(p==null?void 0:p.linkProps.href)!=="#").map((p,f)=>{var d,y;return{"@type":"ListItem",position:f+1,item:{name:p.title,...!((d=p==null?void 0:p.linkProps)!=null&&d.currentPage)&&{id:(y=p==null?void 0:p.linkProps)==null?void 0:y.href}}}})};return JSON.stringify(c)},[e]),n=C.useMemo(()=>vf(e._styles),[e._styles]);return u.jsxs("div",{"data-auto":"runtime-breadcrumbs-widget",children:[e.addSchemaMarkup&&e.links.length>1?u.jsx("script",{"data-auto":"schema",type:"application/ld+json",dangerouslySetInnerHTML:{__html:t}}):null,u.jsx(lk,{styles:(r=n.rules)==null?void 0:r.root,linksStyles:(o=n.rules)==null?void 0:o.links,linksHoverStyles:(i=n.rules)==null?void 0:i.linksHover,unlinkableItemsStyles:(a=n.rules)==null?void 0:a.unlinkableItems,currentItemStyles:(l=n.rules)==null?void 0:l.currentItem,separatorStyles:(s=n.rules)==null?void 0:s.separator,separatorType:e==null?void 0:e.separatorType,links:e.links,showEmptyMessage:et.isEditor,emptyMessage:de.str("ui.ed.breadcrumbs.empty.message")})]})},Qk=Ot({Comp:Yk,componentName:"RuntimeBreadcrumbs"}),O1=e=>u.jsx(Qk,{...e});O1.skipHydration=!0;const Xk=async()=>{if(!window._ecomSDK)throw new Error("_ecomSDK is not defined");await window._ecomSDK.ready},qk=async e=>(await Xk(),await window._ecomSDK.buyBooking(e)),E1=C.createContext(null),k1=()=>{const e=C.useContext(E1);if(!e)throw new Error("useBookingContext must be used within a BookerLoggerProvider");return e},Zk=C.memo(({children:e,...t})=>{const n={...t};return u.jsx(E1.Provider,{value:n,children:e})}),Jk=function(){const t=typeof document<"u"&&document.createElement("link").relList;return t&&t.supports&&t.supports("modulepreload")?"modulepreload":"preload"}(),e2=function(e){return"/"+e},zh={},q=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));o=Promise.all(n.map(l=>{if(l=e2(l),l in zh)return;zh[l]=!0;const s=l.endsWith(".css"),c=s?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${c}`))return;const p=document.createElement("link");if(p.rel=s?"stylesheet":Jk,s||(p.as="script",p.crossOrigin=""),p.href=l,a&&p.setAttribute("nonce",a),document.head.appendChild(p),s)return new Promise((f,d)=>{p.addEventListener("load",f),p.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${l}`)))})}))}return o.then(()=>t()).catch(i=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i})},t2=(e,t,n)=>{const r=e[t];return r?typeof r=="function"?r():Promise.resolve(r):new Promise((o,i)=>{(typeof queueMicrotask=="function"?queueMicrotask:setTimeout)(i.bind(null,new Error("Unknown variable dynamic import: "+t+(t.split("/").length!==n?". Note that variables only represent file names one level deep.":""))))})},on=(e,t)=>{je.error({message:e,tags:["__new-runtime__","RuntimeCalBooking"],isEditor:et.isEditor,...t})},n2=async e=>{const t=await t2(Object.assign({"../../../externals/cal-locales/ar.json":()=>q(()=>import("./ar-DMF8roQU.js"),[]),"../../../externals/cal-locales/az.json":()=>q(()=>import("./az-PUpXumFa.js"),[]),"../../../externals/cal-locales/cs.json":()=>q(()=>import("./cs-Cy6rmWsu.js"),[]),"../../../externals/cal-locales/da.json":()=>q(()=>import("./da-BEgXwLlf.js"),[]),"../../../externals/cal-locales/de.json":()=>q(()=>import("./de-Bo78XtaX.js"),[]),"../../../externals/cal-locales/el.json":()=>q(()=>import("./el-CQunKvah.js"),[]),"../../../externals/cal-locales/en.json":()=>q(()=>import("./en-D3ASJsCt.js"),[]),"../../../externals/cal-locales/es.json":()=>q(()=>import("./es-CzU3LTYQ.js"),[]),"../../../externals/cal-locales/eu.json":()=>q(()=>import("./eu-qNb8kslz.js"),[]),"../../../externals/cal-locales/fr.json":()=>q(()=>import("./fr-DUtiuz4H.js"),[]),"../../../externals/cal-locales/he.json":()=>q(()=>import("./he-BOWsB7D4.js"),[]),"../../../externals/cal-locales/hr.json":()=>q(()=>import("./hr-BjqjhZWp.js"),[]),"../../../externals/cal-locales/hu.json":()=>q(()=>import("./hu-DDfgFyhO.js"),[]),"../../../externals/cal-locales/id.json":()=>q(()=>import("./id-C2GSGsBe.js"),[]),"../../../externals/cal-locales/it.json":()=>q(()=>import("./it-DmrugZQm.js"),[]),"../../../externals/cal-locales/iw.json":()=>q(()=>import("./iw-DUR3a-sP.js"),[]),"../../../externals/cal-locales/ja.json":()=>q(()=>import("./ja-DkIPfOLj.js"),[]),"../../../externals/cal-locales/ko.json":()=>q(()=>import("./ko-Dg-D03mz.js"),[]),"../../../externals/cal-locales/nl.json":()=>q(()=>import("./nl-BnWZvGk-.js"),[]),"../../../externals/cal-locales/no.json":()=>q(()=>import("./no-BW3XZeXU.js"),[]),"../../../externals/cal-locales/pl.json":()=>q(()=>import("./pl-UEpzSIT_.js"),[]),"../../../externals/cal-locales/pt-BR.json":()=>q(()=>import("./pt-BR-CDwWvcxp.js"),[]),"../../../externals/cal-locales/pt.json":()=>q(()=>import("./pt-DXdfs5HV.js"),[]),"../../../externals/cal-locales/ro.json":()=>q(()=>import("./ro-BYZXeQ5U.js"),[]),"../../../externals/cal-locales/ru.json":()=>q(()=>import("./ru-Y5ExON-B.js"),[]),"../../../externals/cal-locales/sk.json":()=>q(()=>import("./sk-CM5pYmgT.js"),[]),"../../../externals/cal-locales/sr.json":()=>q(()=>import("./sr-Dfoy9iMO.js"),[]),"../../../externals/cal-locales/sv.json":()=>q(()=>import("./sv-C_Q-RNxJ.js"),[]),"../../../externals/cal-locales/ta.json":()=>q(()=>import("./ta-RUQSCuhg.js"),[]),"../../../externals/cal-locales/tr.json":()=>q(()=>import("./tr-CsVlLsNN.js"),[]),"../../../externals/cal-locales/uk.json":()=>q(()=>import("./uk-B978q_Ac.js"),[]),"../../../externals/cal-locales/vi.json":()=>q(()=>import("./vi-B3HVODBR.js"),[]),"../../../externals/cal-locales/zh-CN.json":()=>q(()=>import("./zh-CN-BGNRj6L4.js"),[]),"../../../externals/cal-locales/zh-TW.json":()=>q(()=>import("./zh-TW-CLqvweit.js"),[])}),`../../../externals/cal-locales/${e}.json`,6);return JSON.parse(t.default)},r2=e=>{const[t,n]=C.useState(void 0),[r,o]=C.useState(!1);return C.useEffect(()=>{(async()=>{try{if(e){const a=await n2(e);n(a)}}catch(a){on("Failed to load labels",{language:e,errorMessage:a instanceof Error?a.message:"Unknown error"})}finally{o(!0)}})()},[e]),{labels:t,labelsLoaded:r}},M1=()=>q(()=>import("./index-EaOAc3j5-CwY3T4h5.js").then(e=>e.ai),[]),A1=C.lazy(()=>M1().then(e=>({default:e.Booker}))),o2=C.lazy(()=>M1().then(e=>({default:e.CalProvider})));function i2({children:e,clientId:t,apiUrl:n,language:r}){const{labels:o,labelsLoaded:i}=r2(r);return i?u.jsx(C.Suspense,{children:u.jsx(o2,{clientId:t,options:{apiUrl:n},language:r,labels:o,children:e})}):null}const pi=class pi{constructor(){J(this,"calApiDomain","api.cal.com");J(this,"calClientId","")}get apiUrl(){return`https://${this.calApiDomain}/v2`}setCalDomain(t="api.cal.com"){this.calApiDomain=t}setCalClientId(t){this.calClientId=t}async fetchCalCom(t,n){const o=await(await fetch(Zn.apiUrl+t,n)).json();return o.data||o}async cancelBooking(t,n){var o;const r=`/bookings/${t}/cancel`;try{const i={method:"POST",headers:{"cal-api-version":pi.CAL_API_VERSION,"Content-Type":"application/json"},body:JSON.stringify({cancellationReason:n})},a=await this.fetchCalCom(r,i);if(a.error)throw new Error(((o=a.error)==null?void 0:o.message)||a.error);return a}catch(i){throw je.error({message:`Failed to cancel booking with UID: ${t}`,errorMessage:i==null?void 0:i.toString(),url:r,tags:["cal-booking","cancel-booking"]}),i}}async getBooking(t){const n=`/bookings/${t}`;return await this.fetchCalCom(n,{method:"GET"})}async getTeamEventType({eventSlug:t,orgId:n,teamId:r}){const o=`/atoms/event-types/${t}/public?isTeamEvent=true&teamId=${r}&username=&orgId=${n}`;return await this.fetchCalCom(o,{method:"GET",headers:{"x-cal-client-id":this.calClientId,"cal-api-version":pi.CAL_API_VERSION,"Content-Type":"application/json"}})}};J(pi,"CAL_API_VERSION","2024-08-13");let Fc=pi;const Zn=new Fc;var Us=(e=>(e.SITE_THEME="site-theme",e.BASIC="basic",e))(Us||{}),Ws=(e=>(e.FREE="FREE",e.PAID="PAID",e))(Ws||{}),_r=(e=>(e.RESCHEDULE="reschedule",e.CANCEL="cancel",e))(_r||{}),Ui=(e=>(e.CANCELLED="CANCELLED",e.PENDING="PENDING",e.RESCHEDULED="RESCHEDULED",e.ACCEPTED="ACCEPTED",e.REJECTED="REJECTED",e))(Ui||{});const Fh={ar:"ar",az:"az",bg:"bg",cs:"cs",da:"da",de:"de",el:"el",en:"en",es:"es",eu:"eu",fr:"fr",he:"he",hr:"hr",hu:"hu",id:"id",it:"it",iw:"iw",ja:"ja",ko:"ko",nl:"nl",no:"no",pl:"pl",pt:"pt","pt-br":"pt-BR",ro:"ro",ru:"ru",sk:"sk",sr:"sr",sv:"sv",ta:"ta",tr:"tr",uk:"uk",vi:"vi",zh:"zh-CN","zh-cn":"zh-CN","zh-tw":"zh-TW"},a2="en",l2=()=>{var r;const{dmAPI:e}=Ie(),t=((r=e==null?void 0:e.getSiteCurrentLocale)==null?void 0:r.call(e))||"",[n]=t.split("-");return Fh[t.toLowerCase()]||Fh[n]||a2},V={atomsWrapper:"atoms-wrapper",bookerContainer:"booker-container",eventMetaCustomClassNames:{eventMetaContainer:"event-meta-container",eventMetaTitle:"event-meta-title",eventMetaTimezoneSelect:"event-meta-timezone-select",eventMetaChildren:"event-meta-children"},datePickerCustomClassNames:{datePickerContainer:"date-picker-container",datePickerTitle:"date-picker-title",datePickerDays:"date-picker-days",datePickerDate:"date-picker-date",datePickerDatesActive:"date-picker-dates-active",datePickerToggle:"date-picker-toggle"},availableTimeSlotsCustomClassNames:{availableTimeSlotsContainer:"available-time-slots-container",availableTimeSlotsHeaderContainer:"available-time-slots-header-container",availableTimeSlotsTitle:"available-time-slots-title",availableTimeSlotsTimeFormatToggle:"available-time-slots-time-format-toggle",availableTimes:"available-times"},confirmStep:{confirmButton:"confirm-button",backButton:"back-button "}},s2=e=>({...V,confirmStep:{...V.confirmStep,backButton:`${V.confirmStep.backButton} ${e===Us.SITE_THEME?"button_2":""}`}}),T1=e=>e?![Ui.CANCELLED,Ui.REJECTED].includes(e):!1,cr={isNonEmptyString:e=>!!e&&typeof e=="string",isInteger:e=>Number.isInteger(e),isNumber:e=>typeof e=="number",oneOf:e=>t=>e.includes(t)},u2={clientId:cr.isNonEmptyString,apiUrl:cr.isNonEmptyString,eventSlug:cr.isNonEmptyString,teamId:cr.isInteger,language:cr.isNonEmptyString,organizationId:cr.isInteger,pricingType:cr.oneOf(Object.values(Ws))},c2=(e,t)=>{if(!t)return;const n=[];Object.entries(u2).forEach(([r,o])=>{o(e[r])||n.push(r)}),n.length>0&&on("Failed to validate booker received params",{additionalInfoToLog:e,invalidParams:n})},d2=(e,t)=>{var o;const n=Array.isArray((o=t==null?void 0:t.metadata)==null?void 0:o.multipleDuration),r=Object.entries(e.responses).reduce((i,[a,l])=>(typeof l=="string"?i[a]=l:i[a]=l?JSON.stringify(l):"",i),{});return{event_name:(t==null?void 0:t.title)||"",event_type_id:e.eventTypeId,slot_start:e.start,time_zone:e.timeZone,metadata:JSON.stringify(e.metadata),fields_responses:r,customer_name:e.responses.name,customer_email:e.responses.email,...n&&{slot_duration:e.slot_duration}}},j1=({pricingType:e,isPaidNativeBookingAvailable:t})=>t&&e===Ws.PAID,N1=D.div.attrs({className:`bg-default dark:bg-muted grid max-w-full items-start dark:[color-scheme:dark] sm:motion-reduce:transition-none md:flex-row rounded-md sm:transition-[width] sm:duration-300 border-subtle border ${V.bookerContainer}`})`
    grid-template-areas: 'meta main timeslots' 'meta main timeslots';
    width: calc(
        var(--booker-meta-width) + var(--booker-main-width) +
            var(--booker-timeslots-width)
    );
    grid-template-columns: var(--booker-meta-width) 1fr var(
            --booker-timeslots-width
        );
    grid-template-rows: 1fr 0fr;
    min-height: 450px;
    height: auto;
`,xf=D.div`
    opacity: 1;
    transform: none;
`,f2=D(xf).attrs({className:"[grid-area:meta]"})`
    min-width: 100%;
`,P1=D(xf).attrs({className:"[grid-area:main] md:border-subtle ml-[-1px] h-full flex-shrink px-5 py-3 md:border-l lg:w-[var(--booker-main-width)]"})``,I1=D(xf).attrs({className:"[grid-area:timeslots] border-subtle rtl:border-default flex w-full flex-col px-5 py-3 pb-0 rtl:border-r ltr:md:border-l h-full overflow-hidden md:w-[var(--booker-timeslots-width)]"})``,p2=D.p.attrs({className:"text-subtle mt-2 text-sm font-semibold"})`
    font-size: 20px;
    font-weight: 600;
`,h2=D.h1.attrs({className:`text-text text-xl font-semibold ${V.eventMetaCustomClassNames.eventMetaTitle} my-2`})`
    margin-top: 16px;
`,m2=D.div.attrs({className:"w-full border-subtle mb-2 grid grid-cols-7 gap-4 border-b border-t text-center md:mb-0 md:border-0"})``,g2=({day:e})=>u.jsx("div",{className:`text-emphasis my-4 text-xs font-medium uppercase tracking-widest ${V.datePickerCustomClassNames.datePickerDays}`,children:e}),L1=D.div.attrs({className:"relative grid grid-cols-7 grid-rows-6 gap-1 text-center"})``,v2=()=>u.jsx("div",{className:"relative w-full pt-[100%]",children:u.jsx("button",{className:"bg-muted text-muted absolute bottom-0 left-0 right-0 top-0 mx-auto flex w-full items-center justify-center rounded-sm border-transparent text-center font-medium opacity-90 transition",disabled:!0,children:u.jsx("span",{className:"font-size-0 bg-emphasis inline-block animate-pulse rounded-md empty:before:inline-block empty:before:content-[''] h-8 w-9"})})}),y2=({children:e,isPreviousDay:t,isCurrentDay:n})=>{let r="disabled:text-bookinglighter absolute bottom-0 left-0 right-0 top-0 mx-auto w-full rounded-md border-2 border-transparent text-center text-sm font-medium transition disabled:cursor-default disabled:border-transparent disabled:font-light";return t||(r+=" hover:border-brand-default"),!n&&!t&&(r+=` ${V.datePickerCustomClassNames.datePickerDatesActive}`),u.jsx("div",{className:"relative w-full pt-[100%]",children:u.jsx("button",{className:r,"data-disabled":t?"true":"false",disabled:t,children:e})})},R1=D.div.attrs({className:V.datePickerCustomClassNames.datePickerContainer})`
    width: 100%;
`,zr=()=>u.jsx("div",{className:"font-size-0 bg-emphasis inline-block animate-pulse rounded-md empty:before:inline-block empty:before:content-[''] mb-4 h-6 w-full"}),D1=({children:e})=>u.jsx(f2,{children:u.jsx("div",{className:`relative p-6 ${V.eventMetaCustomClassNames.eventMetaContainer}`,children:e})}),$1=()=>u.jsx(m2,{children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>u.jsx(g2,{day:e},e))}),x2=({children:e})=>u.jsx("div",{className:"flex gap-2 w-full",children:u.jsx("button",{"data-testid":"time","data-disabled":"false",className:`group whitespace-nowrap items-center font-medium relative rounded-[10px] disabled:cursor-not-allowed gap-1 bg-default text-default border border-default enabled:hover:bg-muted enabled:hover:text-emphasis disabled:opacity-30 focus-visible:bg-subtle focus-visible:outline-none focus-visible:ring-0 focus-visible:shadow-outline-gray-focused shadow-outline-gray-rested enabled:hover:shadow-outline-gray-hover enabled:active:shadow-outline-gray-active transition-shadow duration-200 px-2.5 text-sm leading-none hover:border-brand-default min-h-9 mb-2 flex h-auto w-full flex-grow flex-col justify-center py-2 ${V.availableTimeSlotsCustomClassNames.availableTimes}`,type:"button",children:u.jsx("div",{className:"contents visible group-active:translate-y-[0.5px]",children:u.jsx("div",{className:"flex items-center gap-2",children:e})})})}),B1=({dayInWeek:e,day:t,slots:n})=>u.jsxs(u.Fragment,{children:[u.jsx("div",{className:`flex ${V.availableTimeSlotsCustomClassNames.availableTimeSlotsContainer}`,children:u.jsxs("div",{className:"mb-3 h-8",children:[u.jsx("span",{className:V.availableTimeSlotsCustomClassNames.availableTimeSlotsTitle,children:e})," ",u.jsx("span",{className:"available-time-slots-title",children:t})]})}),u.jsx("div",{className:`scroll-bar flex-grow overflow-auto ${V.availableTimeSlotsCustomClassNames.availableTimeSlotsContainer}`,children:u.jsx("div",{className:"flex w-[20%] flex-col only:w-full",children:n})})]}),z1=({month:e,year:t,actions:n})=>u.jsxs("div",{className:"w-full mb-2 flex items-center justify-between text-xl",children:[u.jsxs("span",{className:"text-default w-1/2 text-base",children:[u.jsx("span",{className:V.datePickerCustomClassNames.datePickerTitle,children:e})," ",u.jsx("span",{className:V.datePickerCustomClassNames.datePickerTitle,children:t})]}),n]}),_2=()=>u.jsx("div",{className:"calcom-atoms atoms-wrapper booker-skeleton",dir:"ltr",children:u.jsxs(N1,{children:[u.jsxs(D1,{children:[u.jsx(zr,{}),u.jsx(zr,{}),u.jsx(zr,{})]}),u.jsx(P1,{children:u.jsxs(R1,{children:[u.jsx(z1,{month:" ",year:" ",actions:u.jsx(zr,{})}),u.jsx($1,{}),u.jsx(L1,{children:[...Array(35)].map((e,t)=>u.jsx(v2,{},t))})]})}),u.jsx(I1,{children:u.jsx(B1,{dayInWeek:" ",day:" ",slots:u.jsxs(u.Fragment,{children:[u.jsx(zr,{}),u.jsx(zr,{})]})})})]})}),w2=()=>{const e=C.useMemo(()=>{const n=new Date;return{year:n.getFullYear(),month:n.toLocaleString("default",{month:"long"}),day:n.getDate(),dayInWeek:n.toLocaleString("default",{weekday:"short"}),timeZone:Intl.DateTimeFormat().resolvedOptions().timeZone}},[]),t={userName:de.str("ui.runtimessr.calbooking.markup.userName"),eventName:de.str("ui.runtimessr.calbooking.markup.eventType"),data:[{value:"30m",icon:"⏱"},{value:"Online Meeting",icon:"🗓️"},{value:e.timeZone,icon:"🌐"}]};return{date:e,mockInfo:t}},C2=()=>{const{date:e,mockInfo:t}=w2();return u.jsx("div",{className:"calcom-atoms atoms-wrapper booker-markup",dir:"ltr","data-auto":"cal-booking-markup",children:u.jsxs(N1,{children:[u.jsxs(D1,{children:[u.jsx(p2,{children:t.userName}),u.jsx(h2,{children:t.eventName}),t.data.map((n,r)=>u.jsxs(O2,{children:[u.jsx("span",{children:n.icon})," ",n.value]},r))]}),u.jsx(P1,{children:u.jsxs(R1,{children:[u.jsx(z1,{month:e.month,year:e.year}),u.jsx($1,{}),u.jsx(L1,{children:[...Array(31)].map((n,r)=>{const o=r+1,i=e.day,a=o===i,l=o<i;return u.jsx(y2,{isPreviousDay:l,isCurrentDay:a,children:o},`day_${o}`)})})]})}),u.jsx(I1,{children:u.jsx(B1,{dayInWeek:e.dayInWeek,day:e.day,slots:u.jsx(b2,{})})})]})})};function b2(){return u.jsx(S2,{children:["9:00am","9:30am","11:00am","11:30am","1:00pm","1:30pm","2:00pm","2:30pm"].map(e=>u.jsx(x2,{children:e},e))})}const S2=D.div`
    display: flex;
    flex-direction: column;
    gap: 4px;
    overflow-y: hidden;
    align-items: center;
`,O2=D.div`
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
`,E2=e=>u.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",...e,children:[u.jsx("path",{d:"M6.75 10.5C6.75 11.7427 8.7645 12.75 11.25 12.75C13.7355 12.75 15.75 11.7427 15.75 10.5C15.75 9.25725 13.7355 8.25 11.25 8.25C8.7645 8.25 6.75 9.25725 6.75 10.5Z",stroke:"currentColor",strokeWidth:"1.33333",strokeLinecap:"round",strokeLinejoin:"round"}),u.jsx("path",{d:"M6.75 10.5V13.5C6.75 14.742 8.7645 15.75 11.25 15.75C13.7355 15.75 15.75 14.742 15.75 13.5V10.5",stroke:"currentColor",strokeWidth:"1.33333",strokeLinecap:"round",strokeLinejoin:"round"}),u.jsx("path",{d:"M2.25 4.5C2.25 5.304 3.108 6.0465 4.5 6.4485C5.892 6.8505 7.608 6.8505 9 6.4485C10.392 6.0465 11.25 5.304 11.25 4.5C11.25 3.696 10.392 2.9535 9 2.5515C7.608 2.1495 5.892 2.1495 4.5 2.5515C3.108 2.9535 2.25 3.696 2.25 4.5Z",stroke:"currentColor",strokeWidth:"1.33333",strokeLinecap:"round",strokeLinejoin:"round"}),u.jsx("path",{d:"M2.25 4.5V12C2.25 12.666 2.829 13.0875 3.75 13.5",stroke:"currentColor",strokeWidth:"1.33333",strokeLinecap:"round",strokeLinejoin:"round"}),u.jsx("path",{d:"M2.25 8.25C2.25 8.916 2.829 9.3375 3.75 9.75",stroke:"currentColor",strokeWidth:"1.33333",strokeLinecap:"round",strokeLinejoin:"round"})]}),{str:k2}=de,M2=()=>{const e=k1();let t=k2("ui.runtimessr.calbooking.meta.price.free");return j1(e)&&(t=`$${e.price}`),u.jsxs(A2,{className:"cal-booking-price-meta-section",children:[u.jsx(T2,{"aria-hidden":"true",width:18,height:18}),u.jsx(j2,{"data-auto":"cal-booking-price-value",children:t})]})},A2=D.div`
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: var(--cal-spacing-2);
    font-size: 0.875rem;
    line-height: 1.25rem;
    margin-bottom: var(--cal-spacing-4);
`,T2=D(E2)`
    fill: transparent;
    flex-shrink: 0;
    // need to align with other cal icons
    margin-inline: -1px;
`,j2=D.div`
    position: relative;
    max-width: 100%;
    word-break: break-word;
    font-weight: 500;
`,Uc=()=>u.jsx("svg",{children:u.jsx("path",{d:"M8 8 L24 24 M8 24 L24 8"})}),_f=e=>u.jsxs(N2,{width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",...e,children:[u.jsx("line",{x1:"6",y1:"6",x2:"16",y2:"16",stroke:"currentColor",strokeWidth:"1",strokeLinecap:"round"}),u.jsx("line",{x1:"6",y1:"16",x2:"16",y2:"6",stroke:"currentColor",strokeWidth:"1",strokeLinecap:"round"})]}),N2=D.svg`
    position: absolute;
    right: 16px;
    top: 16px;
    cursor: pointer;
`,wf=({children:e,dataAuto:t,width:n="580px",disableBodyScroll:r,header:o})=>u.jsxs(u.Fragment,{children:[r&&u.jsx(P2,{}),u.jsx(I2,{"data-auto":t,children:u.jsxs(L2,{width:n,children:[o,u.jsx(R2,{children:e})]})})]}),P2=IS`
    body {
        overflow: hidden !important;
    }
`,I2=D.div`
    position: fixed;
    z-index: 999;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
`,L2=D.div`
    display: flex;
    flex-direction: column;
    gap: 24px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    width: ${({width:e})=>e};
    overflow: hidden;
    box-sizing: border-box;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    @media (max-width: 767px) {
        width: 100%;
        height: 100vh;
        max-height: unset;
        border-radius: 0;
        border: none;
        padding: 20px;
    }
`,R2=D.div`
    max-height: 90vh;
    overflow-y: auto;
    padding: 32px;
    font-weight: 500;
    font-size: 13px;
    box-sizing: border-box;
    position: relative;
    cursor: default;
    font-family: 'Duda Sans', Roboto, sans-serif;
    color: #272b35;

    @media (max-width: 767px) {
        display: flex;
        flex-direction: column;
    }
`,F1=D.span`
    font-size: 24px;
    font-weight: 700;
`,Wc=D.hr`
    height: 1px;
    border: none;
    background: #e4e4e6;
    margin: 20px 0;
`,U1=D.div`
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 8px;
    margin-block: auto;

    font-size: 16px;
    font-weight: 350;
    
    span {
        text-decoration: underline;
        cursor: pointer;
        &[data-disabled='true'] {
            pointer-events: none;
            opacity: 0.5;
        }
    }
`,Fr=e=>de.str(`ui.runtimessr.calbooking.confirmation.success.${e}`),nt=e=>`booking-modal-view-${e}`;var Wi=(e=>(e.SUCCESS="success",e.CANCELLATION="cancellation",e.CANCELED="canceled",e.PENDING="pending",e))(Wi||{});const Ql=({bookingData:e,children:t,dataAuto:n,onClose:r,title:o,svgIcon:i,subtitle:a,type:l="success"})=>{const s=new Date(e.startTime),c=new Date(e.endTime),p=s.toLocaleDateString(void 0,{weekday:"long",year:"numeric",month:"long",day:"numeric"}),f={hour:"numeric",minute:"2-digit",hour12:!0},d=s.toLocaleTimeString(void 0,f),y=c.toLocaleTimeString(void 0,f),w=s.toLocaleTimeString(void 0,{timeZoneName:"long",hour:"numeric",minute:"2-digit",hour12:!0}).split(" ").slice(2).join(" ");return u.jsx(wf,{dataAuto:n,disableBodyScroll:!0,children:u.jsxs(D2,{"data-auto":nt(l),children:[r&&u.jsx(_f,{onClick:r,"data-auto":nt("close-button")}),u.jsxs(V2,{children:[i&&u.jsx(H2,{type:l,"data-type":nt(`${l}-icon`),children:i}),u.jsx(F1,{"data-auto":nt("title"),children:o}),a&&u.jsx($2,{"data-auto":nt("subtitle"),children:a})]}),u.jsx(Wc,{}),u.jsxs(B2,{children:[u.jsxs(Va,{children:[u.jsxs(Ha,{"data-auto":nt("what-label"),children:[Fr("what"),":"]}),u.jsx(dn,{"data-auto":nt("what-value"),children:e.title})]}),u.jsxs(Va,{children:[u.jsxs(Ha,{"data-auto":nt("when-label"),children:[Fr("when"),":"]}),u.jsxs(U2,{isCanceled:l==="canceled",children:[u.jsx(dn,{"data-auto":nt("when-value"),children:p}),u.jsx(dn,{children:`${d} - ${y} ${w}`})]})]}),u.jsxs(Va,{children:[u.jsxs(Ha,{"data-auto":nt("who-label"),children:[Fr("who"),":"]}),u.jsxs(z2,{children:[u.jsxs("div",{children:[u.jsxs(dn,{children:[e.user.name,u.jsx(W2,{children:Fr("host")})]}),u.jsx(dn,{"data-auto":nt("who-user-value"),children:e.userPrimaryEmail})]}),e.attendees.map(x=>u.jsxs("div",{"data-auto":nt("who-attendees-value"),children:[u.jsx(dn,{children:x.name}),u.jsx(dn,{children:x.email})]},x.email))]})]}),u.jsxs(Va,{children:[u.jsxs(Ha,{"data-auto":nt("where-label"),children:[Fr("where"),":"]}),e.videoCallUrl?u.jsx(dn,{"data-auto":nt("where-value"),children:u.jsxs(F2,{href:e.videoCallUrl,target:"_blank",rel:"noopener noreferrer",children:[Fr("videoCallLink"),u.jsx(zc,{name:"icon-external-link"})]})}):u.jsx(dn,{"data-auto":nt("where-value"),children:e.location})]})]}),t&&u.jsxs(u.Fragment,{children:[u.jsx(Wc,{}),u.jsx(U1,{children:t})]})]})})},gt={bgSuccess:"#e8fbee",textSuccess:"#60ac68",hostTagBg:"#D9E9FC",hostTagText:"#174983",bgCancelation:"#f5e3e2",textCancelation:"#ca3932",bgPending:"#DEE8FC",textPending:"#6786B1"},D2=D.div`
    margin: 0 auto;
`,$2=D.div`
    font-weight: 350;
    font-size: 18px;
    line-height: 34px;
    color: #5a5a5a;
    margin-block-start: 4px;
`,B2=D.div`
    display: flex;
    flex-direction: column;
    gap: 24px;
    line-height: 30px;
`,Va=D.div`
    display: grid;
    grid-template-columns: 140px 1fr;
    a {
        display: flex;
        align-items: center;
        gap: 4px;
        text-decoration: none;
    }
`,Ha=D.span`
    font-weight: 500;
    font-size: 16px;
    color: #5d6068;
    text-align: start;
    width: 140px;
`,z2=D.div`
    display: flex;
    flex-direction: column;
    gap: 16px;
    > div {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
`,dn=D.span`
    text-align: start;
    word-break: break-word;
    font-weight: 350;
    font-size: 16px;
`,F2=D.a`
    color: inherit;
    text-decoration: none;
`,U2=D.span`
    text-align: start;
    display: flex;
    flex-direction: column;
    ${({isCanceled:e})=>e&&qt`
            text-decoration: line-through;
        `}
`,W2=D.span`
    background-color: ${gt.hostTagBg};
    color: ${gt.hostTagText};
    padding: 2px 6px;
    box-sizing: border-box;
    border-radius: 4px;
    margin-inline-start: 8px;
    font-size: 12px;
    font-weight: 500;
`,V2=D.div`
    text-align: center;
    display: flex;
    flex-direction: column;
`,Tu={success:{bg:gt.bgSuccess,text:gt.textSuccess,stroke:gt.textSuccess},cancellation:{bg:gt.bgCancelation,text:gt.textCancelation,stroke:gt.textCancelation},canceled:{bg:gt.bgCancelation,text:gt.textCancelation,stroke:gt.textCancelation},pending:{bg:gt.bgPending,text:gt.textPending,stroke:"unset"}},H2=D.div`
    display: flex;
    align-items: center;
    justify-content: center;
    width: 58px;
    height: 58px;
    border-radius: 50%;
    background-color: ${({type:e})=>{var t;return((t=Tu[e])==null?void 0:t.bg)||"transparent"}};
    margin: 0 auto 16px auto;

    svg {
        width: 32px;
        height: 32px;
        stroke: ${({type:e})=>{var t;return((t=Tu[e])==null?void 0:t.stroke)||"currentColor"}};
        color: ${({type:e})=>{var t;return((t=Tu[e])==null?void 0:t.text)||"currentColor"}};
        stroke-width: 2;
        fill: none;
    }
`,G2=({bookingUid:e,closeCancellation:t})=>{const[n,r]=C.useState(""),[o,i]=C.useState(!1),[a,l]=C.useState(null),[s,c]=C.useState(null),p=k1();C.useEffect(()=>{Zn.getBooking(e).then(d=>{T1(d.status)?l(d):t()}).catch(d=>{on("Failed to get booking data (cancellation flow)",{errorMessage:d.message,additionalInfoToLog:p})})},[]);const f=C.useCallback(async d=>{if(e)try{i(!0);const y=await Zn.cancelBooking(e,d);c(y)}catch(y){on("Failed to cancel booking",{errorMessage:y.message,additionalInfoToLog:p})}finally{i(!1)}},[e]);return a?s?u.jsx(Ql,{bookingData:a,title:de.str("ui.runtimessr.calbooking.canceledBooking.title"),svgIcon:u.jsx(Uc,{}),type:Wi.CANCELED,onClose:t}):u.jsx(Ql,{bookingData:a,title:de.str("ui.runtimessr.calbooking.cancelBooking.title"),svgIcon:u.jsx(Uc,{}),type:Wi.CANCELLATION,children:u.jsxs(Q2,{children:[u.jsx(K2,{children:de.str("ui.runtimessr.calbooking.cancelBooking.reason")}),u.jsx(Y2,{value:n,name:"reason",onChange:d=>r(d.target.value),placeholder:de.str("ui.runtimessr.calbooking.cancelBooking.cancelling.placeholder")}),u.jsx(X2,{onClick:()=>f(n),disabled:o,"data-auto":"booking-cancel-confirm-button",children:o?de.str("ui.runtimessr.calbooking.cancelBooking.cancelling"):de.str("ui.runtimessr.calbooking.cancelBooking.cancelButton")})]})}):null},K2=D.label`
    text-align: start;
    font-size: 13px;
    margin-block-end: 8px;
    font-weight: 350;
`,Y2=D.textarea`
    && {
        background-color: transparent;
        border: 1px solid #c9cacd;
        border-radius: 4px;
        height: 100px;
        padding: 8px;
        box-sizing: border-box;
        resize: vertical;
        &::placeholder {
            color: #93959a;
            font-size: 13px;
        }
    }
`,Q2=D.div`
    display: flex;
    flex-direction: column;
    width: 100%;
`,X2=D.button`
    height: 32px;
    width: fit-content;
    background-color: #000;
    color: #fff;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    margin-inline-start: auto;
    padding-inline: 16px;
    margin-block-start: 16px;
    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
`,q2=()=>u.jsx("svg",{viewBox:"0 0 24 24",children:u.jsx("polyline",{points:"4 12 10 18 20 6"})}),yn=e=>de.str(`ui.runtimessr.calbooking.confirmation.success.${e}`),Z2=({confirmationData:e,closeConfirmation:t,rescheduleBooking:n,setCancelBookingId:r})=>u.jsxs(Ql,{bookingData:e,dataAuto:"booking-confirmation-screen",onClose:t,type:Wi.SUCCESS,title:yn("title"),svgIcon:u.jsx(q2,{}),children:[u.jsx("label",{children:yn("change")}),u.jsx("span",{onClick:n,"data-auto":"booking-confirmation-reschedule-button",children:yn("reschedule")}),u.jsx("label",{children:yn("or")}),u.jsx("span",{onClick:()=>{r(e.uid),t()},"data-auto":"booking-confirmation-cancel-button",children:yn("cancel")})]}),Uh=e=>de.str(`ui.runtimessr.calbooking.confirmation.dryRun.${e}`),J2=({closeConfirmation:e})=>u.jsxs(wf,{disableBodyScroll:!0,"data-auto":"booking-confirmation-screen",children:[u.jsx(_f,{onClick:e}),u.jsxs(e3,{children:[u.jsx(t3,{children:u.jsx(Uc,{})}),u.jsx(F1,{children:Uh("title")})]}),u.jsx(Wc,{}),u.jsx(U1,{children:Uh("note")})]}),e3=D.div`
    text-align: center;
    display: flex;
    flex-direction: column;
`,t3=D.div`
    display: flex;
    align-items: center;
    justify-content: center;
    width: 58px;
    height: 58px;
    border-radius: 50%;
    background-color: #fae3e4;
    margin: 0 auto 16px auto;

    svg {
        width: 32px;
        height: 32px;
        stroke: #bc3834;
        stroke-width: 2;
        stroke-linecap: round;
        fill: none;
    }
`,n3=()=>u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 28 28",fill:"none",stroke:"none",children:u.jsxs("g",{transform:"translate(4,4)",children:[u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.16679 4.79102C3.89052 4.79102 3.62557 4.90076 3.43022 5.09611C3.23487 5.29146 3.12512 5.55641 3.12512 5.83268V15.8327C3.12512 16.1089 3.23487 16.3739 3.43022 16.5693C3.62557 16.7646 3.89052 16.8743 4.16679 16.8743H9.82929C10.1745 16.8743 10.4543 17.1542 10.4543 17.4993C10.4543 17.8445 10.1745 18.1243 9.82929 18.1243H4.16679C3.559 18.1243 2.97611 17.8829 2.54634 17.4531C2.11656 17.0234 1.87512 16.4405 1.87512 15.8327V5.83268C1.87512 5.22489 2.11656 4.642 2.54634 4.21223C2.97611 3.78246 3.559 3.54102 4.16679 3.54102H14.1668C14.7746 3.54102 15.3575 3.78246 15.7872 4.21223C16.217 4.642 16.4585 5.22489 16.4585 5.83268V9.16602C16.4585 9.51119 16.1786 9.79102 15.8335 9.79102C15.4883 9.79102 15.2085 9.51119 15.2085 9.16602V5.83268C15.2085 5.55642 15.0987 5.29146 14.9034 5.09611C14.708 4.90076 14.4431 4.79102 14.1668 4.79102H4.16679Z",fill:"currentColor"}),u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.0004 12.291C13.5047 12.291 12.2921 13.5036 12.2921 14.9993C12.2921 16.4951 13.5047 17.7077 15.0004 17.7077C16.4962 17.7077 17.7088 16.4951 17.7088 14.9993C17.7088 13.5036 16.4962 12.291 15.0004 12.291ZM11.0421 14.9993C11.0421 12.8132 12.8143 11.041 15.0004 11.041C17.1866 11.041 18.9588 12.8132 18.9588 14.9993C18.9588 17.1855 17.1866 18.9577 15.0004 18.9577C12.8143 18.9577 11.0421 17.1855 11.0421 14.9993Z",fill:"currentColor"}),u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.5001 1.875C12.8453 1.875 13.1251 2.15482 13.1251 2.5V5.83333C13.1251 6.17851 12.8453 6.45833 12.5001 6.45833C12.1549 6.45833 11.8751 6.17851 11.8751 5.83333V2.5C11.8751 2.15482 12.1549 1.875 12.5001 1.875Z",fill:"currentColor"}),u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.83313 1.875C6.17831 1.875 6.45813 2.15482 6.45813 2.5V5.83333C6.45813 6.17851 6.17831 6.45833 5.83313 6.45833C5.48795 6.45833 5.20813 6.17851 5.20813 5.83333V2.5C5.20813 2.15482 5.48795 1.875 5.83313 1.875Z",fill:"currentColor"}),u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.87512 9.16602C1.87512 8.82084 2.15494 8.54102 2.50012 8.54102H15.8335C16.1786 8.54102 16.4585 8.82084 16.4585 9.16602C16.4585 9.51119 16.1786 9.79102 15.8335 9.79102H2.50012C2.15494 9.79102 1.87512 9.51119 1.87512 9.16602Z",fill:"currentColor"}),u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.0001 13.1211C15.3453 13.1211 15.6251 13.4009 15.6251 13.7461V14.7405L16.2754 15.3908C16.5195 15.6349 16.5195 16.0306 16.2754 16.2747C16.0313 16.5188 15.6356 16.5188 15.3915 16.2747L14.5582 15.4414C14.441 15.3242 14.3751 15.1652 14.3751 14.9994V13.7461C14.3751 13.4009 14.6549 13.1211 15.0001 13.1211Z",fill:"currentColor"})]})}),Wh=e=>de.str(`ui.runtimessr.calbooking.confirmation.pending.${e}`),r3=({confirmationData:e,closeConfirmation:t,rescheduleBooking:n,setCancelBookingId:r})=>u.jsxs(Ql,{bookingData:e,dataAuto:"booking-confirmation-pending-screen",onClose:t,type:Wi.PENDING,title:Wh("title"),subtitle:Wh("subtitle"),svgIcon:u.jsx(n3,{}),children:[u.jsx("label",{children:yn("change")}),u.jsx("span",{onClick:n,children:yn("reschedule")}),u.jsx("label",{children:yn("or")}),u.jsx("span",{onClick:()=>{r(e.uid),t()},children:yn("cancel")})]}),o3=({bookerProps:e,onClose:t})=>{const[n,r]=C.useState(!1),{bookingUid:o,...i}=e;return C.useEffect(()=>{o&&Zn.getBooking(o).then(a=>{T1(a.status)?r(!0):t()}).catch(a=>{on("Failed to get booking data (reschedule flow)",{errorMessage:a.message,additionalInfoToLog:{...e,bookingUid:o}})})},[]),n?u.jsx(wf,{width:"65vw",header:u.jsxs(a3,{children:[u.jsx("span",{children:de.str("ui.runtimessr.calbooking.reschedule.booking")}),u.jsx(_f,{onClick:t})]}),dataAuto:"ssr-cal-booking-reschedule-modal",children:u.jsx(i3,{children:u.jsx(A1,{...i,rescheduleUid:o})})}):null},i3=D.div`
    display: flex;
    flex-direction: column;
`,a3=D.div`
    text-align: center;
    display: flex;
    flex-direction: column;
    height: 58px;
    background-color: #f5f5f7;
    line-height: 58px;
    position: relative;
    padding: 0 32px;
    font-weight: 550;
    font-size: 16px;
    span {
        width: fit-content;
    }
`,Vh={SLUG:"slug",ACTION:"action",TEAM_ID:"teamId",BOOKING_UID:"bookingUid",USERNAME:"username",RESCHEDULE:"reschedule",CANCEL:"cancel",ALL_REMAINING_BOOKINGS:"allRemainingBookings"};let fn=null;(function(){typeof window>"u"||l3()})();function l3(){const e=new URL(Ie().location.href),t=new URLSearchParams(e.search);if(t.get(Vh.ACTION)){fn=Object.fromEntries(t.entries());const r=Ie().document.querySelector('[data-widget-type="ssrcalbooking"]');r&&r.scrollIntoView(),setTimeout(()=>{Object.values(Vh).forEach(o=>e.searchParams.delete(o)),history.replaceState(null,"",e)},0)}}function s3(e){if(e||et.isServer||!fn)return{slug:void 0,action:void 0,teamId:void 0,bookingUid:void 0,username:void 0,isUrlAction:!1};const t={slug:fn.slug,action:fn.action,bookingUid:fn.bookingUid,username:fn.username,teamId:parseInt(fn.teamId),isUrlAction:[_r.RESCHEDULE,_r.CANCEL].includes(fn.action)};return fn=null,t}const u3=e=>{const[{slug:t,action:n,teamId:r,bookingUid:o,username:i,isUrlAction:a}]=C.useState(()=>s3(e.isBookingDryRun)),l=n===_r.CANCEL,s=n===_r.RESCHEDULE;return C.useEffect(()=>{o&&(n===_r.RESCHEDULE&&e.setRescheduleUid(o),n===_r.CANCEL&&e.setCancelBookingId(o))},[]),a?{...e,eventSlug:t||e.eventSlug,teamId:r||e.teamId,userName:i||e.userName,isCancelAction:l,isRescheduleAction:s,bookingUid:o}:e},c3=({teamId:e,organizationId:t,eventSlug:n})=>{const[r,o]=C.useState(null);return C.useEffect(()=>{e&&t&&n&&Zn.getTeamEventType({orgId:t,eventSlug:n,teamId:e}).then(o).catch(i=>{on("Failed to get cal event type by slug",{errorMessage:i.message,additionalInfoToLog:{teamId:e,organizationId:t,eventSlug:n}})})},[e,t,n]),{calEventData:r}},hr=(e,t=2)=>`.${e}`.repeat(t),d3=qt`
    ${hr(V.bookerContainer)} {
        border-radius: 6px;
        width: 100% !important;
        height: 100% !important;
        isolation: isolate;

        > div[class*='grid-area:meta'] {
            position: static !important;
        }

        form {
            [data-testid='select-control'] {
                > div {
                    height: var(--cal-spacing-8);
                    font-size: small;
                }

                div:has(> svg) {
                    height: auto;
                }

                // fix for dropdown chevron position
                div:has(> div > svg) {
                    top: 50%;
                    position: relative;
                    translate: 0 -50%;
                }

                input {
                    height: auto;
                }
            }

            // cal default styles
            textarea,
            input {
                font-size: 0.875rem;
                background-color: var(--cal-bg, #fff);
                border-color: var(--cal-border);
                border-width: 1px;
                width: 100%;
                margin-bottom: var(--cal-spacing-2);
                height: var(--cal-spacing-8);
                padding-top: var(--cal-spacing-2);
                padding-bottom: var(--cal-spacing-2);

                &:focus {
                    border-color: var(--brand-color);
                }
            }

            [data-fob-field-name='guests'] {
                input {
                    background-color: transparent;
                    height: 100%;
                    margin: 0;
                    border: none;
                }

                div:has(> input):focus-within {
                    border-width: 1px;
                }
            }
        }
    }
`,f3=qt`
    .${V.atomsWrapper}:not(.booker-skeleton,.booker-markup)
        .${V.eventMetaCustomClassNames.eventMetaContainer} {
        > div {
            display: flex;
            flex-direction: column;

            // hide empty block (placeholder)
            > div[class='h-6'] {
                display: none;
            }

            // TODO: ask cal to add more class names
            // ensure these blocks are always at the start, keep order of internal booker elements and then show pricing:
            // 0) list of avatars
            > ul {
                order: -1;
            }
            // 1) providers names
            > p {
                order: -1;
            }
            // 2) event meta title
            > .${V.eventMetaCustomClassNames.eventMetaTitle} {
                order: -1;
            }
            // 3) event meta description
            > div[data-testid='event-meta-description'] {
                order: -1;
            }
            // 4) booking pricing meta section
            > div:has(> .cal-booking-price-meta-section) {
                order: -1;
            }
        }
    }

    .${V.eventMetaCustomClassNames.eventMetaTitle} {
        font-size: 18px !important;
        color: unset !important;
    }
`,p3=qt`
    background-color: var(--btn-bg-color);
    background-image: var(--btn-bg-image);
    background-position: var(--btn-bg-position);
    background-repeat: var(--btn-bg-repeat);
    background-size: var(--btn-bg-size);
    border-color: var(--btn-border-color);
    border-bottom-color: var(--btn-border-b-color);
    border-left-color: var(--btn-border-l-color);
    border-right-color: var(--btn-border-r-color);
    border-top-color: var(--btn-border-t-color);
    border-radius: var(--btn-border-radius);
    border-bottom-left-radius: var(--btn-border-bl-radius);
    border-bottom-right-radius: var(--btn-border-br-radius);
    border-top-left-radius: var(--btn-border-tl-radius);
    border-top-right-radius: var(--btn-border-tr-radius);
    border-width: var(--btn-border-width);
    border-bottom-width: var(--btn-border-b-width);
    border-left-width: var(--btn-border-l-width);
    border-right-width: var(--btn-border-r-width);
    border-top-width: var(--btn-border-t-width);
    direction: var(--btn-text-direction);
    text-align: var(--btn-text-align);
    color: var(--btn-text-color);
    font-family: var(--btn-text-font-family);
    font-size: var(--btn-text-font-size);
    font-weight: var(--btn-text-font-weight);
    text-decoration: var(--btn-text-decoration);
    font-style: var(--btn-text-font-style);
    padding: 8px 14px;

    &:hover {
        background: var(--btn-hover-bg);
        border-color: var(--btn-hover-border-color);
        border-bottom-color: var(--btn-hover-border-b-color);
        border-left-color: var(--btn-hover-border-l-color);
        border-right-color: var(--btn-hover-border-r-color);
        border-top-color: var(--btn-hover-border-t-color);
        color: var(--btn-hover-text-color) !important;
        font-weight: var(--btn-hover-text-font-weight);
        text-decoration: var(--btn-hover-text-decoration);
        font-style: var(--btn-hover-text-font-style);
    }
`,h3=qt`
    .${V.bookerContainer} {
        --cal-text-color: var(--color_1);
        --cal-border-subtle: var(--color_1);
        --cal-border: var(--color_1);
        --cal-border-emphasis: var(--color_1);

        border-color: var(--color_1);
        background-color: var(--color_3);
        color: var(--color_1);

        .react-tel-input,
        .flag-dropdown {
            background-color: inherit !important;
        }

        label {
            color: var(--color_1);

            * {
                color: var(--color_1);
            }
        }

        [data-testid='select-control'] {
            > div {
                background-color: var(--color_3);
            }

            * {
                color: var(--color_1);
            }

            input {
                border-width: 0 !important;
            }
        }

        textarea,
        input {
            background-color: var(--color_3);
            border-color: var(--color_1);
            border-width: 1px;

            &:not(:is(:focus, :hover)) {
                box-shadow: none;
            }

            &::placeholder {
                color: var(--color_1);
                opacity: 0.25;
            }
        }

        [data-fob-field-name='guests'] {
            input {
                background-color: transparent !important;
                height: 100%;
            }

            div:has(> input) {
                background-color: var(--color_3);
            }
        }
    }
`,m3=qt`
    .${V.eventMetaCustomClassNames.eventMetaTitle} {
        color: var(--color_1) !important;
    }

    .${V.eventMetaCustomClassNames.eventMetaContainer} {
        svg,
        p {
            color: var(--color_1);
        }
    }
`,g3=qt`
    .${V.datePickerCustomClassNames.datePickerContainer} {
        button {
            color: var(--color_1);
        }

        button[data-disabled='false']:not(
                .${V.datePickerCustomClassNames.datePickerDatesActive}
            ) {
            color: var(--color_3);
            background-color: var(--color_1);
        }
    }

    .${V.datePickerCustomClassNames.datePickerDays} {
        color: var(--color_1);
        font-size: 0.7rem;
    }

    .${V.datePickerCustomClassNames.datePickerTitle} {
        color: var(--color_1);
        font-weight: 500;
    }

    .${V.datePickerCustomClassNames.datePickerDatesActive} {
        background-color: var(--color_4);
        color: var(--color_1);

        &:hover {
            border-color: var(--color_1);
        }
    }

    .${V.datePickerCustomClassNames.datePickerToggle} {
        color: var(--color_1);

        &:disabled {
            opacity: 0.4;
        }
    }
`,v3=qt`
    .${V.availableTimeSlotsCustomClassNames.availableTimeSlotsHeaderContainer} {
        background-color: transparent;
    }

    .${V.availableTimeSlotsCustomClassNames.availableTimeSlotsTitle} {
        color: var(--color_1);
        font-weight: 500;
        font-size: 1rem;
        line-height: 1.5rem;
    }

    .${V.availableTimeSlotsCustomClassNames.availableTimes} {
        border-radius: var(--btn-border-radius);
        border-color: var(--color_1);

        &:not(:disabled):hover {
            background-color: var(--color_4) !important;
        }
    }

    .${V.availableTimeSlotsCustomClassNames.availableTimeSlotsTimeFormatToggle} {
        background-color: transparent;
        border-color: var(--color_1);

        button {
            color: var(--color_1);
        }

        button[aria-checked='true'] {
            background-color: var(--color_5);
        }
    }
`,y3=qt`
    button.${V.confirmStep.confirmButton},
        button.${V.confirmStep.backButton} {
        ${p3}
    }

    button[data-testid='add-guests'] {
        color: var(--color_1);
        border: unset;

        &:not(:disabled):hover {
            background-color: transparent;
        }
    }
`,x3=D.div`
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;

    .booker-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
    }

    .${V.atomsWrapper} {
        height: 100%;
        width: 100%;
        text-align: initial;
    }

    html:not(:has(#cal-booking-css)) & {
        .${V.atomsWrapper}.booker-skeleton,
            .${V.atomsWrapper}.booker-markup {
            display: none;
        }
    }

    &.booker-loading {
        .${V.atomsWrapper}:not(.booker-skeleton) {
            display: none;
        }
    }

    &.booker-ready {
        .${V.atomsWrapper}:not(.booker-skeleton) {
            display: block;
        }
    }

    ${d3}
    ${f3}

    &.${Us.SITE_THEME} {
        ${h3}
        ${m3}
        ${g3}
        ${v3}
        ${y3}
    }
`,_3=e=>`
    [id="${e}"] ${hr(V.atomsWrapper)} {
        container-type: inline-size;
        container-name: atoms-wrapper;
    }

    @container atoms-wrapper (max-width: 819px) {
        [id="${e}"] ${hr(V.bookerContainer)} {
            --booker-meta-width: 100%;
            --booker-main-width: 100%;
            --booker-timeslots-width: 100%;
            display: flex !important;
            flex-direction: column;
        }

        [id="${e}"] ${hr(V.bookerContainer)} > div {
            border: unset;
            height: auto;
            width: 100%;
        }
    }

    @container atoms-wrapper (min-width: 820px) {
        [id="${e}"] ${hr(V.bookerContainer)} {
            --booker-meta-width: 1fr;
            --booker-main-width: 2fr;
            --booker-timeslots-width: 1fr;
            
            width: 100% !important;
        }

        [id="${e}"] ${hr(V.bookerContainer)}:has([class*='grid-area:timeslots']) {
            grid-template-columns: var(--booker-meta-width) var(--booker-main-width) var(--booker-timeslots-width) !important;
        }
    }

    @container atoms-wrapper (min-width: 1500px) {
        [id="${e}"] ${hr(V.bookerContainer)} {
            --booker-main-width: 750px;
        }
    }
`,w3=e=>{const[t,n]=C.useState(!0),[r,o]=C.useState(null),[i,a]=C.useState(null),[l,s]=C.useState(null),{calEventData:c}=c3({teamId:e.teamId,organizationId:e.organizationId,eventSlug:e.eventSlug}),[{editor:p,language:f,apiUrl:d}]=C.useState(()=>(Zn.setCalDomain(e.calApiDomain),Zn.setCalClientId(e.clientId),{editor:fa(),language:l2(),apiUrl:Zn.apiUrl})),y=C.useMemo(()=>{var G;const k=(G=c==null?void 0:c.subsetOfHosts)==null?void 0:G[0];return!Rt.getBoolean("runtime.ssr.booking.dryrun.for.default.staffmember")||!k?!1:k.user.username===`staffmember1-${e.clientId}-example-com`},[c]),w=m1(),{clientId:x,featureMetadata:O,widgetId:v,organizationId:m,pricingType:g,price:_,designStyle:S}=e,E=p.isInPreviewOrEditor()||et.isServer||!O.isNativeBookingPublishingAvailable||y,{eventSlug:T,teamId:M,userName:B}=u3({...e,isBookingDryRun:E,setRescheduleUid:o,setCancelBookingId:a}),z=!!T,Z=S||Us.SITE_THEME,ee=C.useMemo(()=>s2(Z),[Z]),ce={eventSlug:T,teamId:M,userName:B,rescheduleUid:r,cancelBookingId:i,widgetId:v,designStyle:Z,language:f,apiUrl:d,clientId:x,organizationId:m,pricingType:g||Ws.FREE,price:_,...O};if(c2(ce,z),!z&&!p.isInPreviewOrEditor())return null;const Me=()=>{s(null)},Oe=()=>{l!=null&&l.uid&&(o(l.uid),Me())},tt=k=>{o(null);const me=k.data;s(me)},Ne=async k=>{try{!E&&c&&await qk(d2(k,c)),tt({data:k,status:"success"})}catch(me){on("Failed to create paid booking",{errorMessage:me.message,additionalInfoToLog:ce})}},j=()=>{n(!1)},F=l&&E,U=l&&!E&&(l==null?void 0:l.status)===Ui.ACCEPTED,ne=l&&!E&&(l==null?void 0:l.status)===Ui.PENDING,I=w&&z,L=z&&t,R=!z,W=!E&&r,b=[Z];z&&b.push(t?"booker-loading":"booker-ready");const Y={eventSlug:T,customClassNames:ee,...j1(ce)?{handleCreateBooking:Ne,handleSlotReservation:async()=>{}}:{onCreateBookingSuccess:tt},onCreateBookingError:k=>{on("Failed to create booking inside Booker atom",{...k instanceof Error?{errorMessage:k.message,additionalInfoToLog:ce}:{errorMessage:k.error.message,errorCode:k.error.code,errorDetails:k.error.details,additionalInfoToLog:ce}})},onReserveSlotError:k=>{on("Failed to reserve slot inside Booker atom",{errorMessage:k.error.message,errorCode:k.error.code,errorDetails:k.error.details,additionalInfoToLog:ce})},onDeleteSlotError:k=>{on("Failed to delete slot inside Booker atom",{errorMessage:k.error.message,errorCode:k.error.code,errorDetails:k.error.details,additionalInfoToLog:ce})},showNoAvailabilityDialog:!1,roundRobinHideOrgAndTeam:!0,eventMetaChildren:u.jsx(M2,{}),metadata:{originalBookingPath:window.location.pathname}};return u.jsx(x3,{"data-auto":"ssr-cal-booking-widget",className:b.join(" "),children:u.jsxs(Zk,{...ce,children:[u.jsx("style",{dangerouslySetInnerHTML:{__html:_3(v)}}),u.jsxs("div",{className:"booker-wrapper",children:[I&&u.jsxs(i2,{clientId:x,apiUrl:d,language:f,children:[u.jsx(A1,{...M?{isTeamEvent:!0,teamId:M}:{username:B},isBookingDryRun:E,onBookerStateChange:k=>{k.state==="selecting_time"&&j()},...Y},l?"booker-confirmed":"booker-initial"),W&&u.jsx(o3,{bookerProps:{bookingUid:r,isTeamEvent:!0,teamId:M,...Y},onClose:()=>o(null)})]}),U&&u.jsx(Z2,{confirmationData:l,closeConfirmation:Me,rescheduleBooking:Oe,setCancelBookingId:a}),ne&&u.jsx(r3,{confirmationData:l,closeConfirmation:Me,rescheduleBooking:Oe,setCancelBookingId:a}),F&&u.jsx(J2,{closeConfirmation:Me}),i&&u.jsx(G2,{bookingUid:i,closeCancellation:()=>a(null)}),L&&u.jsx(_2,{}),R&&u.jsx(C2,{})]})]})})},C3=Ot({Comp:w3,logProps:!0,componentName:"CalBooking",additionalInfoToLog:{tag:"booking"}}),b3=D.span`
    display: grid;
    width: 16px;
    ${({styles:e={}})=>Po(e)}
    svg {
        fill: currentColor;
    }
`;function S3({className:e,dataAuto:t,svgMarkup:n,styles:r,onClick:o}){return u.jsx(b3,{onClick:o,className:e,"data-auto":t,styles:r,dangerouslySetInnerHTML:{__html:n||O3()}})}function O3(){return`<svg version="1.1" id='cart-widget' xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
           viewBox="0 0 52 54" style="enable-background:new 0 0 52 54;" xml:space="preserve">
            <path clip-rule="evenodd" fill-rule="evenodd"  d="M22.5,2.8c-4.2,0-7.6,3.4-7.6,7.6V14h3.6v-3.5c0-2.2,1.8-4,4-4h7c2.2,0,4,1.8,4,4V14h3.6v-3.5
              c0-4.2-3.4-7.6-7.6-7.6H22.5z M11.5,17.6h29.1c2.8,0,5.1,2.3,5.1,5.1v19.8c0,2.8-2.3,5.1-5.1,5.1H11.5c-2.8,0-5.1-2.3-5.1-5.1V22.7
              C6.4,19.8,8.6,17.6,11.5,17.6z M2.8,22.7c0-4.8,3.9-8.7,8.7-8.7h29.1c4.8,0,8.7,3.9,8.7,8.7v19.8c0,4.8-3.9,8.7-8.7,8.7H11.5
              c-4.8,0-8.7-3.9-8.7-8.7V22.7z"/>
            </svg>`}const E3=({svgMarkup:e,_styles:t})=>{var l,s;const{isInEditor:n}=nf(),{itemsCount:r,openSnipcart:o}=Eb(),i=()=>{!n&&o()},a=C.useMemo(()=>vf(t),[t]);return u.jsxs(k3,{"data-auto":"ssr-cart-widget",children:[u.jsx(S3,{className:"cart-icon",dataAuto:"cart-icon",svgMarkup:e,styles:((l=a.rules)==null?void 0:l.cartIcon)||{},onClick:i}),u.jsx(M3,{styles:(s=a.rules)==null?void 0:s.itemsCountLabel,className:"cart-count-label","data-auto":"cart-count-label",onClick:i,children:(n||r>0)&&r})]})},k3=D.div`
    display: flex;
    align-items: center;
    justify-content: center;

    .cart-icon,
    .cart-count-label {
        cursor: pointer;
        user-select: none;
    }

    label.cart-count-label {
        margin-inline: 5px;
    }
`,M3=D.label`
    ${e=>Po(e.styles)}
`,A3=Ot({Comp:E3,logProps:!0,componentName:"Cart",additionalInfoToLog:{tag:"native-ecom"}});function T3(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Vc(e,t){return Vc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},Vc(e,t)}function j3(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Vc(e,t)}const Hh={disabled:!1},W1=Ce.createContext(null);var N3=function(t){return t.scrollTop},ei="unmounted",mr="exited",gr="entering",Hr="entered",Hc="exiting",jn=function(e){j3(t,e);function t(r,o){var i;i=e.call(this,r,o)||this;var a=o,l=a&&!a.isMounting?r.enter:r.appear,s;return i.appearStatus=null,r.in?l?(s=mr,i.appearStatus=gr):s=Hr:r.unmountOnExit||r.mountOnEnter?s=ei:s=mr,i.state={status:s},i.nextCallback=null,i}t.getDerivedStateFromProps=function(o,i){var a=o.in;return a&&i.status===ei?{status:mr}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(o){var i=null;if(o!==this.props){var a=this.state.status;this.props.in?a!==gr&&a!==Hr&&(i=gr):(a===gr||a===Hr)&&(i=Hc)}this.updateStatus(!1,i)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var o=this.props.timeout,i,a,l;return i=a=l=o,o!=null&&typeof o!="number"&&(i=o.exit,a=o.enter,l=o.appear!==void 0?o.appear:a),{exit:i,enter:a,appear:l}},n.updateStatus=function(o,i){if(o===void 0&&(o=!1),i!==null)if(this.cancelNextCallback(),i===gr){if(this.props.unmountOnExit||this.props.mountOnEnter){var a=this.props.nodeRef?this.props.nodeRef.current:Ia.findDOMNode(this);a&&N3(a)}this.performEnter(o)}else this.performExit();else this.props.unmountOnExit&&this.state.status===mr&&this.setState({status:ei})},n.performEnter=function(o){var i=this,a=this.props.enter,l=this.context?this.context.isMounting:o,s=this.props.nodeRef?[l]:[Ia.findDOMNode(this),l],c=s[0],p=s[1],f=this.getTimeouts(),d=l?f.appear:f.enter;if(!o&&!a||Hh.disabled){this.safeSetState({status:Hr},function(){i.props.onEntered(c)});return}this.props.onEnter(c,p),this.safeSetState({status:gr},function(){i.props.onEntering(c,p),i.onTransitionEnd(d,function(){i.safeSetState({status:Hr},function(){i.props.onEntered(c,p)})})})},n.performExit=function(){var o=this,i=this.props.exit,a=this.getTimeouts(),l=this.props.nodeRef?void 0:Ia.findDOMNode(this);if(!i||Hh.disabled){this.safeSetState({status:mr},function(){o.props.onExited(l)});return}this.props.onExit(l),this.safeSetState({status:Hc},function(){o.props.onExiting(l),o.onTransitionEnd(a.exit,function(){o.safeSetState({status:mr},function(){o.props.onExited(l)})})})},n.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(o,i){i=this.setNextCallback(i),this.setState(o,i)},n.setNextCallback=function(o){var i=this,a=!0;return this.nextCallback=function(l){a&&(a=!1,i.nextCallback=null,o(l))},this.nextCallback.cancel=function(){a=!1},this.nextCallback},n.onTransitionEnd=function(o,i){this.setNextCallback(i);var a=this.props.nodeRef?this.props.nodeRef.current:Ia.findDOMNode(this),l=o==null&&!this.props.addEndListener;if(!a||l){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var s=this.props.nodeRef?[this.nextCallback]:[a,this.nextCallback],c=s[0],p=s[1];this.props.addEndListener(c,p)}o!=null&&setTimeout(this.nextCallback,o)},n.render=function(){var o=this.state.status;if(o===ei)return null;var i=this.props,a=i.children;i.in,i.mountOnEnter,i.unmountOnExit,i.appear,i.enter,i.exit,i.timeout,i.addEndListener,i.onEnter,i.onEntering,i.onEntered,i.onExit,i.onExiting,i.onExited,i.nodeRef;var l=T3(i,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Ce.createElement(W1.Provider,{value:null},typeof a=="function"?a(o,l):Ce.cloneElement(Ce.Children.only(a),l))},t}(Ce.Component);jn.contextType=W1;jn.propTypes={};function Ur(){}jn.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Ur,onEntering:Ur,onEntered:Ur,onExit:Ur,onExiting:Ur,onExited:Ur};jn.UNMOUNTED=ei;jn.EXITED=mr;jn.ENTERING=gr;jn.ENTERED=Hr;jn.EXITING=Hc;const P3=()=>u.jsx("svg",{width:"100%",viewBox:"0 0 26 26",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.625 10.4375C2.625 6.12277 6.12277 2.625 10.4375 2.625C14.7522 2.625 18.25 6.12277 18.25 10.4375C18.25 14.7522 14.7522 18.25 10.4375 18.25C6.12277 18.25 2.625 14.7522 2.625 10.4375ZM10.4375 0.75C5.08724 0.75 0.75 5.08724 0.75 10.4375C0.75 15.7878 5.08724 20.125 10.4375 20.125C12.775 20.125 14.9192 19.2971 16.5927 17.9186L23.5246 24.8504C23.8907 25.2165 24.4843 25.2165 24.8504 24.8504C25.2165 24.4843 25.2165 23.8907 24.8504 23.5246L17.9186 16.5927C19.2971 14.9192 20.125 12.775 20.125 10.4375C20.125 5.08724 15.7878 0.75 10.4375 0.75Z",fill:"currentColor"})}),V1=({styles:e,dataAuto:t="search-icon",openSearchPopup:n})=>u.jsx(zi,{type:"button","data-auto":t,styles:[I3,e==null?void 0:e.iconBtn],onClick:n,"aria-label":"search","data-grab":"collection-search-widget-icon-btn",children:u.jsx(P3,{})}),I3={[h.COMMON]:{display:"inline-block",color:"inherit",width:26}},L3=({styles:e})=>u.jsx(N,{styles:R3,children:u.jsx(V1,{styles:e,dataAuto:"",openSearchPopup:()=>{}})}),R3={[h.COMMON]:{display:"none"}},H1=()=>{const e=C.useRef(null),[t,n]=C.useState(!1);return C.useEffect(()=>{const r=Ie();if(e.current&&r){const o=new r.IntersectionObserver(([i])=>{n(i.isIntersecting)});return o.observe(e.current),()=>o.disconnect()}},[e.current]),{elementRef:e,isVisible:t}},D3=()=>{var r;const e=Ie(),t=$3(),n=(r=e==null?void 0:e.Parameters)==null?void 0:r.SiteAlias;return C.useCallback(({collectionPath:o,itemUrl:i})=>{var l;const a=B3(`${o}/${i}`);return t?`/${a}`:`/site/${n}/${a}${(l=document==null?void 0:document.location)==null?void 0:l.search}`},[t,n])};function $3(){var e,t,n;return((n=(t=(e=Ie())==null?void 0:e.dmAPI)==null?void 0:t.getCurrentEnvironment)==null?void 0:n.call(t))==="live"}const B3=e=>{var o,i;const t=Ie(),n=(o=t==null?void 0:t.Parameters)==null?void 0:o.defaultLang,r=((i=t==null?void 0:t.Parameters)==null?void 0:i.IsSiteMultilingual)&&(t==null?void 0:t.dmAPI.getSiteCurrentLocale());return r&&n&&r!==n?`${r}/${e}`:e},z3=D.a(e=>Tn(e.styles,!0)),F3=({grabId:e,domAttrs:t,...n})=>u.jsx(z3,{"data-auto":e,"data-grab":e,...t,...n}),G1=({href:e="",styles:t,grabId:n,item:r})=>u.jsxs(F3,{styles:[U3,Fe(Yt([W3,t==null?void 0:t.dropdownItemHover]),{pseudoSelector:"hover"})],domAttrs:{href:e,onClick:o=>{et.isEditor&&o.preventDefault()}},grabId:n,children:[u.jsx(Di,{styles:V3,src:r.data.image,alt:""}),u.jsx(Lt,{styles:H3,children:r.data.name}),u.jsx(Lt,{children:r.data.displayed_price})]}),U3={[h.COMMON]:{display:"flex",alignItems:"center",gap:16,textDecoration:"none",color:"inherit",padding:"8px 40px",borderTop:"1px solid #E2E2E2"},[h.MOBILE]:{padding:"8px 20px"}},W3={[h.COMMON]:{background:"#F4F4F4"}},V3={[h.COMMON]:{width:48,height:48,objectFit:"cover",borderRadius:3}},H3={[h.COMMON]:{flexGrow:1,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}},G3=({collection:e,styles:t,fetchMore:n})=>{const{isVisible:r,elementRef:o}=H1(),i=D3();return C.useEffect(()=>{r&&n()},[r]),e.items?u.jsxs(N,{"data-auto":"search-items-group",children:[e.items.map(a=>u.jsx(G1,{href:i({collectionPath:e.path,itemUrl:a.page_item_url}),styles:t,item:a,grabId:"collection-search-visible-dropdown-item"},a.data.identifier)),u.jsx("div",{"data-auto":"end-of-items-container",ref:o})]}):null},K3=({styles:e})=>u.jsx(N,{styles:Y3,children:u.jsx(G1,{item:{data:{identifier:"",name:"",image:"",displayed_price:""},page_item_url:""},styles:e,grabId:"collection-search-widget-dropdown-item"})}),Y3={[h.COMMON]:{display:"none"}},Q3=({collections:e,styles:t,isResultsShown:n,isLoading:r,fetchMore:o})=>{const i=e.reduce((a,l)=>a+l.items.length,0);return u.jsxs(N,{styles:[X3,t==null?void 0:t.dropdown],"data-grab":"collection-search-widget-dropdown",children:[n&&u.jsxs(u.Fragment,{children:[i>0&&e.map(a=>u.jsx(G3,{collection:a,styles:t,fetchMore:()=>{o(a.name)}},a.name)),i===0&&!r&&u.jsx(N,{styles:q3,children:de.str("ui.runtimessr.collectionSearch.noResults")})]}),u.jsx(K3,{styles:t})]})},X3={[h.COMMON]:{textAlign:"left",overflowY:"auto",maxHeight:"1000px"}},q3={[h.COMMON]:{color:"#a1a1a1",padding:"30px 40px",borderTop:"1px solid #E2E2E2"},[h.MOBILE]:{padding:20}},Z3=()=>u.jsx("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:u.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.1629 2.16291C17.529 1.7968 17.529 1.2032 17.1629 0.837087C16.7968 0.470971 16.2032 0.470971 15.8371 0.837087L9 7.67417L2.16291 0.837087C1.7968 0.470971 1.2032 0.470971 0.837087 0.837087C0.470971 1.2032 0.470971 1.7968 0.837087 2.16291L7.67417 9L0.837087 15.8371C0.470971 16.2032 0.470971 16.7968 0.837087 17.1629C1.2032 17.529 1.7968 17.529 2.16291 17.1629L9 10.3258L15.8371 17.1629C16.2032 17.529 16.7968 17.529 17.1629 17.1629C17.529 16.7968 17.529 16.2032 17.1629 15.8371L10.3258 9L17.1629 2.16291Z",fill:"currentColor"})}),J3=({inputRef:e,value:t,placeholder:n,styles:r,onChange:o,onFocus:i,onClose:a})=>{const[l,s]=C.useState(!1);return u.jsxs(N,{styles:eM,onKeyDown:c=>{c.key==="Tab"&&s(!0)},onMouseDown:()=>s(!1),children:[u.jsx(Fs,{ref:e,styles:[tM,l?{}:nM,r==null?void 0:r.input,Fe(Yt(r==null?void 0:r.inputFocus),{pseudoSelector:"focus"})],"data-grab":"collection-search-widget-input",value:t,placeholder:n,onChange:o,onFocus:i,increaseSpecificity:!0,role:"search","aria-label":"search products","data-auto":"collection-search-input"}),u.jsx(zi,{styles:rM,onClick:a,"data-auto":"search-popup-close-btn",children:u.jsx(Z3,{})})]})},eM={[h.COMMON]:{display:"flex",alignItems:"center",padding:40},[h.MOBILE]:{padding:20}},tM={[h.COMMON]:{display:"block",width:"100%",padding:"5px"}},nM={[h.COMMON]:{outline:"none"}},rM={[h.COMMON]:{display:"flex",color:"#313131",marginInlineStart:"15px"}},K1=2;let Gc;const oM=async()=>{var e,t,n;Gc=await((n=(t=(e=Ie())==null?void 0:e.dmAPI)==null?void 0:t.loadCollectionsAPI)==null?void 0:n.call(t))},Gh=async()=>(Gc||await oM(),Gc),iM=({collectionsToSearch:e})=>{const[t,n]=C.useState(""),[r,o]=C.useState(!1),[i,a]=C.useState(!1),[l,s]=C.useState(()=>ju(e)),c=C.useCallback(hf(async w=>{try{const x=await Gh();if(w.length<K1||!x){a(!1),s(ju(e));return}je.info({message:"User searches in the collection search widget",tags:["runtimeCollectionSearch"],searchValue:w});const O=await Promise.all(e.map(({name:v})=>x.storeData(v).pageSize(50).search(w).get()));s(v=>v.map((m,g)=>{var S,E;const _=(S=O[g])==null?void 0:S.values;return{...m,items:_||[],page:((E=O[g])==null?void 0:E.page)||{}}}))}catch{je.warn({message:"Error in collection search widget on search",tags:["__new-runtime__"]})}a(!1)},500),[]);return{inputValue:t,isDropdownOpen:r,isLoading:i,collections:l,onInputChange:w=>{const x=w.target.value;n(x),a(!0),c(x.trim())},onInputFocus:()=>{o(!0)},clearSearchState:()=>{n(""),s(ju(e))},fetchMore:async w=>{var O;const x=(O=l.find(v=>v.name===w))==null?void 0:O.page;if(!(i||!x||x.pageNumber+1===x.totalPages)){a(!0);try{const v=await Gh();if(!v){a(!1);return}const m=await v.storeData(w).pageSize(50).pageNumber(x.pageNumber+1).search(t).get();s(g=>g.map(_=>{if(_.name!==w)return _;const S=m==null?void 0:m.values;return{..._,items:[..._.items,...S],page:m==null?void 0:m.page}}))}catch{je.warn({message:"Error in collection search widget on fetch more",tags:["__new-runtime__"]})}a(!1),je.info(`Fetch more items in the collection search widget, page ${x.pageNumber+1}`)}}}},ju=e=>e.map(({name:t,path:n})=>({name:t,path:n,items:[],page:{pageNumber:0,totalPages:0}})),aM=({portalRootSelector:e="#site_content",children:t})=>{const[n,r]=C.useState(null);return C.useEffect(()=>{var a,l,s,c;const o=(l=(a=Ie())==null?void 0:a.document)==null?void 0:l.createElement("div"),i=(c=(s=Ie())==null?void 0:s.document)==null?void 0:c.querySelector(e);return i==null||i.appendChild(o),r(o),()=>{o&&(i==null||i.removeChild(o))}},[]),n?Xi.createPortal(t,n):null};function lM({portalRootSelector:e=".dmContent",children:t}){const n=C.useMemo(()=>{var r,o;return(o=(r=Ie())==null?void 0:r.document)==null?void 0:o.querySelector(e)},[]);return n?Xi.createPortal(t,n):null}const sM=({inputRef:e,widgetId:t,placeholder:n,styles:r,collectionsToSearch:o,closeSearchPopup:i,animatedBlockRef:a,animationState:l})=>{const{inputValue:s,isDropdownOpen:c,isLoading:p,collections:f,onInputChange:d,onInputFocus:y,clearSearchState:w,fetchMore:x}=iM({collectionsToSearch:o}),O=()=>{w(),i()};return u.jsx(aM,{children:u.jsxs(N,{styles:uM,"data-grab":`collection-search-widget-portal-${t}`,children:[u.jsx(N,{style:pM[l],styles:cM,onClick:O}),u.jsxs(N,{styles:[dM,r==null?void 0:r.popup],ref:a,style:fM[l],"data-grab":"collection-search-widget-popup",children:[u.jsx(J3,{inputRef:e,styles:r,value:s,placeholder:n,onChange:d,onFocus:y,onClose:O}),u.jsx(Q3,{collections:f,styles:r,isResultsShown:c&&s.length>=K1,isLoading:p,fetchMore:x})]}),u.jsx(L3,{styles:r})]})})},uM={[h.COMMON]:{position:"fixed",top:0,left:0,right:0,zIndex:200}},cM={[h.COMMON]:{display:"none",background:"rgba(0, 0, 0, 0.4)",position:"absolute",top:0,left:0,right:0,height:"100vh"}},dM={[h.COMMON]:{position:"relative",background:"#fff",display:"flex",flexDirection:"column",maxHeight:"100vh",transition:"all 0.2s linear",marginTop:"-200px",opacity:0}},fM={entering:{marginTop:0,opacity:1},entered:{marginTop:0,opacity:1},exiting:{marginTop:"-200px",opacity:0},exited:{marginTop:"-200px",opacity:0,height:0,overflow:"hidden"}},pM={entering:{display:"block"},entered:{display:"block"},exiting:{display:"none"},exited:{display:"none"}},hM=500,mM=({widgetId:e,placeholder:t,_styles:n,collectionsToSearch:r=[{name:"catalog_product",path:"product"}]})=>{const o=C.useRef(null),[i,a]=C.useState(!1),l=C.useRef(null),s=p=>{var f;et.isEditor||(a(!0),(f=l==null?void 0:l.current)==null||f.focus(),p.preventDefault())},c=()=>{a(!1)};return u.jsxs("div",{children:[u.jsx(V1,{styles:n,openSearchPopup:s}),u.jsx(jn,{nodeRef:o,in:i,timeout:hM,children:p=>u.jsx(sM,{inputRef:l,widgetId:e,placeholder:t,styles:n,collectionsToSearch:r,animatedBlockRef:o,animationState:p,closeSearchPopup:c})})]})},gM=Ot({Comp:mM,componentName:"RuntimeSearch"}),vM=e=>u.jsx(gM,{...e});var Cf=(e=>(e.ONE_OF="ONE_OF",e.BETWEEN="BETWEEN",e))(Cf||{}),Vi=(e=>(e.CATEGORY_ID="category_ids",e.PRICE="price",e))(Vi||{}),Kc=(e=>(e.NEWEST="created_at",e.NAME="name",e.PRICE="price",e))(Kc||{});const Y1=Ce.createContext({styles:{}});function ha(){return ma().styles}function ma(){return Ce.useContext(Y1)}function yM(){var e;return!!((e=globalThis==null?void 0:globalThis.document)!=null&&e.querySelector('[data-element-type="dSnipcartProductGalleryId"]'))}const bf={menuSelectors:{wrapper:null,list:"filter-sort-menu-list",divider:"filter-sort-menu-divider",closeIcon:"filter-sort-menu-closeIcon"},inputsSelectors:{slider:"range-slider-",input:"filterItem-input-icon-wrapper",inputIcon:"input-icon",inputLabel:"filterItem-input-label"},textSelectors:{displayName:"filter-sort-menu-displayName",title:"filter-sort-menu-item-title"}},Yc=bf.menuSelectors,Er=bf.inputsSelectors,Q1=bf.textSelectors,{slider:xM}=Er,_M=5;function wM(e){const{item:t,currentValues:n,onChange:r}=e,{min:o,max:i,displayPrice:a}=t.filtersData,{sign:l,direction:s="ltr"}=SM(a),{inputsStyle:c}=ha(),p=f=>{f.start===o&&f.end===i?r(void 0):r(f)};return u.jsx(Gk,{min:o,max:i,currentValues:n,onChange:p,minRange:_M,sign:l,trackDataGrab:`${xM}${t.fieldId}`,labelsWrapperStyles:CM,labelStyles:[bM(s),c==null?void 0:c.inputLabel],trackStyles:c==null?void 0:c.slider,"data-auto":`range-slider-${t.fieldId}`})}const CM={[h.COMMON]:{fontStyle:"normal",fontWeight:400,fontSize:"16px",lineHeight:"19px"}};function bM(e){return{[h.COMMON]:{display:"flex",flexDirection:e==="rtl"?"row":"row-reverse"}}}function SM(e){const t={},n=e.match(/[^\d.,]/);if(n===null)return t;const r=n[0],o=e.indexOf(r);if(o===0)t.direction="ltr";else if(o===e.length-1)t.direction="rtl";else return t;return t.sign=r,t}const OM=C.memo(wM),EM={labelDataGrab:Er.inputLabel,iconWrapperDataGrab:Er.input,iconDataGrab:Er.inputIcon};function kM(e){const{item:t,currentValues:n,onChange:r}=e,{inputsStyle:o}=ha(),i=new Set(n||[]);function a(l,s){i[s?"add":"delete"](l),r([...i])}return u.jsx(u.Fragment,{children:Object.entries(t.filtersData).map(([l,s])=>{const c=i.has(s),p=`${t.fieldId}_${l}`;return u.jsx(xk,{"data-auto":p,dataGrabs:EM,checked:c,onChange:f=>{a(s,f)},label:l,inputStyles:o==null?void 0:o.input,labelWrapperStyles:o==null?void 0:o.inputLabel,inputIconStyles:o==null?void 0:o.inputIcon,labelStyles:[MM,o==null?void 0:o.inputLabel]},p)})})}const MM={[h.COMMON]:{minHeight:"31px"}};function X1({title:e,children:t,dataAuto:n}){const{styles:r}=ma(),{menuStyle:o,textStyle:i}=r;return u.jsxs(u.Fragment,{children:[u.jsx(fk,{title:e,styles:{title:i==null?void 0:i.title,headerWrapper:i==null?void 0:i.title},isAccordionOpen:!0,"data-auto":n,dataGrabs:{titleDataGrab:Q1.title},children:t}),u.jsx(Lt,{styles:[AM,o==null?void 0:o.divider,Fe({[h.COMMON]:{display:"none"}},{innerSelector:"&:last-child"})],"data-grab":Yc.divider})]})}const AM={[h.COMMON]:{width:"100%",backgroundColor:"#e1e1e1",marginBlock:"24px",display:"block",height:"2px",minHeight:"2px"}};function TM(e){return u.jsx(X1,{title:e.item.fieldDisplayName,dataAuto:`filter-item-${e.item.fieldId}`,children:e.type===Cf.BETWEEN?u.jsx(OM,{...e}):u.jsx(kM,{...e})})}function jM({values:e,currentSelection:t,onChange:n}){const{inDesignMode:r,hasProducts:o,currentPageType:i}=ma();return u.jsx(u.Fragment,{children:e.map(a=>{var p;if(a.disabled&&!r||NM({filterField:a,hasProducts:o,currentPageType:i}))return null;const l=a.fieldId,s=a.filterType||Cf.ONE_OF,c=((p=t[l])==null?void 0:p.selectedValues)||void 0;return u.jsx(TM,{item:a,type:s,currentValues:c,onChange:f=>{n({...a,filterType:s,selectedValues:f})}},l)})})}function NM({filterField:e,hasProducts:t,currentPageType:n}){const r=e.fieldId;if(r===Vi.CATEGORY_ID)return n==="STORE_CATEGORY_PAGE"||Object.keys(e.filtersData).length===0;if(r===Vi.PRICE)return!t}const PM={labelDataGrab:Er.inputLabel,outerCircleDataGrab:Er.input,innerCircleDataGrab:Er.inputIcon},IM=[Kc.NEWEST,Kc.PRICE];function LM({name:e,values:t,currentSelection:n,onChange:r}){const{inDesignMode:o,isPremiumWidget:i}=ma(),{inputsStyle:a}=ha();return u.jsx(X1,{title:e,dataAuto:"menu-item-sort",children:t.map(l=>{if(!i&&IM.includes(l.fieldId)||l.disabled&&!o)return null;const s=`${l.fieldId}_${l.sortDirection}`,c=`${n.fieldId}_${n.sortDirection}`;return u.jsx(yf,{value:s,selectedValue:c,label:l.fieldDisplayName,onChange:()=>r(l),labelWrapperStyles:[RM,a==null?void 0:a.inputLabel],labelStyles:a==null?void 0:a.inputLabel,inputStyles:a==null?void 0:a.input,inputIconStyles:a==null?void 0:a.inputIcon,"data-auto":s,dataGrabs:PM},s)})})}const RM={[h.COMMON]:{minHeight:"31px"}},DM=C.memo(LM),{str:Kh}=de;function $M({props:e,filterSortLogic:t}){const{menuStyle:n,textStyle:r}=ha(),{inDesignMode:o,isPremiumWidget:i}=ma(),{sortableFields:a,filterableFields:l,buttonText:s,isFilterSectionHidden:c,styleWrapperSelector:p,setIsMenuOpen:f}=e,{currentSelection:d,onChangeFilter:y,showSortSection:w,onChangeSort:x,clearCollectionValues:O,selectedCount:v}=t,m=i&&(!c||o),g=w||o;return u.jsx(lM,{children:u.jsxs(N,{styles:[BM,n==null?void 0:n.wrapper],"data-auto":"filter-sort-floating-menu","data-grab":p,children:[u.jsxs(Q,{tag:ae.h3,"data-grab":Q1.displayName,styles:[zM,r==null?void 0:r.displayName],children:[u.jsx(Lt,{styles:FM,children:s}),u.jsx(Rh,{styles:[WM,n==null?void 0:n.closeIcon],dataGrab:Yc.closeIcon,onClick:()=>f(!1)})]}),v>0&&u.jsxs(Q,{tag:ae.h4,styles:[UM,r==null?void 0:r.title],domAttrs:{onClick:O},"data-auto":"clear-filters",children:[u.jsx(Lt,{children:Kh("widget.filtersort.clear-all")}),u.jsx(Rh,{size:8,styles:{[h.COMMON]:{marginInlineStart:"4px",display:"grid",alignItems:"center"}}})]}),u.jsxs(N,{className:"dmNewParagraph",styles:[VM,HM,n==null?void 0:n.list],"data-grab":Yc.list,children:[g&&u.jsx(DM,{name:Kh("widget.filtersort.sort-by.title"),values:a,onChange:x,currentSelection:{sortDirection:d.sortDirection||"asc",fieldId:d.sortBy||""}}),m&&u.jsx(jM,{values:l,currentSelection:d.filters,onChange:y})]})]})})}const BM={[h.COMMON]:{cursor:"auto",height:"100%",position:"fixed",right:0,top:0,bottom:0,backgroundColor:"#ffffff",color:"#000000",zIndex:999,display:"flex",flexDirection:"column",border:"1px solid #000",width:"418px",padding:"40px",maxWidth:"100%",minWidth:"300px",boxSizing:"border-box"}},zM={[h.COMMON]:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlockStart:"0",marginBlockEnd:"26px",lineHeight:"29px",width:"100%"}},FM={[h.COMMON]:{flex:1,margin:0}},UM={[h.COMMON]:{lineHeight:"normal",textTransform:"uppercase",marginBlockStart:"calc(50px - 26px)",marginBlockEnd:"50px",display:"flex",alignItems:"center",gap:"8px",cursor:"pointer"}},WM={[h.COMMON]:{cursor:"pointer",color:"#333",display:"flex",padding:"10px",margin:"-10px",boxSizing:"border-box"}},VM={[h.COMMON]:{overflowY:"auto",height:"100%",display:"flex",flexDirection:"column",margin:0,padding:0}},HM=Fe({[h.COMMON]:{width:"10px",backgroundColor:"transparent"}},{innerSelector:"&::-webkit-scrollbar"}),GM="fs-btn-wrapper";function KM(e){var l;const{triggerStyle:t}=ha(),{showWarning:n,onClick:r}=e,{iconName:o,showIcon:i}=(t==null?void 0:t.iconProps)||{},a=e.selectedCount?`${e.text} (${e.selectedCount})`:e.text;return u.jsxs(N,{styles:YM,onClick:r,"data-auto":GM,children:[u.jsx(Bs,{styles:{root:[QM,t==null?void 0:t.root],rootHover:t==null?void 0:t.rootHover,text:t==null?void 0:t.text,rootHoverText:t==null?void 0:t.rootHoverText,buttonTypeCSSClass:t==null?void 0:t.buttonTypeCSSClass,icon:t==null?void 0:t.icon},buttonContent:{text:a,iconClassNames:[((l=t==null?void 0:t.iconProps)==null?void 0:l.iconName)||""]},dataGrab:e.styleWrapperSelector,iconClass:i&&o,showHoverEffect:e.showButtonHoverEffect}),n&&u.jsx(uk,{size:24})]})}const YM={[h.COMMON]:{position:"relative",width:"100%",height:"100%"}},QM={[h.COMMON]:{display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",minHeight:"32px",padding:"8px 14px",minWidth:"fit-content",textAlign:"center",margin:0}};function XM(e){const[t,n]=C.useState(null);return C.useEffect(()=>{if(typeof window>"u")return;const{collectionsFilterService:r}=window.runtime||{};r&&n({setCollectionSort:(o,i)=>r.setCollectionSort(e,o,i),setCollectionFilter:(o,i)=>r.setCollectionFilter(e,o,i),onCollectionValueChange:r.onCollectionValueChange,clearCollectionValues:()=>r.clearCollectionValues(e)})},[]),t}function qM(e,t){const[n,r]=C.useState({filters:{},sortBy:"",sortDirection:"asc"}),o=XM(e);C.useEffect(()=>{async function p(){if(o)try{return await o.onCollectionValueChange(e,d=>{r(d)})}catch(d){je.error({message:"Failed to execute filterService.onCollectionValueChange",errorMessage:d==null?void 0:d.toString(),tags:["FilterSortMenu","updateCurrentSelection"]})}}let f=()=>{};return p().then(d=>{d&&(f=d)}),f},[o]);const i=C.useCallback(p=>{o==null||o.setCollectionFilter(p.fieldId,p)},[o]),a=C.useCallback(p=>{o==null||o.setCollectionSort(p.fieldId,p.sortDirection)},[o]),l=C.useCallback(()=>{o==null||o.clearCollectionValues()},[o]),s=C.useMemo(()=>{var x,O;let p=0;const{sortBy:f,filters:d}=n,y=(x=d[Vi.PRICE])==null?void 0:x.selectedValues,w=(O=d[Vi.CATEGORY_ID])==null?void 0:O.selectedValues;return f&&p++,y&&p++,Array.isArray(w)&&(p+=w.length),p},[n]);if(!o)return null;const c=!t.isSortSectionHidden&&t.sortableFields.some(p=>!p.disabled);return{currentSelection:n,onChangeFilter:i,showSortSection:c,onChangeSort:a,clearCollectionValues:l,selectedCount:s}}function ZM(e){const[t,n]=C.useState(!1),[r,o]=C.useState(!1),{isInEditor:i}=nf(),{filterableFields:a=[],sortableFields:l,collectionValue:s,isSortSectionHidden:c,hasNativeStore:p}=e,f=qM(s,{isSortSectionHidden:c,sortableFields:l}),d=C.useMemo(()=>e.isFilterSectionHidden&&e.isSortSectionHidden?!0:![...e.isFilterSectionHidden?[]:a,...e.isSortSectionHidden?[]:l].some(v=>!v.disabled),[a,l]);C.useEffect(()=>{const x=!p||!yM();o(!!i&&x),i&&t&&n(!1)},[i]);const y=()=>{et.isEditor||n(x=>!x)},w=(t||!!e.menuPanelMode)&&f&&!d;return{filterSortLogic:f,onBtnClick:y,showWarning:r,setIsMenuOpen:n,shouldRenderMenu:w}}const JM=e=>{const{widgetId:t,menuPanelMode:n,premiumWidget:r,currentPageType:o,_styles:i={}}=e,{filterSortLogic:a,onBtnClick:l,showWarning:s,setIsMenuOpen:c,shouldRenderMenu:p}=ZM(e),f={styles:i,menuPanelMode:n,inDesignMode:n==="DESIGN",hasProducts:e.hasProducts,isPremiumWidget:r,currentPageType:o};return u.jsxs(Y1.Provider,{value:f,children:[u.jsx(N,{styles:eA,children:u.jsx(KM,{onClick:l,showWarning:s,selectedCount:a==null?void 0:a.selectedCount,text:e.buttonText,styleWrapperSelector:`filter-sort-trigger-${t}`,showButtonHoverEffect:e.showButtonHoverEffect})}),p&&a&&u.jsx($M,{filterSortLogic:a,props:{...e,styleWrapperSelector:`filter-sort-menu-${t}`,setIsMenuOpen:c}})]})},eA={[h.COMMON]:{width:"100%",height:"100%",minWidth:"fit-content"}},tA=Ot({Comp:JM,logProps:!0,componentName:"FilterSort",additionalInfoToLog:{tag:"native-ecom"}}),Sf={bgWhite:"#ffffff",borderColor:"#ced6d9",headerColor:"#f5f5f7",iconColor:"#616C79",primaryTextColor:"#313131",secondaryTextColor:"#828894"},nA=f1("select",!0),Yh=D.option`
    &:not(:disabled) {
        color: initial;
    }
    background-color: ${Sf.bgWhite};
`,rA=({selectedValue:e,placeholder:t,options:n=[],onChange:r,styles:o})=>{const i=C.useRef(null),{insideEditor:a}=fa(),[l,s]=C.useState((e==null?void 0:e.value)||"");C.useEffect(()=>{const f=i.current;if(f){const d=y=>{a()&&y.preventDefault()};return f.addEventListener("mousedown",d),()=>{f.removeEventListener("mousedown",d)}}},[]);const c=C.useCallback(({target:f})=>{const d=n.find(({value:y})=>y===f.value);d&&(s(f.value),r==null||r(d))},[]),p=[oA,o,aA,!l&&{[h.COMMON]:{color:Sf.secondaryTextColor}}];return u.jsx(N,{styles:iA,className:"dropdown-component",children:u.jsxs(nA,{ref:i,required:!0,value:l,onChange:c,"data-auto":"dropdown-component","data-grab":"dropdown-component",className:"dropdown",styles:p,children:[u.jsx(Yh,{value:"",disabled:!0,hidden:!0,children:t}),n.map(({value:f,label:d,disabled:y})=>u.jsx(Yh,{value:f,disabled:y,"data-auto":"select-option",children:d},f))]})})},oA={[h.COMMON]:{width:"100%",height:"100%",margin:"0",display:"flex",alignItems:"center",boxSizing:"border-box",paddingInlineStart:"8px",border:`1px solid ${Sf.borderColor}`,borderRadius:"3px",outline:"none",boxShadow:"none",lineHeight:"normal",appearance:"none",backgroundColor:"inherit",cursor:"pointer"}},iA={[h.COMMON]:{position:"relative",cursor:"pointer",width:"100%",height:"30px",maxHeight:"40px",border:"0px solid"}},aA={[h.COMMON]:{backgroundImage:`url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' fill='none'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.5769 5.5L12.5 6.4375L8.5 10.5L4.5 6.4375L5.42308 5.5L8.5 8.625L11.5769 5.5Z' fill='%23828894'/%3E%3C/svg%3E")`,backgroundRepeat:"no-repeat",backgroundPosition:"calc(100% - 4px) center",":dir(rtl)":{backgroundPosition:"4px"}}},Qh=({title:e,dropdownPlaceholder:t,styles:n,direction:r,...o})=>{const i=C.useCallback(a=>{var l;a&&!a.dir&&(a.dir=((l=getComputedStyle(a))==null?void 0:l.direction)||"ltr")},[]);return u.jsxs(N,{styles:lA,"data-grab":"dropdown-wrapper","data-auto":"dropdown-wrapper",className:"dropdown-wrapper",dir:r,ref:i,children:[e&&u.jsx(Q,{tag:ae.h5,"data-auto":"dropdown-title","data-grab":"dropdown-title",className:"dropdown-title",styles:[sA,n==null?void 0:n.title],children:e}),u.jsx(rA,{placeholder:t,styles:n==null?void 0:n.fields,...o})]})},lA={[h.COMMON]:{width:"100%",display:"flex",flexDirection:"column","*":{userSelect:"none"}}},sA={[h.COMMON]:{textAlign:"start",marginBlockEnd:"8px",lineHeight:"100%",marginTop:"0",direction:"inherit"}};var Qc=(e=>(e.DROPDOWN="DROPDOWN",e.RADIOBUTTON="RADIOBUTTON",e))(Qc||{});const uA=({productState:e,editorEmptyMessage:t,dropdownPlaceholder:n,renderType:r,_styles:o})=>{const i=C1({stylesInput:o,monitorKey:"options.variations"}),a=Object.entries((e==null?void 0:e.dropdownOptions)||{});if(a.length===0)return et.isEditor&&t?u.jsx("div",{children:t}):u.jsx("div",{});const l=dA(r);return u.jsx(N,{styles:[cA,i==null?void 0:i.wrapper],"data-auto":"options-variations","data-grab":"options-variations",className:"options-variations",children:a.map(([s,c])=>{var p,f;return u.jsx(l,{selectedValue:e.getSelectedField(s),options:c,title:s,onChange:d=>{e.updateVariation(s,d.value.toString())},direction:(f=(p=i==null?void 0:i.wrapper)==null?void 0:p.common)==null?void 0:f.direction,dropdownPlaceholder:n,styles:i},`d_option_${s}`)})})},cA={[h.COMMON]:{display:"flex",flexDirection:"column",height:"fit-content",width:"100%",padding:"4px",gap:"24px",border:"0px solid"}},dA=e=>{switch(e){case Qc.RADIOBUTTON:return kk;case Qc.DROPDOWN:return Qh;default:return Qh}},fA=Ot({Comp:pa(uA),componentName:"RuntimeOptionsVariations",additionalInfoToLog:{tag:"native-ecom"}}),q1=D.textarea(({styles:e,increaseSpecificity:t})=>Tn([pA,e],t)),pA={[h.COMMON]:{padding:"unset",margin:"unset",backgroundColor:"unset",border:"1px solid #000000",color:"#000000",minHeight:"unset",height:"unset",fontSize:"unset",resize:"none"}},hA=({error:e,dataAuto:t="field-error",tag:n=ae.paragraph,styles:r,...o})=>u.jsxs(Q,{"data-auto":t,tag:n,...o,styles:[mA,r],children:[u.jsx(dk,{size:16}),e]}),mA={[h.COMMON]:{display:"flex",alignItems:"center",gap:"6px",padding:"2px",color:"#DC1C1C",fontSize:"12px",margin:0}};function gA({text:e,dataGrab:t,dataAuto:n="field-label",styles:r,required:o,...i}){return u.jsxs(mf,{styles:[vA,r],"data-grab":t,"data-auto":n,...i,children:[e," ",o&&u.jsx(Z1,{children:"*"})]})}const vA={[h.COMMON]:{textTransform:"capitalize",margin:0,boxSizing:"border-box"}},Z1=D.span`
    color: #e33e3b;
`,J1=({valueLength:e,maxLength:t,dataGrab:n,styles:r,dataAuto:o="char-counter",tag:i=ae.paragraph})=>{const a=t!==void 0?`${e}/${t}`:`${e}`;return u.jsx(Q,{styles:[yA,r],"data-auto":o,"data-grab":n,tag:i,children:a})},yA={[h.COMMON]:{margin:0,boxSizing:"border-box"}},xA={[h.COMMON]:{boxSizing:"border-box"}},_A={[h.COMMON]:{display:"flex",alignItems:"center",justifyContent:"space-between",boxSizing:"border-box",direction:"inherit",marginBlockEnd:"8px"}},ey={[h.COMMON]:{width:"100%",padding:"8px",height:"86px",boxSizing:"border-box",borderRadius:"3px",border:"1px solid rgba(201, 202, 205, 1)"}},wA={[h.COMMON]:{fontFamily:"Open Sans",fontSize:"11px",fontWeight:"400",lineHeight:"22px",color:"rgba(130, 136, 148, 1)"}},CA={[h.COMMON]:{marginBlockStart:"4px"}};function bA({name:e,id:t=e,value:n="",label:r="",error:o=null,showCounter:i=!1,styles:a,dataGrabs:l,dataAuto:s="text-field",onChange:c,labelElement:p,charCounterElement:f,textareaElement:d,...y}){const w=Yt([wA,a==null?void 0:a.placeholder]),x=g=>{c==null||c(g.target.value,g)},O=r&&(p||u.jsx(gA,{text:r,styles:a==null?void 0:a.label,htmlFor:t,required:y.required,dataGrab:l==null?void 0:l.labelDataGrab})),v=i&&(f||u.jsx(J1,{valueLength:n.length,maxLength:y.maxLength,dataGrab:l==null?void 0:l.counterDataGrab,styles:a==null?void 0:a.counter,dataAuto:`${s}-counter`})),m=d||u.jsx(q1,{id:t,name:e,value:n,"data-grab":l==null?void 0:l.textareaDataGrab,styles:[ey,a==null?void 0:a.textarea,Fe(w,{innerSelector:"&::placeholder"})],increaseSpecificity:!0,onChange:x,...y});return u.jsxs(N,{"data-auto":s,styles:[xA,a==null?void 0:a.textFieldWrapper],"data-grab":l==null?void 0:l.textFieldWrapperDataGrab,children:[u.jsxs(N,{styles:_A,children:[O,v]}),m,!!o&&u.jsx(hA,{error:o,styles:CA})]})}const wr={textFieldWrapperDataGrab:"product-customizations-text-field",labelDataGrab:"product-customizations-text-field-label",textareaDataGrab:"product-customizations-text-field-textarea",textareaPlaceholderDataGrab:"product-customizations-text-field-placeholder",counterDataGrab:"product-customizations-text-field-counter"},SA=320,OA=({styles:e,label:t,required:n,valueLength:r,maxLength:o})=>u.jsxs(Q,{tag:ae.h5,styles:kA,children:[u.jsxs(Q,{tag:ae.span,styles:[MA,e.inputLabel],"data-grab":wr.labelDataGrab,"data-auto":"product-customizations-text-field-label",children:[t,n&&u.jsx(Z1,{children:"*"})]}),u.jsx(J1,{valueLength:r,maxLength:o,tag:ae.span,styles:[AA,e.inputCharacterCounter],dataGrab:wr.counterDataGrab,dataAuto:"product-customizations-text-field-counter"})]}),EA=e=>{var r,o;const{textareaStyles:t,placeholderStyles:n}=C.useMemo(()=>{var a,l;const i=Yt([ey,e.error?jA:{},Fe({[h.COMMON]:{opacity:0}},{innerSelector:"&::placeholder"}),(a=e.styles)==null?void 0:a.textarea]);return{textareaStyles:i,placeholderStyles:Yt([US(i,PA),NA,(l=e.styles)==null?void 0:l.placeholder])}},[(r=e.styles)==null?void 0:r.textarea,(o=e.styles)==null?void 0:o.placeholder,e.error]);return u.jsxs(TA,{className:"dmNewParagraph",children:[u.jsx(q1,{...e,styles:t}),!e.value&&u.jsx(Q,{styles:n,"data-grab":wr.textareaPlaceholderDataGrab,tag:ae.paragraph,children:e.placeholder})]})},kA={[h.COMMON]:{display:"flex",justifyContent:"space-between",margin:0,width:"100%"}},MA={[h.COMMON]:{margin:0}},AA={[h.COMMON]:{margin:0}},TA=D.div`
    height: 86px;
    position: relative;
    margin: 0;
    padding: 0;
`,jA={[h.COMMON]:{borderColor:"#E33E3B"}},Wr={position:"absolute",top:0,bottom:0,left:0,right:0,boxSizing:"border-box",pointerEvents:"none",margin:0,borderColor:"transparent",overflow:"hidden",color:"rgba(201, 202, 205, 1)"},NA={[h.COMMON]:Wr,[h.MOBILE]:Wr,[h.TABLET]:Wr,[h.DESKTOP]:Wr,[h.MOBILE_IMPLICIT]:Wr,[h.TABLET_IMPLICIT]:Wr},PA=new Set(["border","borderWidth","borderTopWidth","borderBottomWidth","borderLeftWidth","borderRightWidth","borderRadius","borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingInline","paddingBlock","paddingInlineStart","paddingInlineEnd","paddingBlockStart","paddingBlockEnd"]),IA=({id:e,label:t,hint:n,title:r,value:o="",mandatory:i=!1,maxLength:a=SA,styles:l,error:s,onChange:c,validateField:p})=>{const f=y=>{c(e,y.target.value)},d=()=>{p(e)};return u.jsx(bA,{label:t,placeholder:n,error:s,styles:{label:l==null?void 0:l.inputLabel,counter:l==null?void 0:l.inputCharacterCounter},dataAuto:"product-customizations-text-field",dataGrabs:wr,labelElement:u.jsx(OA,{styles:{inputLabel:l==null?void 0:l.inputLabel,inputCharacterCounter:l==null?void 0:l.inputCharacterCounter},required:i,label:t,valueLength:o.length,maxLength:a}),textareaElement:u.jsx(EA,{name:r,id:e,value:o,placeholder:n,required:i,maxLength:a,error:!!s,onBlur:d,styles:{textarea:l==null?void 0:l.input,placeholder:l==null?void 0:l.inputPlaceholder},"data-grab":wr==null?void 0:wr.textareaDataGrab,increaseSpecificity:!0,onChange:f})})},LA=C.memo(IA),{str:RA}=de,DA=({productState:{customizations:e},_styles:t})=>Rt.getBoolean("runtime.ssr.productCustomizations")?e.isEmpty()?et.isEditor?u.jsx("div",{"data-auto":"product-customizations-placeholder",children:RA("ui.runtimessr.productCustomizations.noCustomizations")}):u.jsx("div",{}):u.jsx(N,{styles:[BA,t==null?void 0:t.wrapper],"data-auto":"product-customizations","data-grab":"product-customizations",children:e.list.map(n=>$A(e,n,t))}):null;function $A(e,t,n){return C.createElement(LA,{...t,key:t.id,styles:n,value:e.values[t.id],error:e.errors[t.id],onChange:e.updateValue,validateField:e.validate})}const BA={[h.COMMON]:{display:"flex",flexDirection:"column",gap:"24px",width:"100%"}},zA=Ot({Comp:pa(DA),componentName:"ProductCustomizations",logProps:!0}),{str:dr}=de,FA=({option:e,styles:t})=>{const{id:n,name:r,tag_line:o,frequency:i,expiration_count:a,interval:l,plan_displayed_price:s}=e,c=a&&l&&a*l;return u.jsxs(u.Fragment,{children:[u.jsx(Q,{tag:ae.h4,"data-grab":"price-name",styles:[UA,t==null?void 0:t.name],children:r}),o&&u.jsx(Q,{tag:ae.h6,"data-grab":"price-tagLine",styles:[WA,t==null?void 0:t.tagLine],children:o}),u.jsxs(N,{children:[u.jsxs(Q,{tag:ae.h4,"data-grab":"price-and-frequency",styles:[VA,t==null?void 0:t.priceAndFrequency],children:[s,i&&u.jsxs(u.Fragment,{children:[" ","/"," ",l&&l>1?u.jsxs(u.Fragment,{children:[l," ",dr(`ui.runtimessr.productPriceOptions.frequencyPlural.${i.toLowerCase()}`)]}):u.jsx(u.Fragment,{children:dr(`ui.runtimessr.productPriceOptions.frequency.${i.toLowerCase()}`)})]})]}),n!==Li&&u.jsx(Q,{tag:ae.h6,"data-grab":"expiration",styles:[HA,t==null?void 0:t.expiration],children:c&&i?u.jsxs(u.Fragment,{children:[dr("ui.runtimessr.productPriceOptions.expiresAfter")," ",c," ",dr(`ui.runtimessr.productPriceOptions.frequency${c>1?"Plural":""}.${i.toLowerCase()}`)]}):dr("ui.runtimessr.productPriceOptions.autoRenew")}),!!e.tax_percentage&&u.jsxs(N,{styles:GA,"data-auto":"price-vat",children:[u.jsx(Q,{"data-auto":"vat-msg",children:dr("ui.runtimessr.productPrice.omnibus.vat",{vatPercentage:e.tax_percentage.toString()})}),u.jsx(Q,{"data-auto":"vat-desc",children:dr("ui.runtimessr.productPrice.omnibus.total.price.might.change")})]})]})]})},UA={[h.COMMON]:{paddingBlockEnd:4,margin:0}},WA={[h.COMMON]:{paddingBlockEnd:8,margin:0}},VA={[h.COMMON]:{paddingInlineEnd:8,margin:0,display:"inline"}},HA={[h.COMMON]:{margin:0,display:"inline"}},GA={[h.COMMON]:{fontSize:"13px",margin:"0",color:"#939393",lineHeight:"15px"}},KA=({productState:e,_styles:t})=>!e||!e.paymentPlanOptions.length?null:u.jsxs(N,{"data-auto":"price-options-widget",children:[u.jsx(Q,{styles:[YA,t==null?void 0:t.title],tag:ae.h4,grabId:"price-options-title",children:de.str("ui.runtimessr.productPriceOptions.title")}),u.jsx(N,{styles:QA,children:e.paymentPlanOptions.map(n=>{var r;return u.jsx(yf,{"data-grab":"price-option","data-auto":"price-option",value:n.id,selectedValue:((r=e.selectedPaymentPlan)==null?void 0:r.id)||"",label:u.jsx(FA,{option:n,styles:t}),labelTag:ae.div,onChange:()=>{e.selectPaymentPlanOption(n)},labelWrapperStyles:[XA,t==null?void 0:t.option],labelStyles:qA,inputStyles:t==null?void 0:t.input,inputIconStyles:t==null?void 0:t.inputIcon},n.id)})})]}),YA={[h.COMMON]:{paddingBottom:8,margin:0}},QA={[h.COMMON]:{display:"flex",flexFlow:"column",gap:16}},XA={[h.COMMON]:{border:"1px solid #CED6D9",borderRadius:3,padding:16,minHeight:67,gap:16}},qA={[h.COMMON]:{display:"block",textTransform:"none"}},ZA=Ot({Comp:pa(KA),componentName:"RuntimeProductPriceOptions",logProps:!0}),{str:JA}=de;function eT({productState:e,styles:t}){var a;const n=e.selectedVariation||e.productData.variations[0],r=n==null?void 0:n.lowest_prices;if(!r)return null;const o=((a=e.selectedPaymentPlan)==null?void 0:a.id)||Li,i=r.find(l=>(l.plan_id||Li)===o);return u.jsx(N,{styles:t,"data-grab":"product-lowest-price","data-auto":"product-lowest-price",children:i&&u.jsx(Q,{tag:ae.paragraph,"data-auto":`low_price_${i.displayed_price}`,styles:tT,children:JA("ui.runtimessr.productPrice.omnibus.last.price",{period:i.period.toString(),displayedPrice:i.displayed_price})})})}const tT={[h.COMMON]:{margin:0}},{str:Xh}=de;function nT({productState:e,styles:t}){var i,a;const n=e.selectedVariation||e.productData.variations[0],r=(i=n==null?void 0:n.tax_percentage)==null?void 0:i.toString();return((a=e.productData.is_tax_included)==null?void 0:a.toString())==="true"&&r?u.jsxs(N,{styles:[t,rT],"data-auto":"price-vat",children:[u.jsx(Q,{"data-auto":"vat-msg",children:Xh("ui.runtimessr.productPrice.omnibus.vat",{vatPercentage:r})}),u.jsx(Q,{"data-auto":"vat-desc",children:Xh("ui.runtimessr.productPrice.omnibus.total.price.might.change")})]}):null}const rT={[h.COMMON]:{marginBlockEnd:"8px"}},oT=({_styles:e,productState:t})=>{var i;const n=t==null?void 0:t.productData;if(!n)return null;const r=(i=t.selectedPaymentPlan)==null?void 0:i.frequency,o=n.displayed_compare_at;return u.jsxs(N,{"data-auto":"product-price-widget","data-grab":"product-price-frame",styles:e==null?void 0:e.frame,children:[u.jsx(Q,{tag:ae.h4,"data-grab":"product-compare-at-price",styles:[o?aT:iT,e==null?void 0:e.compareAtPrice],children:u.jsx(Lt,{styles:lT,children:n.displayed_compare_at})}),u.jsx(Q,{tag:ae.h4,"data-grab":"product-price",styles:[sT,e==null?void 0:e.price],children:n.displayed_price}),u.jsx(Q,{tag:ae.h4,"data-grab":"product-frequency",styles:[uT,e==null?void 0:e.frequency],children:r&&u.jsxs(u.Fragment,{children:["/",de.str(`ui.runtimessr.productPriceOptions.frequency.${r.toLowerCase()}`)]})}),u.jsx(nT,{productState:t,styles:[e==null?void 0:e.compareAtPrice,qh]}),o&&u.jsx(eT,{productState:t,styles:[e==null?void 0:e.compareAtPrice,qh]})]})},iT={[h.COMMON]:{display:"none"}},aT={[h.COMMON]:{display:"inline",paddingRight:8,margin:0}},lT={[h.COMMON]:{textDecoration:"line-through"}},sT={[h.COMMON]:{display:"inline",margin:0}},uT={[h.COMMON]:{display:"inline",margin:0}},qh={[h.COMMON]:{fontSize:"13px",margin:0},[h.DESKTOP]:{fontSize:"13px",margin:0}},cT=Ot({Comp:pa(oT),componentName:"RuntimeProductPrice",additionalInfoToLog:{tag:"native-ecom"}});var ty=(e=>(e.Slide="slide",e.Fade="fade",e))(ty||{});const _e={LAYOUT_1:"LAYOUT_1",LAYOUT_2:"LAYOUT_2",LAYOUT_3:"LAYOUT_3",LAYOUT_4:"LAYOUT_4",LAYOUT_5:"LAYOUT_5",LAYOUT_6:"LAYOUT_6",LAYOUT_3_B:"LAYOUT_3_B"};function dT(e){return e?Object.values(e).some(t=>t!=null):!1}const fT=e=>{const t=[pT,e.styles];return dT(e.linkFunctionalityDomAttributes)?u.jsx(h1,{...e,linkFunctionalityDomAttributes:e.linkFunctionalityDomAttributes,styles:t}):u.jsx(N,{...e,styles:t})},pT={[h.COMMON]:{width:"100%",height:"100%",position:"relative",display:"block",overflow:"hidden"}};var ti=(e=>(e.AS_ELEMENT="AS_ELEMENT",e.BACKGROUND="BACKGROUND",e.AS_CONTENT_ELEMENT="AS_CONTENT_ELEMENT",e))(ti||{});const hT=e=>{var m;const{styles:t,imageLayout:n="BACKGROUND",uuid:r,title:o,desc:i,button:a,media:l,linkDomAttributes:s,showButton:c,contentAnimationTypeCssClass:p,contentAnimationMode:f,imgCssObjectPositionValue:d,showButtonHoverEffect:y,onContentAnimationCompleted:w,outOFViewPort:x}=e,O=!p||p==="none"?"off":f??"off";if(!(l||o||i||s))return u.jsx(gf,{});const v=u.jsxs(N,{"data-grab":"slide-media-container",styles:[Jt.mediaContainer,n==="BACKGROUND"&&Jt.mediaContainerBg,t==null?void 0:t.mediaContainer],children:[l&&u.jsx(g1,{isHidden:x,dataGrab:"slide-media",styles:[Jt.media,n==="BACKGROUND"&&Jt.imageBg,{common:{objectPosition:d}},t==null?void 0:t.media],...l}),l&&u.jsx(N,{"data-grab":"slide-overlay",styles:[Jt.imgOverlay,t==null?void 0:t.overlay]})]});return u.jsxs(fT,{styles:t==null?void 0:t.container,linkFunctionalityDomAttributes:!c&&e.linkDomAttributes,"data-auto":`ssr-slide-${r}`,children:[n!=="AS_CONTENT_ELEMENT"&&v,u.jsxs(N,{className:`${O==="anim-active"?["animated",p].join(" "):""} d-ext-mediaSlider-slide__contentContainer`,"data-grab":"slideContentContainer",styles:[Jt.contentContainer,t==null?void 0:t.contentContainer,{common:{visibility:O==="anim-idle"?"hidden":"visible"}}],onAnimationEnd:w,children:[n==="AS_CONTENT_ELEMENT"&&v,o&&u.jsx(Q,{grabId:"title",styles:[Jt.title,t==null?void 0:t.title],tag:ae.h3,className:"d-ext-mediaSlider-contentContainer__title",children:o}),i&&u.jsx(Q,{className:"d-ext-mediaSlider-contentContainer__description",grabId:"description","data-auto":"desc",styles:[Fe(Jt.descOverrides,{innerSelector:"p"}),Jt.descContainer,t==null?void 0:t.desc],tag:ae.div,domAttrs:{dangerouslySetInnerHTML:{__html:i}}}),c&&a&&u.jsx(Bs,{styles:{...(t==null?void 0:t.button)??{},root:[Jt.buttonContainer,(m=t==null?void 0:t.button)==null?void 0:m.root]},linkFunctionalityDomAttributes:s,buttonContent:a,showHoverEffect:y,className:"d-ext-mediaSlider-contentContainer__button"})]})]})},Jt={mediaContainer:{common:{backgroundColor:"#eee",overflow:"hidden",position:"relative"}},mediaContainerBg:{common:{position:"absolute",left:0,bottom:0,top:0,right:0}},imageBg:{common:{width:"100%",height:"100%"}},media:{common:{objectFit:"cover",objectPosition:"center",display:"block",width:"100%",height:"100%"}},imgOverlay:{common:{position:"absolute",top:0,bottom:0,left:0,right:0}},contentElement:{common:{margin:0}},descOverrides:{common:{marginBlock:0}},descContainer:{common:{marginBlockEnd:24}},buttonContainer:{common:{minWidth:150,width:"auto",paddingInlineStart:20,paddingInlineEnd:20,margin:0}},contentContainer:{common:{display:"flex",visibility:"visible"}},title:{common:{margin:0,marginBlockEnd:8}}},Xl=Ot({Comp:hT,componentName:"SlideContent"}),Zh="#e1e3e7",Nu="rgba(0,0,0,0.3)",Ga="#CED6D9",mT=()=>{const e=Rt.getBoolean("runtime.ssr.slider.image.fillAvailableSpace.enabled",!1);return{[_e.LAYOUT_1]:{styles:{container:{[h.COMMON]:{width:"100%"}},slide_container:{[h.COMMON]:{width:"100%"}},slide_overlay:{[h.COMMON]:{backgroundColor:Nu}},slide_contentContainer:{[h.COMMON]:{position:"absolute",left:0,bottom:35,paddingBlockStart:35,paddingInline:24,top:60,right:0,alignItems:"center",flexDirection:"column",justifyContent:"center",textAlign:"center"},[h.MOBILE]:{top:0}},slide_title:{[h.COMMON]:{color:"white"}},slide_desc:{[h.COMMON]:{color:"white"}},pagination_container:{[h.COMMON]:{position:"absolute",bottom:24,width:"100%"}},pagination_buttonBullet:{[h.COMMON]:{backgroundColor:Ga}},pagination_buttonBulletActive:{[h.COMMON]:{backgroundColor:"currentColor",color:"white"}},pagination_buttonArrow:{[h.COMMON]:{color:"white"}}}},[_e.LAYOUT_2]:{paginationType:Wt.ARROWS,defaultSlotsInFrame:3,styles:{container:{[h.COMMON]:{paddingInline:59,position:"relative"},[h.MOBILE]:{paddingInline:15}},slidesContainer:{[h.MOBILE]:{height:"100%"}},slide_mediaContainer:{[h.COMMON]:{borderRadius:16}},slide_overlay:{[h.COMMON]:{backgroundColor:Nu}},slide_contentContainer:{[h.COMMON]:{position:"absolute",left:0,bottom:30,right:0,alignItems:"center",flexDirection:"column",textAlign:"center",paddingBlockStart:30,paddingInline:24}},slide_title:{[h.COMMON]:{color:"white"}},slide_desc:{[h.COMMON]:{color:"white"}},pagination_container:{[h.COMMON]:{position:"absolute",left:0,right:0,bottom:0,top:0}},pagination_buttonArrow:{[h.COMMON]:{color:"black"}},pagination_buttonBullet:{[h.COMMON]:{backgroundColor:Ga}},pagination_buttonBulletActive:{[h.COMMON]:{backgroundColor:"currentColor",color:"black"}}}},[_e.LAYOUT_3]:{paginationType:Wt.ARROWS,imageLayout:ti.AS_ELEMENT,styles:{container:{[h.COMMON]:{paddingInline:45,position:"relative"},[h.MOBILE]:{paddingInline:0,position:"relative"}},slide_container:{[h.COMMON]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"space-around"}},slide_mediaContainer:{[h.COMMON]:{borderRadius:"50%",aspectRatio:"1/1",flexBasis:"39%"}},slide_contentContainer:{[h.COMMON]:{flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center",paddingInline:32}},pagination_container:{[h.COMMON]:{position:"absolute",left:28,right:28,bottom:0,top:0},[h.MOBILE]:{left:0,right:0}},pagination_buttonBullet:{[h.COMMON]:{backgroundColor:Ga}},pagination_buttonBulletActive:{[h.COMMON]:{backgroundColor:"currentColor",color:"black"}}}},[_e.LAYOUT_3_B]:{paginationType:Wt.ARROWS,imageLayout:ti.AS_CONTENT_ELEMENT,styles:{container:{[h.COMMON]:{paddingInline:45,position:"relative",paddingTop:32},[h.MOBILE]:{paddingInline:0,position:"relative"}},slide_mediaContainer:{[h.COMMON]:{borderRadius:"50%",aspectRatio:"1/1",position:"relative",flexBasis:"39%",flexShrink:0,order:-1,":empty":{display:"none"},marginBottom:32}},slide_media:{[h.COMMON]:{position:"absolute",bottom:0,top:0,left:0,right:0}},slide_contentContainer:{[h.COMMON]:{flexDirection:"column",alignItems:"center",textAlign:"center",paddingInline:32,height:"100%",justifyContent:"start"}},slide_title:{common:{marginBottom:0}},pagination_container:{[h.COMMON]:{position:"absolute",left:28,right:28,bottom:0,top:0},[h.MOBILE]:{left:0,right:0}},slide_button:{root:{[h.COMMON]:{marginTop:0}}},pagination_buttonBullet:{[h.COMMON]:{backgroundColor:Ga}},pagination_buttonBulletActive:{[h.COMMON]:{backgroundColor:"currentColor",color:"black"}}}},[_e.LAYOUT_4]:{paginationType:Wt.THUMBS,strictSlotsInFrame:1,styles:{container:{[h.COMMON]:{display:"flex",flexDirection:"column",gap:20,alignItems:"stretch"}},slide_overlay:{[h.COMMON]:{backgroundColor:Nu}},slide_contentContainer:{[h.COMMON]:{position:"absolute",left:0,bottom:0,top:0,right:0,alignItems:"center",flexDirection:"column",justifyContent:"center",padding:20,textAlign:"center"}},slide_title:{[h.COMMON]:{color:"white"}},slide_desc:{[h.COMMON]:{color:"white"}},pagination_thumbsContainer:{[h.MOBILE]:{overflowX:"auto",justifyContent:"start"}},pagination_buttonThumb:{[h.COMMON]:{flexBasis:80,flexGrow:0,flexShrink:1,width:"revert",borderWidth:4,backgroundOrigin:"border-box"},[h.MOBILE]:{flexBasis:80,flexShrink:0}},pagination_buttonThumbActive:{[h.COMMON]:{borderColor:"#000"}},pagination_buttonArrow:{[h.COMMON]:{color:"white"}}}},[_e.LAYOUT_5]:{imageLayout:ti.AS_ELEMENT,defaultSlotsInFrame:3,styles:{slide_mediaContainer:{[h.COMMON]:{...e?{flex:1}:{height:"46%",minHeight:"46%"},borderRadius:16,marginBlockEnd:24}},slide_contentContainer:{[h.COMMON]:{flexDirection:"column",justifyContent:"flex-start",textAlign:"center",alignItems:"center"},[h.MOBILE]:{alignItems:"center",textAlign:"center",paddingBlock:0,paddingInline:24}},container:{[h.COMMON]:{display:"flex",flexDirection:"column"}},...e&&{slide_container:{[h.COMMON]:{display:"flex",flexDirection:"column"}},slidesContainer:{[h.COMMON]:{paddingBlockEnd:56}}},slide_media:{[h.COMMON]:{width:"100%"}},pagination_buttonBullet:{[h.COMMON]:{backgroundColor:Zh}},pagination_buttonBulletActive:{[h.COMMON]:{backgroundColor:"currentColor",color:"black"}},pagination_container:{[h.COMMON]:{marginBlockEnd:24,position:"absolute",width:"100%",bottom:0}},pagination_buttonArrow:{[h.COMMON]:{color:"black"}}}},[_e.LAYOUT_6]:{imageLayout:ti.AS_ELEMENT,slideAnimationDirection:"vertical",strictSlotsInFrame:1,styles:{container:{[h.COMMON]:{display:"flex",flexDirection:"row-reverse",gap:24},[h.MOBILE]:{flexDirection:"column"}},pagination_container:{[h.COMMON]:{order:0,flexDirection:"column",paddingInlineStart:2},[h.MOBILE]:{flexDirection:"row",paddingInlineStart:0,paddingBlockEnd:2}},pagination_buttonBullet:{[h.COMMON]:{backgroundColor:Zh}},pagination_buttonBulletActive:{[h.COMMON]:{backgroundColor:"currentColor",color:"black"}},slidesContainer:{[h.COMMON]:{flexGrow:1}},slide_container:{[h.COMMON]:{display:"flex",gap:32},[h.MOBILE]:{flexDirection:"column",gap:24}},slide_contentContainer:{[h.COMMON]:{paddingBlock:24,flexDirection:"column",width:"61%",justifyContent:"center",alignItems:"flex-start",textAlign:"left"},[h.MOBILE]:{width:"auto",alignItems:"center",textAlign:"center",flex:"revert",paddingBlock:0,paddingInline:24}},slide_mediaContainer:{[h.COMMON]:{width:"37%",borderRadius:16},[h.MOBILE]:{width:"auto",flex:"revert",flexBasis:"55%"}}}}}},gT={[Wt.ARROWS]:{[_e.LAYOUT_1]:{styles:{pagination_container:{[h.COMMON]:{position:"absolute",left:0,right:0,bottom:0,top:0}}}},[_e.LAYOUT_2]:{styles:{container:{[h.MOBILE]:{paddingInline:48}},slidesContainer:{[h.MOBILE]:{overflow:"hidden"}},pagination_buttonArrow:{[h.COMMON]:{padding:16},[h.MOBILE]:{padding:16}}}},[_e.LAYOUT_3]:{styles:{pagination_buttonArrow:{[h.COMMON]:{padding:16}}}},[_e.LAYOUT_3_B]:{styles:{pagination_buttonArrow:{[h.COMMON]:{padding:16}}}},[_e.LAYOUT_5]:{styles:{container:{[h.COMMON]:{paddingInline:59,position:"relative",display:"block"},[h.MOBILE]:{paddingInline:50,display:"block"}},slidesContainer:{[h.MOBILE]:{overflow:"hidden"}},slide_mediaContainer:{[h.COMMON]:{height:"57%",minHeight:"57%"},[h.MOBILE]:{height:"59.4%",minHeight:"59.4%"}},pagination_container:{[h.COMMON]:{position:"absolute",left:0,right:0,bottom:0,top:0}},pagination_buttonArrow:{[h.COMMON]:{paddingInline:16}}}},[_e.LAYOUT_6]:{styles:{container:{[h.COMMON]:{display:"flex",flexDirection:"row-reverse",gap:24},[h.MOBILE]:{paddingInline:50,display:"block"}},pagination_container:{[h.COMMON]:{order:0,flexDirection:"column",paddingInlineStart:2},[h.MOBILE]:{position:"absolute",left:0,right:0,top:83}},pagination_buttonArrow:{[h.COMMON]:{transform:"rotate(90deg)"},[h.MOBILE]:{transform:"rotate(0)"}}}}},[Wt.BULLETS]:{[_e.LAYOUT_2]:{styles:{container:{[h.COMMON]:{paddingInline:0,position:"relative"},[h.MOBILE]:{paddingInline:0}},pagination_container:{[h.COMMON]:{position:"absolute",bottom:24,width:"100%",top:"auto"}},slidesContainer:{[h.COMMON]:{height:"87%"},[h.MOBILE]:{height:"87%"}}}},[_e.LAYOUT_3]:{styles:{container:{[h.COMMON]:{paddingInline:0,position:"relative"},[h.MOBILE]:{paddingInline:0}},pagination_container:{[h.COMMON]:{position:"absolute",bottom:24,left:0,right:0,width:"100%",top:"auto"}}}},[_e.LAYOUT_3_B]:{styles:{container:{[h.COMMON]:{paddingTop:0},[h.MOBILE]:{}},pagination_container:{[h.COMMON]:{position:"absolute",bottom:24,width:"100%",top:"auto",left:0,right:0}}}}},[Wt.ARROWS_AND_THUMBS]:{[_e.LAYOUT_4]:{styles:{container:{[h.COMMON]:{'& > [data-auto="pagination-arrows"]':{position:"absolute",left:0,right:0,bottom:0,top:-106}}}}}}},vT=()=>Rt.getBoolean("runtime.ssr.ssrSlider.multiplePaginationPerLayout.enabled",!1)?gT:{},Jn={getContainer:e=>({[h.COMMON]:{width:"100%",height:"100%"},[h.MOBILE]:e<3?{}:{position:"absolute",left:"-75%",width:"250%"}}),filmRole:{[h.COMMON]:{display:"flex"}},slotParent:{[h.COMMON]:{flex:"1",position:"relative"}},getSlideSlot:e=>{const t=e>1,n=t?"3%":0,r=t?"2%":0;return{[h.COMMON]:{position:"absolute",top:0,bottom:0,left:n,right:n},[h.MOBILE]:{left:r,right:r}}}},yT=e=>{const t=Rt.getBoolean("runtime.ssr.slider.image.fillAvailableSpace.enabled",!1);return{[Wt.BULLETS]:{[_e.LAYOUT_2]:{container:{[h.MOBILE]:e<3?{}:{position:"relative"}}},...t&&{[_e.LAYOUT_5]:{container:{[h.MOBILE]:{position:"relative"}}}}},[Wt.ARROWS]:{[_e.LAYOUT_2]:{container:{[h.MOBILE]:{width:`${100*e}%`,height:"100%",position:"relative",left:"unset"}}},[_e.LAYOUT_5]:{container:{[h.MOBILE]:{width:`${100*e}%`,height:"100%",position:"relative",left:"unset"}}}}}},Of=({paginationType:e,slotsInFrame:t,layout:n})=>{var o,i;return!Rt.getBoolean("runtime.ssr.ssrSlider.multiplePaginationPerLayout.enabled",!1)||!e?{}:(i=(o=yT(t))==null?void 0:o[e])==null?void 0:i[n]},xT=({slideAnimationProps:{slotsInFrame:e,cursor:t,slideAnimationDirection:n="horizontal",layout:r,paginationType:o},slideProps:i,slidesData:a})=>{const l=Rt.getBoolean("runtime.ssr.slider.alternative.animation.to.reduce.cls.enabled",!1),s=n==="horizontal";let c,p,f;c=[...a];for(let M=0;M<e-1;M++)c.push(a[M%a.length]);p=t,f=t;const d=c.length,y=l?d-e:d-e-f,w=`-${f*100/e}%`,x=s?"left":"top",O=`-${y*100/e}%`,v=s?"right":"bottom",m=100/d*t,g=new Array(e).fill(null).map((M,B)=>f+B),[_,S]=C.useState(g),E=Of({paginationType:o,layout:r,slotsInFrame:e}),T={[h.COMMON]:{position:"absolute",left:0,top:0,flexDirection:s?"row":"column",...l?{right:s?O:0,bottom:s?0:O,transform:`translate${s?"X":"Y"}(-${m}%)`,transition:"transform 1s ease-in-out"}:{right:0,bottom:0,[x]:w,[v]:O,transition:"left, top, right, bottom",transitionDuration:"1s",transitionTimingFunction:"ease-in-out"}}};return u.jsx(N,{styles:[Jn.getContainer(e),{[h.COMMON]:{position:"relative",overflow:"hidden"},[h.MOBILE]:{width:e<3?`${100*e}%`:`${250/3*e}%`}},E==null?void 0:E.container],children:u.jsx(N,{styles:[Jn.filmRole,T],onTransitionEnd:()=>S(g),"data-auto":"slider-filmRole",children:c.map((M,B)=>u.jsx(N,{styles:[{[h.COMMON]:{position:"relative",flex:1}}],"data-auto":`slideSlot ${B}${p===B?" slideSlotActive":""}`,className:`d-ext-mediaSlider-slidesContainer__slide${p===B?"--active":""}`,children:u.jsx(N,{styles:[Jn.getSlideSlot(e)],children:u.jsx(Xl,{...i,...M,contentAnimationMode:_.includes(B)?"anim-active":"anim-idle",outOFViewPort:!_.includes(B)})})},B))})})},_T=({slidesData:e})=>{const{elementRef:t,isVisible:n}=H1();return u.jsx(N,{ref:t,styles:{common:{position:"absolute"}},children:u.jsx(N,{styles:wT,children:n&&e.map(r=>{var o;return((o=r.media)==null?void 0:o.imgSrc)&&u.jsx(Di,{src:r.media.imgSrc},r.uuid)})})})},wT={[h.COMMON]:{display:"none"}},CT=({onAnimationEnd:e,isTransitioning:t,currentSlideData:n,previousSlideData:r,slideProps:o,shouldAnimateContent:i,styles:a})=>{const l=i?t?"anim-idle":"anim-active":"off";return u.jsxs(N,{styles:[{[h.COMMON]:{position:"relative"}},a],children:[t&&u.jsx(N,{styles:[Jh,ST],children:u.jsx(Xl,{...o,...r})},r.uuid),u.jsx(N,{"data-auto":"slideSlot",styles:[Jh,bT],onAnimationEnd:e,className:"d-ext-mediaSlider-slidesContainer__slide",children:u.jsx(Xl,{contentAnimationMode:l,...o,...n})},n.uuid)]})},Jh={[h.COMMON]:{position:"absolute",left:0,right:0,top:0,bottom:0}},bT={[h.COMMON]:{animationName:"fadeIn",animationDuration:"1s",opacity:1,animationTimingFunction:"ease-in"}},ST={[h.COMMON]:{opacity:0,transition:"opacity 1s ease-in"}};function OT(e,t){return(e%t+t)%t}function ET(){const e=C.useRef(!0);return e.current?(e.current=!1,!0):e.current}function kT(e,t){const n=ET();C.useEffect(()=>{if(!n)return e()},t)}function ny(e){return new Array(e).fill(null)}function em(e,t,n){return ny(t).map((r,o)=>OT(e+o,n))}function MT(){const[e,t]=C.useState(!1),n=C.useRef(!1);function r(o){t(o),n.current=o}return{isTransitiongRef:n,isTransitioning:e,setIsTransitioning:r}}const tm=({slideAnimationProps:{cursor:e,newEnters:t,slotsInFrame:n,layout:r,paginationType:o},slideProps:i,slidesData:a})=>{const[l,s]=C.useState(e),[c,p]=C.useState(e),{isTransitiongRef:f,isTransitioning:d,setIsTransitioning:y}=MT();kT(()=>{y(!0),s(e),p(l)},[e]);const w=em(l,n,a.length),x=em(c,n,a.length),O=ny(n),v=Of({paginationType:o,layout:r,slotsInFrame:n});return u.jsxs(N,{styles:[Jn.getContainer(n),Jn.filmRole,{common:{gap:"2%"},mobile:{gap:"1.25%"}},v==null?void 0:v.container],"data-auto":"fade-wrapper",children:[O.map((m,g)=>{const _=w[g],S=x[g],E=t>0&&g<t||t<0&&g-(n-1)>t;return u.jsx(CT,{currentSlideData:a[_],previousSlideData:a[S],shouldAnimateContent:E,onAnimationEnd:()=>{f.current&&y(!1)},slideProps:i,isTransitioning:d,styles:[{common:{flex:"1"}},AT(g,n)]},g)}),u.jsx(_T,{slidesData:a})]})};function AT(e,t){const n=t>2?3:1;return e<n?{}:{mobile:{display:"none"}}}const TT=({totalPages:e,selectedIndex:t=0,autoPagination:n})=>{const[r,o]=C.useState(t),[i,a]=C.useState(0),[l,s]=C.useState(!1),c=C.useRef(),{isInEditor:p}=nf();C.useEffect(()=>{o(t)},[t]);const f=()=>{o(x=>{const O=x+1;return O===e?0:O}),a(x=>x-1)},d=()=>{o(x=>{const O=x-1;return O===-1?e-1:O}),a(x=>x+1)},y=x=>{o(x),a(r-x)},w=()=>{n!=null&&n.on&&(clearInterval(c.current),c.current=Ie().setInterval(()=>{f()},n.intervalInSeconds*1e3))};return C.useEffect(()=>(l&&(p||n!=null&&n.pauseOnHover)||w(),()=>{clearInterval(c.current)}),[n,l]),{cursor:r,restartAutoPagination:w,newEnters:i,paginationAction:{goToCursor:y,goNext:f,goPrevious:d},onmouseenter:()=>{s(!0)},onmouseleave:()=>{s(!1)},onPageTransitionCompleted:()=>{a(0)}}},nm=Fe({common:{visibility:"hidden",opacity:"0"}},{innerSelector:'[data-grab="pagination-container"]'}),jT=[{common:{paddingInline:0}},Fe({common:{display:"none"}},{innerSelector:'[data-grab="pagination-container"]'})],NT=Fe({common:{visibility:"visible",opacity:"1",transition:"opacity 300ms ease"}},{pseudoSelector:"hover",innerSelector:'[data-grab="pagination-container"]'}),PT=e=>{const t=[];switch(e){case"onHover":{t.push(nm,NT);break}case"never":{t.push(nm,jT);break}}return t},IT="https://static-cdn.multiscreensite.com",LT=40,RT=({onSwipeForward:e,onSwipeBack:t})=>{const n=C.useRef(0);return{handleTouchStart:i=>{n.current=i.changedTouches[0].screenX},handleTouchEnd:i=>{const a=i.changedTouches[0].screenX;Math.abs(a-n.current)<=LT||(a>n.current?e():t())}}},ry=e=>{const t=C.useRef(e),n=C.useRef();return t.current!==e&&(n.current=t.current,t.current=e),n.current},DT=e=>({setStyle:r=>{e.current!==null&&Object.assign(e.current.style,r)},resetStyle:r=>{e.current!==null&&r.forEach(o=>{Object.assign(e.current.style,{[o]:null})})}}),$T=({wrapDirection:e,numberOfSlides:t,currentSlideIndex:n,lastSlideIndex:r,slidesInFrame:o})=>{let i="",a="";return e==="toLast"?(i="-0%",a=`-${(t+1)*100}%`):e==="toFirst"?(i=`-${(t+1)*100}%`,a="0%"):(i=`-${n*100/o}%`,a=`-${r*100/o}%`),{startPosition:i,endPosition:a}},BT=(e,t)=>{const n=ry(e),[r,o]=C.useState();return C.useEffect(()=>{n===0&&e===t.length-1&&o("toLast"),n===t.length-1&&e===0&&o("toFirst")},[n,e,t.length]),{wrapDirection:r,resetWrapDirection:()=>{o(void 0)}}},zT=({slideAnimationProps:{slotsInFrame:e,cursor:t,slideAnimationDirection:n="horizontal",layout:r,paginationType:o},slideProps:i,slidesData:a})=>{const l=C.useRef(null),{setStyle:s,resetStyle:c}=DT(l),p=ry(t),{wrapDirection:f,resetWrapDirection:d}=BT(t,a);C.useEffect(()=>{l.current&&!f&&c(["transitionDuration","left","right"])},[t,p,l.current,f]);const y=n==="horizontal";let w,x;w=[a[a.length-1],...a,a[0]],x=t+1;const O=a.length,v=w.length-e-x,{startPosition:m,endPosition:g}=$T({wrapDirection:f,numberOfSlides:O,currentSlideIndex:x,lastSlideIndex:v,slidesInFrame:e}),_=y?"left":"top",S=y?"right":"bottom",E=new Array(e).fill(null).map((Z,ee)=>x+ee),[T,M]=C.useState(E),B=Z=>{(Z.propertyName==="left"||Z.propertyName==="up")&&f&&l.current&&(s({transitionDuration:"0ms",[f==="toLast"?"left":"right"]:`-${O*100}%`,[f==="toLast"?"right":"left"]:"-100%"}),setTimeout(()=>{d()},0)),M(E)},z=Of({paginationType:o,layout:r,slotsInFrame:e});return u.jsx(N,{styles:[Jn.getContainer(e),{[h.COMMON]:{position:"relative",overflow:"hidden"},mobile:{width:e<3?`${100*e}%`:`${250/3*e}%`}},z==null?void 0:z.container],children:u.jsx(N,{ref:l,styles:[Jn.filmRole,{[h.COMMON]:{position:"absolute",left:0,top:0,right:0,bottom:0,flexDirection:y?"row":"column",[_]:m,[S]:g,transition:"left, top, right, bottom",transitionDuration:"1s",transitionTimingFunction:"ease-in-out"}}],onTransitionEnd:B,"data-auto":"slider-filmRole",children:w.map((Z,ee)=>u.jsx(N,{styles:[{[h.COMMON]:{position:"relative",flex:1}}],"data-auto":`slideSlot ${ee}${x===ee?" slideSlotActive":""}`,className:`d-ext-mediaSlider-slidesContainer__slide${x===ee?"--active":""}`,children:u.jsx(N,{styles:[Jn.getSlideSlot(e)],children:u.jsx(Xl,{...i,...Z,contentAnimationMode:T.includes(ee)?"anim-active":"anim-idle",outOFViewPort:!T.includes(ee)})})},ee))})})},FT=({animationType:e,props:t})=>{switch(e){case"fade":return u.jsx(tm,{...t});case"slide":return t.slideAnimationProps.slotsInFrame===1&&t.slideAnimationProps.layout==="LAYOUT_3_B"?u.jsx(zT,{...t}):u.jsx(xT,{...t});default:return u.jsx(tm,{...t})}},oy=({slidesData:e,styles:t,paginationType:n,animationType:r=ty.Slide,slotsInFrame:o,selectedIndex:i,autoPagination:a,dataAuto:l,paginationShow:s,arrowStyle:c,layout:p,...f})=>{const{onmouseenter:d,onmouseleave:y,paginationAction:w,cursor:x,newEnters:O,onPageTransitionCompleted:v,restartAutoPagination:m}=TT({selectedIndex:i,totalPages:e.length,autoPagination:a}),{handleTouchStart:g,handleTouchEnd:_}=RT({onSwipeForward:()=>{w.goPrevious(),m()},onSwipeBack:()=>{w.goNext(),m()}}),S={slideAnimationProps:{cursor:x,slotsInFrame:o,newEnters:O,layout:p,paginationType:n,...f},slideProps:{styles:t.slide,onContentAnimationCompleted:v,...f},slidesData:e};return u.jsxs(N,{styles:[UT,t==null?void 0:t.container,PT(s)],onMouseEnter:d,onMouseLeave:y,"data-auto":l,children:[u.jsx(N,{"data-auto":"slider-slides-container",onTouchStart:g,onTouchEnd:_,styles:[WT,t.slidesContainer],children:FT({animationType:r,props:S})}),u.jsx(QE,{arrowStyle:c,type:n,styles:t==null?void 0:t.pagination,cursor:x,totalPages:e==null?void 0:e.length,...w,thumbList:e.map(E=>E.media??{imgSrc:`${IT}/runtime/ssr-slider/no-image-pagination-thumb.png`})})]})},UT={[h.COMMON]:{height:"100%",alignItems:"center",backgroundRepeat:"no-repeat",backgroundSize:"100%",position:"relative"}},WT={[h.COMMON]:{height:"100%",width:"100%",boxSizing:"border-box"}},VT=["styles","animationType","imageLayout","paginationType","slideAnimationDirection","arrowStyle"],HT=e=>{const t=RS(e,VT),{styles:n}=t;return u.jsx(oy,{layout:_e.LAYOUT_1,slidesData:[{showButton:!0,button:{text:"a"},uuid:"a",title:"a",desc:"a",media:{imgSrc:"https://du-cdn.cdn-website.com/duda_website/images/home/<USER>",alt:"a"},linkDomAttributes:{type:"url",href:"https://www.duda.co/"}}],...t,styles:{...n,container:[n.container,GT]},slotsInFrame:1})},GT={[h.COMMON]:{display:"none"}},KT={selectedIndex:0,autoPaginationInterval:3e3,transitionDuration:1,transitionType:S1.slideFromRight,shouldShowImages:!0,shouldShowPaginationOnHover:!1,shouldEnlargeImageOnClick:!0,layout:_e.LAYOUT_1},YT=e=>{var g,_,S,E,T,M,B,z,Z,ee,ce,Me,Oe,tt,Ne,j,F,U,ne,I;const t={...KT,...e},{layout:n,_styles:r,slidesData:o,bindingSource:i,slotsInFrame:a,paginationType:l,...s}=t,c=mT()[n],p=l??c.paginationType??Wt.BULLETS,{styles:f}=c,{styles:d}=((g=vT()[p])==null?void 0:g[n])??{},y=(_=r==null?void 0:r.layoutSpecificStyles)==null?void 0:_[n],w={container:[f.container,d==null?void 0:d.container,r==null?void 0:r.container,y==null?void 0:y.container],slidesContainer:[f.slidesContainer,d==null?void 0:d.slidesContainer],slide:{container:[f.slide_container,d==null?void 0:d.slide_container,r==null?void 0:r.slide_container,y==null?void 0:y.slide_container],button:{root:[(S=f.slide_button)==null?void 0:S.root,(E=d==null?void 0:d.slide_button)==null?void 0:E.root,(T=r==null?void 0:r.slide_button)==null?void 0:T.root,(M=y==null?void 0:y.slide_button)==null?void 0:M.root],text:[(B=f.slide_button)==null?void 0:B.text,(z=d==null?void 0:d.slide_button)==null?void 0:z.text,(Z=r==null?void 0:r.slide_button)==null?void 0:Z.text,(ee=y==null?void 0:y.slide_button)==null?void 0:ee.text],rootHover:[(ce=f.slide_button)==null?void 0:ce.rootHover,(Me=d==null?void 0:d.slide_button)==null?void 0:Me.rootHover,(Oe=r==null?void 0:r.slide_button)==null?void 0:Oe.rootHover,(tt=y==null?void 0:y.slide_button)==null?void 0:tt.rootHover],rootHoverText:[(Ne=f.slide_button)==null?void 0:Ne.rootHoverText,(j=d==null?void 0:d.slide_button)==null?void 0:j.rootHoverText,(F=r==null?void 0:r.slide_button)==null?void 0:F.rootHoverText,(U=y==null?void 0:y.slide_button)==null?void 0:U.rootHoverText],buttonTypeCSSClass:(ne=r==null?void 0:r.slide_button)==null?void 0:ne.buttonTypeCSSClass},title:[f.slide_title,d==null?void 0:d.slide_title,r==null?void 0:r.slide_title,y==null?void 0:y.slide_title],mediaContainer:[f.slide_mediaContainer,d==null?void 0:d.slide_mediaContainer,r==null?void 0:r.slide_mediaContainer,y==null?void 0:y.slide_mediaContainer],contentContainer:[f.slide_contentContainer,d==null?void 0:d.slide_contentContainer,r==null?void 0:r.slide_contentContainer,y==null?void 0:y.slide_contentContainer],desc:[f.slide_desc,d==null?void 0:d.slide_desc,r==null?void 0:r.slide_desc,y==null?void 0:y.slide_desc],media:[f.slide_media,d==null?void 0:d.slide_media,r==null?void 0:r.slide_media,y==null?void 0:y.slide_media],overlay:[f.slide_overlay,d==null?void 0:d.slide_overlay,r==null?void 0:r.slide_overlay,y==null?void 0:y.slide_overlay]},pagination:{container:[f.pagination_container,d==null?void 0:d.pagination_container,r==null?void 0:r.pagination_container,y==null?void 0:y.pagination_container],buttonArrow:[f.pagination_buttonArrow,d==null?void 0:d.pagination_buttonArrow,r==null?void 0:r.pagination_buttonArrow,y==null?void 0:y.pagination_buttonArrow],buttonBullet:[f.pagination_buttonBullet,d==null?void 0:d.pagination_buttonBullet,r==null?void 0:r.pagination_buttonBullet,y==null?void 0:y.pagination_buttonBullet],buttonBulletActive:[f.pagination_buttonBulletActive,d==null?void 0:d.pagination_buttonBulletActive,r==null?void 0:r.pagination_buttonBulletActive,y==null?void 0:y.pagination_buttonBulletActive],thumbsContainer:[f.pagination_thumbsContainer,d==null?void 0:d.pagination_thumbsContainer,r==null?void 0:r.pagination_thumbsContainer,y==null?void 0:y.pagination_thumbsContainer],buttonThumb:[f.pagination_buttonThumb,d==null?void 0:d.pagination_buttonThumb,r==null?void 0:r.pagination_buttonThumb,y==null?void 0:y.pagination_buttonThumb],buttonThumbActive:[f.pagination_buttonThumbActive,d==null?void 0:d.pagination_buttonThumbActive,r==null?void 0:r.pagination_buttonThumbActive,y==null?void 0:y.pagination_buttonThumbActive]}},[x,O]=C.useState();C.useEffect(()=>{if(i)return y1.listen("selected-image-changed",i,L=>O(L.detail.newIndex))},[i,O]);const v={...s,...c,styles:w,selectedIndex:x??s.selectedIndex,paginationType:p},m=c.strictSlotsInFrame??a??c.defaultSlotsInFrame??1;return u.jsxs(N,{style:{height:"100%",overflow:"hidden"},"data-auto":"slider-wrapper",className:"d-ext-mediaSlider-slidesContainer",children:[o.length===0?u.jsx(gf,{}):u.jsx(oy,{...v,layout:n,paginationShow:s.paginationShow,slidesData:o,autoPagination:s.autoPagination&&{...s.autoPagination,on:s.overrideAutoPaginationOn??((I=s.autoPagination)==null?void 0:I.on)},dataAuto:"actual-slider",slotsInFrame:m}),et.isEditor&&u.jsx(HT,{...v})]})},QT=Ot({Comp:YT,componentName:"RuntimeSlider",logProps:!0}),XT=[rt.Cart,rt.Breadcrumbs],qT={[rt.Slider]:QT,[rt.Cart]:A3,[rt.Breadcrumbs]:O1,[rt.AddToCart]:LE,[rt.OptionsVariations]:fA,[rt.RuntimeFilterSort]:tA,[rt.CollectionSearch]:vM,[rt.ProductPrice]:cT,[rt.ProductPriceOptions]:ZA,[rt.Accordion]:zO,[rt.CalBooking]:C3,[rt.ProductCustomizations]:zA},Ef=e=>qT[e],iy=(e,t,n)=>{var o;const r=F0.getWidgetStore(e);r.legacyWidget=XT.includes(n)||!!((o=r.model.styles)!=null&&o.rules),r.mergeModel(t),r.setInitialProps(t,e)};function ZT(e){const t=Ef(e.type);return!(t!=null&&t.skipHydration)}function kf(e){return`ssrWrap-${e}`}const rm=new Set,JT=(e,t)=>{var r;if(!Rt.getBoolean("runtime.ssr.log.showHydrationDiff",!1))return{};try{const o=e.replace(/<script\s+data-role="hydration"[^>]*>[\s\S]*?<\/script>/gi,""),i=((r=document.getElementById(kf(t)))==null?void 0:r.outerHTML)||"null";return{preHydration:o,postHydration:i}}catch(o){return{preHydration:e,postHydration:`Error while getting post-hydration markup: ${o}`}}},ej=(e,t,n,r,o)=>{if(!Rt.getBoolean("runtime.ssr.log.showHydrationErrors",!0)||rm.has(o))return;rm.add(o);const{Parameters:a}=Ie(),l=a==null?void 0:a.isInEditor,s={message:"SSR hydration mismatch between server and client render",error:t,errorInfo:n,widget:r,siteAlias:a==null?void 0:a.SiteAlias,isEditor:l,...JT(e,o)};l?je.debug(s):je.warn(s)};function tj({type:e,props:t,id:n,observer:r},{markupId:o=n}){t._styles=(t==null?void 0:t._styles)||{};const i=Ef(e);if(i&&n&&o&&document.getElementById(n)){r&&iy(n,t,e);const a=r?u.jsx(V0,{Component:i,widgetId:n}):u.jsx(i,{...t,widgetId:n}),l=document.getElementById(kf(n));if(l){const s=l.outerHTML;xv(l,a,{onRecoverableError:(c,p)=>ej(s,c,p,e,n)})}}}const nj=({type:e,props:t,id:n})=>{const r=Ef(e);if(r&&n&&document.getElementById(n)){iy(n,t,e);const o=document.getElementById(n);_v(o).render(u.jsx(V0,{Component:r,widgetId:n}))}},Vs=(e,{observer:t=!1,markupId:n=e.id,asyncHydration:r=!!window.requestIdleCallback}={})=>{const o=()=>{tj({...e,observer:t},{markupId:n})};r?requestIdleCallback(o):o()},ay=e=>{Vs(e,{observer:!0})},rj=(e=[])=>{e.forEach(t=>Vs(t))},oj=(e=[])=>{e.forEach(t=>ay(t))},ij=({id:e,type:t,props:n,model:r})=>{nj({id:e,type:t,props:n,model:r,observer:!0})},aj=e=>e.some(t=>ZT(t));function lj(e,t){let n;const r=e.id,o=document.getElementById(kf(r));if(!o){const a="ssr initiate widget - element not found";throw je.warn({msg:a,tags:["__new-runtime__"],widgetId:r}),new Error(a)}new IntersectionObserver(a=>{if(n)return;a.some(s=>s.isIntersecting)&&(Vs(e,{observer:t}),n=!0)}).observe(o)}window.SSRRuntime={RuntimeReactHelpers:{hydrate:Vs,hydrateObserverWidget:ay,hydrateAll:rj,hydrateAllObserverWidgets:oj,renderObserver:ij,shouldHydrateOnRuntime:aj,initiateWidget:lj}};var im,am;typeof window<"u"&&((am=(im=window==null?void 0:window.getDeferred)==null?void 0:im.call(window,"ssrLibrariesLoaded"))==null||am.resolve());export{cj as $,Ce as B,Ia as G,uj as Z,q as _,Xi as a,u as j,C as r};
//# sourceMappingURL=runtime-react.js.map
