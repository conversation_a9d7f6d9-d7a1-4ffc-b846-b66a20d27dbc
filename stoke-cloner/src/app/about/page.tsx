'use client';

import { ArrowLeft, Code, Database, Download, Globe, Layers, Shield, Zap } from 'lucide-react';
import Link from 'next/link';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <div className="container mx-auto px-6 py-8 pb-24">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-12">
            <Link
              href="/"
              className="inline-flex items-center text-orange-500 hover:text-orange-400 mb-6 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Stoke Cloner
            </Link>

            <div className="text-center">
              <h1 className="text-3xl font-bold mb-4">
                Technical Details
              </h1>
              <p className="text-xl text-slate-400">
                How Stoke Cloner works under the hood
              </p>
            </div>
          </div>

          {/* Architecture Overview */}
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8">
            <div className="flex items-center mb-6">
              <Layers className="w-8 h-8 text-orange-500 mr-3" />
              <h2 className="text-2xl font-bold text-white">
                System Architecture
              </h2>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Frontend Stack</h3>
                <ul className="space-y-2 text-slate-300">
                  <li><strong className="text-orange-400">Next.js 15:</strong> React framework with App Router</li>
                  <li><strong className="text-orange-400">TypeScript:</strong> Type-safe development</li>
                  <li><strong className="text-orange-400">Tailwind CSS:</strong> Utility-first styling</li>
                  <li><strong className="text-orange-400">Lucide React:</strong> Beautiful SVG icons</li>
                  <li><strong className="text-orange-400">Turbopack:</strong> Fast development builds</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Backend Stack</h3>
                <ul className="space-y-2 text-slate-300">
                  <li><strong className="text-orange-400">Next.js API Routes:</strong> Serverless functions</li>
                  <li><strong className="text-orange-400">Puppeteer:</strong> Headless Chrome automation</li>
                  <li><strong className="text-orange-400">Archiver:</strong> ZIP file generation</li>
                  <li><strong className="text-orange-400">Node.js Streams:</strong> Memory-efficient processing</li>
                  <li><strong className="text-orange-400">File System API:</strong> Temporary file management</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Core Features */}
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            {/* Web Scraping Engine */}
            <div className="bg-slate-800 rounded-xl border border-slate-700 p-6">
              <div className="flex items-center mb-4">
                <Globe className="w-6 h-6 text-orange-500 mr-3" />
                <h3 className="text-xl font-semibold text-white">
                  Web Scraping Engine
                </h3>
              </div>
              <ul className="space-y-2 text-sm text-slate-300">
                <li>• <strong className="text-orange-400">Puppeteer-based:</strong> Full browser rendering</li>
                <li>• <strong className="text-orange-400">Depth-first crawling:</strong> Configurable 1-3 levels</li>
                <li>• <strong className="text-orange-400">Smart asset discovery:</strong> CSS, JS, images, fonts</li>
                <li>• <strong className="text-orange-400">Background image extraction:</strong> From computed styles</li>
                <li>• <strong className="text-orange-400">Font detection:</strong> @font-face rule parsing</li>
                <li>• <strong>Assets-only mode:</strong> Extract only media and documents</li>
                <li>• <strong>Retry logic:</strong> 3 attempts with exponential backoff</li>
              </ul>
            </div>

            {/* Asset Processing */}
            <div className="bg-slate-800 rounded-xl border border-slate-700 p-6">
              <div className="flex items-center mb-4">
                <Code className="w-6 h-6 text-orange-500 mr-3" />
                <h3 className="text-xl font-semibold text-white">
                  Asset Localization
                </h3>
              </div>
              <ul className="space-y-2 text-sm text-slate-300">
                <li>• <strong className="text-orange-400">URL rewriting:</strong> Absolute to relative paths</li>
                <li>• <strong className="text-orange-400">CSS processing:</strong> url() and @import statements</li>
                <li>• <strong className="text-orange-400">Inline style handling:</strong> &lt;style&gt; tag processing</li>
                <li>• <strong className="text-orange-400">CDN localization:</strong> External asset downloading</li>
                <li>• <strong className="text-orange-400">Organized structure:</strong> assets/css/, js/, images/</li>
                <li>• <strong className="text-orange-400">Duplicate prevention:</strong> Asset deduplication</li>
                <li>• <strong className="text-orange-400">Smart navigation:</strong> Internal links converted for offline use</li>
                <li>• <strong className="text-orange-400">Markdown extraction:</strong> Clean text content for AI analysis</li>
              </ul>
            </div>

            {/* Performance & Safety */}
            <div className="bg-slate-800 rounded-xl border border-slate-700 p-6">
              <div className="flex items-center mb-4">
                <Zap className="w-6 h-6 text-orange-500 mr-3" />
                <h3 className="text-xl font-semibold text-white">
                  Performance Optimizations
                </h3>
              </div>
              <ul className="space-y-2 text-sm text-slate-300">
                <li>• <strong className="text-orange-400">Parallel downloads:</strong> Concurrent asset fetching</li>
                <li>• <strong className="text-orange-400">Memory streaming:</strong> Buffer-based processing</li>
                <li>• <strong className="text-orange-400">Temporary files:</strong> OS temp directory usage</li>
                <li>• <strong className="text-orange-400">Progress tracking:</strong> Real-time status updates</li>
                <li>• <strong className="text-orange-400">Timeout handling:</strong> 30s page, 10s asset limits</li>
                <li>• <strong className="text-orange-400">Resource cleanup:</strong> Automatic temp file removal</li>
              </ul>
            </div>

            {/* Security & Limits */}
            <div className="bg-slate-800 rounded-xl border border-slate-700 p-6">
              <div className="flex items-center mb-4">
                <Shield className="w-6 h-6 text-orange-500 mr-3" />
                <h3 className="text-xl font-semibold text-white">
                  Security & Limits
                </h3>
              </div>
              <ul className="space-y-2 text-sm text-slate-300">
                <li>• <strong className="text-orange-400">2GB size limit:</strong> Prevents resource exhaustion</li>
                <li>• <strong className="text-orange-400">100 page limit:</strong> Reasonable crawl boundaries</li>
                <li>• <strong className="text-orange-400">Same-origin policy:</strong> Domain-restricted crawling</li>
                <li>• <strong className="text-orange-400">User-Agent spoofing:</strong> Bypass basic bot detection</li>
                <li>• <strong className="text-orange-400">Input validation:</strong> URL and depth sanitization</li>
                <li>• <strong className="text-orange-400">Error isolation:</strong> Graceful failure handling</li>
              </ul>
            </div>
          </div>

          {/* Technical Implementation */}
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8">
            <div className="flex items-center mb-6">
              <Database className="w-8 h-8 text-orange-500 mr-3" />
              <h2 className="text-2xl font-bold text-white">
                Implementation Details
              </h2>
            </div>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  Crawling Algorithm
                </h3>
                <div className="bg-slate-700 rounded-lg p-4 font-mono text-sm">
                  <pre className="text-slate-200">
{`1. Initialize queue with start URL
2. While queue not empty:
   a. Dequeue URL
   b. Check visited set
   c. Launch Puppeteer page
   d. Extract HTML content
   e. Discover assets via DOM
   f. Download assets in parallel
   g. Extract internal links
   h. Add links to queue
   i. Update progress
3. Generate ZIP archive`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  Asset Discovery Methods
                </h3>
                <div className="space-y-3 text-sm text-slate-300">
                  <div>
                    <strong className="text-orange-400">CSS Files:</strong>
                    <code className="block bg-slate-700 p-2 mt-1 rounded text-slate-200">
                      document.querySelectorAll('link[rel="stylesheet"]')
                    </code>
                  </div>
                  <div>
                    <strong className="text-orange-400">JavaScript:</strong>
                    <code className="block bg-slate-700 p-2 mt-1 rounded text-slate-200">
                      document.querySelectorAll('script[src]')
                    </code>
                  </div>
                  <div>
                    <strong className="text-orange-400">Images:</strong>
                    <code className="block bg-slate-700 p-2 mt-1 rounded text-slate-200">
                      document.querySelectorAll('img[src]')
                    </code>
                  </div>
                  <div>
                    <strong className="text-orange-400">Background Images:</strong>
                    <code className="block bg-slate-700 p-2 mt-1 rounded text-slate-200">
                      window.getComputedStyle(el).backgroundImage
                    </code>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* File Structure */}
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8">
            <div className="flex items-center mb-6">
              <Download className="w-8 h-8 text-orange-500 mr-3" />
              <h2 className="text-2xl font-bold text-white">
                Generated ZIP Structure
              </h2>
            </div>

            <div className="bg-slate-700 rounded-lg p-6 font-mono text-sm">
              <pre className="text-slate-200">
{`website-clone-[timestamp].zip
├── README.md                    # Usage instructions
├── index.html                   # Main page
├── page2.html                   # Additional pages
├── assets/
│   ├── css/
│   │   ├── main.css            # Stylesheets
│   │   └── bootstrap.min.css
│   ├── js/
│   │   ├── app.js              # JavaScript files
│   │   └── jquery.min.js
│   ├── images/
│   │   ├── logo.png            # Images & graphics
│   │   └── background.jpg
│   └── fonts/
│       ├── font.woff2          # Web fonts
│       └── icons.ttf`}
              </pre>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-slate-800 border border-orange-500/20 rounded-xl p-8 mb-8">
            <h2 className="text-2xl font-bold text-white mb-6">
              Performance Characteristics
            </h2>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-500 mb-2">~15s</div>
                <div className="text-sm text-slate-300">
                  Average clone time<br />
                  <span className="text-xs text-slate-400">(single page with assets)</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-orange-500 mb-2">2GB</div>
                <div className="text-sm text-slate-300">
                  Maximum output size<br />
                  <span className="text-xs text-slate-400">(safety limit)</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-orange-500 mb-2">100</div>
                <div className="text-sm text-slate-300">
                  Maximum pages<br />
                  <span className="text-xs text-slate-400">(per crawl session)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Development & Deployment */}
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8">
            <h2 className="text-2xl font-bold text-white mb-6">
              Development & Deployment
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  Project Structure
                </h3>
                <div className="bg-slate-700 rounded-lg p-4 font-mono text-sm">
                  <pre className="text-slate-200">
{`stoke-cloner/
├── src/app/
│   ├── api/clone/
│   │   └── route.ts      # API endpoint
│   ├── about/
│   │   └── page.tsx      # This page
│   ├── page.tsx          # Main UI
│   ├── layout.tsx        # App layout
│   └── globals.css       # Global styles
├── package.json          # Dependencies
├── next.config.ts        # Next.js config
├── tailwind.config.ts    # Tailwind config
└── tsconfig.json         # TypeScript config`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  Key Dependencies
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="font-medium text-white">next</span>
                    <span className="text-sm text-slate-400">^15.5.3</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="font-medium text-white">puppeteer</span>
                    <span className="text-sm text-slate-400">^23.x</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="font-medium text-white">archiver</span>
                    <span className="text-sm text-slate-400">^7.x</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="font-medium text-white">tailwindcss</span>
                    <span className="text-sm text-slate-400">^3.x</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="font-medium text-white">lucide-react</span>
                    <span className="text-sm text-slate-400">^0.x</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="font-medium text-white">typescript</span>
                    <span className="text-sm text-slate-400">^5.x</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* API Specification */}
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-8">
            <h2 className="text-2xl font-bold text-white mb-6">
              API Specification
            </h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">
                  POST /api/clone
                </h3>
                <p className="text-slate-400 mb-4">
                  Clones a website and returns a ZIP file containing the offline copy.
                </p>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-white mb-2">Request Body</h4>
                    <div className="bg-slate-700 rounded-lg p-4 font-mono text-sm">
                      <pre className="text-slate-200">
{`{
  "url": "https://example.com",
  "depth": 1,
  "assetsOnly": false
}`}
                      </pre>
                    </div>
                    <ul className="mt-2 text-sm text-slate-400">
                      <li><strong className="text-orange-400">url:</strong> Target website URL (required)</li>
                      <li><strong className="text-orange-400">depth:</strong> Crawl depth 1-3 (required)</li>
                      <li><strong className="text-orange-400">assetsOnly:</strong> Extract only media/documents (optional, default: false)</li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-white mb-2">Response</h4>
                    <div className="bg-slate-700 rounded-lg p-4 font-mono text-sm">
                      <pre className="text-slate-200">
{`Content-Type: application/zip
Content-Disposition: attachment;
  filename="website-clone-[timestamp].zip"
Content-Length: [size]

[Binary ZIP data]`}
                      </pre>
                    </div>
                    <ul className="mt-2 text-sm text-slate-400">
                      <li><strong className="text-orange-400">200:</strong> Success - ZIP file returned</li>
                      <li><strong className="text-orange-400">400:</strong> Invalid URL or depth</li>
                      <li><strong className="text-orange-400">500:</strong> Server error</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
