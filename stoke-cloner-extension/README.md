# Stoke Cloner Chrome Extension

A Chrome extension version of the Stoke Cloner web application for one-click website cloning directly from your browser.

## Features

- 🚀 **One-click cloning** from any webpage
- ⚙️ **Configurable settings** (crawl depth 1-3, assets-only mode)
- 📱 **Clean popup interface** matching the web app design
- 🔄 **Real-time progress tracking** with visual feedback
- 💾 **Automatic downloads** via Chrome Downloads API
- 🌐 **Local server integration** for instant preview
- 🎯 **Context menu integration** for right-click cloning
- 💾 **Settings persistence** across browser sessions

## Installation

### Method 1: Load Unpacked (Development)

1. **Download** this extension folder to your computer
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable "Developer mode"** (toggle in top right corner)
4. **Click "Load unpacked"** and select this extension folder
5. **Pin the extension** to your toolbar for easy access

### Method 2: Chrome Web Store (Future)
*This extension will be published to the Chrome Web Store in the future.*

## Prerequisites

**Important**: This extension communicates with the local Stoke Cloner web application for full functionality.

1. **Download and run** the Stoke Cloner web app from: https://github.com/mvalencia464/stoke-cloner
2. **Start the development server**: `npm run dev`
3. **Ensure it's running** on `http://localhost:3000`

## Usage

### Basic Cloning
1. **Navigate** to any website you want to clone
2. **Click the Stoke Cloner extension icon** in your toolbar
3. **Configure settings** (depth, assets-only mode)
4. **Click "Clone Website"** and wait for completion
5. **Choose action**: Download ZIP or View Locally

### Context Menu
- **Right-click** on any webpage
- **Select "Clone this website with Stoke Cloner"**
- The extension popup will open automatically

### Settings
- **Crawl Depth**: 1-3 levels (higher = more complete but slower)
- **Assets Only**: Download only media files and text content
- Settings are automatically saved and restored

## Architecture

### Extension Components
- **`manifest.json`**: Extension configuration and permissions
- **`popup.html/css/js`**: Main interface (400x500px popup)
- **`background.js`**: Service worker for downloads and server communication
- **`content.js`**: Page analysis and content extraction
- **`icons/`**: Extension icons (16px, 32px, 48px, 128px)

### Communication Flow
1. **Popup** → **Background Script** → **Local Server** (localhost:3000)
2. **Server processes** website with Puppeteer crawling
3. **Background Script** → **Chrome Downloads API** → **User's Downloads**
4. **Optional**: Local server startup for instant preview

## Color Scheme

Maintains exact Stoke Cloner branding:
- **Background**: `#0f172a` (slate-900)
- **Cards/Inputs**: `#1e293b` (slate-800) 
- **Borders**: `#334155` (slate-700)
- **Text**: `#ffffff` (white)
- **Secondary**: `#94a3b8` (slate-400)
- **Accent**: `#f97316` (orange-500)
- **Hover**: `#ea580c` (orange-600)

## Permissions Explained

- **`activeTab`**: Access current webpage URL and title
- **`storage`**: Save user settings (depth, assets-only preference)
- **`downloads`**: Automatically download cloned website files
- **`scripting`**: Inject content scripts for page analysis
- **`contextMenus`**: Add right-click menu option
- **`host_permissions`**: Access websites and local server

## Troubleshooting

### "Cannot connect to Stoke Cloner server"
- Ensure the web app is running on `http://localhost:3000`
- Check that `npm run dev` is active in the web app directory
- Verify no firewall is blocking localhost connections

### Downloads not working
- Check Chrome's download settings
- Ensure popup blockers aren't interfering
- Verify Downloads permission is granted

### Extension not loading
- Check `chrome://extensions/` for error messages
- Ensure all files are present in the extension folder
- Try reloading the extension

## Development

### File Structure
```
stoke-cloner-extension/
├── manifest.json          # Extension configuration
├── popup.html            # Main interface HTML
├── popup.css             # Styling (matches web app)
├── popup.js              # Popup functionality
├── background.js         # Service worker
├── content.js            # Page analysis
├── icons/                # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md            # This file
```

### Testing
1. **Load extension** in Chrome
2. **Test on various websites** (static, dynamic, SPA)
3. **Verify download functionality** 
4. **Test progress tracking** and error handling
5. **Validate local server integration**

## Future Enhancements

- [ ] **Keyboard shortcuts** for quick cloning
- [ ] **Batch cloning** multiple tabs
- [ ] **Custom server URL** configuration
- [ ] **Export/import** settings
- [ ] **Clone history** and management
- [ ] **Advanced filtering** options
- [ ] **Chrome Web Store** publication

## License

Same license as the main Stoke Cloner project.

## Support

For issues and feature requests, please use the main project repository: https://github.com/mvalencia464/stoke-cloner
