# Quick Installation Guide

## Step 1: Create Icons (Required)

The extension needs icon files to work properly. You have two options:

### Option A: Generate Simple Icons
1. Open `create-icons.html` in your browser
2. Click "Generate Icons" 
3. Download all 4 icon files (icon16.png, icon32.png, icon48.png, icon128.png)
4. Save them in the `icons/` folder

### Option B: Create Custom Icons
Create 4 PNG files with these exact names in the `icons/` folder:
- `icon16.png` (16x16 pixels)
- `icon32.png` (32x32 pixels) 
- `icon48.png` (48x48 pixels)
- `icon128.png` (128x128 pixels)

## Step 2: Install Extension

1. **Open Chrome** and go to `chrome://extensions/`
2. **Enable "Developer mode"** (toggle in top-right corner)
3. **Click "Load unpacked"**
4. **Select this folder** (`stoke-cloner-extension`)
5. **Pin the extension** to your toolbar

## Step 3: Start Stoke Cloner Server

**Important**: The extension needs the web app running locally.

1. Navigate to your `stoke-cloner` web app folder
2. Run: `npm run dev`
3. Ensure it shows: `Local: http://localhost:3000`

## Step 4: Test the Extension

1. **Visit any website** (e.g., https://example.com)
2. **Click the Stoke Cloner extension icon** in your toolbar
3. **Click "Clone Website"**
4. **Wait for completion** and download the ZIP file

## Troubleshooting

### "Cannot connect to Stoke Cloner server"
- Make sure the web app is running on localhost:3000
- Check that `npm run dev` is active

### Extension won't load
- Ensure all icon files are present in `icons/` folder
- Check for errors in `chrome://extensions/`

### No download happening
- Check Chrome's download settings
- Look for blocked downloads in Chrome's address bar

## Success!

If everything works, you should see:
- ✅ Extension icon in toolbar
- ✅ Popup opens when clicked
- ✅ Current page URL auto-filled
- ✅ Clone button starts the process
- ✅ ZIP file downloads automatically
- ✅ "View Locally" opens the cloned site

You now have a fully functional Chrome extension version of Stoke Cloner!
