{"manifest_version": 3, "name": "Stoke Cloner", "version": "1.0.0", "description": "Clone any website and download it as a local copy. Easy-to-use tool for creating offline copies of websites.", "permissions": ["activeTab", "storage", "downloads", "scripting", "contextMenus"], "host_permissions": ["http://*/*", "https://*/*", "http://localhost:3000/*"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "Stoke Cloner", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["content.js"]}], "web_accessible_resources": [{"resources": ["injected.js"], "matches": ["http://*/*", "https://*/*"]}]}