{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Download, Globe, Settings, AlertCircle, CheckCircle, Loader2, Info, Eye } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface CloneProgress {\n  status: 'idle' | 'crawling' | 'downloading' | 'packaging' | 'complete' | 'error';\n  message: string;\n  progress: number;\n  downloadUrl?: string;\n  error?: string;\n  filename?: string;\n}\n\nexport default function Home() {\n  const [url, setUrl] = useState('');\n  const [depth, setDepth] = useState(1);\n  const [assetsOnly, setAssetsOnly] = useState(false);\n  const [progress, setProgress] = useState<CloneProgress>({\n    status: 'idle',\n    message: '',\n    progress: 0\n  });\n\n  // Handle URL parameter from bookmarklet\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const bookmarkUrl = urlParams.get('url');\n    if (bookmarkUrl) {\n      setUrl(decodeURIComponent(bookmarkUrl));\n      // Clear the URL parameter from the address bar\n      window.history.replaceState({}, document.title, window.location.pathname);\n    }\n  }, []);\n\n  const handleClone = async () => {\n    if (!url) return;\n\n    // Auto-add https:// if no protocol is specified\n    let processedUrl = url.trim();\n    if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {\n      processedUrl = 'https://' + processedUrl;\n    }\n\n    setProgress({ status: 'crawling', message: 'Starting website crawl...', progress: 10 });\n\n    try {\n      const response = await fetch('/api/clone', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url: processedUrl, depth, assetsOnly }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(errorText || 'Failed to clone website');\n      }\n\n      const blob = await response.blob();\n      const downloadUrl = URL.createObjectURL(blob);\n\n      // Generate filename for local viewing\n      const urlObj = new URL(url);\n      const filename = `${urlObj.hostname.replace(/[^a-zA-Z0-9]/g, '_')}_clone.zip`;\n\n      setProgress({\n        status: 'complete',\n        message: 'Website cloned successfully!',\n        progress: 100,\n        downloadUrl,\n        filename\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      setProgress({\n        status: 'error',\n        message: errorMessage,\n        progress: 0,\n        error: errorMessage\n      });\n    }\n  };\n\n  const handleDownload = () => {\n    if (progress.downloadUrl) {\n      const a = document.createElement('a');\n      a.href = progress.downloadUrl;\n      a.download = `website-clone-${new Date().getTime()}.zip`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(progress.downloadUrl);\n\n      // Auto-reset form after download\n      setTimeout(() => {\n        setUrl('');\n        setDepth(1);\n        setAssetsOnly(false);\n        setProgress({ status: 'idle', message: '', progress: 0 });\n      }, 1000); // Small delay to ensure download starts\n    }\n  };\n\n  const handleViewLocally = async () => {\n    if (!progress.downloadUrl || !progress.filename) return;\n\n    try {\n      // Show loading state\n      const originalButton = document.querySelector('[data-view-locally]') as HTMLButtonElement;\n      if (originalButton) {\n        originalButton.disabled = true;\n        originalButton.innerHTML = '<svg class=\"w-5 h-5 mr-2 animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\"><circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle><path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg>Starting Local Server...';\n      }\n\n      // Download the file first\n      const response = await fetch(progress.downloadUrl);\n      const blob = await response.blob();\n\n      // Create FormData to send the file to our local server endpoint\n      const formData = new FormData();\n      formData.append('file', blob, progress.filename);\n\n      // Send to our local server endpoint\n      const serverResponse = await fetch('/api/local-server', {\n        method: 'POST',\n        body: formData\n      });\n\n      if (serverResponse.ok) {\n        const { url: localUrl } = await serverResponse.json();\n        // Open the local server URL\n        window.open(localUrl, '_blank');\n\n        // Show success message\n        alert(`✅ Local server started!\\n\\nYour website is now running at: ${localUrl}\\n\\nThe page should open automatically in a new tab.`);\n      } else {\n        throw new Error('Failed to start local server');\n      }\n    } catch (error) {\n      console.error('Local server error:', error);\n\n      // Fallback to simple instructions\n      const simpleInstructions = `Sorry, automatic server setup failed. Here's the simple manual method:\n\n1. Download the ZIP file (button below)\n2. Extract it anywhere on your computer\n3. Double-click the \"index.html\" file\n\nNote: Some styling may not work perfectly, but the content will be visible.`;\n\n      if (confirm(simpleInstructions + '\\n\\nClick OK to download the ZIP file now.')) {\n        handleDownload();\n      }\n    } finally {\n      // Reset button state\n      const originalButton = document.querySelector('[data-view-locally]') as HTMLButtonElement;\n      if (originalButton) {\n        originalButton.disabled = false;\n        originalButton.innerHTML = '<svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"></path></svg>View Locally (One Click!)';\n      }\n    }\n  };\n\n  const resetProgress = () => {\n    setProgress({ status: 'idle', message: '', progress: 0 });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-6 py-8 pb-24\">\n        <div className=\"max-w-md mx-auto\">\n          {/* Header */}\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-3xl font-bold\">\n              Stoke Cloner\n            </h1>\n          </div>\n\n          {/* URL Input */}\n          <div className=\"mb-8\">\n            <label htmlFor=\"url\" className=\"block text-white text-base font-medium mb-3\">\n              Website URL\n            </label>\n            <input\n              type=\"url\"\n              id=\"url\"\n              value={url}\n              onChange={(e) => setUrl(e.target.value)}\n              placeholder=\"https://example.com\"\n              className=\"w-full px-4 py-4 bg-slate-800 border border-slate-700 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all\"\n              disabled={progress.status !== 'idle'}\n            />\n          </div>\n\n          {/* Crawling Depth */}\n          <div className=\"mb-8\">\n            <label className=\"block text-white text-base font-medium mb-4\">\n              Crawling Depth\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"range\"\n                min=\"1\"\n                max=\"3\"\n                value={depth}\n                onChange={(e) => setDepth(Number(e.target.value))}\n                className=\"w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider\"\n                disabled={progress.status !== 'idle'}\n              />\n              <div className=\"flex justify-between text-sm text-slate-400 mt-2\">\n                <span>1</span>\n                <span>2</span>\n                <span>3</span>\n              </div>\n            </div>\n            <p className=\"text-sm text-slate-400 mt-3\">\n              Crawling depth determines how many levels of links from the main page will be included in the clone. Higher depth means a more complete clone but may take longer.\n            </p>\n          </div>\n\n          {/* Assets Only */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-white text-base font-medium\">Assets Only</h3>\n                <p className=\"text-sm text-slate-400 mt-1\">\n                  Download only media files (images, videos, etc.)\n                </p>\n              </div>\n              <button\n                onClick={() => setAssetsOnly(!assetsOnly)}\n                disabled={progress.status !== 'idle'}\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-slate-900 ${\n                  assetsOnly ? 'bg-orange-500' : 'bg-slate-600'\n                }`}\n              >\n                <span\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                    assetsOnly ? 'translate-x-6' : 'translate-x-1'\n                  }`}\n                />\n              </button>\n            </div>\n          </div>\n\n          {/* Clone Button */}\n          <button\n            onClick={handleClone}\n            disabled={!url || progress.status !== 'idle'}\n            className=\"w-full bg-orange-500 hover:bg-orange-600 disabled:bg-slate-600 disabled:text-slate-400 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center text-lg\"\n          >\n            {progress.status === 'idle' ? (\n              'Clone Website'\n            ) : (\n              <>\n                <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n                Cloning...\n              </>\n            )}\n          </button>\n\n          {/* Progress Section */}\n          {progress.status !== 'idle' && (\n            <div className=\"mt-6 bg-slate-800 rounded-xl p-6 border border-slate-700\">\n              <div className=\"space-y-4\">\n                {/* Status Icon and Message */}\n                <div className=\"flex items-center\">\n                  {progress.status === 'error' ? (\n                    <AlertCircle className=\"w-6 h-6 text-red-400 mr-3\" />\n                  ) : progress.status === 'complete' ? (\n                    <CheckCircle className=\"w-6 h-6 text-green-400 mr-3\" />\n                  ) : (\n                    <Loader2 className=\"w-6 h-6 text-orange-500 mr-3 animate-spin\" />\n                  )}\n                  <span className=\"text-lg font-medium text-white\">\n                    {progress.message}\n                  </span>\n                </div>\n\n                {/* Progress Bar */}\n                {progress.status !== 'error' && (\n                  <div className=\"w-full bg-slate-700 rounded-full h-2\">\n                    <div\n                      className=\"bg-orange-500 h-2 rounded-full transition-all duration-300\"\n                      style={{ width: `${progress.progress}%` }}\n                    />\n                  </div>\n                )}\n\n                {/* Error Message */}\n                {progress.error && (\n                  <div className=\"bg-red-900/20 border border-red-800 rounded-lg p-4\">\n                    <p className=\"text-red-300\">{progress.error}</p>\n                  </div>\n                )}\n\n                {/* Download and View Buttons */}\n                {progress.status === 'complete' && progress.downloadUrl && (\n                  <div className=\"space-y-3\">\n                    <button\n                      onClick={handleViewLocally}\n                      data-view-locally\n                      className=\"w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center\"\n                    >\n                      <Eye className=\"w-5 h-5 mr-2\" />\n                      View Locally\n                    </button>\n                    <button\n                      onClick={handleDownload}\n                      className=\"w-full bg-slate-700 hover:bg-slate-600 text-white font-semibold py-4 px-6 rounded-xl transition-colors flex items-center justify-center\"\n                    >\n                      <Download className=\"w-5 h-5 mr-2\" />\n                      Download ZIP\n                    </button>\n                  </div>\n                )}\n\n                {/* Reset Button */}\n                {(progress.status === 'complete' || progress.status === 'error') && (\n                  <button\n                    onClick={resetProgress}\n                    className=\"w-full bg-slate-700 hover:bg-slate-600 text-white font-semibold py-4 px-6 rounded-xl transition-colors\"\n                  >\n                    Clone Another Website\n                  </button>\n                )}\n              </div>\n            </div>\n          )}\n\n\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"fixed bottom-0 left-0 right-0 bg-slate-800 border-t border-slate-700\">\n        <div className=\"text-center py-4\">\n          <Link href=\"/about\" className=\"text-slate-400 hover:text-orange-500 transition-colors text-sm\">\n            Technical Details\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAee,SAAS;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,iNAAQ,EAAC;IAC/B,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAgB;QACtD,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IAEA,wCAAwC;IACxC,IAAA,kNAAS,EAAC;QACR,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC5D,MAAM,cAAc,UAAU,GAAG,CAAC;QAClC,IAAI,aAAa;YACf,OAAO,mBAAmB;YAC1B,+CAA+C;YAC/C,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,SAAS,KAAK,EAAE,OAAO,QAAQ,CAAC,QAAQ;QAC1E;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI,CAAC,KAAK;QAEV,gDAAgD;QAChD,IAAI,eAAe,IAAI,IAAI;QAC3B,IAAI,CAAC,aAAa,UAAU,CAAC,cAAc,CAAC,aAAa,UAAU,CAAC,aAAa;YAC/E,eAAe,aAAa;QAC9B;QAEA,YAAY;YAAE,QAAQ;YAAY,SAAS;YAA6B,UAAU;QAAG;QAErF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,KAAK;oBAAc;oBAAO;gBAAW;YAC9D;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,aAAa;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,cAAc,IAAI,eAAe,CAAC;YAExC,sCAAsC;YACtC,MAAM,SAAS,IAAI,IAAI;YACvB,MAAM,WAAW,GAAG,OAAO,QAAQ,CAAC,OAAO,CAAC,iBAAiB,KAAK,UAAU,CAAC;YAE7E,YAAY;gBACV,QAAQ;gBACR,SAAS;gBACT,UAAU;gBACV;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBACV,QAAQ;gBACR,SAAS;gBACT,UAAU;gBACV,OAAO;YACT;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,SAAS,WAAW,EAAE;YACxB,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG,SAAS,WAAW;YAC7B,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE,IAAI,OAAO,OAAO,GAAG,IAAI,CAAC;YACxD,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC,SAAS,WAAW;YAExC,iCAAiC;YACjC,WAAW;gBACT,OAAO;gBACP,SAAS;gBACT,cAAc;gBACd,YAAY;oBAAE,QAAQ;oBAAQ,SAAS;oBAAI,UAAU;gBAAE;YACzD,GAAG,OAAO,wCAAwC;QACpD;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,QAAQ,EAAE;QAEjD,IAAI;YACF,qBAAqB;YACrB,MAAM,iBAAiB,SAAS,aAAa,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,eAAe,QAAQ,GAAG;gBAC1B,eAAe,SAAS,GAAG;YAC7B;YAEA,0BAA0B;YAC1B,MAAM,WAAW,MAAM,MAAM,SAAS,WAAW;YACjD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,gEAAgE;YAChE,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ,MAAM,SAAS,QAAQ;YAE/C,oCAAoC;YACpC,MAAM,iBAAiB,MAAM,MAAM,qBAAqB;gBACtD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,eAAe,EAAE,EAAE;gBACrB,MAAM,EAAE,KAAK,QAAQ,EAAE,GAAG,MAAM,eAAe,IAAI;gBACnD,4BAA4B;gBAC5B,OAAO,IAAI,CAAC,UAAU;gBAEtB,uBAAuB;gBACvB,MAAM,CAAC,2DAA2D,EAAE,SAAS,oDAAoD,CAAC;YACpI,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YAErC,kCAAkC;YAClC,MAAM,qBAAqB,CAAC;;;;;;2EAMyC,CAAC;YAEtE,IAAI,QAAQ,qBAAqB,+CAA+C;gBAC9E;YACF;QACF,SAAU;YACR,qBAAqB;YACrB,MAAM,iBAAiB,SAAS,aAAa,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,eAAe,QAAQ,GAAG;gBAC1B,eAAe,SAAS,GAAG;YAC7B;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY;YAAE,QAAQ;YAAQ,SAAS;YAAI,UAAU;QAAE;IACzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAM,WAAU;8CAA8C;;;;;;8CAG7E,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;oCACtC,aAAY;oCACZ,WAAU;oCACV,UAAU,SAAS,MAAM,KAAK;;;;;;;;;;;;sCAKlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAA8C;;;;;;8CAG/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;4CACV,UAAU,SAAS,MAAM,KAAK;;;;;;sDAEhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAGV,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAM7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAI7C,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,UAAU,SAAS,MAAM,KAAK;wCAC9B,WAAW,CAAC,gLAAgL,EAC1L,aAAa,kBAAkB,gBAC/B;kDAEF,cAAA,8OAAC;4CACC,WAAW,CAAC,0EAA0E,EACpF,aAAa,kBAAkB,iBAC/B;;;;;;;;;;;;;;;;;;;;;;sCAOV,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,OAAO,SAAS,MAAM,KAAK;4BACtC,WAAU;sCAET,SAAS,MAAM,KAAK,SACnB,gCAEA;;kDACE,8OAAC,4NAAO;wCAAC,WAAU;;;;;;oCAA8B;;;;;;;;wBAOtD,SAAS,MAAM,KAAK,wBACnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,MAAM,KAAK,wBACnB,8OAAC,mOAAW;gDAAC,WAAU;;;;;uDACrB,SAAS,MAAM,KAAK,2BACtB,8OAAC,0OAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC,4NAAO;gDAAC,WAAU;;;;;;0DAErB,8OAAC;gDAAK,WAAU;0DACb,SAAS,OAAO;;;;;;;;;;;;oCAKpB,SAAS,MAAM,KAAK,yBACnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,SAAS,QAAQ,CAAC,CAAC,CAAC;4CAAC;;;;;;;;;;;oCAM7C,SAAS,KAAK,kBACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAgB,SAAS,KAAK;;;;;;;;;;;oCAK9C,SAAS,MAAM,KAAK,cAAc,SAAS,WAAW,kBACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,mBAAiB;gDACjB,WAAU;;kEAEV,8OAAC,uMAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC,sNAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;oCAO1C,CAAC,SAAS,MAAM,KAAK,cAAc,SAAS,MAAM,KAAK,OAAO,mBAC7D,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAab,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uKAAI;wBAAC,MAAK;wBAAS,WAAU;kCAAiE;;;;;;;;;;;;;;;;;;;;;;AAOzG", "debugId": null}}]}