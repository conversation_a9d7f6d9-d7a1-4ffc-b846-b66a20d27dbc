{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/src/app/api/clone/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport puppeteer from 'puppeteer';\nimport archiver from 'archiver';\nimport { Readable } from 'stream';\nimport path from 'path';\nimport { URL } from 'url';\nimport fs from 'fs';\nimport os from 'os';\n\ninterface CloneRequest {\n  url: string;\n  depth: number;\n}\n\ninterface CrawledPage {\n  url: string;\n  html: string;\n  assets: Asset[];\n  textContent?: string;\n  title?: string;\n}\n\ninterface Asset {\n  url: string;\n  localPath: string;\n  content: Buffer;\n  type: 'css' | 'js' | 'image' | 'other';\n}\n\nconst MAX_SIZE_BYTES = 2 * 1024 * 1024 * 1024; // 2GB\nconst MAX_PAGES = 100; // Reasonable limit for pages\n\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  });\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url: rawUrl, depth, assetsOnly }: CloneRequest & { assetsOnly?: boolean } = await request.json();\n\n    // Auto-add https:// if no protocol is specified\n    let url = rawUrl?.trim();\n    if (!url) {\n      return NextResponse.json({ error: 'URL is required' }, { status: 400 });\n    }\n\n    if (!url.startsWith('http://') && !url.startsWith('https://')) {\n      url = 'https://' + url;\n    }\n\n    if (!isValidUrl(url)) {\n      return NextResponse.json({ error: 'Invalid URL provided' }, { status: 400 });\n    }\n\n    if (depth < 1 || depth > 3) {\n      return NextResponse.json({ error: 'Depth must be between 1 and 3' }, { status: 400 });\n    }\n\n    // Create temporary directory\n    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'stoke-clone-'));\n\n    try {\n      // Add progress tracking\n      const progressCallback = (message: string, progress: number) => {\n        console.log(`Progress: ${progress}% - ${message}`);\n      };\n\n      const crawledData = await crawlWebsite(url, depth, progressCallback, assetsOnly);\n\n      if (crawledData.length === 0) {\n        throw new Error('No pages could be crawled from the provided URL');\n      }\n\n      progressCallback('Creating ZIP file...', 90);\n      const baseUrl = new URL(url);\n      const zipBuffer = await createZipFile(crawledData, tempDir, baseUrl, assetsOnly);\n\n      // Check final size\n      if (zipBuffer.length > MAX_SIZE_BYTES) {\n        throw new Error(`Generated file is too large (${(zipBuffer.length / 1024 / 1024).toFixed(1)}MB). Maximum size is 2GB.`);\n      }\n\n      // Clean up temp directory\n      fs.rmSync(tempDir, { recursive: true, force: true });\n\n      progressCallback('Complete!', 100);\n\n      return new NextResponse(zipBuffer, {\n        headers: {\n          'Content-Type': 'application/zip',\n          'Content-Disposition': `attachment; filename=\"website-clone-${Date.now()}.zip\"`,\n          'Content-Length': zipBuffer.length.toString(),\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type',\n        },\n      });\n    } catch (error) {\n      // Clean up temp directory on error\n      fs.rmSync(tempDir, { recursive: true, force: true });\n      throw error;\n    }\n  } catch (error) {\n    console.error('Clone error:', error);\n    return NextResponse.json(\n      { error: error instanceof Error ? error.message : 'Failed to clone website' },\n      {\n        status: 500,\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type',\n        }\n      }\n    );\n  }\n}\n\nfunction isValidUrl(string: string): boolean {\n  try {\n    new URL(string);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nasync function crawlWebsite(\n  startUrl: string,\n  maxDepth: number,\n  progressCallback?: (message: string, progress: number) => void,\n  assetsOnly: boolean = false\n): Promise<CrawledPage[]> {\n  const browser = await puppeteer.launch({\n    headless: true,\n    args: ['--no-sandbox', '--disable-setuid-sandbox']\n  });\n\n  try {\n    const crawledPages: CrawledPage[] = [];\n    const visitedUrls = new Set<string>();\n    const urlQueue: { url: string; depth: number }[] = [{ url: startUrl, depth: 0 }];\n    const baseUrl = new URL(startUrl);\n    let totalSize = 0;\n\n    progressCallback?.('Starting website crawl...', 5);\n\n    while (urlQueue.length > 0 && crawledPages.length < MAX_PAGES) {\n      const { url: currentUrl, depth } = urlQueue.shift()!;\n\n      if (visitedUrls.has(currentUrl) || depth >= maxDepth) {\n        continue;\n      }\n\n      visitedUrls.add(currentUrl);\n\n      // Update progress based on pages crawled\n      const progress = Math.min(80, 10 + (crawledPages.length / Math.min(MAX_PAGES, 20)) * 70);\n      progressCallback?.(`Crawling page ${crawledPages.length + 1}: ${currentUrl}`, progress);\n\n      try {\n        const page = await browser.newPage();\n        \n        // Set user agent to avoid blocking\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');\n        \n        // Try with domcontentloaded first, fallback to load if needed\n        try {\n          await page.goto(currentUrl, {\n            waitUntil: 'domcontentloaded',\n            timeout: 45000\n          });\n        } catch (timeoutError) {\n          console.log(`Timeout with domcontentloaded, trying with 'load' for ${currentUrl}`);\n          await page.goto(currentUrl, {\n            waitUntil: 'load',\n            timeout: 30000\n          });\n        }\n\n        // Get page HTML\n        const html = await page.content();\n\n        // Extract text content and title for markdown\n        const { textContent, title } = await extractTextContent(page, currentUrl);\n\n        // Extract assets\n        const assets = await extractAssets(page, currentUrl);\n\n        // In assets-only mode, we only keep media assets\n        const filteredAssets = assetsOnly ?\n          assets.filter(asset => {\n            const ext = asset.url.split('.').pop()?.toLowerCase() || '';\n            return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'mp4', 'mp3', 'wav', 'pdf', 'doc', 'docx', 'zip', 'rar'].includes(ext);\n          }) : assets;\n\n        // Calculate size\n        const pageSize = Buffer.byteLength(html, 'utf8') +\n          filteredAssets.reduce((sum, asset) => sum + asset.content.length, 0);\n        \n        if (totalSize + pageSize > MAX_SIZE_BYTES) {\n          const sizeMB = (totalSize / 1024 / 1024).toFixed(1);\n          progressCallback?.(`Size limit reached (${sizeMB}MB), stopping crawl`, progress);\n          await page.close();\n          break;\n        }\n        \n        totalSize += pageSize;\n\n        crawledPages.push({\n          url: currentUrl,\n          html: assetsOnly ? '' : html, // Don't store HTML in assets-only mode\n          assets: filteredAssets,\n          textContent,\n          title\n        });\n\n        // Extract links for next depth level\n        if (depth + 1 < maxDepth) {\n          const links = await page.evaluate(() => {\n            const anchors = Array.from(document.querySelectorAll('a[href]'));\n            return anchors.map(a => (a as HTMLAnchorElement).href);\n          });\n\n          // Filter and add internal links to queue\n          for (const link of links) {\n            try {\n              const linkUrl = new URL(link);\n              // Only follow links on the same domain and avoid fragments/anchors\n              if (linkUrl.hostname === baseUrl.hostname &&\n                  !visitedUrls.has(link) &&\n                  !link.includes('mailto:') &&\n                  !link.includes('tel:') &&\n                  !link.includes('javascript:') &&\n                  !link.includes('#') &&\n                  linkUrl.pathname !== '/' || (linkUrl.pathname === '/' && !visitedUrls.has(linkUrl.origin))) {\n\n                // Clean the URL (remove query params and hash for simpler offline browsing)\n                const cleanUrl = `${linkUrl.origin}${linkUrl.pathname}`;\n                if (!visitedUrls.has(cleanUrl) && cleanUrl !== currentUrl) {\n                  urlQueue.push({ url: cleanUrl, depth: depth + 1 });\n                }\n              }\n            } catch {\n              // Invalid URL, skip\n            }\n          }\n        }\n\n        await page.close();\n      } catch (error) {\n        console.error(`Error crawling ${currentUrl}:`, error);\n\n        // If this is the first page and it fails, provide a more helpful error\n        if (crawledPages.length === 0 && currentUrl === startUrl) {\n          throw new Error(`Failed to access the website: ${error instanceof Error ? error.message : 'Unknown error'}. The website may be down, require authentication, or block automated access.`);\n        }\n\n        // Continue with other pages\n      }\n    }\n\n    return crawledPages;\n  } finally {\n    await browser.close();\n  }\n}\n\nasync function extractTextContent(page: any, pageUrl: string): Promise<{ textContent: string; title: string }> {\n  try {\n    const result = await page.evaluate(() => {\n      // Get page title\n      const title = document.title || '';\n\n      // Remove script and style elements\n      const elementsToRemove = document.querySelectorAll('script, style, noscript, iframe');\n      elementsToRemove.forEach(el => el.remove());\n\n      // Get main content areas (prioritize semantic elements)\n      const contentSelectors = [\n        'main',\n        'article',\n        '[role=\"main\"]',\n        '.content',\n        '.main-content',\n        '#content',\n        '#main',\n        'body'\n      ];\n\n      let contentElement = null;\n      for (const selector of contentSelectors) {\n        contentElement = document.querySelector(selector);\n        if (contentElement) break;\n      }\n\n      if (!contentElement) {\n        contentElement = document.body;\n      }\n\n      // Extract text content\n      let textContent = contentElement?.textContent || '';\n\n      // Clean up the text\n      textContent = textContent\n        .replace(/\\s+/g, ' ')  // Replace multiple whitespace with single space\n        .replace(/\\n\\s*\\n/g, '\\n\\n')  // Clean up line breaks\n        .trim();\n\n      return { textContent, title };\n    });\n\n    return result;\n  } catch (error) {\n    console.error('Error extracting text content:', error);\n    return { textContent: '', title: '' };\n  }\n}\n\nasync function extractAssets(page: any, pageUrl: string): Promise<Asset[]> {\n  const assets: Asset[] = [];\n  const baseUrl = new URL(pageUrl);\n\n  // Extract CSS files\n  const cssUrls = await page.evaluate((pageUrl) => {\n    const links = Array.from(document.querySelectorAll('link[rel=\"stylesheet\"]'));\n    return links.map(link => {\n      const href = (link as HTMLLinkElement).href;\n      // If it's a relative URL, resolve it against the page URL\n      if (href.startsWith('/') || href.startsWith('./') || (!href.startsWith('http') && !href.startsWith('//'))) {\n        return new URL(href, pageUrl).href;\n      }\n      return href;\n    });\n  }, pageUrl);\n\n  // Wait a bit for dynamic scripts to load\n  await new Promise(resolve => setTimeout(resolve, 2000));\n\n  // Extract JS files with multiple approaches\n  const jsUrls = await page.evaluate((pageUrl) => {\n    // Try multiple selectors to catch all script tags\n    const scriptSelectors = [\n      'script[src]',\n      'script[type=\"module\"][src]',\n      'script[type=\"text/javascript\"][src]'\n    ];\n\n    const allScripts = new Set<string>();\n\n    scriptSelectors.forEach(selector => {\n      const scripts = Array.from(document.querySelectorAll(selector));\n      scripts.forEach(script => {\n        const src = (script as HTMLScriptElement).src;\n        if (src) {\n          allScripts.add(src);\n        }\n      });\n    });\n\n    // Also check the HTML source directly for script tags\n    const htmlContent = document.documentElement.outerHTML;\n    const scriptMatches = htmlContent.match(/<script[^>]*src\\s*=\\s*[\"']([^\"']+)[\"'][^>]*>/gi);\n    if (scriptMatches) {\n      scriptMatches.forEach(match => {\n        const srcMatch = match.match(/src\\s*=\\s*[\"']([^\"']+)[\"']/i);\n        if (srcMatch && srcMatch[1]) {\n          allScripts.add(srcMatch[1]);\n        }\n      });\n    }\n\n    // Convert to array and resolve URLs\n    return Array.from(allScripts).map(src => {\n      // If it's a relative URL, resolve it against the page URL\n      if (src.startsWith('/') || src.startsWith('./') || (!src.startsWith('http') && !src.startsWith('//'))) {\n        return new URL(src, pageUrl).href;\n      }\n      return src;\n    });\n  }, pageUrl);\n\n  // Log JavaScript discovery results\n  console.log(`Found ${jsUrls.length} JavaScript files for ${pageUrl}`);\n\n  // Extract images (including background images from CSS)\n  const imageUrls = await page.evaluate((pageUrl) => {\n    const images = Array.from(document.querySelectorAll('img[src]'));\n    const imgSrcs = images.map(img => {\n      const src = (img as HTMLImageElement).src;\n      // If it's a relative URL, resolve it against the page URL\n      if (src.startsWith('/') || src.startsWith('./') || (!src.startsWith('http') && !src.startsWith('//'))) {\n        return new URL(src, pageUrl).href;\n      }\n      return src;\n    });\n\n    // Also extract background images\n    const elements = Array.from(document.querySelectorAll('*'));\n    const bgImages: string[] = [];\n\n    elements.forEach(el => {\n      const style = window.getComputedStyle(el);\n      const bgImage = style.backgroundImage;\n      if (bgImage && bgImage !== 'none') {\n        const matches = bgImage.match(/url\\(['\"]?([^'\"]+)['\"]?\\)/g);\n        if (matches) {\n          matches.forEach(match => {\n            const url = match.replace(/url\\(['\"]?([^'\"]+)['\"]?\\)/, '$1');\n            // Handle both absolute and relative URLs\n            if (url.startsWith('http') || url.startsWith('//')) {\n              bgImages.push(url);\n            } else if (url.startsWith('/') || url.startsWith('./') || (!url.startsWith('data:'))) {\n              try {\n                bgImages.push(new URL(url, pageUrl).href);\n              } catch (e) {\n                // Skip invalid URLs\n              }\n            }\n          });\n        }\n      }\n    });\n\n    return [...imgSrcs, ...bgImages];\n  }, pageUrl);\n\n  // Extract fonts\n  const fontUrls = await page.evaluate(() => {\n    const fonts: string[] = [];\n\n    // Extract from CSS @font-face rules\n    Array.from(document.styleSheets).forEach(sheet => {\n      try {\n        Array.from(sheet.cssRules || []).forEach(rule => {\n          if (rule instanceof CSSFontFaceRule) {\n            const src = rule.style.src;\n            if (src) {\n              const matches = src.match(/url\\(['\"]?([^'\"]+)['\"]?\\)/g);\n              if (matches) {\n                matches.forEach(match => {\n                  const url = match.replace(/url\\(['\"]?([^'\"]+)['\"]?\\)/, '$1');\n                  if (url.startsWith('http')) {\n                    fonts.push(url);\n                  }\n                });\n              }\n            }\n          }\n        });\n      } catch (e) {\n        // Cross-origin stylesheets may throw errors\n      }\n    });\n\n    return fonts;\n  });\n\n  // Download assets with better error handling and retries\n  const allAssetUrls = [\n    ...cssUrls.map(url => ({ url, type: 'css' as const })),\n    ...jsUrls.map(url => ({ url, type: 'js' as const })),\n    ...imageUrls.map(url => ({ url, type: 'image' as const })),\n    ...fontUrls.map(url => ({ url, type: 'other' as const }))\n  ];\n\n  // Remove duplicates\n  const uniqueAssets = Array.from(new Map(allAssetUrls.map(item => [item.url, item])).values());\n\n  // Log asset discovery summary\n  console.log(`Discovered ${uniqueAssets.length} unique assets for ${pageUrl}`);\n\n  for (const { url: assetUrl, type } of uniqueAssets) {\n    try {\n      const content = await downloadAssetWithRetry(assetUrl);\n      if (content) {\n        const localPath = generateLocalPath(assetUrl, baseUrl, type);\n\n        assets.push({\n          url: assetUrl,\n          localPath,\n          content,\n          type\n        });\n      }\n    } catch (error) {\n      console.error(`Failed to download asset ${assetUrl}:`, error);\n      // Continue with other assets\n    }\n  }\n\n  return assets;\n}\n\nasync function downloadAssetWithRetry(url: string, maxRetries: number = 3): Promise<Buffer | null> {\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      const response = await fetch(url, {\n        headers: {\n          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n        },\n        timeout: 10000 // 10 second timeout\n      });\n\n      if (response.ok) {\n        return Buffer.from(await response.arrayBuffer());\n      } else if (response.status === 404) {\n        // Don't retry 404s\n        return null;\n      }\n    } catch (error) {\n      if (attempt === maxRetries) {\n        console.error(`Failed to download ${url} after ${maxRetries} attempts:`, error);\n        return null;\n      }\n      // Wait before retry\n      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));\n    }\n  }\n  return null;\n}\n\nfunction generateLocalPath(assetUrl: string, baseUrl: URL, type: string): string {\n  try {\n    const parsedUrl = new URL(assetUrl);\n    let pathname = parsedUrl.pathname;\n\n    // Remove leading slash and handle empty paths\n    if (pathname === '/') {\n      pathname = 'index.html';\n    } else if (pathname.startsWith('/')) {\n      pathname = pathname.substring(1);\n    }\n\n    // Create directory structure based on asset type\n    let directory = '';\n    switch (type) {\n      case 'css':\n        directory = 'assets/css/';\n        break;\n      case 'js':\n        directory = 'assets/js/';\n        break;\n      case 'image':\n        directory = 'assets/images/';\n        break;\n      case 'other':\n        directory = 'assets/fonts/';\n        break;\n    }\n\n    // If no extension, add appropriate one\n    if (!path.extname(pathname)) {\n      switch (type) {\n        case 'css':\n          pathname += '.css';\n          break;\n        case 'js':\n          pathname += '.js';\n          break;\n        case 'image':\n          pathname += '.jpg'; // Default image extension\n          break;\n        case 'other':\n          if (assetUrl.includes('font') || assetUrl.includes('woff') || assetUrl.includes('ttf')) {\n            pathname += '.woff2';\n          } else {\n            pathname += '.bin';\n          }\n          break;\n        default:\n          pathname += '.html';\n      }\n    }\n\n    // Create safe filename - more aggressive sanitization\n    let safeName = path.basename(pathname);\n\n    // Replace spaces and special characters with underscores\n    safeName = safeName.replace(/[\\s\\(\\)\\[\\]%20]+/g, '_');\n\n    // Remove any remaining problematic characters\n    safeName = safeName.replace(/[^a-zA-Z0-9.\\-_]/g, '_');\n\n    // Remove multiple consecutive underscores\n    safeName = safeName.replace(/_+/g, '_');\n\n    // Remove leading/trailing underscores\n    safeName = safeName.replace(/^_+|_+$/g, '');\n\n    // Ensure we have a valid filename\n    if (!safeName || safeName === '.') {\n      safeName = `asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n\n    return directory + safeName;\n  } catch {\n    // Fallback for invalid URLs\n    const extension = getExtensionForType(type);\n    return `assets/${type}/asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}${extension}`;\n  }\n}\n\nfunction getExtensionForType(type: string): string {\n  switch (type) {\n    case 'css': return '.css';\n    case 'js': return '.js';\n    case 'image': return '.jpg';\n    case 'other': return '.bin';\n    default: return '.html';\n  }\n}\n\nasync function createZipFile(crawledPages: CrawledPage[], tempDir: string, baseUrl: URL, assetsOnly: boolean = false): Promise<Buffer> {\n  return new Promise((resolve, reject) => {\n    const archive = archiver('zip', { zlib: { level: 9 } });\n    const chunks: Buffer[] = [];\n\n    archive.on('data', (chunk) => chunks.push(chunk));\n    archive.on('end', () => resolve(Buffer.concat(chunks)));\n    archive.on('error', reject);\n\n    // Keep track of added assets to avoid duplicates\n    const addedAssets = new Set<string>();\n\n    // Add a README file with instructions\n    const readmeContent = assetsOnly ? `# Website Assets Collection\n\nThis ZIP file contains media assets and content extracted from: ${baseUrl.href}\n\n## Contents:\n- Images, videos, and media files\n- Documents (PDF, DOC, etc.)\n- website-content.md - All text content in markdown format for AI analysis\n\n## Assets Only Mode:\nThis collection contains only media assets and text content.\nHTML, CSS, and JavaScript files have been excluded for a lighter download.\n\nGenerated on: ${new Date().toISOString()}\n` : `# Website Clone\n\nThis ZIP file contains a complete offline copy of: ${baseUrl.href}\n\n## How to View:\n\n### ⚠️ IMPORTANT: Use Local Server for Best Results\n\n**Modern browsers block local file access for security reasons.** For the website to display correctly with all CSS, JavaScript, and images, you MUST use a local HTTP server.\n\n### Option 1: Local HTTP Server (Required for Full Functionality)\n\n**Using Python (Recommended):**\n\\`\\`\\`bash\n# Navigate to the extracted folder, then run:\npython3 -m http.server 8000\n# Or on Windows:\npython -m http.server 8000\n\\`\\`\\`\n\n**Using Node.js:**\n\\`\\`\\`bash\nnpx serve .\n# or\nnpx http-server\n\\`\\`\\`\n\n**Using PHP:**\n\\`\\`\\`bash\nphp -S localhost:8000\n\\`\\`\\`\n\nThen open: **http://localhost:8000**\n\n### Option 2: Direct File Opening (Limited Functionality)\nOpening index.html directly in your browser will show:\n- ✅ Basic HTML structure\n- ❌ No CSS styling (appears unstyled)\n- ❌ No JavaScript functionality\n- ❌ CORS errors in console\n\n**This is normal browser security behavior and not a bug.**\n\n## Contents:\n- HTML pages from the original website\n- CSS stylesheets in assets/css/\n- JavaScript files in assets/js/\n- Images in assets/images/\n- Fonts and other assets in assets/fonts/\n- website-content.md - All text content in markdown format for AI analysis\n\n## Note:\nSome dynamic functionality may not work in this offline copy.\nExternal links will still point to the original websites.\n\nThe website-content.md file contains all extracted text content from the website\nin a clean markdown format, perfect for AI tools and content analysis.\n\nGenerated on: ${new Date().toISOString()}\n`;\n\n    archive.append(readmeContent, { name: 'README.md' });\n\n    // Generate and add markdown content file\n    const markdownContent = generateMarkdownContent(crawledPages, baseUrl);\n    archive.append(markdownContent, { name: 'website-content.md' });\n\n    // Add pages and assets to zip\n    for (let i = 0; i < crawledPages.length; i++) {\n      const page = crawledPages[i];\n      const pageUrl = new URL(page.url);\n\n      // Only add HTML files if not in assets-only mode\n      if (!assetsOnly && page.html) {\n        let filename = generatePageFilename(pageUrl, i === 0);\n        // Process HTML to update asset references\n        const processedHtml = updateAssetReferences(page.html, page.assets, page.url);\n        archive.append(processedHtml, { name: filename });\n      }\n\n      // Add assets (avoiding duplicates)\n      for (const asset of page.assets) {\n        if (!addedAssets.has(asset.localPath)) {\n          addedAssets.add(asset.localPath);\n          archive.append(asset.content, { name: asset.localPath });\n        }\n      }\n    }\n\n    archive.finalize();\n  });\n}\n\nfunction generatePageFilename(pageUrl: URL, isHomePage: boolean): string {\n  if (isHomePage) {\n    return 'index.html';\n  }\n\n  let pathname = pageUrl.pathname;\n\n  // Remove leading slash\n  if (pathname.startsWith('/')) {\n    pathname = pathname.substring(1);\n  }\n\n  // Handle empty or root paths\n  if (pathname === '' || pathname === '/') {\n    return 'index.html';\n  }\n\n  // If it's a directory path, add index.html\n  if (pathname.endsWith('/')) {\n    pathname += 'index.html';\n  }\n\n  // If no extension, add .html\n  if (!path.extname(pathname)) {\n    pathname += '.html';\n  }\n\n  // Create safe filename\n  return pathname.replace(/[^a-zA-Z0-9.\\-_/]/g, '_');\n}\n\nfunction updateAssetReferences(html: string, assets: Asset[], pageUrl: string): string {\n  let updatedHtml = html;\n  const baseUrl = new URL(pageUrl);\n\n  // Create a map for faster lookups\n  const assetMap = new Map(assets.map(asset => [asset.url, asset.localPath]));\n\n  // Replace asset URLs in various contexts, but be careful with navigation links\n  for (const [originalUrl, localPath] of assetMap) {\n    // Escape special regex characters\n    const escapedUrl = escapeRegExp(originalUrl);\n\n    // Also try to match relative URLs (e.g., /assets/file.css vs https://domain.com/assets/file.css)\n    const urlObj = new URL(originalUrl);\n    const relativePath = urlObj.pathname;\n    const escapedRelativePath = escapeRegExp(relativePath);\n\n    // Also try to match the original filename (for cases where HTML has different paths)\n    const originalFilename = path.basename(relativePath);\n    const escapedFilename = escapeRegExp(originalFilename);\n\n    // Handle URL encoding - decode the filename for matching\n    const decodedFilename = decodeURIComponent(originalFilename);\n    const escapedDecodedFilename = escapeRegExp(decodedFilename);\n\n    // Replace in different contexts:\n    // 1. src attributes (images, scripts) - try full URL, relative path, and filename\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`src=['\"]${escapedUrl}['\"]`, 'g'),\n      `src=\"${localPath}\"`\n    );\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`src=['\"]${escapedRelativePath}['\"]`, 'g'),\n      `src=\"${localPath}\"`\n    );\n    // Also try to match just the filename (for cases where path differs)\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`src=['\"][^\"']*/${escapedFilename}['\"]`, 'g'),\n      `src=\"${localPath}\"`\n    );\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`src=['\"]${escapedFilename}['\"]`, 'g'),\n      `src=\"${localPath}\"`\n    );\n    // Also try with decoded filename (handles URL encoding)\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`src=['\"][^\"']*/${escapedDecodedFilename}['\"]`, 'g'),\n      `src=\"${localPath}\"`\n    );\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`src=['\"]${escapedDecodedFilename}['\"]`, 'g'),\n      `src=\"${localPath}\"`\n    );\n\n    // 2. href attributes - but only for stylesheets and actual assets, not navigation\n    // Check if this is a stylesheet link - try both full URL and relative path\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`<link[^>]*rel=['\"]stylesheet['\"][^>]*href=['\"]${escapedUrl}['\"]`, 'g'),\n      (match) => match.replace(originalUrl, localPath)\n    );\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`<link[^>]*rel=['\"]stylesheet['\"][^>]*href=['\"]${escapedRelativePath}['\"]`, 'g'),\n      (match) => match.replace(relativePath, localPath)\n    );\n\n    // 3. CSS url() functions - try both full URL and relative path\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`url\\\\(['\"]?${escapedUrl}['\"]?\\\\)`, 'g'),\n      `url(\"${localPath}\")`\n    );\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`url\\\\(['\"]?${escapedRelativePath}['\"]?\\\\)`, 'g'),\n      `url(\"${localPath}\")`\n    );\n\n    // 4. CSS @import statements - try both full URL and relative path\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`@import\\\\s+['\"]${escapedUrl}['\"]`, 'g'),\n      `@import \"${localPath}\"`\n    );\n    updatedHtml = updatedHtml.replace(\n      new RegExp(`@import\\\\s+['\"]${escapedRelativePath}['\"]`, 'g'),\n      `@import \"${localPath}\"`\n    );\n  }\n\n  // Handle navigation links separately - convert internal links to work offline\n  updatedHtml = processNavigationLinks(updatedHtml, baseUrl);\n\n  // Also process any inline CSS for additional asset references\n  updatedHtml = processInlineCSS(updatedHtml, assetMap);\n\n  return updatedHtml;\n}\n\nfunction processNavigationLinks(html: string, baseUrl: URL): string {\n  // Find all anchor tags with href attributes\n  return html.replace(/<a([^>]*?)href=['\"]([^'\"]+)['\"]([^>]*?)>/g, (match, before, href, after) => {\n    try {\n      // Skip if it's already a local file reference or external link\n      if (href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:') ||\n          href.startsWith('javascript:') || href.includes('://') && !href.startsWith(baseUrl.origin)) {\n        return match;\n      }\n\n      // If it's an internal link on the same domain\n      if (href.startsWith('/') || href.startsWith(baseUrl.origin)) {\n        const fullUrl = href.startsWith('/') ? baseUrl.origin + href : href;\n        const url = new URL(fullUrl);\n\n        // Convert to local filename\n        let localPath = url.pathname;\n        if (localPath === '/' || localPath === '') {\n          localPath = 'index.html';\n        } else {\n          // Remove leading slash and add .html if needed\n          localPath = localPath.substring(1);\n          if (!localPath.includes('.')) {\n            localPath += '.html';\n          }\n        }\n\n        return `<a${before}href=\"${localPath}\"${after}>`;\n      }\n\n      return match;\n    } catch (error) {\n      // If URL parsing fails, return original\n      return match;\n    }\n  });\n}\n\nfunction processInlineCSS(html: string, assetMap: Map<string, string>): string {\n  // Find and process <style> tags\n  return html.replace(/<style[^>]*>([\\s\\S]*?)<\\/style>/gi, (match, cssContent) => {\n    let processedCSS = cssContent;\n\n    for (const [originalUrl, localPath] of assetMap) {\n      const escapedUrl = escapeRegExp(originalUrl);\n\n      // Replace URLs in CSS\n      processedCSS = processedCSS.replace(\n        new RegExp(`url\\\\(['\"]?${escapedUrl}['\"]?\\\\)`, 'g'),\n        `url(\"${localPath}\")`\n      );\n\n      processedCSS = processedCSS.replace(\n        new RegExp(`@import\\\\s+['\"]${escapedUrl}['\"]`, 'g'),\n        `@import \"${localPath}\"`\n      );\n    }\n\n    return match.replace(cssContent, processedCSS);\n  });\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nfunction generateMarkdownContent(crawledPages: CrawledPage[], baseUrl: URL): string {\n  const siteName = baseUrl.hostname.replace('www.', '');\n  const timestamp = new Date().toISOString().split('T')[0];\n\n  let markdown = `# Website Content: ${siteName}\\n\\n`;\n  markdown += `**Crawled on:** ${timestamp}\\n`;\n  markdown += `**Source:** ${baseUrl.href}\\n`;\n  markdown += `**Pages crawled:** ${crawledPages.length}\\n\\n`;\n  markdown += `---\\n\\n`;\n\n  // Table of contents\n  markdown += `## Table of Contents\\n\\n`;\n  crawledPages.forEach((page, index) => {\n    const url = new URL(page.url);\n    const pageName = page.title || url.pathname.replace('/', '') || 'Home';\n    markdown += `${index + 1}. [${pageName}](#page-${index + 1})\\n`;\n  });\n  markdown += `\\n---\\n\\n`;\n\n  // Page content\n  crawledPages.forEach((page, index) => {\n    const url = new URL(page.url);\n    const pageName = page.title || url.pathname.replace('/', '') || 'Home';\n\n    markdown += `## Page ${index + 1}: ${pageName}\\n\\n`;\n    markdown += `**URL:** ${page.url}\\n`;\n    if (page.title) {\n      markdown += `**Title:** ${page.title}\\n`;\n    }\n    markdown += `\\n`;\n\n    if (page.textContent && page.textContent.trim()) {\n      // Clean and format the text content\n      let content = page.textContent\n        .replace(/\\s+/g, ' ')  // Normalize whitespace\n        .replace(/\\. /g, '.\\n\\n')  // Add paragraph breaks after sentences\n        .replace(/\\n\\n+/g, '\\n\\n')  // Normalize paragraph breaks\n        .trim();\n\n      markdown += `${content}\\n\\n`;\n    } else {\n      markdown += `*No text content extracted from this page.*\\n\\n`;\n    }\n\n    markdown += `---\\n\\n`;\n  });\n\n  // Footer\n  markdown += `## Extraction Details\\n\\n`;\n  markdown += `- **Total pages:** ${crawledPages.length}\\n`;\n  markdown += `- **Extraction method:** Automated web scraping\\n`;\n  markdown += `- **Generated by:** StokeCloner\\n`;\n  markdown += `- **Date:** ${new Date().toLocaleString()}\\n\\n`;\n  markdown += `*This markdown file contains all text content from the crawled website for easy analysis and processing.*\\n`;\n\n  return markdown;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;AAsBA,MAAM,iBAAiB,IAAI,OAAO,OAAO,MAAM,MAAM;AACrD,MAAM,YAAY,KAAK,6BAA6B;AAE7C,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gJAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAA4C,MAAM,QAAQ,IAAI;QAEtG,gDAAgD;QAChD,IAAI,MAAM,QAAQ;QAClB,IAAI,CAAC,KAAK;YACR,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,aAAa;YAC7D,MAAM,aAAa;QACrB;QAEA,IAAI,CAAC,WAAW,MAAM;YACpB,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuB,GAAG;gBAAE,QAAQ;YAAI;QAC5E;QAEA,IAAI,QAAQ,KAAK,QAAQ,GAAG;YAC1B,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgC,GAAG;gBAAE,QAAQ;YAAI;QACrF;QAEA,6BAA6B;QAC7B,MAAM,UAAU,wGAAE,CAAC,WAAW,CAAC,4GAAI,CAAC,IAAI,CAAC,wGAAE,CAAC,MAAM,IAAI;QAEtD,IAAI;YACF,wBAAwB;YACxB,MAAM,mBAAmB,CAAC,SAAiB;gBACzC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,SAAS,IAAI,EAAE,SAAS;YACnD;YAEA,MAAM,cAAc,MAAM,aAAa,KAAK,OAAO,kBAAkB;YAErE,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,MAAM,IAAI,MAAM;YAClB;YAEA,iBAAiB,wBAAwB;YACzC,MAAM,UAAU,IAAI,sGAAG,CAAC;YACxB,MAAM,YAAY,MAAM,cAAc,aAAa,SAAS,SAAS;YAErE,mBAAmB;YACnB,IAAI,UAAU,MAAM,GAAG,gBAAgB;gBACrC,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,CAAC,UAAU,MAAM,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC,GAAG,yBAAyB,CAAC;YACxH;YAEA,0BAA0B;YAC1B,wGAAE,CAAC,MAAM,CAAC,SAAS;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAElD,iBAAiB,aAAa;YAE9B,OAAO,IAAI,gJAAY,CAAC,WAAW;gBACjC,SAAS;oBACP,gBAAgB;oBAChB,uBAAuB,CAAC,oCAAoC,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;oBAC/E,kBAAkB,UAAU,MAAM,CAAC,QAAQ;oBAC3C,+BAA+B;oBAC/B,gCAAgC;oBAChC,gCAAgC;gBAClC;YACF;QACF,EAAE,OAAO,OAAO;YACd,mCAAmC;YACnC,wGAAE,CAAC,MAAM,CAAC,SAAS;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAClD,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAA0B,GAC5E;YACE,QAAQ;YACR,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IAEJ;AACF;AAEA,SAAS,WAAW,MAAc;IAChC,IAAI;QACF,IAAI,sGAAG,CAAC;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,eAAe,aACb,QAAgB,EAChB,QAAgB,EAChB,gBAA8D,EAC9D,aAAsB,KAAK;IAE3B,MAAM,UAAU,MAAM,6HAAS,CAAC,MAAM,CAAC;QACrC,UAAU;QACV,MAAM;YAAC;YAAgB;SAA2B;IACpD;IAEA,IAAI;QACF,MAAM,eAA8B,EAAE;QACtC,MAAM,cAAc,IAAI;QACxB,MAAM,WAA6C;YAAC;gBAAE,KAAK;gBAAU,OAAO;YAAE;SAAE;QAChF,MAAM,UAAU,IAAI,sGAAG,CAAC;QACxB,IAAI,YAAY;QAEhB,mBAAmB,6BAA6B;QAEhD,MAAO,SAAS,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,UAAW;YAC7D,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,EAAE,GAAG,SAAS,KAAK;YAEjD,IAAI,YAAY,GAAG,CAAC,eAAe,SAAS,UAAU;gBACpD;YACF;YAEA,YAAY,GAAG,CAAC;YAEhB,yCAAyC;YACzC,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,KAAK,AAAC,aAAa,MAAM,GAAG,KAAK,GAAG,CAAC,WAAW,MAAO;YACrF,mBAAmB,CAAC,cAAc,EAAE,aAAa,MAAM,GAAG,EAAE,EAAE,EAAE,YAAY,EAAE;YAE9E,IAAI;gBACF,MAAM,OAAO,MAAM,QAAQ,OAAO;gBAElC,mCAAmC;gBACnC,MAAM,KAAK,YAAY,CAAC;gBAExB,8DAA8D;gBAC9D,IAAI;oBACF,MAAM,KAAK,IAAI,CAAC,YAAY;wBAC1B,WAAW;wBACX,SAAS;oBACX;gBACF,EAAE,OAAO,cAAc;oBACrB,QAAQ,GAAG,CAAC,CAAC,sDAAsD,EAAE,YAAY;oBACjF,MAAM,KAAK,IAAI,CAAC,YAAY;wBAC1B,WAAW;wBACX,SAAS;oBACX;gBACF;gBAEA,gBAAgB;gBAChB,MAAM,OAAO,MAAM,KAAK,OAAO;gBAE/B,8CAA8C;gBAC9C,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAmB,MAAM;gBAE9D,iBAAiB;gBACjB,MAAM,SAAS,MAAM,cAAc,MAAM;gBAEzC,iDAAiD;gBACjD,MAAM,iBAAiB,aACrB,OAAO,MAAM,CAAC,CAAA;oBACZ,MAAM,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;oBACzD,OAAO;wBAAC;wBAAO;wBAAQ;wBAAO;wBAAO;wBAAQ;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAQ;wBAAO;qBAAM,CAAC,QAAQ,CAAC;gBACxH,KAAK;gBAEP,iBAAiB;gBACjB,MAAM,WAAW,OAAO,UAAU,CAAC,MAAM,UACvC,eAAe,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,OAAO,CAAC,MAAM,EAAE;gBAEpE,IAAI,YAAY,WAAW,gBAAgB;oBACzC,MAAM,SAAS,CAAC,YAAY,OAAO,IAAI,EAAE,OAAO,CAAC;oBACjD,mBAAmB,CAAC,oBAAoB,EAAE,OAAO,mBAAmB,CAAC,EAAE;oBACvE,MAAM,KAAK,KAAK;oBAChB;gBACF;gBAEA,aAAa;gBAEb,aAAa,IAAI,CAAC;oBAChB,KAAK;oBACL,MAAM,aAAa,KAAK;oBACxB,QAAQ;oBACR;oBACA;gBACF;gBAEA,qCAAqC;gBACrC,IAAI,QAAQ,IAAI,UAAU;oBACxB,MAAM,QAAQ,MAAM,KAAK,QAAQ,CAAC;wBAChC,MAAM,UAAU,MAAM,IAAI,CAAC,SAAS,gBAAgB,CAAC;wBACrD,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,AAAC,EAAwB,IAAI;oBACvD;oBAEA,yCAAyC;oBACzC,KAAK,MAAM,QAAQ,MAAO;wBACxB,IAAI;4BACF,MAAM,UAAU,IAAI,sGAAG,CAAC;4BACxB,mEAAmE;4BACnE,IAAI,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IACrC,CAAC,YAAY,GAAG,CAAC,SACjB,CAAC,KAAK,QAAQ,CAAC,cACf,CAAC,KAAK,QAAQ,CAAC,WACf,CAAC,KAAK,QAAQ,CAAC,kBACf,CAAC,KAAK,QAAQ,CAAC,QACf,QAAQ,QAAQ,KAAK,OAAQ,QAAQ,QAAQ,KAAK,OAAO,CAAC,YAAY,GAAG,CAAC,QAAQ,MAAM,GAAI;gCAE9F,4EAA4E;gCAC5E,MAAM,WAAW,GAAG,QAAQ,MAAM,GAAG,QAAQ,QAAQ,EAAE;gCACvD,IAAI,CAAC,YAAY,GAAG,CAAC,aAAa,aAAa,YAAY;oCACzD,SAAS,IAAI,CAAC;wCAAE,KAAK;wCAAU,OAAO,QAAQ;oCAAE;gCAClD;4BACF;wBACF,EAAE,OAAM;wBACN,oBAAoB;wBACtB;oBACF;gBACF;gBAEA,MAAM,KAAK,KAAK;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,EAAE;gBAE/C,uEAAuE;gBACvE,IAAI,aAAa,MAAM,KAAK,KAAK,eAAe,UAAU;oBACxD,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,gBAAgB,6EAA6E,CAAC;gBAC1L;YAEA,4BAA4B;YAC9B;QACF;QAEA,OAAO;IACT,SAAU;QACR,MAAM,QAAQ,KAAK;IACrB;AACF;AAEA,eAAe,mBAAmB,IAAS,EAAE,OAAe;IAC1D,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,QAAQ,CAAC;YACjC,iBAAiB;YACjB,MAAM,QAAQ,SAAS,KAAK,IAAI;YAEhC,mCAAmC;YACnC,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,iBAAiB,OAAO,CAAC,CAAA,KAAM,GAAG,MAAM;YAExC,wDAAwD;YACxD,MAAM,mBAAmB;gBACvB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,IAAI,iBAAiB;YACrB,KAAK,MAAM,YAAY,iBAAkB;gBACvC,iBAAiB,SAAS,aAAa,CAAC;gBACxC,IAAI,gBAAgB;YACtB;YAEA,IAAI,CAAC,gBAAgB;gBACnB,iBAAiB,SAAS,IAAI;YAChC;YAEA,uBAAuB;YACvB,IAAI,cAAc,gBAAgB,eAAe;YAEjD,oBAAoB;YACpB,cAAc,YACX,OAAO,CAAC,QAAQ,KAAM,gDAAgD;aACtE,OAAO,CAAC,YAAY,QAAS,uBAAuB;aACpD,IAAI;YAEP,OAAO;gBAAE;gBAAa;YAAM;QAC9B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YAAE,aAAa;YAAI,OAAO;QAAG;IACtC;AACF;AAEA,eAAe,cAAc,IAAS,EAAE,OAAe;IACrD,MAAM,SAAkB,EAAE;IAC1B,MAAM,UAAU,IAAI,sGAAG,CAAC;IAExB,oBAAoB;IACpB,MAAM,UAAU,MAAM,KAAK,QAAQ,CAAC,CAAC;QACnC,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACnD,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,MAAM,OAAO,AAAC,KAAyB,IAAI;YAC3C,0DAA0D;YAC1D,IAAI,KAAK,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC,SAAU,CAAC,KAAK,UAAU,CAAC,WAAW,CAAC,KAAK,UAAU,CAAC,OAAQ;gBACzG,OAAO,IAAI,sGAAG,CAAC,MAAM,SAAS,IAAI;YACpC;YACA,OAAO;QACT;IACF,GAAG;IAEH,yCAAyC;IACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,4CAA4C;IAC5C,MAAM,SAAS,MAAM,KAAK,QAAQ,CAAC,CAAC;QAClC,kDAAkD;QAClD,MAAM,kBAAkB;YACtB;YACA;YACA;SACD;QAED,MAAM,aAAa,IAAI;QAEvB,gBAAgB,OAAO,CAAC,CAAA;YACtB,MAAM,UAAU,MAAM,IAAI,CAAC,SAAS,gBAAgB,CAAC;YACrD,QAAQ,OAAO,CAAC,CAAA;gBACd,MAAM,MAAM,AAAC,OAA6B,GAAG;gBAC7C,IAAI,KAAK;oBACP,WAAW,GAAG,CAAC;gBACjB;YACF;QACF;QAEA,sDAAsD;QACtD,MAAM,cAAc,SAAS,eAAe,CAAC,SAAS;QACtD,MAAM,gBAAgB,YAAY,KAAK,CAAC;QACxC,IAAI,eAAe;YACjB,cAAc,OAAO,CAAC,CAAA;gBACpB,MAAM,WAAW,MAAM,KAAK,CAAC;gBAC7B,IAAI,YAAY,QAAQ,CAAC,EAAE,EAAE;oBAC3B,WAAW,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC5B;YACF;QACF;QAEA,oCAAoC;QACpC,OAAO,MAAM,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA;YAChC,0DAA0D;YAC1D,IAAI,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,SAAU,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,OAAQ;gBACrG,OAAO,IAAI,sGAAG,CAAC,KAAK,SAAS,IAAI;YACnC;YACA,OAAO;QACT;IACF,GAAG;IAEH,mCAAmC;IACnC,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,MAAM,CAAC,sBAAsB,EAAE,SAAS;IAEpE,wDAAwD;IACxD,MAAM,YAAY,MAAM,KAAK,QAAQ,CAAC,CAAC;QACrC,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpD,MAAM,UAAU,OAAO,GAAG,CAAC,CAAA;YACzB,MAAM,MAAM,AAAC,IAAyB,GAAG;YACzC,0DAA0D;YAC1D,IAAI,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,SAAU,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,OAAQ;gBACrG,OAAO,IAAI,sGAAG,CAAC,KAAK,SAAS,IAAI;YACnC;YACA,OAAO;QACT;QAEA,iCAAiC;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACtD,MAAM,WAAqB,EAAE;QAE7B,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,QAAQ,OAAO,gBAAgB,CAAC;YACtC,MAAM,UAAU,MAAM,eAAe;YACrC,IAAI,WAAW,YAAY,QAAQ;gBACjC,MAAM,UAAU,QAAQ,KAAK,CAAC;gBAC9B,IAAI,SAAS;oBACX,QAAQ,OAAO,CAAC,CAAA;wBACd,MAAM,MAAM,MAAM,OAAO,CAAC,6BAA6B;wBACvD,yCAAyC;wBACzC,IAAI,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,OAAO;4BAClD,SAAS,IAAI,CAAC;wBAChB,OAAO,IAAI,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,SAAU,CAAC,IAAI,UAAU,CAAC,UAAW;4BACpF,IAAI;gCACF,SAAS,IAAI,CAAC,IAAI,sGAAG,CAAC,KAAK,SAAS,IAAI;4BAC1C,EAAE,OAAO,GAAG;4BACV,oBAAoB;4BACtB;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;eAAI;eAAY;SAAS;IAClC,GAAG;IAEH,gBAAgB;IAChB,MAAM,WAAW,MAAM,KAAK,QAAQ,CAAC;QACnC,MAAM,QAAkB,EAAE;QAE1B,oCAAoC;QACpC,MAAM,IAAI,CAAC,SAAS,WAAW,EAAE,OAAO,CAAC,CAAA;YACvC,IAAI;gBACF,MAAM,IAAI,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;oBACvC,IAAI,gBAAgB,iBAAiB;wBACnC,MAAM,MAAM,KAAK,KAAK,CAAC,GAAG;wBAC1B,IAAI,KAAK;4BACP,MAAM,UAAU,IAAI,KAAK,CAAC;4BAC1B,IAAI,SAAS;gCACX,QAAQ,OAAO,CAAC,CAAA;oCACd,MAAM,MAAM,MAAM,OAAO,CAAC,6BAA6B;oCACvD,IAAI,IAAI,UAAU,CAAC,SAAS;wCAC1B,MAAM,IAAI,CAAC;oCACb;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,EAAE,OAAO,GAAG;YACV,4CAA4C;YAC9C;QACF;QAEA,OAAO;IACT;IAEA,yDAAyD;IACzD,MAAM,eAAe;WAChB,QAAQ,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE;gBAAK,MAAM;YAAe,CAAC;WACjD,OAAO,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE;gBAAK,MAAM;YAAc,CAAC;WAC/C,UAAU,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE;gBAAK,MAAM;YAAiB,CAAC;WACrD,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;gBAAE;gBAAK,MAAM;YAAiB,CAAC;KACxD;IAED,oBAAoB;IACpB,MAAM,eAAe,MAAM,IAAI,CAAC,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,OAAQ;YAAC,KAAK,GAAG;YAAE;SAAK,GAAG,MAAM;IAE1F,8BAA8B;IAC9B,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,MAAM,CAAC,mBAAmB,EAAE,SAAS;IAE5E,KAAK,MAAM,EAAE,KAAK,QAAQ,EAAE,IAAI,EAAE,IAAI,aAAc;QAClD,IAAI;YACF,MAAM,UAAU,MAAM,uBAAuB;YAC7C,IAAI,SAAS;gBACX,MAAM,YAAY,kBAAkB,UAAU,SAAS;gBAEvD,OAAO,IAAI,CAAC;oBACV,KAAK;oBACL;oBACA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC,EAAE;QACvD,6BAA6B;QAC/B;IACF;IAEA,OAAO;AACT;AAEA,eAAe,uBAAuB,GAAW,EAAE,aAAqB,CAAC;IACvE,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,cAAc;gBAChB;gBACA,SAAS,MAAM,oBAAoB;YACrC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,OAAO,IAAI,CAAC,MAAM,SAAS,WAAW;YAC/C,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,mBAAmB;gBACnB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,IAAI,YAAY,YAAY;gBAC1B,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,IAAI,OAAO,EAAE,WAAW,UAAU,CAAC,EAAE;gBACzE,OAAO;YACT;YACA,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO;QAC1D;IACF;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,QAAgB,EAAE,OAAY,EAAE,IAAY;IACrE,IAAI;QACF,MAAM,YAAY,IAAI,sGAAG,CAAC;QAC1B,IAAI,WAAW,UAAU,QAAQ;QAEjC,8CAA8C;QAC9C,IAAI,aAAa,KAAK;YACpB,WAAW;QACb,OAAO,IAAI,SAAS,UAAU,CAAC,MAAM;YACnC,WAAW,SAAS,SAAS,CAAC;QAChC;QAEA,iDAAiD;QACjD,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QAEA,uCAAuC;QACvC,IAAI,CAAC,4GAAI,CAAC,OAAO,CAAC,WAAW;YAC3B,OAAQ;gBACN,KAAK;oBACH,YAAY;oBACZ;gBACF,KAAK;oBACH,YAAY;oBACZ;gBACF,KAAK;oBACH,YAAY,QAAQ,0BAA0B;oBAC9C;gBACF,KAAK;oBACH,IAAI,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,QAAQ;wBACtF,YAAY;oBACd,OAAO;wBACL,YAAY;oBACd;oBACA;gBACF;oBACE,YAAY;YAChB;QACF;QAEA,sDAAsD;QACtD,IAAI,WAAW,4GAAI,CAAC,QAAQ,CAAC;QAE7B,yDAAyD;QACzD,WAAW,SAAS,OAAO,CAAC,qBAAqB;QAEjD,8CAA8C;QAC9C,WAAW,SAAS,OAAO,CAAC,qBAAqB;QAEjD,0CAA0C;QAC1C,WAAW,SAAS,OAAO,CAAC,OAAO;QAEnC,sCAAsC;QACtC,WAAW,SAAS,OAAO,CAAC,YAAY;QAExC,kCAAkC;QAClC,IAAI,CAAC,YAAY,aAAa,KAAK;YACjC,WAAW,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAC7E;QAEA,OAAO,YAAY;IACrB,EAAE,OAAM;QACN,4BAA4B;QAC5B,MAAM,YAAY,oBAAoB;QACtC,OAAO,CAAC,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK,WAAW;IACpG;AACF;AAEA,SAAS,oBAAoB,IAAY;IACvC,OAAQ;QACN,KAAK;YAAO,OAAO;QACnB,KAAK;YAAM,OAAO;QAClB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAS,OAAO;QACrB;YAAS,OAAO;IAClB;AACF;AAEA,eAAe,cAAc,YAA2B,EAAE,OAAe,EAAE,OAAY,EAAE,aAAsB,KAAK;IAClH,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,UAAU,IAAA,8IAAQ,EAAC,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAE;QAAE;QACrD,MAAM,SAAmB,EAAE;QAE3B,QAAQ,EAAE,CAAC,QAAQ,CAAC,QAAU,OAAO,IAAI,CAAC;QAC1C,QAAQ,EAAE,CAAC,OAAO,IAAM,QAAQ,OAAO,MAAM,CAAC;QAC9C,QAAQ,EAAE,CAAC,SAAS;QAEpB,iDAAiD;QACjD,MAAM,cAAc,IAAI;QAExB,sCAAsC;QACtC,MAAM,gBAAgB,aAAa,CAAC;;gEAEwB,EAAE,QAAQ,IAAI,CAAC;;;;;;;;;;;cAWjE,EAAE,IAAI,OAAO,WAAW,GAAG;AACzC,CAAC,GAAG,CAAC;;mDAE8C,EAAE,QAAQ,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAwDpD,EAAE,IAAI,OAAO,WAAW,GAAG;AACzC,CAAC;QAEG,QAAQ,MAAM,CAAC,eAAe;YAAE,MAAM;QAAY;QAElD,yCAAyC;QACzC,MAAM,kBAAkB,wBAAwB,cAAc;QAC9D,QAAQ,MAAM,CAAC,iBAAiB;YAAE,MAAM;QAAqB;QAE7D,8BAA8B;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,OAAO,YAAY,CAAC,EAAE;YAC5B,MAAM,UAAU,IAAI,sGAAG,CAAC,KAAK,GAAG;YAEhC,iDAAiD;YACjD,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;gBAC5B,IAAI,WAAW,qBAAqB,SAAS,MAAM;gBACnD,0CAA0C;gBAC1C,MAAM,gBAAgB,sBAAsB,KAAK,IAAI,EAAE,KAAK,MAAM,EAAE,KAAK,GAAG;gBAC5E,QAAQ,MAAM,CAAC,eAAe;oBAAE,MAAM;gBAAS;YACjD;YAEA,mCAAmC;YACnC,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;gBAC/B,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,SAAS,GAAG;oBACrC,YAAY,GAAG,CAAC,MAAM,SAAS;oBAC/B,QAAQ,MAAM,CAAC,MAAM,OAAO,EAAE;wBAAE,MAAM,MAAM,SAAS;oBAAC;gBACxD;YACF;QACF;QAEA,QAAQ,QAAQ;IAClB;AACF;AAEA,SAAS,qBAAqB,OAAY,EAAE,UAAmB;IAC7D,IAAI,YAAY;QACd,OAAO;IACT;IAEA,IAAI,WAAW,QAAQ,QAAQ;IAE/B,uBAAuB;IACvB,IAAI,SAAS,UAAU,CAAC,MAAM;QAC5B,WAAW,SAAS,SAAS,CAAC;IAChC;IAEA,6BAA6B;IAC7B,IAAI,aAAa,MAAM,aAAa,KAAK;QACvC,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,QAAQ,CAAC,MAAM;QAC1B,YAAY;IACd;IAEA,6BAA6B;IAC7B,IAAI,CAAC,4GAAI,CAAC,OAAO,CAAC,WAAW;QAC3B,YAAY;IACd;IAEA,uBAAuB;IACvB,OAAO,SAAS,OAAO,CAAC,sBAAsB;AAChD;AAEA,SAAS,sBAAsB,IAAY,EAAE,MAAe,EAAE,OAAe;IAC3E,IAAI,cAAc;IAClB,MAAM,UAAU,IAAI,sGAAG,CAAC;IAExB,kCAAkC;IAClC,MAAM,WAAW,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS;YAAC,MAAM,GAAG;YAAE,MAAM,SAAS;SAAC;IAEzE,+EAA+E;IAC/E,KAAK,MAAM,CAAC,aAAa,UAAU,IAAI,SAAU;QAC/C,kCAAkC;QAClC,MAAM,aAAa,aAAa;QAEhC,iGAAiG;QACjG,MAAM,SAAS,IAAI,sGAAG,CAAC;QACvB,MAAM,eAAe,OAAO,QAAQ;QACpC,MAAM,sBAAsB,aAAa;QAEzC,qFAAqF;QACrF,MAAM,mBAAmB,4GAAI,CAAC,QAAQ,CAAC;QACvC,MAAM,kBAAkB,aAAa;QAErC,yDAAyD;QACzD,MAAM,kBAAkB,mBAAmB;QAC3C,MAAM,yBAAyB,aAAa;QAE5C,iCAAiC;QACjC,kFAAkF;QAClF,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,QAAQ,EAAE,WAAW,IAAI,CAAC,EAAE,MACxC,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEtB,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,QAAQ,EAAE,oBAAoB,IAAI,CAAC,EAAE,MACjD,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEtB,qEAAqE;QACrE,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,eAAe,EAAE,gBAAgB,IAAI,CAAC,EAAE,MACpD,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEtB,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,QAAQ,EAAE,gBAAgB,IAAI,CAAC,EAAE,MAC7C,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEtB,wDAAwD;QACxD,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,eAAe,EAAE,uBAAuB,IAAI,CAAC,EAAE,MAC3D,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEtB,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,QAAQ,EAAE,uBAAuB,IAAI,CAAC,EAAE,MACpD,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAGtB,kFAAkF;QAClF,2EAA2E;QAC3E,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,8CAA8C,EAAE,WAAW,IAAI,CAAC,EAAE,MAC9E,CAAC,QAAU,MAAM,OAAO,CAAC,aAAa;QAExC,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,8CAA8C,EAAE,oBAAoB,IAAI,CAAC,EAAE,MACvF,CAAC,QAAU,MAAM,OAAO,CAAC,cAAc;QAGzC,+DAA+D;QAC/D,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,WAAW,EAAE,WAAW,QAAQ,CAAC,EAAE,MAC/C,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC;QAEvB,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,WAAW,EAAE,oBAAoB,QAAQ,CAAC,EAAE,MACxD,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC;QAGvB,kEAAkE;QAClE,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,eAAe,EAAE,WAAW,IAAI,CAAC,EAAE,MAC/C,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAE1B,cAAc,YAAY,OAAO,CAC/B,IAAI,OAAO,CAAC,eAAe,EAAE,oBAAoB,IAAI,CAAC,EAAE,MACxD,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IAE5B;IAEA,8EAA8E;IAC9E,cAAc,uBAAuB,aAAa;IAElD,8DAA8D;IAC9D,cAAc,iBAAiB,aAAa;IAE5C,OAAO;AACT;AAEA,SAAS,uBAAuB,IAAY,EAAE,OAAY;IACxD,4CAA4C;IAC5C,OAAO,KAAK,OAAO,CAAC,6CAA6C,CAAC,OAAO,QAAQ,MAAM;QACrF,IAAI;YACF,+DAA+D;YAC/D,IAAI,KAAK,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC,WACtE,KAAK,UAAU,CAAC,kBAAkB,KAAK,QAAQ,CAAC,UAAU,CAAC,KAAK,UAAU,CAAC,QAAQ,MAAM,GAAG;gBAC9F,OAAO;YACT;YAEA,8CAA8C;YAC9C,IAAI,KAAK,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,MAAM,GAAG;gBAC3D,MAAM,UAAU,KAAK,UAAU,CAAC,OAAO,QAAQ,MAAM,GAAG,OAAO;gBAC/D,MAAM,MAAM,IAAI,sGAAG,CAAC;gBAEpB,4BAA4B;gBAC5B,IAAI,YAAY,IAAI,QAAQ;gBAC5B,IAAI,cAAc,OAAO,cAAc,IAAI;oBACzC,YAAY;gBACd,OAAO;oBACL,+CAA+C;oBAC/C,YAAY,UAAU,SAAS,CAAC;oBAChC,IAAI,CAAC,UAAU,QAAQ,CAAC,MAAM;wBAC5B,aAAa;oBACf;gBACF;gBAEA,OAAO,CAAC,EAAE,EAAE,OAAO,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,wCAAwC;YACxC,OAAO;QACT;IACF;AACF;AAEA,SAAS,iBAAiB,IAAY,EAAE,QAA6B;IACnE,gCAAgC;IAChC,OAAO,KAAK,OAAO,CAAC,qCAAqC,CAAC,OAAO;QAC/D,IAAI,eAAe;QAEnB,KAAK,MAAM,CAAC,aAAa,UAAU,IAAI,SAAU;YAC/C,MAAM,aAAa,aAAa;YAEhC,sBAAsB;YACtB,eAAe,aAAa,OAAO,CACjC,IAAI,OAAO,CAAC,WAAW,EAAE,WAAW,QAAQ,CAAC,EAAE,MAC/C,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC;YAGvB,eAAe,aAAa,OAAO,CACjC,IAAI,OAAO,CAAC,eAAe,EAAE,WAAW,IAAI,CAAC,EAAE,MAC/C,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAE5B;QAEA,OAAO,MAAM,OAAO,CAAC,YAAY;IACnC;AACF;AAEA,SAAS,aAAa,MAAc;IAClC,OAAO,OAAO,OAAO,CAAC,uBAAuB;AAC/C;AAEA,SAAS,wBAAwB,YAA2B,EAAE,OAAY;IACxE,MAAM,WAAW,QAAQ,QAAQ,CAAC,OAAO,CAAC,QAAQ;IAClD,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAExD,IAAI,WAAW,CAAC,mBAAmB,EAAE,SAAS,IAAI,CAAC;IACnD,YAAY,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;IAC5C,YAAY,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC;IAC3C,YAAY,CAAC,mBAAmB,EAAE,aAAa,MAAM,CAAC,IAAI,CAAC;IAC3D,YAAY,CAAC,OAAO,CAAC;IAErB,oBAAoB;IACpB,YAAY,CAAC,wBAAwB,CAAC;IACtC,aAAa,OAAO,CAAC,CAAC,MAAM;QAC1B,MAAM,MAAM,IAAI,sGAAG,CAAC,KAAK,GAAG;QAC5B,MAAM,WAAW,KAAK,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,OAAO;QAChE,YAAY,GAAG,QAAQ,EAAE,GAAG,EAAE,SAAS,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAC;IACjE;IACA,YAAY,CAAC,SAAS,CAAC;IAEvB,eAAe;IACf,aAAa,OAAO,CAAC,CAAC,MAAM;QAC1B,MAAM,MAAM,IAAI,sGAAG,CAAC,KAAK,GAAG;QAC5B,MAAM,WAAW,KAAK,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,OAAO;QAEhE,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS,IAAI,CAAC;QACnD,YAAY,CAAC,SAAS,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;QACpC,IAAI,KAAK,KAAK,EAAE;YACd,YAAY,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;QAC1C;QACA,YAAY,CAAC,EAAE,CAAC;QAEhB,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,IAAI,IAAI;YAC/C,oCAAoC;YACpC,IAAI,UAAU,KAAK,WAAW,CAC3B,OAAO,CAAC,QAAQ,KAAM,uBAAuB;aAC7C,OAAO,CAAC,QAAQ,SAAU,uCAAuC;aACjE,OAAO,CAAC,UAAU,QAAS,6BAA6B;aACxD,IAAI;YAEP,YAAY,GAAG,QAAQ,IAAI,CAAC;QAC9B,OAAO;YACL,YAAY,CAAC,+CAA+C,CAAC;QAC/D;QAEA,YAAY,CAAC,OAAO,CAAC;IACvB;IAEA,SAAS;IACT,YAAY,CAAC,yBAAyB,CAAC;IACvC,YAAY,CAAC,mBAAmB,EAAE,aAAa,MAAM,CAAC,EAAE,CAAC;IACzD,YAAY,CAAC,iDAAiD,CAAC;IAC/D,YAAY,CAAC,iCAAiC,CAAC;IAC/C,YAAY,CAAC,YAAY,EAAE,IAAI,OAAO,cAAc,GAAG,IAAI,CAAC;IAC5D,YAAY,CAAC,2GAA2G,CAAC;IAEzH,OAAO;AACT", "debugId": null}}]}