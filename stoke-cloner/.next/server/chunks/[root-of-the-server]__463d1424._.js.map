{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/src/app/api/local-server/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { spawn } from 'child_process';\nimport fs from 'fs';\nimport path from 'path';\nimport os from 'os';\nimport { promisify } from 'util';\nimport { exec } from 'child_process';\nimport AdmZip from 'adm-zip';\n\nconst execAsync = promisify(exec);\n\n// Store active servers to avoid conflicts\nconst activeServers = new Map<number, { process: any; directory: string }>();\n\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  });\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n    \n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    // Create a temporary directory for extraction\n    const tempDir = path.join(os.tmpdir(), `stoke-clone-${Date.now()}`);\n    fs.mkdirSync(tempDir, { recursive: true });\n\n    try {\n      // Save the uploaded ZIP file\n      const zipPath = path.join(tempDir, 'website.zip');\n      const arrayBuffer = await file.arrayBuffer();\n      fs.writeFileSync(zipPath, Buffer.from(arrayBuffer));\n\n      // Extract the ZIP file\n      const zip = new AdmZip(zipPath);\n      const extractDir = path.join(tempDir, 'extracted');\n      zip.extractAllTo(extractDir, true);\n\n      // Find an available port\n      const port = await findAvailablePort(8000);\n      \n      // Start a local HTTP server\n      const serverProcess = await startLocalServer(extractDir, port);\n      \n      // Store the server info\n      activeServers.set(port, {\n        process: serverProcess,\n        directory: extractDir\n      });\n\n      // Clean up the ZIP file\n      fs.unlinkSync(zipPath);\n\n      // Schedule cleanup after 1 hour\n      setTimeout(() => {\n        cleanupServer(port);\n      }, 60 * 60 * 1000); // 1 hour\n\n      const localUrl = `http://localhost:${port}`;\n      \n      return NextResponse.json({\n        url: localUrl,\n        port,\n        message: 'Local server started successfully'\n      }, {\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type',\n        }\n      });\n\n    } catch (error) {\n      // Clean up temp directory on error\n      if (fs.existsSync(tempDir)) {\n        fs.rmSync(tempDir, { recursive: true, force: true });\n      }\n      throw error;\n    }\n\n  } catch (error) {\n    console.error('Error starting local server:', error);\n    return NextResponse.json(\n      { error: 'Failed to start local server' },\n      {\n        status: 500,\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type',\n        }\n      }\n    );\n  }\n}\n\nasync function findAvailablePort(startPort: number): Promise<number> {\n  for (let port = startPort; port < startPort + 100; port++) {\n    try {\n      // Check if port is available\n      await execAsync(`lsof -i :${port}`);\n      // If no error, port is in use, try next\n    } catch {\n      // Error means port is available\n      return port;\n    }\n  }\n  throw new Error('No available ports found');\n}\n\nasync function startLocalServer(directory: string, port: number): Promise<any> {\n  return new Promise((resolve, reject) => {\n    const isWindows = process.platform === 'win32';\n\n    // Try different server options in order of preference\n    const serverOptions = [\n      // Python 3 (most common)\n      { cmd: isWindows ? 'python' : 'python3', args: ['-m', 'http.server', port.toString()] },\n      // Python 2 fallback\n      { cmd: 'python', args: ['-m', 'SimpleHTTPServer', port.toString()] },\n      // Node.js serve package\n      { cmd: 'npx', args: ['serve', '-p', port.toString(), '.'] },\n      // Node.js http-server package\n      { cmd: 'npx', args: ['http-server', '-p', port.toString()] }\n    ];\n\n    let currentOption = 0;\n\n    function tryNextServer() {\n      if (currentOption >= serverOptions.length) {\n        reject(new Error('No suitable server found. Please install Python or Node.js.'));\n        return;\n      }\n\n      const option = serverOptions[currentOption];\n      console.log(`Trying server option ${currentOption + 1}: ${option.cmd} ${option.args.join(' ')}`);\n\n      const serverProcess = spawn(option.cmd, option.args, {\n        cwd: directory,\n        stdio: ['ignore', 'pipe', 'pipe'],\n        shell: isWindows\n      });\n\n      serverProcess.on('error', (error) => {\n        console.log(`Server option ${currentOption + 1} failed:`, error.message);\n        currentOption++;\n        tryNextServer();\n      });\n\n      // Wait a moment for the server to start\n      setTimeout(() => {\n        if (serverProcess.pid) {\n          console.log(`Server started successfully with option ${currentOption + 1} on port ${port}`);\n          resolve(serverProcess);\n        } else {\n          currentOption++;\n          tryNextServer();\n        }\n      }, 3000);\n    }\n\n    tryNextServer();\n  });\n}\n\nfunction cleanupServer(port: number) {\n  const serverInfo = activeServers.get(port);\n  if (serverInfo) {\n    // Kill the server process\n    if (serverInfo.process && serverInfo.process.pid) {\n      try {\n        process.kill(serverInfo.process.pid);\n      } catch (error) {\n        console.error(`Error killing server process: ${error}`);\n      }\n    }\n\n    // Clean up the directory\n    if (fs.existsSync(serverInfo.directory)) {\n      try {\n        fs.rmSync(serverInfo.directory, { recursive: true, force: true });\n      } catch (error) {\n        console.error(`Error cleaning up directory: ${error}`);\n      }\n    }\n\n    activeServers.delete(port);\n  }\n}\n\n// Cleanup endpoint\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const port = parseInt(searchParams.get('port') || '');\n    \n    if (port && activeServers.has(port)) {\n      cleanupServer(port);\n      return NextResponse.json({ message: 'Server stopped successfully' });\n    }\n    \n    return NextResponse.json({ error: 'Server not found' }, { status: 404 });\n  } catch (error) {\n    return NextResponse.json({ error: 'Failed to stop server' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;AAEA,MAAM,YAAY,IAAA,8GAAS,EAAC,2HAAI;AAEhC,0CAA0C;AAC1C,MAAM,gBAAgB,IAAI;AAEnB,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gJAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,8CAA8C;QAC9C,MAAM,UAAU,4GAAI,CAAC,IAAI,CAAC,wGAAE,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE,KAAK,GAAG,IAAI;QAClE,wGAAE,CAAC,SAAS,CAAC,SAAS;YAAE,WAAW;QAAK;QAExC,IAAI;YACF,6BAA6B;YAC7B,MAAM,UAAU,4GAAI,CAAC,IAAI,CAAC,SAAS;YACnC,MAAM,cAAc,MAAM,KAAK,WAAW;YAC1C,wGAAE,CAAC,aAAa,CAAC,SAAS,OAAO,IAAI,CAAC;YAEtC,uBAAuB;YACvB,MAAM,MAAM,IAAI,qJAAM,CAAC;YACvB,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,SAAS;YACtC,IAAI,YAAY,CAAC,YAAY;YAE7B,yBAAyB;YACzB,MAAM,OAAO,MAAM,kBAAkB;YAErC,4BAA4B;YAC5B,MAAM,gBAAgB,MAAM,iBAAiB,YAAY;YAEzD,wBAAwB;YACxB,cAAc,GAAG,CAAC,MAAM;gBACtB,SAAS;gBACT,WAAW;YACb;YAEA,wBAAwB;YACxB,wGAAE,CAAC,UAAU,CAAC;YAEd,gCAAgC;YAChC,WAAW;gBACT,cAAc;YAChB,GAAG,KAAK,KAAK,OAAO,SAAS;YAE7B,MAAM,WAAW,CAAC,iBAAiB,EAAE,MAAM;YAE3C,OAAO,gJAAY,CAAC,IAAI,CAAC;gBACvB,KAAK;gBACL;gBACA,SAAS;YACX,GAAG;gBACD,SAAS;oBACP,+BAA+B;oBAC/B,gCAAgC;oBAChC,gCAAgC;gBAClC;YACF;QAEF,EAAE,OAAO,OAAO;YACd,mCAAmC;YACnC,IAAI,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC1B,wGAAE,CAAC,MAAM,CAAC,SAAS;oBAAE,WAAW;oBAAM,OAAO;gBAAK;YACpD;YACA,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA+B,GACxC;YACE,QAAQ;YACR,SAAS;gBACP,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IAEJ;AACF;AAEA,eAAe,kBAAkB,SAAiB;IAChD,IAAK,IAAI,OAAO,WAAW,OAAO,YAAY,KAAK,OAAQ;QACzD,IAAI;YACF,6BAA6B;YAC7B,MAAM,UAAU,CAAC,SAAS,EAAE,MAAM;QAClC,wCAAwC;QAC1C,EAAE,OAAM;YACN,gCAAgC;YAChC,OAAO;QACT;IACF;IACA,MAAM,IAAI,MAAM;AAClB;AAEA,eAAe,iBAAiB,SAAiB,EAAE,IAAY;IAC7D,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,YAAY,QAAQ,QAAQ,KAAK;QAEvC,sDAAsD;QACtD,MAAM,gBAAgB;YACpB,yBAAyB;YACzB;gBAAE,KAAK,sCAAY,0BAAW;gBAAW,MAAM;oBAAC;oBAAM;oBAAe,KAAK,QAAQ;iBAAG;YAAC;YACtF,oBAAoB;YACpB;gBAAE,KAAK;gBAAU,MAAM;oBAAC;oBAAM;oBAAoB,KAAK,QAAQ;iBAAG;YAAC;YACnE,wBAAwB;YACxB;gBAAE,KAAK;gBAAO,MAAM;oBAAC;oBAAS;oBAAM,KAAK,QAAQ;oBAAI;iBAAI;YAAC;YAC1D,8BAA8B;YAC9B;gBAAE,KAAK;gBAAO,MAAM;oBAAC;oBAAe;oBAAM,KAAK,QAAQ;iBAAG;YAAC;SAC5D;QAED,IAAI,gBAAgB;QAEpB,SAAS;YACP,IAAI,iBAAiB,cAAc,MAAM,EAAE;gBACzC,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,MAAM,SAAS,aAAa,CAAC,cAAc;YAC3C,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;YAE/F,MAAM,gBAAgB,IAAA,4HAAK,EAAC,OAAO,GAAG,EAAE,OAAO,IAAI,EAAE;gBACnD,KAAK;gBACL,OAAO;oBAAC;oBAAU;oBAAQ;iBAAO;gBACjC,OAAO;YACT;YAEA,cAAc,EAAE,CAAC,SAAS,CAAC;gBACzB,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,gBAAgB,EAAE,QAAQ,CAAC,EAAE,MAAM,OAAO;gBACvE;gBACA;YACF;YAEA,wCAAwC;YACxC,WAAW;gBACT,IAAI,cAAc,GAAG,EAAE;oBACrB,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM;oBAC1F,QAAQ;gBACV,OAAO;oBACL;oBACA;gBACF;YACF,GAAG;QACL;QAEA;IACF;AACF;AAEA,SAAS,cAAc,IAAY;IACjC,MAAM,aAAa,cAAc,GAAG,CAAC;IACrC,IAAI,YAAY;QACd,0BAA0B;QAC1B,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,CAAC,GAAG,EAAE;YAChD,IAAI;gBACF,QAAQ,IAAI,CAAC,WAAW,OAAO,CAAC,GAAG;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,OAAO;YACxD;QACF;QAEA,yBAAyB;QACzB,IAAI,wGAAE,CAAC,UAAU,CAAC,WAAW,SAAS,GAAG;YACvC,IAAI;gBACF,wGAAE,CAAC,MAAM,CAAC,WAAW,SAAS,EAAE;oBAAE,WAAW;oBAAM,OAAO;gBAAK;YACjE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO;YACvD;QACF;QAEA,cAAc,MAAM,CAAC;IACvB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAElD,IAAI,QAAQ,cAAc,GAAG,CAAC,OAAO;YACnC,cAAc;YACd,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;YAA8B;QACpE;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAmB,GAAG;YAAE,QAAQ;QAAI;IACxE,EAAE,OAAO,OAAO;QACd,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}