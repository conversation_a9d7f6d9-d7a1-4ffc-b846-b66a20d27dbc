// Content script for Stoke Cloner Chrome Extension
// This script runs on every webpage and can analyze page content

class PageAnalyzer {
  constructor() {
    this.setupMessageListener();
    this.injectPageInfo();
  }
  
  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Content script received message:', request);
      
      switch (request.action) {
        case 'analyzePage':
          this.analyzePage().then(sendResponse);
          return true; // Keep message channel open for async response
          
        case 'getPageInfo':
          sendResponse(this.getBasicPageInfo());
          break;
          
        case 'highlightCloneable':
          this.highlightCloneableElements();
          sendResponse({success: true});
          break;
          
        default:
          console.warn('Unknown content script action:', request.action);
          sendResponse({success: false, error: 'Unknown action'});
      }
    });
  }
  
  // Inject basic page information into the page context
  injectPageInfo() {
    // Add a small indicator that Stoke Cloner is available
    if (window.location.protocol === 'https:' || window.location.protocol === 'http:') {
      this.addStokeIndicator();
    }
  }
  
  addStokeIndicator() {
    // Only add if not already present
    if (document.getElementById('stoke-cloner-indicator')) return;
    
    const indicator = document.createElement('div');
    indicator.id = 'stoke-cloner-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #f97316;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      z-index: 10000;
      opacity: 0.7;
      pointer-events: none;
      transition: opacity 0.3s;
    `;
    indicator.textContent = '🔧 Stoke Cloner Ready';
    
    document.body.appendChild(indicator);
    
    // Fade out after 3 seconds
    setTimeout(() => {
      indicator.style.opacity = '0';
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      }, 300);
    }, 3000);
  }
  
  // Get basic page information
  getBasicPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      domain: window.location.hostname,
      protocol: window.location.protocol,
      pathname: window.location.pathname,
      timestamp: Date.now()
    };
  }
  
  // Comprehensive page analysis for cloning
  async analyzePage() {
    try {
      const analysis = {
        basic: this.getBasicPageInfo(),
        links: this.extractLinks(),
        assets: this.extractAssets(),
        content: this.extractContent(),
        metadata: this.extractMetadata(),
        structure: this.analyzeStructure()
      };
      
      return {
        success: true,
        data: analysis
      };
    } catch (error) {
      console.error('Error analyzing page:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  // Extract all links from the page
  extractLinks() {
    const links = Array.from(document.querySelectorAll('a[href]'))
      .map(a => ({
        href: a.href,
        text: a.textContent.trim(),
        title: a.title || '',
        target: a.target || '',
        rel: a.rel || ''
      }))
      .filter(link => link.href.startsWith('http'));
    
    // Remove duplicates based on href
    const uniqueLinks = links.filter((link, index, self) => 
      index === self.findIndex(l => l.href === link.href)
    );
    
    return {
      total: uniqueLinks.length,
      internal: uniqueLinks.filter(link => link.href.includes(window.location.hostname)),
      external: uniqueLinks.filter(link => !link.href.includes(window.location.hostname)),
      all: uniqueLinks
    };
  }
  
  // Extract all assets from the page
  extractAssets() {
    const assets = {
      images: this.extractImages(),
      stylesheets: this.extractStylesheets(),
      scripts: this.extractScripts(),
      videos: this.extractVideos(),
      audio: this.extractAudio(),
      fonts: this.extractFonts(),
      other: this.extractOtherAssets()
    };
    
    // Calculate total asset count
    assets.totalCount = Object.values(assets).reduce((sum, assetArray) => {
      return sum + (Array.isArray(assetArray) ? assetArray.length : 0);
    }, 0);
    
    return assets;
  }
  
  extractImages() {
    const images = [];
    
    // Regular img tags
    document.querySelectorAll('img[src]').forEach(img => {
      images.push({
        src: img.src,
        alt: img.alt || '',
        width: img.naturalWidth || img.width,
        height: img.naturalHeight || img.height,
        type: 'img'
      });
    });
    
    // Background images from CSS
    document.querySelectorAll('*').forEach(el => {
      const style = window.getComputedStyle(el);
      const bgImage = style.backgroundImage;
      if (bgImage && bgImage !== 'none' && bgImage.includes('url(')) {
        const match = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
        if (match) {
          images.push({
            src: new URL(match[1], window.location.href).href,
            alt: '',
            width: 0,
            height: 0,
            type: 'background'
          });
        }
      }
    });
    
    return images;
  }
  
  extractStylesheets() {
    return Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link => ({
      href: link.href,
      media: link.media || 'all',
      type: link.type || 'text/css'
    }));
  }
  
  extractScripts() {
    return Array.from(document.querySelectorAll('script[src]')).map(script => ({
      src: script.src,
      type: script.type || 'text/javascript',
      async: script.async,
      defer: script.defer
    }));
  }
  
  extractVideos() {
    const videos = [];
    
    document.querySelectorAll('video').forEach(video => {
      if (video.src) {
        videos.push({src: video.src, type: 'video'});
      }
      video.querySelectorAll('source[src]').forEach(source => {
        videos.push({src: source.src, type: source.type || 'video'});
      });
    });
    
    return videos;
  }
  
  extractAudio() {
    const audio = [];
    
    document.querySelectorAll('audio').forEach(audioEl => {
      if (audioEl.src) {
        audio.push({src: audioEl.src, type: 'audio'});
      }
      audioEl.querySelectorAll('source[src]').forEach(source => {
        audio.push({src: source.src, type: source.type || 'audio'});
      });
    });
    
    return audio;
  }
  
  extractFonts() {
    const fonts = [];
    
    // Extract from link tags
    document.querySelectorAll('link[href*="font"]').forEach(link => {
      fonts.push({
        href: link.href,
        type: 'link'
      });
    });
    
    // Extract from CSS @font-face rules (basic detection)
    Array.from(document.styleSheets).forEach(sheet => {
      try {
        Array.from(sheet.cssRules || []).forEach(rule => {
          if (rule.type === CSSRule.FONT_FACE_RULE) {
            const src = rule.style.src;
            if (src) {
              const match = src.match(/url\(['"]?([^'"]+)['"]?\)/);
              if (match) {
                fonts.push({
                  href: new URL(match[1], window.location.href).href,
                  type: 'font-face'
                });
              }
            }
          }
        });
      } catch (e) {
        // Cross-origin stylesheets may not be accessible
      }
    });
    
    return fonts;
  }
  
  extractOtherAssets() {
    const other = [];
    
    // PDFs, documents, etc.
    document.querySelectorAll('a[href]').forEach(link => {
      const href = link.href.toLowerCase();
      if (href.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar)$/)) {
        other.push({
          href: link.href,
          text: link.textContent.trim(),
          type: 'document'
        });
      }
    });
    
    return other;
  }
  
  // Extract text content for markdown generation
  extractContent() {
    const content = {
      headings: this.extractHeadings(),
      paragraphs: this.extractParagraphs(),
      lists: this.extractLists(),
      tables: this.extractTables(),
      forms: this.extractForms()
    };
    
    return content;
  }
  
  extractHeadings() {
    return Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(h => ({
      level: parseInt(h.tagName.charAt(1)),
      text: h.textContent.trim(),
      id: h.id || ''
    }));
  }
  
  extractParagraphs() {
    return Array.from(document.querySelectorAll('p')).map(p => ({
      text: p.textContent.trim(),
      html: p.innerHTML
    })).filter(p => p.text.length > 0);
  }
  
  extractLists() {
    return Array.from(document.querySelectorAll('ul, ol')).map(list => ({
      type: list.tagName.toLowerCase(),
      items: Array.from(list.querySelectorAll('li')).map(li => li.textContent.trim())
    }));
  }
  
  extractTables() {
    return Array.from(document.querySelectorAll('table')).map(table => ({
      headers: Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim()),
      rows: Array.from(table.querySelectorAll('tr')).map(tr => 
        Array.from(tr.querySelectorAll('td')).map(td => td.textContent.trim())
      ).filter(row => row.length > 0)
    }));
  }
  
  extractForms() {
    return Array.from(document.querySelectorAll('form')).map(form => ({
      action: form.action || '',
      method: form.method || 'get',
      inputs: Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
        type: input.type || input.tagName.toLowerCase(),
        name: input.name || '',
        placeholder: input.placeholder || '',
        required: input.required
      }))
    }));
  }
  
  // Extract metadata
  extractMetadata() {
    const meta = {};
    
    // Basic meta tags
    document.querySelectorAll('meta').forEach(metaTag => {
      const name = metaTag.name || metaTag.property || metaTag.httpEquiv;
      const content = metaTag.content;
      if (name && content) {
        meta[name] = content;
      }
    });
    
    // Title and description
    meta.title = document.title;
    meta.description = meta.description || meta['og:description'] || '';
    
    return meta;
  }
  
  // Analyze page structure
  analyzeStructure() {
    return {
      doctype: document.doctype ? document.doctype.name : 'unknown',
      lang: document.documentElement.lang || '',
      charset: document.characterSet || '',
      viewport: document.querySelector('meta[name="viewport"]')?.content || '',
      hasServiceWorker: 'serviceWorker' in navigator,
      isSPA: this.detectSPA(),
      framework: this.detectFramework()
    };
  }
  
  detectSPA() {
    // Simple SPA detection
    return !!(
      window.history.pushState &&
      (window.React || window.Vue || window.angular || window.ng)
    );
  }
  
  detectFramework() {
    if (window.React) return 'React';
    if (window.Vue) return 'Vue';
    if (window.angular || window.ng) return 'Angular';
    if (window.jQuery || window.$) return 'jQuery';
    if (document.querySelector('[data-reactroot]')) return 'React';
    if (document.querySelector('[data-server-rendered="true"]')) return 'Vue (SSR)';
    return 'Unknown';
  }
  
  // Highlight elements that can be cloned
  highlightCloneable() {
    const style = document.createElement('style');
    style.textContent = `
      .stoke-cloner-highlight {
        outline: 2px solid #f97316 !important;
        outline-offset: 2px !important;
        background: rgba(249, 115, 22, 0.1) !important;
      }
    `;
    document.head.appendChild(style);
    
    // Highlight main content areas
    const selectors = ['main', 'article', '.content', '#content', '.post', '.article'];
    selectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(el => {
        el.classList.add('stoke-cloner-highlight');
      });
    });
    
    // Remove highlights after 5 seconds
    setTimeout(() => {
      document.querySelectorAll('.stoke-cloner-highlight').forEach(el => {
        el.classList.remove('stoke-cloner-highlight');
      });
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
    }, 5000);
  }
}

// Initialize content script
console.log('Stoke Cloner content script loaded');
new PageAnalyzer();
