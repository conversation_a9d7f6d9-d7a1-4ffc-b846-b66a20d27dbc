/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-scroll-snap-strictness: proximity;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-scroll-snap-strictness: proximity;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
    --color-red-50: #fef2f2;
    --color-red-100: #ffe2e2;
    --color-red-200: #ffcaca;
    --color-red-300: #ffa3a3;
    --color-red-400: #ff6568;
    --color-red-500: #fb2c36;
    --color-red-600: #e40014;
    --color-red-700: #bf000f;
    --color-red-800: #9f0712;
    --color-red-900: #82181a;
    --color-red-950: #460809;
    --color-orange-50: #fff7ed;
    --color-orange-100: #ffedd5;
    --color-orange-200: #ffd7a8;
    --color-orange-300: #ffb96d;
    --color-orange-400: #ff8b1a;
    --color-orange-500: #fe6e00;
    --color-orange-600: #f05100;
    --color-orange-700: #c53c00;
    --color-orange-800: #9f2d00;
    --color-orange-900: #7e2a0c;
    --color-orange-950: #441306;
    --color-amber-50: #fffbeb;
    --color-amber-100: #fef3c6;
    --color-amber-200: #fee685;
    --color-amber-300: #ffd236;
    --color-amber-400: #fcbb00;
    --color-amber-500: #f99c00;
    --color-amber-600: #dd7400;
    --color-amber-700: #b75000;
    --color-amber-800: #953d00;
    --color-amber-900: #7b3306;
    --color-amber-950: #461901;
    --color-yellow-50: #fefce8;
    --color-yellow-100: #fef9c2;
    --color-yellow-200: #fff085;
    --color-yellow-300: #ffe02a;
    --color-yellow-400: #fac800;
    --color-yellow-500: #edb200;
    --color-yellow-600: #cd8900;
    --color-yellow-700: #a36100;
    --color-yellow-800: #874b00;
    --color-yellow-900: #733e0a;
    --color-yellow-950: #432004;
    --color-lime-50: #f7fee7;
    --color-lime-100: #ecfcca;
    --color-lime-200: #d8f999;
    --color-lime-300: #bbf451;
    --color-lime-400: #9de500;
    --color-lime-500: #80cd00;
    --color-lime-600: #62a400;
    --color-lime-700: #4b7d00;
    --color-lime-800: #3d6300;
    --color-lime-900: #35530e;
    --color-lime-950: #192e03;
    --color-green-50: #f0fdf4;
    --color-green-100: #dcfce7;
    --color-green-200: #b9f8cf;
    --color-green-300: #7bf1a8;
    --color-green-400: #05df72;
    --color-green-500: #00c758;
    --color-green-600: #00a544;
    --color-green-700: #008138;
    --color-green-800: #016630;
    --color-green-900: #0d542b;
    --color-green-950: #032e15;
    --color-emerald-50: #ecfdf5;
    --color-emerald-100: #d0fae5;
    --color-emerald-200: #a4f4cf;
    --color-emerald-300: #5ee9b5;
    --color-emerald-400: #00d294;
    --color-emerald-500: #00bb7f;
    --color-emerald-600: #009767;
    --color-emerald-700: #007956;
    --color-emerald-800: #005f46;
    --color-emerald-900: #004e3b;
    --color-emerald-950: #002c22;
    --color-teal-50: #f0fdfa;
    --color-teal-100: #cbfbf1;
    --color-teal-200: #96f7e4;
    --color-teal-300: #46ecd5;
    --color-teal-400: #00d3bd;
    --color-teal-500: #00baa7;
    --color-teal-600: #009588;
    --color-teal-700: #00776e;
    --color-teal-800: #005f5a;
    --color-teal-900: #0b4f4a;
    --color-teal-950: #022f2e;
    --color-cyan-50: #ecfeff;
    --color-cyan-100: #cefafe;
    --color-cyan-200: #a2f4fd;
    --color-cyan-300: #53eafd;
    --color-cyan-400: #00d2ef;
    --color-cyan-500: #00b7d7;
    --color-cyan-600: #0092b5;
    --color-cyan-700: #007492;
    --color-cyan-800: #005f78;
    --color-cyan-900: #104e64;
    --color-cyan-950: #053345;
    --color-sky-50: #f0f9ff;
    --color-sky-100: #dff2fe;
    --color-sky-200: #b8e6fe;
    --color-sky-300: #77d4ff;
    --color-sky-400: #00bcfe;
    --color-sky-500: #00a5ef;
    --color-sky-600: #0084cc;
    --color-sky-700: #0069a4;
    --color-sky-800: #005986;
    --color-sky-900: #024a70;
    --color-sky-950: #052f4a;
    --color-blue-50: #eff6ff;
    --color-blue-100: #dbeafe;
    --color-blue-200: #bedbff;
    --color-blue-300: #90c5ff;
    --color-blue-400: #54a2ff;
    --color-blue-500: #3080ff;
    --color-blue-600: #155dfc;
    --color-blue-700: #1447e6;
    --color-blue-800: #193cb8;
    --color-blue-900: #1c398e;
    --color-blue-950: #162456;
    --color-indigo-50: #eef2ff;
    --color-indigo-100: #e0e7ff;
    --color-indigo-200: #c7d2ff;
    --color-indigo-300: #a4b3ff;
    --color-indigo-400: #7d87ff;
    --color-indigo-500: #625fff;
    --color-indigo-600: #4f39f6;
    --color-indigo-700: #432dd7;
    --color-indigo-800: #372aac;
    --color-indigo-900: #312c85;
    --color-indigo-950: #1e1a4d;
    --color-violet-50: #f5f3ff;
    --color-violet-100: #ede9fe;
    --color-violet-200: #ddd6ff;
    --color-violet-300: #c4b4ff;
    --color-violet-400: #a685ff;
    --color-violet-500: #8d54ff;
    --color-violet-600: #7f22fe;
    --color-violet-700: #7008e7;
    --color-violet-800: #5d0ec0;
    --color-violet-900: #4d179a;
    --color-violet-950: #2f0d68;
    --color-purple-50: #faf5ff;
    --color-purple-100: #f3e8ff;
    --color-purple-200: #e9d5ff;
    --color-purple-300: #d9b3ff;
    --color-purple-400: #c07eff;
    --color-purple-500: #ac4bff;
    --color-purple-600: #9810fa;
    --color-purple-700: #8200da;
    --color-purple-800: #6e11b0;
    --color-purple-900: #59168b;
    --color-purple-950: #3c0366;
    --color-fuchsia-50: #fdf4ff;
    --color-fuchsia-100: #fae8ff;
    --color-fuchsia-200: #f6cfff;
    --color-fuchsia-300: #f2a9ff;
    --color-fuchsia-400: #ec6cff;
    --color-fuchsia-500: #e12afb;
    --color-fuchsia-600: #c600db;
    --color-fuchsia-700: #a600b5;
    --color-fuchsia-800: #8a0194;
    --color-fuchsia-900: #721378;
    --color-fuchsia-950: #4b004f;
    --color-pink-50: #fdf2f8;
    --color-pink-100: #fce7f3;
    --color-pink-200: #fccee8;
    --color-pink-300: #fda5d5;
    --color-pink-400: #fb64b6;
    --color-pink-500: #f6339a;
    --color-pink-600: #e30076;
    --color-pink-700: #c4005c;
    --color-pink-800: #a2004c;
    --color-pink-900: #861043;
    --color-pink-950: #510424;
    --color-rose-50: #fff1f2;
    --color-rose-100: #ffe4e6;
    --color-rose-200: #ffccd3;
    --color-rose-300: #ffa2ae;
    --color-rose-400: #ff667f;
    --color-rose-500: #ff2357;
    --color-rose-600: #e70044;
    --color-rose-700: #c20039;
    --color-rose-800: #a30037;
    --color-rose-900: #8b0836;
    --color-rose-950: #4d0218;
    --color-slate-50: #f8fafc;
    --color-slate-200: #e2e8f0;
    --color-slate-300: #cad5e2;
    --color-slate-400: #90a1b9;
    --color-slate-500: #62748e;
    --color-slate-600: #45556c;
    --color-slate-700: #314158;
    --color-slate-800: #1d293d;
    --color-slate-900: #0f172b;
    --color-slate-950: #020618;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5dc;
    --color-gray-400: #99a1af;
    --color-gray-500: #6a7282;
    --color-gray-600: #4a5565;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-gray-950: #030712;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-sm: 40rem;
    --breakpoint-md: 48rem;
    --breakpoint-lg: 64rem;
    --breakpoint-xl: 80rem;
    --breakpoint-2xl: 96rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --tracking-tighter: -.05em;
    --tracking-tight: -.025em;
    --tracking-widest: .1em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --ease-in: cubic-bezier(.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, .2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-lg: 16px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-50: color(display-p3 .988669 .951204 .950419);
      --color-red-100: color(display-p3 .980386 .889727 .887779);
      --color-red-200: color(display-p3 .969562 .798149 .794299);
      --color-red-300: color(display-p3 .956922 .651886 .645122);
      --color-red-400: color(display-p3 .933534 .431676 .423491);
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-red-600: color(display-p3 .830323 .140383 .133196);
      --color-red-700: color(display-p3 .692737 .116232 .104679);
      --color-red-800: color(display-p3 .569606 .121069 .108493);
      --color-red-900: color(display-p3 .466906 .133198 .12139);
      --color-red-950: color(display-p3 .25064 .0550983 .0468818);
      --color-orange-50: color(display-p3 .99533 .970132 .933499);
      --color-orange-100: color(display-p3 .988762 .931393 .843273);
      --color-orange-200: color(display-p3 .974647 .84705 .680111);
      --color-orange-300: color(display-p3 .96801 .734346 .464159);
      --color-orange-400: color(display-p3 .950192 .561807 .211017);
      --color-orange-500: color(display-p3 .946589 .449788 .0757345);
      --color-orange-600: color(display-p3 .887467 .341665 .0219962);
      --color-orange-700: color(display-p3 .729844 .257256 .0511062);
      --color-orange-800: color(display-p3 .575591 .21198 .082665);
      --color-orange-900: color(display-p3 .457137 .188093 .0897628);
      --color-orange-950: color(display-p3 .244276 .0875626 .0387614);
      --color-amber-50: color(display-p3 .997804 .985047 .926312);
      --color-amber-100: color(display-p3 .989391 .954583 .796328);
      --color-amber-200: color(display-p3 .979824 .904554 .57325);
      --color-amber-300: color(display-p3 .974327 .83063 .33298);
      --color-amber-400: color(display-p3 .959186 .738519 .118268);
      --color-amber-500: color(display-p3 .93994 .620584 .0585367);
      --color-amber-600: color(display-p3 .827144 .467166 .0336006);
      --color-amber-700: color(display-p3 .67989 .32771 .0520514);
      --color-amber-800: color(display-p3 .547533 .256058 .0728326);
      --color-amber-900: color(display-p3 .445958 .216422 .0823321);
      --color-amber-950: color(display-p3 .252662 .109091 .026881);
      --color-yellow-50: color(display-p3 .994197 .988062 .917538);
      --color-yellow-100: color(display-p3 .993436 .977463 .782913);
      --color-yellow-200: color(display-p3 .988789 .943116 .579188);
      --color-yellow-300: color(display-p3 .982669 .880884 .32102);
      --color-yellow-400: color(display-p3 .959941 .790171 .0585198);
      --color-yellow-500: color(display-p3 .903651 .703062 .0745389);
      --color-yellow-600: color(display-p3 .776342 .542492 .041709);
      --color-yellow-700: color(display-p3 .613146 .386658 .0579687);
      --color-yellow-800: color(display-p3 .503181 .30478 .075537);
      --color-yellow-900: color(display-p3 .422485 .252729 .095052);
      --color-yellow-950: color(display-p3 .243647 .131076 .0413888);
      --color-lime-50: color(display-p3 .973995 .995624 .914278);
      --color-lime-100: color(display-p3 .936811 .986772 .81208);
      --color-lime-200: color(display-p3 .872841 .974338 .642611);
      --color-lime-300: color(display-p3 .778594 .948857 .421857);
      --color-lime-400: color(display-p3 .66872 .893772 .208808);
      --color-lime-500: color(display-p3 .5627 .801803 .131231);
      --color-lime-600: color(display-p3 .435354 .640614 .0909333);
      --color-lime-700: color(display-p3 .333353 .486326 .0944279);
      --color-lime-800: color(display-p3 .271455 .384177 .0975063);
      --color-lime-900: color(display-p3 .233682 .324055 .105897);
      --color-lime-950: color(display-p3 .117864 .17918 .0379316);
      --color-green-50: color(display-p3 .950677 .990571 .959366);
      --color-green-100: color(display-p3 .885269 .984329 .910368);
      --color-green-200: color(display-p3 .776442 .964383 .823412);
      --color-green-300: color(display-p3 .600292 .935514 .68114);
      --color-green-400: color(display-p3 .399536 .862346 .49324);
      --color-green-500: color(display-p3 .308734 .774754 .374307);
      --color-green-600: color(display-p3 .243882 .640824 .294808);
      --color-green-700: color(display-p3 .198355 .501799 .245335);
      --color-green-800: color(display-p3 .168568 .395123 .211217);
      --color-green-900: color(display-p3 .147288 .323577 .185694);
      --color-green-950: color(display-p3 .0670178 .177818 .0930111);
      --color-emerald-50: color(display-p3 .936818 .989882 .961937);
      --color-emerald-100: color(display-p3 .848335 .975974 .901691);
      --color-emerald-200: color(display-p3 .713164 .947563 .822283);
      --color-emerald-300: color(display-p3 .524941 .903425 .722352);
      --color-emerald-400: color(display-p3 .334701 .819603 .591575);
      --color-emerald-500: color(display-p3 .267113 .726847 .508397);
      --color-emerald-600: color(display-p3 .206557 .589057 .413962);
      --color-emerald-700: color(display-p3 .164041 .470229 .343508);
      --color-emerald-800: color(display-p3 .135396 .371401 .277561);
      --color-emerald-900: color(display-p3 .117821 .302975 .234501);
      --color-emerald-950: color(display-p3 .0549113 .170434 .13484);
      --color-teal-50: color(display-p3 .951444 .990904 .98112);
      --color-teal-100: color(display-p3 .834533 .980328 .946428);
      --color-teal-200: color(display-p3 .675869 .957879 .896029);
      --color-teal-300: color(display-p3 .484989 .914726 .835079);
      --color-teal-400: color(display-p3 .322787 .822056 .744388);
      --color-teal-500: color(display-p3 .266569 .721518 .655462);
      --color-teal-600: color(display-p3 .207114 .579584 .53668);
      --color-teal-700: color(display-p3 .174094 .461318 .433866);
      --color-teal-800: color(display-p3 .1457 .367051 .350749);
      --color-teal-900: color(display-p3 .135344 .303314 .290629);
      --color-teal-950: color(display-p3 .0664925 .181482 .179465);
      --color-cyan-50: color(display-p3 .938135 .993772 .998465);
      --color-cyan-100: color(display-p3 .843085 .97553 .992573);
      --color-cyan-200: color(display-p3 .707418 .947027 .984826);
      --color-cyan-300: color(display-p3 .503734 .904871 .979358);
      --color-cyan-400: color(display-p3 .294638 .813991 .934996);
      --color-cyan-500: color(display-p3 .246703 .710032 .841444);
      --color-cyan-600: color(display-p3 .193249 .564651 .707197);
      --color-cyan-700: color(display-p3 .164124 .451431 .570574);
      --color-cyan-800: color(display-p3 .151437 .365208 .461053);
      --color-cyan-900: color(display-p3 .142586 .302008 .385094);
      --color-cyan-950: color(display-p3 .0805271 .197254 .263206);
      --color-sky-50: color(display-p3 .946501 .975402 .998336);
      --color-sky-100: color(display-p3 .889195 .946622 .992447);
      --color-sky-200: color(display-p3 .758153 .897447 .986665);
      --color-sky-300: color(display-p3 .546356 .822561 .990576);
      --color-sky-400: color(display-p3 .305975 .725011 .980173);
      --color-sky-500: color(display-p3 .219113 .639027 .931479);
      --color-sky-600: color(display-p3 .162505 .509376 .796082);
      --color-sky-700: color(display-p3 .130314 .404568 .640847);
      --color-sky-800: color(display-p3 .120719 .343083 .524405);
      --color-sky-900: color(display-p3 .115471 .285486 .428673);
      --color-sky-950: color(display-p3 .0726429 .180783 .28188);
      --color-blue-50: color(display-p3 .941826 .963151 .995385);
      --color-blue-100: color(display-p3 .869214 .915931 .989622);
      --color-blue-200: color(display-p3 .76688 .855207 .987483);
      --color-blue-300: color(display-p3 .602559 .767214 .993938);
      --color-blue-400: color(display-p3 .397443 .62813 .992116);
      --color-blue-500: color(display-p3 .266422 .491219 .988624);
      --color-blue-600: color(display-p3 .174493 .358974 .950247);
      --color-blue-700: color(display-p3 .1379 .274983 .867624);
      --color-blue-800: color(display-p3 .134023 .230646 .695537);
      --color-blue-900: color(display-p3 .136395 .219428 .537145);
      --color-blue-950: color(display-p3 .0993202 .141621 .323413);
      --color-indigo-50: color(display-p3 .936215 .948621 .995621);
      --color-indigo-100: color(display-p3 .883035 .90499 .993138);
      --color-indigo-200: color(display-p3 .786558 .821755 .988451);
      --color-indigo-300: color(display-p3 .650892 .700156 .990824);
      --color-indigo-400: color(display-p3 .494992 .525291 .985107);
      --color-indigo-500: color(display-p3 .380374 .372235 .971707);
      --color-indigo-600: color(display-p3 .297656 .227891 .929242);
      --color-indigo-700: color(display-p3 .251282 .180274 .81203);
      --color-indigo-800: color(display-p3 .207204 .165242 .647584);
      --color-indigo-900: color(display-p3 .188425 .173312 .503066);
      --color-indigo-950: color(display-p3 .114685 .103142 .289065);
      --color-violet-50: color(display-p3 .959212 .95304 .995713);
      --color-violet-100: color(display-p3 .926222 .913546 .990178);
      --color-violet-200: color(display-p3 .861543 .838846 .988006);
      --color-violet-300: color(display-p3 .758872 .706261 .991729);
      --color-violet-400: color(display-p3 .631215 .522175 .990008);
      --color-violet-500: color(display-p3 .523372 .329605 .990884);
      --color-violet-600: color(display-p3 .459951 .162666 .957985);
      --color-violet-700: color(display-p3 .40161 .0841901 .871151);
      --color-violet-800: color(display-p3 .333914 .0857549 .723825);
      --color-violet-900: color(display-p3 .277841 .103712 .580169);
      --color-violet-950: color(display-p3 .166776 .0591626 .389813);
      --color-purple-50: color(display-p3 .977045 .961759 .996715);
      --color-purple-100: color(display-p3 .945034 .910569 .992972);
      --color-purple-200: color(display-p3 .901181 .835978 .992237);
      --color-purple-300: color(display-p3 .829394 .703737 .996084);
      --color-purple-400: color(display-p3 .719919 .492497 .995173);
      --color-purple-500: color(display-p3 .629519 .30089 .990817);
      --color-purple-600: color(display-p3 .546729 .130167 .94439);
      --color-purple-700: color(display-p3 .465298 .0652579 .824397);
      --color-purple-800: color(display-p3 .393513 .10339 .664476);
      --color-purple-900: color(display-p3 .321698 .107597 .524563);
      --color-purple-950: color(display-p3 .212466 .030662 .385137);
      --color-fuchsia-50: color(display-p3 .985378 .957946 .995991);
      --color-fuchsia-100: color(display-p3 .969181 .911466 .995042);
      --color-fuchsia-200: color(display-p3 .939526 .818938 .987975);
      --color-fuchsia-300: color(display-p3 .91137 .670052 .986215);
      --color-fuchsia-400: color(display-p3 .86677 .447756 .979034);
      --color-fuchsia-500: color(display-p3 .81268 .241966 .951571);
      --color-fuchsia-600: color(display-p3 .716587 .083681 .841036);
      --color-fuchsia-700: color(display-p3 .601715 .077561 .692792);
      --color-fuchsia-800: color(display-p3 .49483 .0920344 .561488);
      --color-fuchsia-900: color(display-p3 .41021 .112087 .453633);
      --color-fuchsia-950: color(display-p3 .26664 .0315437 .299074);
      --color-pink-50: color(display-p3 .983916 .950076 .970581);
      --color-pink-100: color(display-p3 .974549 .908208 .950227);
      --color-pink-200: color(display-p3 .95913 .815822 .905269);
      --color-pink-300: color(display-p3 .944378 .662026 .8283);
      --color-pink-400: color(display-p3 .915485 .430491 .70271);
      --color-pink-500: color(display-p3 .88894 .276457 .595049);
      --color-pink-600: color(display-p3 .825649 .141205 .459338);
      --color-pink-700: color(display-p3 .711537 .101006 .358209);
      --color-pink-800: color(display-p3 .584151 .105343 .297396);
      --color-pink-900: color(display-p3 .480862 .117563 .26009);
      --color-pink-950: color(display-p3 .289266 .0492215 .139542);
      --color-rose-50: color(display-p3 .989671 .946597 .949215);
      --color-rose-100: color(display-p3 .982241 .897232 .902568);
      --color-rose-200: color(display-p3 .96875 .808776 .827317);
      --color-rose-300: color(display-p3 .96017 .647703 .683715);
      --color-rose-400: color(display-p3 .942928 .430764 .503101);
      --color-rose-500: color(display-p3 .921824 .240748 .355666);
      --color-rose-600: color(display-p3 .848792 .102011 .269259);
      --color-rose-700: color(display-p3 .711801 .0770816 .226777);
      --color-rose-800: color(display-p3 .591248 .0929065 .220097);
      --color-rose-900: color(display-p3 .498064 .104884 .214595);
      --color-rose-950: color(display-p3 .275407 .0406065 .0990695);
      --color-slate-50: color(display-p3 .974377 .979815 .986207);
      --color-slate-200: color(display-p3 .890322 .909405 .939294);
      --color-slate-300: color(display-p3 .800294 .834432 .882803);
      --color-slate-400: color(display-p3 .577446 .629622 .716602);
      --color-slate-500: color(display-p3 .397645 .452653 .547642);
      --color-slate-600: color(display-p3 .283418 .332214 .416355);
      --color-slate-700: color(display-p3 .205992 .253487 .336039);
      --color-slate-800: color(display-p3 .121994 .158688 .232363);
      --color-slate-900: color(display-p3 .0639692 .0891152 .163036);
      --color-slate-950: color(display-p3 .0102437 .0227555 .0878731);
      --color-gray-50: color(display-p3 .977213 .98084 .985102);
      --color-gray-100: color(display-p3 .953567 .956796 .964321);
      --color-gray-200: color(display-p3 .899787 .906171 .92106);
      --color-gray-300: color(display-p3 .822033 .835264 .858521);
      --color-gray-400: color(display-p3 .605734 .630385 .680158);
      --color-gray-500: color(display-p3 .421287 .446085 .504784);
      --color-gray-600: color(display-p3 .297358 .332176 .39043);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
      --color-gray-950: color(display-p3 .0144294 .0270755 .068534);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-50: lab(96.5005% 4.18508 1.52328);
      --color-red-100: lab(92.243% 10.2865 3.83865);
      --color-red-200: lab(86.017% 19.8815 7.75869);
      --color-red-300: lab(76.5514% 36.422 15.5335);
      --color-red-400: lab(63.7053% 60.745 31.3109);
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-red-600: lab(48.4493% 77.4328 61.5452);
      --color-red-700: lab(40.4273% 67.2623 53.7441);
      --color-red-800: lab(33.7174% 55.8993 41.0293);
      --color-red-900: lab(28.5139% 44.5539 29.0463);
      --color-red-950: lab(13.003% 29.04 16.7519);
      --color-orange-50: lab(97.7008% 1.53735 5.90649);
      --color-orange-100: lab(94.7127% 3.58394 14.3151);
      --color-orange-200: lab(88.4871% 9.94918 28.8378);
      --color-orange-300: lab(80.8059% 21.7313 50.4455);
      --color-orange-400: lab(70.0429% 42.5156 75.8207);
      --color-orange-500: lab(64.272% 57.1788 90.3583);
      --color-orange-600: lab(57.1026% 64.2584 89.8886);
      --color-orange-700: lab(46.4615% 57.7275 70.8507);
      --color-orange-800: lab(37.1566% 46.6433 50.5562);
      --color-orange-900: lab(30.2951% 36.0434 37.671);
      --color-orange-950: lab(14.1747% 23.4515 19.4461);
      --color-amber-50: lab(98.6252% -.635922 8.42309);
      --color-amber-100: lab(95.916% -1.21653 23.111);
      --color-amber-200: lab(91.7203% -.505269 49.9084);
      --color-amber-300: lab(86.4156% 6.13147 78.3961);
      --color-amber-400: lab(80.1641% 16.6016 99.2089);
      --color-amber-500: lab(72.7183% 31.8672 97.9407);
      --color-amber-600: lab(60.3514% 40.5624 87.1228);
      --color-amber-700: lab(47.2709% 42.9082 69.2966);
      --color-amber-800: lab(37.8822% 37.1699 52.2718);
      --color-amber-900: lab(31.2288% 30.2627 40.0378);
      --color-amber-950: lab(15.8111% 20.9107 23.3752);
      --color-yellow-50: lab(98.6846% -1.79055 9.7766);
      --color-yellow-100: lab(97.3564% -4.51407 27.344);
      --color-yellow-200: lab(94.3433% -5.00429 52.9663);
      --color-yellow-300: lab(89.7033% -.480294 84.4917);
      --color-yellow-400: lab(83.2664% 8.65132 106.895);
      --color-yellow-500: lab(76.3898% 14.5258 98.4589);
      --color-yellow-600: lab(62.7799% 22.4197 86.1544);
      --color-yellow-700: lab(47.8202% 25.2426 66.5015);
      --color-yellow-800: lab(38.7484% 23.5833 51.4916);
      --color-yellow-900: lab(32.3865% 21.1273 38.5959);
      --color-yellow-950: lab(16.8146% 15.7422 23.1133);
      --color-lime-50: lab(98.7039% -5.32573 10.2149);
      --color-lime-100: lab(96.8662% -11.7133 22.0854);
      --color-lime-200: lab(94.0718% -22.5338 42.5238);
      --color-lime-300: lab(89.9218% -35.6546 68.5254);
      --color-lime-400: lab(83.7876% -45.0447 88.4738);
      --color-lime-500: lab(75.3197% -46.6547 86.1778);
      --color-lime-600: lab(61.1055% -41.0235 73.1483);
      --color-lime-700: lab(47.246% -32.2589 55.8249);
      --color-lime-800: lab(37.7655% -25.1694 43.0683);
      --color-lime-900: lab(31.9931% -20.7654 33.7379);
      --color-lime-950: lab(16.5113% -15.1841 22.0145);
      --color-green-50: lab(98.1563% -5.60117 2.75915);
      --color-green-100: lab(96.1861% -13.8464 6.52365);
      --color-green-200: lab(92.4222% -26.4702 12.9427);
      --color-green-300: lab(86.9953% -47.2691 25.0054);
      --color-green-400: lab(78.503% -64.9265 39.7492);
      --color-green-500: lab(70.5521% -66.5147 45.8073);
      --color-green-600: lab(59.0978% -58.6621 41.2579);
      --color-green-700: lab(47.0329% -47.0239 31.4788);
      --color-green-800: lab(37.4616% -36.7971 22.9692);
      --color-green-900: lab(30.797% -29.6927 17.382);
      --color-green-950: lab(15.6845% -20.4225 11.7249);
      --color-emerald-50: lab(97.8462% -6.94966 1.85487);
      --color-emerald-100: lab(94.9004% -17.0769 5.63836);
      --color-emerald-200: lab(90.2247% -31.039 9.47084);
      --color-emerald-300: lab(83.9203% -48.7124 13.8849);
      --color-emerald-400: lab(75.0771% -60.7313 19.4147);
      --color-emerald-500: lab(66.9756% -58.27 19.5419);
      --color-emerald-600: lab(55.0481% -49.9246 15.93);
      --color-emerald-700: lab(44.4871% -41.0396 11.0361);
      --color-emerald-800: lab(35.3675% -33.1188 8.04002);
      --color-emerald-900: lab(28.8637% -26.9249 5.45986);
      --color-emerald-950: lab(15.0582% -17.9507 2.38369);
      --color-teal-50: lab(98.3189% -4.74921 -.111711);
      --color-teal-100: lab(95.1845% -17.4212 -.425422);
      --color-teal-200: lab(90.7612% -33.1343 -.542295);
      --color-teal-300: lab(84.8977% -48.1516 -1.3321);
      --color-teal-400: lab(76.0109% -53.3483 -2.27906);
      --color-teal-500: lab(67.3859% -49.0983 -2.63511);
      --color-teal-600: lab(55.0223% -41.0774 -3.90277);
      --color-teal-700: lab(44.4134% -33.1436 -4.22149);
      --color-teal-800: lab(35.5975% -26.6648 -4.34487);
      --color-teal-900: lab(29.506% -21.4706 -3.59886);
      --color-teal-950: lab(16.6371% -15.3183 -3.81732);
      --color-cyan-50: lab(98.3304% -5.97432 -2.62108);
      --color-cyan-100: lab(95.3146% -13.8285 -6.84732);
      --color-cyan-200: lab(91.0821% -24.0435 -12.8306);
      --color-cyan-300: lab(85.3886% -36.7636 -21.5716);
      --color-cyan-400: lab(76.6045% -40.9406 -29.6231);
      --color-cyan-500: lab(67.805% -35.3952 -30.2018);
      --color-cyan-600: lab(55.1767% -26.7496 -30.5139);
      --color-cyan-700: lab(44.7267% -21.5987 -26.118);
      --color-cyan-800: lab(36.5114% -17.1989 -21.6292);
      --color-cyan-900: lab(30.372% -13.1853 -18.7887);
      --color-cyan-950: lab(19.1528% -9.68757 -15.5267);
      --color-sky-50: lab(97.3623% -2.33802 -4.13098);
      --color-sky-100: lab(94.3709% -4.56053 -8.23453);
      --color-sky-200: lab(88.6983% -11.3978 -16.8488);
      --color-sky-300: lab(80.3307% -20.2945 -31.385);
      --color-sky-400: lab(70.687% -23.6078 -45.9483);
      --color-sky-500: lab(63.3038% -18.433 -51.0407);
      --color-sky-600: lab(51.7754% -11.4712 -49.8349);
      --color-sky-700: lab(41.6013% -9.10804 -42.5647);
      --color-sky-800: lab(35.164% -9.57692 -34.4068);
      --color-sky-900: lab(29.1959% -8.34689 -28.2453);
      --color-sky-950: lab(17.8299% -5.31271 -21.1584);
      --color-blue-50: lab(96.492% -1.14644 -5.11479);
      --color-blue-100: lab(92.0301% -2.24757 -11.6453);
      --color-blue-200: lab(86.15% -4.04379 -21.0797);
      --color-blue-300: lab(77.5052% -6.4629 -36.42);
      --color-blue-400: lab(65.0361% -1.42065 -56.9802);
      --color-blue-500: lab(54.1736% 13.3369 -74.6839);
      --color-blue-600: lab(44.0605% 29.0279 -86.0352);
      --color-blue-700: lab(36.9089% 35.0961 -85.6872);
      --color-blue-800: lab(30.2514% 27.7853 -70.2699);
      --color-blue-900: lab(26.1542% 15.7545 -51.5504);
      --color-blue-950: lab(15.6723% 8.86232 -32.2945);
      --color-indigo-50: lab(95.4818% .411302 -6.78529);
      --color-indigo-100: lab(91.6577% 1.04591 -12.7199);
      --color-indigo-200: lab(84.4329% 3.18977 -23.9688);
      --color-indigo-300: lab(74.0235% 8.54138 -41.6075);
      --color-indigo-400: lab(59.866% 22.4834 -64.4485);
      --color-indigo-500: lab(48.295% 38.3129 -81.9673);
      --color-indigo-600: lab(38.4009% 52.6132 -92.3857);
      --color-indigo-700: lab(32.4486% 49.2217 -84.6695);
      --color-indigo-800: lab(26.6645% 37.9804 -68.6402);
      --color-indigo-900: lab(23.3911% 24.6978 -50.4718);
      --color-indigo-950: lab(12.4853% 14.9672 -31.3418);
      --color-violet-50: lab(96.2416% 2.28849 -5.51657);
      --color-violet-100: lab(93.0838% 4.35197 -9.88284);
      --color-violet-200: lab(87.0888% 8.53688 -19.4189);
      --color-violet-300: lab(76.7419% 18.3911 -37.0706);
      --color-violet-400: lab(62.8239% 34.9159 -60.0512);
      --color-violet-500: lab(49.9355% 55.1776 -81.8963);
      --color-violet-600: lab(41.088% 68.9966 -91.995);
      --color-violet-700: lab(35.2783% 67.9912 -88.793);
      --color-violet-800: lab(29.3188% 57.7986 -76.1493);
      --color-violet-900: lab(24.3783% 45.7525 -61.4902);
      --color-violet-950: lab(14.0706% 33.3353 -46.7553);
      --color-purple-50: lab(97.1627% 2.99937 -4.13398);
      --color-purple-100: lab(93.3333% 6.97437 -9.83434);
      --color-purple-200: lab(87.8405% 13.4282 -18.7159);
      --color-purple-300: lab(78.3298% 26.2195 -34.9499);
      --color-purple-400: lab(63.6946% 47.6127 -59.2066);
      --color-purple-500: lab(52.0183% 66.11 -78.2316);
      --color-purple-600: lab(43.0295% 75.21 -86.5669);
      --color-purple-700: lab(36.1758% 69.8525 -80.0381);
      --color-purple-800: lab(30.6017% 56.7637 -64.4751);
      --color-purple-900: lab(24.9401% 45.2703 -51.2728);
      --color-purple-950: lab(14.8253% 38.9005 -44.5861);
      --color-fuchsia-50: lab(97.1083% 4.46233 -4.09334);
      --color-fuchsia-100: lab(93.9419% 9.57647 -9.08735);
      --color-fuchsia-200: lab(87.7108% 19.9958 -18.2054);
      --color-fuchsia-300: lab(78.5378% 39.3533 -32.9615);
      --color-fuchsia-400: lab(66.1178% 66.0652 -52.4733);
      --color-fuchsia-500: lab(56.4256% 83.132 -64.639);
      --color-fuchsia-600: lab(47.5131% 83.4271 -63.0363);
      --color-fuchsia-700: lab(39.787% 72.2653 -53.1244);
      --color-fuchsia-800: lab(32.904% 60.2883 -43.6569);
      --color-fuchsia-900: lab(27.755% 48.6174 -34.3553);
      --color-fuchsia-950: lab(15.7348% 39.0235 -27.4073);
      --color-pink-50: lab(96.4459% 4.53997 -1.49434);
      --color-pink-100: lab(93.5864% 9.01193 -3.15079);
      --color-pink-200: lab(87.4504% 19.6 -6.46662);
      --color-pink-300: lab(77.8308% 38.525 -10.5394);
      --color-pink-400: lab(64.5597% 64.3615 -12.7988);
      --color-pink-500: lab(56.9303% 76.8162 -8.07021);
      --color-pink-600: lab(49.5493% 79.8381 2.31768);
      --color-pink-700: lab(42.1737% 71.8009 7.42233);
      --color-pink-800: lab(34.9559% 60.2885 5.99639);
      --color-pink-900: lab(29.4367% 49.3962 3.35757);
      --color-pink-950: lab(15.6116% 35.2166 3.53979);
      --color-rose-50: lab(96.2369% 4.94155 1.28011);
      --color-rose-100: lab(92.8221% 9.86832 2.60075);
      --color-rose-200: lab(86.806% 19.1909 4.07754);
      --color-rose-300: lab(76.6339% 38.3549 9.68835);
      --color-rose-400: lab(64.4125% 63.0291 19.2068);
      --color-rose-500: lab(56.101% 79.4328 31.4532);
      --color-rose-600: lab(49.1882% 81.577 36.0311);
      --color-rose-700: lab(41.1651% 71.6251 30.3087);
      --color-rose-800: lab(34.6481% 60.802 20.1957);
      --color-rose-900: lab(29.7104% 51.514 12.6253);
      --color-rose-950: lab(14.2323% 34.0086 9.80922);
      --color-slate-50: lab(98.1434% -.369519 -1.05966);
      --color-slate-200: lab(91.7353% -.998765 -4.76968);
      --color-slate-300: lab(84.7652% -1.94535 -7.93337);
      --color-slate-400: lab(65.5349% -2.25151 -14.5072);
      --color-slate-500: lab(48.0876% -2.03595 -16.5814);
      --color-slate-600: lab(35.5623% -1.74978 -15.4316);
      --color-slate-700: lab(26.9569% -1.47016 -15.6993);
      --color-slate-800: lab(16.132% -.318035 -14.6672);
      --color-slate-900: lab(7.78673% 1.82345 -15.0537);
      --color-slate-950: lab(1.76974% 1.32743 -9.28855);
      --color-gray-50: lab(98.2596% -.247031 -.706708);
      --color-gray-100: lab(96.1596% -.0823438 -1.13575);
      --color-gray-200: lab(91.6229% -.159115 -2.26791);
      --color-gray-300: lab(85.1236% -.612259 -3.7138);
      --color-gray-400: lab(65.9269% -.832707 -8.17473);
      --color-gray-500: lab(47.7841% -.393182 -10.0268);
      --color-gray-600: lab(35.6337% -1.58697 -10.8425);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
      --color-gray-950: lab(1.90334% .278696 -5.48866);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-calendar-picker-indicator {
    line-height: 1;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .\@container {
    container-type: inline-size;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .fixed\! {
    position: fixed !important;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-1 {
    inset: calc(var(--spacing) * 1);
  }

  .inset-1\/2 {
    top: 50%;
    bottom: 50%;
    left: 50%;
    right: 50%;
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-x-1\/2 {
    left: 50%;
    right: 50%;
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .-top-6 {
    top: calc(var(--spacing) * -6);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-16 {
    top: calc(var(--spacing) * 16);
  }

  .top-\[-2px\] {
    top: -2px;
  }

  .top-\[186px\] {
    top: 186px;
  }

  .-right-3 {
    right: calc(var(--spacing) * -3);
  }

  .-right-4 {
    right: calc(var(--spacing) * -4);
  }

  .-right-6 {
    right: calc(var(--spacing) * -6);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1\/2 {
    right: 50%;
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-\[-2px\] {
    right: -2px;
  }

  .-bottom-6 {
    bottom: calc(var(--spacing) * -6);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-12 {
    bottom: calc(var(--spacing) * 12);
  }

  .bottom-\[-2px\] {
    bottom: -2px;
  }

  .-left-1 {
    left: calc(var(--spacing) * -1);
  }

  .-left-2 {
    left: calc(var(--spacing) * -2);
  }

  .-left-6 {
    left: calc(var(--spacing) * -6);
  }

  .-left-\[300\%\] {
    left: -300%;
  }

  .-left-\[var\(--gutter-width\)\] {
    left: calc(var(--gutter-width) * -1);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-\[-2px\] {
    left: -2px;
  }

  .left-full {
    left: 100%;
  }

  .isolate {
    isolation: isolate;
  }

  .z-0 {
    z-index: 0;
  }

  .z-1 {
    z-index: 1;
  }

  .z-10 {
    z-index: 10;
  }

  .z-50 {
    z-index: 50;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .col-span-4 {
    grid-column: span 4 / span 4;
  }

  .col-span-full {
    grid-column: 1 / -1;
  }

  .col-start-1 {
    grid-column-start: 1;
  }

  .row-span-1 {
    grid-row: span 1 / span 1;
  }

  .row-span-full {
    grid-row: 1 / -1;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .row-start-3 {
    grid-row-start: 3;
  }

  .row-start-5 {
    grid-row-start: 5;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .-my-1 {
    margin-block: calc(var(--spacing) * -1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }

  .-mt-6 {
    margin-top: calc(var(--spacing) * -6);
  }

  .-mt-18 {
    margin-top: calc(var(--spacing) * -18);
  }

  .-mt-27 {
    margin-top: calc(var(--spacing) * -27);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-18 {
    margin-top: calc(var(--spacing) * 18);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }

  .-mr-0\.5 {
    margin-right: calc(var(--spacing) * -.5);
  }

  .-mr-16 {
    margin-right: calc(var(--spacing) * -16);
  }

  .-mr-124 {
    margin-right: calc(var(--spacing) * -124);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .-mb-8 {
    margin-bottom: calc(var(--spacing) * -8);
  }

  .-mb-16 {
    margin-bottom: calc(var(--spacing) * -16);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }

  .mb-px {
    margin-bottom: 1px;
  }

  .-ml-0\.5 {
    margin-left: calc(var(--spacing) * -.5);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-100 {
    margin-left: calc(var(--spacing) * 100);
  }

  .ml-150 {
    margin-left: calc(var(--spacing) * 150);
  }

  .ml-\[-1px\] {
    margin-left: -1px;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .inline-grid {
    display: inline-grid;
  }

  .table {
    display: table;
  }

  .aspect-2\/1 {
    aspect-ratio: 2;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .size-\(--size\) {
    width: var(--size);
    height: var(--size);
  }

  .size-1\.5 {
    width: calc(var(--spacing) * 1.5);
    height: calc(var(--spacing) * 1.5);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }

  .size-11 {
    width: calc(var(--spacing) * 11);
    height: calc(var(--spacing) * 11);
  }

  .size-12 {
    width: calc(var(--spacing) * 12);
    height: calc(var(--spacing) * 12);
  }

  .size-15 {
    width: calc(var(--spacing) * 15);
    height: calc(var(--spacing) * 15);
  }

  .size-16 {
    width: calc(var(--spacing) * 16);
    height: calc(var(--spacing) * 16);
  }

  .size-18 {
    width: calc(var(--spacing) * 18);
    height: calc(var(--spacing) * 18);
  }

  .size-20 {
    width: calc(var(--spacing) * 20);
    height: calc(var(--spacing) * 20);
  }

  .size-24 {
    width: calc(var(--spacing) * 24);
    height: calc(var(--spacing) * 24);
  }

  .size-29 {
    width: calc(var(--spacing) * 29);
    height: calc(var(--spacing) * 29);
  }

  .size-30 {
    width: calc(var(--spacing) * 30);
    height: calc(var(--spacing) * 30);
  }

  .size-32 {
    width: calc(var(--spacing) * 32);
    height: calc(var(--spacing) * 32);
  }

  .size-33 {
    width: calc(var(--spacing) * 33);
    height: calc(var(--spacing) * 33);
  }

  .size-36 {
    width: calc(var(--spacing) * 36);
    height: calc(var(--spacing) * 36);
  }

  .size-48 {
    width: calc(var(--spacing) * 48);
    height: calc(var(--spacing) * 48);
  }

  .size-55 {
    width: calc(var(--spacing) * 55);
    height: calc(var(--spacing) * 55);
  }

  .size-60 {
    width: calc(var(--spacing) * 60);
    height: calc(var(--spacing) * 60);
  }

  .size-64 {
    width: calc(var(--spacing) * 64);
    height: calc(var(--spacing) * 64);
  }

  .size-82 {
    width: calc(var(--spacing) * 82);
    height: calc(var(--spacing) * 82);
  }

  .size-\[150px\] {
    width: 150px;
    height: 150px;
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-\(--height\) {
    height: var(--height);
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-18 {
    height: calc(var(--spacing) * 18);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-66 {
    height: calc(var(--spacing) * 66);
  }

  .h-112 {
    height: calc(var(--spacing) * 112);
  }

  .h-148 {
    height: calc(var(--spacing) * 148);
  }

  .h-150 {
    height: calc(var(--spacing) * 150);
  }

  .h-\[80vh\] {
    height: 80vh;
  }

  .h-\[150px\] {
    height: 150px;
  }

  .h-\[500px\] {
    height: 500px;
  }

  .h-\[600px\] {
    height: 600px;
  }

  .h-\[calc\(100vh-3\.5rem\)\] {
    height: calc(100vh - 3.5rem);
  }

  .h-\[calc\(var\(--width\)\*var\(--sin\)\+var\(--height\)\*var\(--cos\)\)\] {
    height: calc(var(--width) * var(--sin)  + var(--height) * var(--cos));
  }

  .h-auto {
    height: auto;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-9 {
    min-height: calc(var(--spacing) * 9);
  }

  .min-h-dvh {
    min-height: 100dvh;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-\(--width\) {
    width: var(--width);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-11 {
    width: calc(var(--spacing) * 11);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-28 {
    width: calc(var(--spacing) * 28);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-380 {
    width: calc(var(--spacing) * 380);
  }

  .w-\[20\%\] {
    width: 20%;
  }

  .w-\[50cqw\] {
    width: 50cqw;
  }

  .w-\[60cqw\] {
    width: 60cqw;
  }

  .w-\[262px\] {
    width: 262px;
  }

  .w-\[375px\] {
    width: 375px;
  }

  .w-\[calc\(50cqw-\(var\(--size\)\/2\)-\(var\(--gap\)\)\)\] {
    width: calc(50cqw - (var(--size) / 2)  - (var(--gap)));
  }

  .w-\[calc\(var\(--width\)\*var\(--cos\)\+var\(--height\)\*var\(--sin\)\)\] {
    width: calc(var(--width) * var(--cos)  + var(--height) * var(--sin));
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-px {
    width: 1px;
  }

  .w-screen {
    width: 100vw;
  }

  .max-w-\(--breakpoint-md\) {
    max-width: var(--breakpoint-md);
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-80 {
    max-width: calc(var(--spacing) * 80);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-screen {
    max-width: 100vw;
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-auto {
    flex: auto;
  }

  .flex-shrink {
    flex-shrink: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .shrink {
    flex-shrink: 1;
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow, .grow {
    flex-grow: 1;
  }

  .grow-0 {
    flex-grow: 0;
  }

  .origin-bottom-right {
    transform-origin: 100% 100%;
  }

  .origin-top-left {
    transform-origin: 0 0;
  }

  .-translate-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-px {
    --tw-translate-x: 1px;
    --tw-translate-y: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-4 {
    --tw-translate-x: calc(var(--spacing) * -4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-1 {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-4 {
    --tw-translate-x: calc(var(--spacing) * 4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-5 {
    --tw-translate-x: calc(var(--spacing) * 5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-6 {
    --tw-translate-x: calc(var(--spacing) * 6);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-4 {
    --tw-translate-y: calc(var(--spacing) * -4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-2 {
    --tw-translate-y: calc(var(--spacing) * 2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-4 {
    --tw-translate-y: calc(var(--spacing) * 4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-20 {
    --tw-translate-y: calc(var(--spacing) * 20);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-120 {
    --tw-scale-x: 120%;
    --tw-scale-y: 120%;
    --tw-scale-z: 120%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-\(--angle\) {
    rotate: var(--angle);
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-225 {
    rotate: 225deg;
  }

  .rotate-x-55 {
    --tw-rotate-x: rotateX(55deg);
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .rotate-y-0 {
    --tw-rotate-y: rotateY(0deg);
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .-rotate-z-45 {
    --tw-rotate-z: rotateZ(calc(45deg * -1));
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-ew-resize {
    cursor: ew-resize;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .snap-x {
    scroll-snap-type: x var(--tw-scroll-snap-strictness);
  }

  .snap-mandatory {
    --tw-scroll-snap-strictness: mandatory;
  }

  .snap-proximity {
    --tw-scroll-snap-strictness: proximity;
  }

  .snap-center {
    scroll-snap-align: center;
  }

  .snap-end {
    scroll-snap-align: end;
  }

  .snap-always {
    scroll-snap-stop: always;
  }

  .appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .grid-flow-dense {
    grid-auto-flow: dense;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .grid-cols-30 {
    grid-template-columns: repeat(30, minmax(0, 1fr));
  }

  .grid-cols-\[auto_1fr\] {
    grid-template-columns: auto 1fr;
  }

  .grid-cols-\[auto_1fr_auto\] {
    grid-template-columns: auto 1fr auto;
  }

  .grid-cols-\[repeat\(2\,var\(--size\)\)\] {
    grid-template-columns: repeat(2, var(--size));
  }

  .grid-cols-\[repeat\(3\,_minmax\(125px\,_1fr\)\)\] {
    grid-template-columns: repeat(3, minmax(125px, 1fr));
  }

  .grid-cols-\[repeat\(var\(--columns\)\,var\(--width\)\)\] {
    grid-template-columns: repeat(var(--columns), var(--width));
  }

  .grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .grid-rows-6 {
    grid-template-rows: repeat(6, minmax(0, 1fr));
  }

  .grid-rows-\[1fr_1px_auto_1px_auto\] {
    grid-template-rows: 1fr 1px auto 1px auto;
  }

  .grid-rows-\[repeat\(3\,var\(--size\)\)\] {
    grid-template-rows: repeat(3, var(--size));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .place-content-center {
    place-content: center;
  }

  .place-items-center {
    place-items: center;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-\(--gap\) {
    gap: var(--gap);
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  .gap-24 {
    gap: calc(var(--spacing) * 24);
  }

  .gap-\[calc\(1rem\/16\*7\)\] {
    gap: .4375rem;
  }

  .gap-\[inherit\] {
    gap: inherit;
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-2 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-3 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-4 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-8 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-0 {
    row-gap: calc(var(--spacing) * 0);
  }

  :where(.divide-x > :not(:last-child)) {
    --tw-divide-x-reverse: 0;
    border-inline-style: var(--tw-border-style);
  }

  :where(.divide-x > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: calc(1px * var(--tw-divide-x-reverse));
    border-right-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: calc(1px * var(--tw-divide-x-reverse));
    border-right-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: calc(1px * var(--tw-divide-x-reverse));
    border-right-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-gray-950\/5 > :not(:last-child)) {
    border-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(.divide-gray-950\/5 > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  :where(.divide-gray-950\/10 > :not(:last-child)) {
    border-color: rgba(3, 7, 18, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(.divide-gray-950\/10 > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--color-gray-950) 10%, transparent);
    }
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-4xl {
    border-radius: var(--radius-4xl);
  }

  .rounded-\[10px\] {
    border-radius: 10px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-t-2xl {
    border-top-left-radius: var(--radius-2xl);
    border-top-right-radius: var(--radius-2xl);
  }

  .rounded-t-4xl {
    border-top-left-radius: var(--radius-4xl);
    border-top-right-radius: var(--radius-4xl);
  }

  .rounded-b-2xl {
    border-bottom-right-radius: var(--radius-2xl);
    border-bottom-left-radius: var(--radius-2xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-x {
    border-inline-style: var(--tw-border-style);
    border-left-width: 1px;
    border-right-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-\(--pattern-fg\) {
    border-color: var(--pattern-fg);
  }

  .border-\[\#EAEAEA\]\/20 {
    border-color: rgba(234, 234, 234, .2);
    border-color: color(display-p3 .917647 .917647 .917647 / .2);
    border-color: lab(92.6977% -.0000596046 .0000238419 / .2);
  }

  .border-\[\#F07520\] {
    border-color: #f07520;
  }

  .border-\[\#F07520\]\/20 {
    border-color: rgba(240, 117, 32, .2);
    border-color: color(display-p3 .879914 .485548 .223529 / .2);
    border-color: lab(63.5305% 45.1819 64.3684 / .2);
  }

  .border-black\/5 {
    border-color: rgba(0, 0, 0, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-black\/5 {
      border-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-950 {
    border-color: var(--color-gray-950);
  }

  .border-gray-950\/5 {
    border-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-950\/5 {
      border-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .border-gray-950\/20 {
    border-color: rgba(3, 7, 18, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-950\/20 {
      border-color: color-mix(in oklab, var(--color-gray-950) 20%, transparent);
    }
  }

  .border-orange-500\/20 {
    border-color: rgba(254, 110, 0, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-orange-500\/20 {
      border-color: color-mix(in oklab, var(--color-orange-500) 20%, transparent);
    }
  }

  .border-red-800 {
    border-color: var(--color-red-800);
  }

  .border-sky-300\/60 {
    border-color: rgba(119, 212, 255, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-sky-300\/60 {
      border-color: color-mix(in oklab, var(--color-sky-300) 60%, transparent);
    }
  }

  .border-slate-600 {
    border-color: var(--color-slate-600);
  }

  .border-slate-700 {
    border-color: var(--color-slate-700);
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .border-x-\(--pattern-fg\) {
    border-inline-color: var(--pattern-fg);
  }

  .border-b-white\/5 {
    border-bottom-color: rgba(255, 255, 255, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-b-white\/5 {
      border-bottom-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .bg-\(--color\) {
    background-color: var(--color);
  }

  .bg-\[\#1D2B40\] {
    background-color: #1d2b40;
  }

  .bg-\[\#1D2B40\]\/95 {
    background-color: rgba(29, 43, 64, .95);
    background-color: color(display-p3 .125231 .1671 .244343 / .95);
    background-color: lab(17.0651% -.716999 -15.3829 / .95);
  }

  .bg-\[\#EAEAEA\] {
    background-color: #eaeaea;
  }

  .bg-\[\#EAEAEA\]\/10 {
    background-color: rgba(234, 234, 234, .1);
    background-color: color(display-p3 .917647 .917647 .917647 / .1);
    background-color: lab(92.6977% -.0000596046 .0000238419 / .1);
  }

  .bg-\[\#F07520\] {
    background-color: #f07520;
  }

  .bg-\[\#F07520\]\/10 {
    background-color: rgba(240, 117, 32, .1);
    background-color: color(display-p3 .879914 .485548 .223529 / .1);
    background-color: lab(63.5305% 45.1819 64.3684 / .1);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/5 {
    background-color: rgba(0, 0, 0, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/5 {
      background-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-950 {
    background-color: var(--color-gray-950);
  }

  .bg-gray-950\/2 {
    background-color: rgba(3, 7, 18, .02);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-950\/2 {
      background-color: color-mix(in oklab, var(--color-gray-950) 2%, transparent);
    }
  }

  .bg-gray-950\/5 {
    background-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-950\/5 {
      background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .bg-gray-950\/10 {
    background-color: rgba(3, 7, 18, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-950\/10 {
      background-color: color-mix(in oklab, var(--color-gray-950) 10%, transparent);
    }
  }

  .bg-gray-950\/20 {
    background-color: rgba(3, 7, 18, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-950\/20 {
      background-color: color-mix(in oklab, var(--color-gray-950) 20%, transparent);
    }
  }

  .bg-gray-950\/90 {
    background-color: rgba(3, 7, 18, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-950\/90 {
      background-color: color-mix(in oklab, var(--color-gray-950) 90%, transparent);
    }
  }

  .bg-gray-950\/\[2\.5\%\] {
    background-color: rgba(3, 7, 18, .024);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-950\/\[2\.5\%\] {
      background-color: color-mix(in oklab, var(--color-gray-950) 2.5%, transparent);
    }
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-indigo-400 {
    background-color: var(--color-indigo-400);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-orange-500\/10 {
    background-color: rgba(254, 110, 0, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-orange-500\/10 {
      background-color: color-mix(in oklab, var(--color-orange-500) 10%, transparent);
    }
  }

  .bg-pink-400 {
    background-color: var(--color-pink-400);
  }

  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }

  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }

  .bg-red-900\/20 {
    background-color: rgba(130, 24, 26, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-900\/20 {
      background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
    }
  }

  .bg-sky-400 {
    background-color: var(--color-sky-400);
  }

  .bg-sky-400\/10 {
    background-color: rgba(0, 188, 254, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-sky-400\/10 {
      background-color: color-mix(in oklab, var(--color-sky-400) 10%, transparent);
    }
  }

  .bg-sky-500 {
    background-color: var(--color-sky-500);
  }

  .bg-slate-600 {
    background-color: var(--color-slate-600);
  }

  .bg-slate-700 {
    background-color: var(--color-slate-700);
  }

  .bg-slate-800 {
    background-color: var(--color-slate-800);
  }

  .bg-slate-900 {
    background-color: var(--color-slate-900);
  }

  .bg-slate-950\/20 {
    background-color: rgba(2, 6, 24, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-slate-950\/20 {
      background-color: color-mix(in oklab, var(--color-slate-950) 20%, transparent);
    }
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/2\.5 {
    background-color: rgba(255, 255, 255, .024);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/2\.5 {
      background-color: color-mix(in oklab, var(--color-white) 2.5%, transparent);
    }
  }

  .bg-white\/10 {
    background-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-white\/15 {
    background-color: rgba(255, 255, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/15 {
      background-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }

  .bg-white\/20 {
    background-color: rgba(255, 255, 255, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-white\/75\! {
    background-color: rgba(255, 255, 255, .75) !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/75\! {
      background-color: color-mix(in oklab, var(--color-white) 75%, transparent) !important;
    }
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-linear-to-b {
    --tw-gradient-position: to bottom;
  }

  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-b {
      --tw-gradient-position: to bottom in oklab;
    }
  }

  .bg-linear-to-b {
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-linear-to-r {
    --tw-gradient-position: to right;
  }

  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-r {
      --tw-gradient-position: to right in oklab;
    }
  }

  .bg-linear-to-r {
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[image\:radial-gradient\(var\(--pattern-fg\)_1px\,_transparent_0\)\] {
    background-image: radial-gradient(var(--pattern-fg) 1px, transparent 0);
  }

  .bg-\[image\:repeating-linear-gradient\(315deg\,_var\(--pattern-fg\)_0\,_var\(--pattern-fg\)_1px\,_transparent_0\,_transparent_50\%\)\] {
    background-image: repeating-linear-gradient(315deg, var(--pattern-fg) 0, var(--pattern-fg) 1px, transparent 0, transparent 50%);
  }

  .from-\[\#1D2B40\] {
    --tw-gradient-from: #1d2b40;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#1D2B40\]\/40 {
    --tw-gradient-from: rgba(29, 43, 64, .4);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color(display-p3 0 0 0)) {
    .from-\[\#1D2B40\]\/40 {
      --tw-gradient-from: color(display-p3 .125231 .1671 .244343 / .4);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .from-\[\#1D2B40\]\/40 {
      --tw-gradient-from: lab(17.0651% -.716969 -15.3829 / .4);
    }
  }

  .from-\[\#1D2B40\]\/70 {
    --tw-gradient-from: rgba(29, 43, 64, .7);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color(display-p3 0 0 0)) {
    .from-\[\#1D2B40\]\/70 {
      --tw-gradient-from: color(display-p3 .125231 .1671 .244343 / .7);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .from-\[\#1D2B40\]\/70 {
      --tw-gradient-from: lab(17.0651% -.716999 -15.3829 / .7);
    }
  }

  .from-\[\#F07520\] {
    --tw-gradient-from: #f07520;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-emerald-500 {
    --tw-gradient-from: var(--color-emerald-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-pink-600 {
    --tw-gradient-from: var(--color-pink-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-600 {
    --tw-gradient-from: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-\[\#1D2B40\]\/80 {
    --tw-gradient-via: rgba(29, 43, 64, .8);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  @supports (color: color(display-p3 0 0 0)) {
    .via-\[\#1D2B40\]\/80 {
      --tw-gradient-via: color(display-p3 .125231 .1671 .244343 / .8);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .via-\[\#1D2B40\]\/80 {
      --tw-gradient-via: lab(17.0651% -.716969 -15.3829 / .8);
    }
  }

  .via-transparent {
    --tw-gradient-via: transparent;
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-\[\#2A3B52\] {
    --tw-gradient-to: #2a3b52;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#F07520\]\/30 {
    --tw-gradient-to: rgba(240, 117, 32, .3);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color(display-p3 0 0 0)) {
    .to-\[\#F07520\]\/30 {
      --tw-gradient-to: color(display-p3 .879914 .485548 .223529 / .3);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .to-\[\#F07520\]\/30 {
      --tw-gradient-to: lab(63.5305% 45.1819 64.3684 / .3);
    }
  }

  .to-amber-300 {
    --tw-gradient-to: var(--color-amber-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-400 {
    --tw-gradient-to: var(--color-cyan-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-50 {
    --tw-gradient-to: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-950 {
    --tw-gradient-to: var(--color-gray-950);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-sky-400 {
    --tw-gradient-to: var(--color-sky-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-\[size\:10px_10px\] {
    background-size: 10px 10px;
  }

  .bg-fixed {
    background-attachment: fixed;
  }

  .bg-clip-padding {
    background-clip: padding-box;
  }

  .fill-black\/40 {
    fill: rgba(0, 0, 0, .4);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .fill-black\/40 {
      fill: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-gray-400 {
    fill: var(--color-gray-400);
  }

  .fill-gray-600 {
    fill: var(--color-gray-600);
  }

  .fill-sky-300 {
    fill: var(--color-sky-300);
  }

  .fill-sky-400 {
    fill: var(--color-sky-400);
  }

  .fill-white {
    fill: var(--color-white);
  }

  .stroke-white {
    stroke: var(--color-white);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0\! {
    padding: calc(var(--spacing) * 0) !important;
  }

  .p-0\.75 {
    padding: calc(var(--spacing) * .75);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pt-10 {
    padding-top: calc(var(--spacing) * 10);
  }

  .pt-14\.25 {
    padding-top: calc(var(--spacing) * 14.25);
  }

  .pt-\[100\%\] {
    padding-top: 100%;
  }

  .pr-1\.5 {
    padding-right: calc(var(--spacing) * 1.5);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }

  .pb-24 {
    padding-bottom: calc(var(--spacing) * 24);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-2\.5 {
    padding-left: calc(var(--spacing) * 2.5);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .font-sans {
    font-family: var(--font-geist-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-3xl\/12 {
    font-size: var(--text-3xl);
    line-height: calc(var(--spacing) * 12);
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-4xl\/12 {
    font-size: var(--text-4xl);
    line-height: calc(var(--spacing) * 12);
  }

  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-8xl {
    font-size: var(--text-8xl);
    line-height: var(--tw-leading, var(--text-8xl--line-height));
  }

  .text-\[0\.8125rem\]\/\[1\.5rem\] {
    font-size: .8125rem;
    line-height: 1.5rem;
  }

  .text-\[2\.5rem\]\/10 {
    font-size: 2.5rem;
    line-height: calc(var(--spacing) * 10);
  }

  .text-\[13px\]\/6 {
    font-size: 13px;
    line-height: calc(var(--spacing) * 6);
  }

  .text-\[13px\]\/7 {
    font-size: 13px;
    line-height: calc(var(--spacing) * 7);
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-base\/6 {
    font-size: var(--text-base);
    line-height: calc(var(--spacing) * 6);
  }

  .text-base\/7 {
    font-size: var(--text-base);
    line-height: calc(var(--spacing) * 7);
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-lg\/7 {
    font-size: var(--text-lg);
    line-height: calc(var(--spacing) * 7);
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-sm\/5 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 5);
  }

  .text-sm\/6 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 6);
  }

  .text-sm\/7 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 7);
  }

  .text-sm\/loose {
    font-size: var(--text-sm);
    line-height: var(--leading-loose);
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xl\/6 {
    font-size: var(--text-xl);
    line-height: calc(var(--spacing) * 6);
  }

  .text-xl\/10 {
    font-size: var(--text-xl);
    line-height: calc(var(--spacing) * 10);
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-xs\/4 {
    font-size: var(--text-xs);
    line-height: calc(var(--spacing) * 4);
  }

  .text-xs\/5 {
    font-size: var(--text-xs);
    line-height: calc(var(--spacing) * 5);
  }

  .text-xs\/6 {
    font-size: var(--text-xs);
    line-height: calc(var(--spacing) * 6);
  }

  .text-\[1\.0625rem\] {
    font-size: 1.0625rem;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-tighter {
    --tw-tracking: var(--tracking-tighter);
    letter-spacing: var(--tracking-tighter);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-nowrap {
    text-wrap: nowrap;
  }

  .text-clip {
    text-overflow: clip;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre {
    white-space: pre;
  }

  .text-\[\#1D2B40\] {
    color: #1d2b40;
  }

  .text-\[\#1D2B40\]\/20 {
    color: rgba(29, 43, 64, .2);
    color: color(display-p3 .125231 .1671 .244343 / .2);
    color: lab(17.0651% -.716969 -15.3829 / .2);
  }

  .text-\[\#EAEAEA\] {
    color: #eaeaea;
  }

  .text-\[\#F07520\] {
    color: #f07520;
  }

  .text-black {
    color: var(--color-black);
  }

  .text-black\/20 {
    color: rgba(0, 0, 0, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-black\/20 {
      color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .text-fuchsia-500 {
    color: var(--color-fuchsia-500);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-gray-950 {
    color: var(--color-gray-950);
  }

  .text-gray-950\/50 {
    color: rgba(3, 7, 18, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-gray-950\/50 {
      color: color-mix(in oklab, var(--color-gray-950) 50%, transparent);
    }
  }

  .text-gray-950\/75 {
    color: rgba(3, 7, 18, .75);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-gray-950\/75 {
      color: color-mix(in oklab, var(--color-gray-950) 75%, transparent);
    }
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-orange-400 {
    color: var(--color-orange-400);
  }

  .text-orange-500 {
    color: var(--color-orange-500);
  }

  .text-pink-300 {
    color: var(--color-pink-300);
  }

  .text-pink-500 {
    color: var(--color-pink-500);
  }

  .text-pink-600 {
    color: var(--color-pink-600);
  }

  .text-purple-300 {
    color: var(--color-purple-300);
  }

  .text-red-300 {
    color: var(--color-red-300);
  }

  .text-red-400 {
    color: var(--color-red-400);
  }

  .text-sky-300 {
    color: var(--color-sky-300);
  }

  .text-sky-500 {
    color: var(--color-sky-500);
  }

  .text-sky-800 {
    color: var(--color-sky-800);
  }

  .text-slate-50 {
    color: var(--color-slate-50);
  }

  .text-slate-200 {
    color: var(--color-slate-200);
  }

  .text-slate-300 {
    color: var(--color-slate-300);
  }

  .text-slate-400 {
    color: var(--color-slate-400);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/80 {
    color: rgba(255, 255, 255, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/80 {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .text-white\/90 {
    color: rgba(255, 255, 255, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/90 {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .capitalize {
    text-transform: capitalize;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .line-through {
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .placeholder-slate-400::placeholder {
    color: var(--color-slate-400);
  }

  .scheme-dark {
    --lightningcss-light: ;
    --lightningcss-dark: initial;
    color-scheme: dark;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-5 {
    opacity: .05;
  }

  .opacity-25 {
    opacity: .25;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-75 {
    opacity: .75;
  }

  .opacity-90 {
    opacity: .9;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, rgba(0, 0, 0, .05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring, .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .inset-ring {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-indigo-400\/50 {
    --tw-shadow-color: rgba(125, 135, 255, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-indigo-400\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-indigo-400) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-pink-400\/50 {
    --tw-shadow-color: rgba(251, 100, 182, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-pink-400\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-pink-400) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-purple-400\/50 {
    --tw-shadow-color: rgba(192, 126, 255, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-purple-400\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-purple-400) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .shadow-sky-400\/50 {
    --tw-shadow-color: rgba(0, 188, 254, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-sky-400\/50 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-sky-400) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-gray-950\/5 {
    --tw-ring-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-gray-950\/5 {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .ring-gray-950\/10 {
    --tw-ring-color: rgba(3, 7, 18, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-gray-950\/10 {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-950) 10%, transparent);
    }
  }

  .inset-ring-gray-950\/5 {
    --tw-inset-ring-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .inset-ring-gray-950\/5 {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .inset-ring-gray-950\/8 {
    --tw-inset-ring-color: rgba(3, 7, 18, .08);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .inset-ring-gray-950\/8 {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-gray-950) 8%, transparent);
    }
  }

  .inset-ring-gray-950\/10 {
    --tw-inset-ring-color: rgba(3, 7, 18, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .inset-ring-gray-950\/10 {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-gray-950) 10%, transparent);
    }
  }

  .inset-ring-white\/5 {
    --tw-inset-ring-color: rgba(255, 255, 255, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .inset-ring-white\/5 {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .inset-ring-white\/10 {
    --tw-inset-ring-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .inset-ring-white\/10 {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .inset-ring-white\/20 {
    --tw-inset-ring-color: rgba(255, 255, 255, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .inset-ring-white\/20 {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .-outline-offset-1 {
    outline-offset: calc(1px * -1);
  }

  .outline-black\/5 {
    outline-color: rgba(0, 0, 0, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .outline-black\/5 {
      outline-color: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .outline-gray-950\/5 {
    outline-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .outline-gray-950\/5 {
      outline-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .outline-gray-950\/10 {
    outline-color: rgba(3, 7, 18, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .outline-gray-950\/10 {
      outline-color: color-mix(in oklab, var(--color-gray-950) 10%, transparent);
    }
  }

  .outline-white\/15 {
    outline-color: rgba(255, 255, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .outline-white\/15 {
      outline-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-sm {
    --tw-blur: blur(var(--blur-sm));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .brightness-150 {
    --tw-brightness: brightness(150%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .contrast-150 {
    --tw-contrast: contrast(150%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .saturate-200 {
    --tw-saturate: saturate(200%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .sepia {
    --tw-sepia: sepia(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-lg {
    --tw-backdrop-blur: blur(var(--blur-lg));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-brightness-150 {
    --tw-backdrop-brightness: brightness(150%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-contrast-150 {
    --tw-backdrop-contrast: contrast(150%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-grayscale {
    --tw-backdrop-grayscale: grayscale(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-saturate-200 {
    --tw-backdrop-saturate: saturate(200%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-sepia {
    --tw-backdrop-sepia: sepia(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition\! {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events !important;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function)) !important;
    transition-duration: var(--tw-duration, var(--default-transition-duration)) !important;
  }

  .transition-\[border-radius\] {
    transition-property: border-radius;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[font-size\] {
    transition-property: font-size;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-none {
    transition-property: none;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-350 {
    --tw-duration: .35s;
    transition-duration: .35s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-750 {
    --tw-duration: .75s;
    transition-duration: .75s;
  }

  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .will-change-\[transform\,opacity\] {
    will-change: transform, opacity;
  }

  .outline-dashed {
    --tw-outline-style: dashed;
    outline-style: dashed;
  }

  .\[--gap\:--spacing\(10\)\] {
    --gap: calc(var(--spacing) * 10);
  }

  .\[--gutter-width\:2\.5rem\] {
    --gutter-width: 2.5rem;
  }

  .\[--height\:--spacing\(6\)\] {
    --height: calc(var(--spacing) * 6);
  }

  .\[--pattern-fg\:var\(--color-black\)\]\/5 {
    --pattern-fg: rgba(0, 0, 0, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[--pattern-fg\:var\(--color-black\)\]\/5 {
      --pattern-fg: color-mix(in oklab, var(--color-black) 5%, transparent);
    }
  }

  .\[--pattern-fg\:var\(--color-gray-950\)\]\/5 {
    --pattern-fg: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[--pattern-fg\:var\(--color-gray-950\)\]\/5 {
      --pattern-fg: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .\[--site-background\:var\(--color-white\)\] {
    --site-background: var(--color-white);
  }

  .\[--size\:--spacing\(48\)\] {
    --size: calc(var(--spacing) * 48);
  }

  .\[--size\:--spacing\(72\)\] {
    --size: calc(var(--spacing) * 72);
  }

  .\[--width\:--spacing\(10\)\] {
    --width: calc(var(--spacing) * 10);
  }

  .\[clip\:rect\(0px\,calc\(var\(--midpoint\)-var\(--offset\)\)\,621px\,0px\)\] {
    clip: rect(0px, calc(var(--midpoint)  - var(--offset)), 621px, 0px);
  }

  .\[grid-area\:main\] {
    grid-area: main;
  }

  .\[grid-area\:meta\] {
    grid-area: meta;
  }

  .\[grid-area\:timeslots\] {
    grid-area: timeslots;
  }

  .perspective-\[1200px\] {
    perspective: 1200px;
  }

  .perspective-origin-top {
    perspective-origin: top;
  }

  .ring-inset {
    --tw-ring-inset: inset;
  }

  .transform-3d {
    transform-style: preserve-3d;
  }

  :is(.\*\:flex > *) {
    display: flex;
  }

  :is(.\*\:size-7 > *) {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  :is(:is(.\*\:\*\:max-w-none > *) > *) {
    max-width: none;
  }

  :is(:is(.\*\:\*\:shrink-0 > *) > *) {
    flex-shrink: 0;
  }

  :is(:is(.\*\:\*\:grow > *) > *) {
    flex-grow: 1;
  }

  :is(.\*\:overflow-auto > *) {
    overflow: auto;
  }

  :is(.\*\:rounded-lg > *) {
    border-radius: var(--radius-lg);
  }

  :is(.\*\:bg-white\/10\! > *) {
    background-color: rgba(255, 255, 255, .1) !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:bg-white\/10\! > *) {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent) !important;
    }
  }

  :is(:is(.\*\:\*\:p-3\! > *) > *) {
    padding: calc(var(--spacing) * 3) !important;
  }

  :is(.\*\:p-5 > *) {
    padding: calc(var(--spacing) * 5);
  }

  :is(.\*\:px-3 > *) {
    padding-inline: calc(var(--spacing) * 3);
  }

  :is(.\*\:px-4 > *) {
    padding-inline: calc(var(--spacing) * 4);
  }

  :is(.\*\:py-2 > *) {
    padding-block: calc(var(--spacing) * 2);
  }

  :is(.\*\:inset-ring > *) {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  :is(.\*\:inset-ring-white\/10 > *) {
    --tw-inset-ring-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:inset-ring-white\/10 > *) {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .not-group-has-data-lg\:bg-gray-950\/5:not(:is(:where(.group):has([data-lg]) *)) {
    background-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .not-group-has-data-lg\:bg-gray-950\/5:not(:is(:where(.group):has([data-lg]) *)) {
      background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .not-group-has-data-lg\:opacity-40:not(:is(:where(.group):has([data-lg]) *)) {
    opacity: .4;
  }

  .not-group-has-data-md\:bg-gray-950\/5:not(:is(:where(.group):has([data-md]) *)) {
    background-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .not-group-has-data-md\:bg-gray-950\/5:not(:is(:where(.group):has([data-md]) *)) {
      background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .not-group-has-data-md\:opacity-40:not(:is(:where(.group):has([data-md]) *)) {
    opacity: .4;
  }

  .not-group-has-data-sm\:bg-gray-950\/5:not(:is(:where(.group):has([data-sm]) *)) {
    background-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .not-group-has-data-sm\:bg-gray-950\/5:not(:is(:where(.group):has([data-sm]) *)) {
      background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .not-group-has-data-sm\:opacity-40:not(:is(:where(.group):has([data-sm]) *)) {
    opacity: .4;
  }

  .not-group-has-data-xl\:bg-gray-950\/5:not(:is(:where(.group):has([data-xl]) *)) {
    background-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .not-group-has-data-xl\:bg-gray-950\/5:not(:is(:where(.group):has([data-xl]) *)) {
      background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .not-group-has-data-xl\:opacity-40:not(:is(:where(.group):has([data-xl]) *)) {
    opacity: .4;
  }

  .not-in-data-dragging\:animate-ping:not(:where([data-dragging]) *) {
    animation: var(--animate-ping);
  }

  @media not (min-width: 40rem) {
    .not-sm\:hidden {
      display: none;
    }
  }

  @media not (min-width: 48rem) {
    .not-md\:border-0 {
      border-style: var(--tw-border-style);
      border-width: 0;
    }
  }

  @media (hover: hover) {
    .group-hover\:flex:is(:where(.group):hover *) {
      display: flex;
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:-translate-y-0\.5:is(:where(.group):hover *) {
      --tw-translate-y: calc(var(--spacing) * -.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-y-0:is(:where(.group):hover *) {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-sky-400\/15:is(:where(.group):hover *) {
      background-color: rgba(0, 188, 254, .15);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:bg-sky-400\/15:is(:where(.group):hover *) {
        background-color: color-mix(in oklab, var(--color-sky-400) 15%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:text-\[\#F07520\]:is(:where(.group):hover *) {
      color: #f07520;
    }
  }

  @media (hover: hover) {
    .group-hover\:text-white:is(:where(.group):hover *) {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-75:is(:where(.group):hover *) {
      opacity: .75;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  .group-active\:translate-y-\[0\.5px\]:is(:where(.group):active *) {
    --tw-translate-y: .5px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-dragging\:bg-slate-950\/40:is(:where(.group)[data-dragging] *) {
    background-color: rgba(2, 6, 24, .4);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .group-data-dragging\:bg-slate-950\/40:is(:where(.group)[data-dragging] *) {
      background-color: color-mix(in oklab, var(--color-slate-950) 40%, transparent);
    }
  }

  .group-data-finished\:opacity-0:is(:where(.group)[data-finished] *) {
    opacity: 0;
  }

  .group-data-finished\:opacity-100:is(:where(.group)[data-finished] *) {
    opacity: 1;
  }

  .group-data-modified\:italic:is(:where(.group)[data-modified] *) {
    font-style: italic;
  }

  .group-data-modified\:opacity-100:is(:where(.group)[data-modified] *) {
    opacity: 1;
  }

  .group-data-modified\:duration-100:is(:where(.group)[data-modified] *) {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .group-data-modified\:ease-linear:is(:where(.group)[data-modified] *) {
    --tw-ease: linear;
    transition-timing-function: linear;
  }

  .group-data-running\:opacity-100:is(:where(.group)[data-running] *) {
    opacity: 1;
  }

  .group-data-selected\:translate-y-0:is(:where(.group)[data-selected] *) {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[tooltip-hover\=true\]\:opacity-100:is(:where(.group)[data-tooltip-hover="true"] *) {
    opacity: 1;
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:-inset-x-0\.5:before {
    content: var(--tw-content);
    inset-inline: calc(var(--spacing) * -.5);
  }

  .before\:-inset-y-0\.25:before {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * -.25);
  }

  .before\:top-0:before {
    content: var(--tw-content);
    top: calc(var(--spacing) * 0);
  }

  .before\:-left-\[100vw\]:before {
    content: var(--tw-content);
    left: -100vw;
  }

  .before\:-z-10:before {
    content: var(--tw-content);
    z-index: calc(10 * -1);
  }

  .before\:block:before {
    content: var(--tw-content);
    display: block;
  }

  .before\:h-px:before {
    content: var(--tw-content);
    height: 1px;
  }

  .before\:w-\[200vw\]:before {
    content: var(--tw-content);
    width: 200vw;
  }

  .before\:rounded-sm:before {
    content: var(--tw-content);
    border-radius: var(--radius-sm);
  }

  .before\:bg-\[lab\(19\.93_-1\.66_-9\.7\)\]:before {
    content: var(--tw-content);
    background-color: #27313e;
    background-color: color(display-p3 .160853 .19211 .239463);
    background-color: lab(19.93% -1.66 -9.7);
  }

  .before\:bg-gray-950\/5:before {
    content: var(--tw-content);
    background-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .before\:bg-gray-950\/5:before {
      background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .before\:text-white:before {
    content: var(--tw-content);
    color: var(--color-white);
  }

  .after\:pointer-events-none:after {
    content: var(--tw-content);
    pointer-events: none;
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:inset-0:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .after\:bottom-0:after {
    content: var(--tw-content);
    bottom: calc(var(--spacing) * 0);
  }

  .after\:-left-\[100vw\]:after {
    content: var(--tw-content);
    left: -100vw;
  }

  .after\:mt-1\.5:after {
    content: var(--tw-content);
    margin-top: calc(var(--spacing) * 1.5);
  }

  .after\:inline-block:after {
    content: var(--tw-content);
    display: inline-block;
  }

  .after\:h-\[1\.2em\]:after {
    content: var(--tw-content);
    height: 1.2em;
  }

  .after\:h-px:after {
    content: var(--tw-content);
    height: 1px;
  }

  .after\:w-\[200vw\]:after {
    content: var(--tw-content);
    width: 200vw;
  }

  .after\:w-px:after {
    content: var(--tw-content);
    width: 1px;
  }

  .after\:rounded-lg:after {
    content: var(--tw-content);
    border-radius: var(--radius-lg);
  }

  .after\:border-r-2:after {
    content: var(--tw-content);
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }

  .after\:border-sky-400:after {
    content: var(--tw-content);
    border-color: var(--color-sky-400);
  }

  .after\:bg-gray-950\/5:after {
    content: var(--tw-content);
    background-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .after\:bg-gray-950\/5:after {
      background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .after\:bg-transparent:after {
    content: var(--tw-content);
    background-color: rgba(0, 0, 0, 0);
  }

  .after\:inset-ring:after {
    content: var(--tw-content);
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .after\:inset-ring-gray-950\/5:after {
    content: var(--tw-content);
    --tw-inset-ring-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .after\:inset-ring-gray-950\/5:after {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .after\:content-\[\'\'\]:after {
    --tw-content: "";
    content: var(--tw-content);
  }

  :is(.\*\:first\:border-l-0 > *):first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  :is(.\*\:last\:border-r-0 > *):last-child {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .only\:w-full:only-child {
    width: 100%;
  }

  .empty\:before\:inline-block:empty:before {
    content: var(--tw-content);
    display: inline-block;
  }

  .empty\:before\:content-\[\'\'\]:empty:before {
    --tw-content: "";
    content: var(--tw-content);
  }

  @media (hover: hover) {
    .hover\:-translate-y-2:hover {
      --tw-translate-y: calc(var(--spacing) * -2);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#E06610\]:hover {
      background-color: #e06610;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#F07520\]:hover {
      background-color: #f07520;
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-400:hover {
      background-color: var(--color-blue-400);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800:hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-950\/2\.5:hover {
      background-color: rgba(3, 7, 18, .024);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-950\/2\.5:hover {
        background-color: color-mix(in oklab, var(--color-gray-950) 2.5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-950\/5:hover {
      background-color: rgba(3, 7, 18, .05);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-950\/5:hover {
        background-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-950\/7\.5:hover {
      background-color: rgba(3, 7, 18, .075);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-950\/7\.5:hover {
        background-color: color-mix(in oklab, var(--color-gray-950) 7.5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-orange-600:hover {
      background-color: var(--color-orange-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-600:hover {
      background-color: var(--color-slate-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-950\/40:hover {
      background-color: rgba(2, 6, 24, .4);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-slate-950\/40:hover {
        background-color: color-mix(in oklab, var(--color-slate-950) 40%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/10:hover {
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/10:hover {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-\[\#F07520\]:hover {
      color: #f07520;
    }
  }

  @media (hover: hover) {
    .hover\:text-orange-400:hover {
      color: var(--color-orange-400);
    }
  }

  @media (hover: hover) {
    .hover\:text-orange-500:hover {
      color: var(--color-orange-500);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      -webkit-text-decoration-line: underline;
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-2xl:hover {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:border-transparent:focus {
    border-color: rgba(0, 0, 0, 0);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-\[\#F07520\]:focus {
    --tw-ring-color: #f07520;
  }

  .focus\:ring-orange-500:focus {
    --tw-ring-color: var(--color-orange-500);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:ring-offset-slate-900:focus {
    --tw-ring-offset-color: var(--color-slate-900);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:ring-0:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .disabled\:cursor-default:disabled {
    cursor: default;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:border-transparent:disabled {
    border-color: rgba(0, 0, 0, 0);
  }

  .disabled\:bg-slate-600:disabled {
    background-color: var(--color-slate-600);
  }

  .disabled\:font-light:disabled {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }

  .disabled\:text-slate-400:disabled {
    color: var(--color-slate-400);
  }

  .disabled\:opacity-30:disabled {
    opacity: .3;
  }

  :where(:-webkit-any(figure)) .in-\[figure\]\:-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  :where(:-moz-any(figure)) .in-\[figure\]\:-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  :where(:is(figure)) .in-\[figure\]\:-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  :where(:-webkit-any(figure)) .in-\[figure\]\:-mb-1 {
    margin-bottom: calc(var(--spacing) * -1);
  }

  :where(:-moz-any(figure)) .in-\[figure\]\:-mb-1 {
    margin-bottom: calc(var(--spacing) * -1);
  }

  :where(:is(figure)) .in-\[figure\]\:-mb-1 {
    margin-bottom: calc(var(--spacing) * -1);
  }

  .aria-selected\:bg-white\/10[aria-selected="true"] {
    background-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-selected\:bg-white\/10[aria-selected="true"] {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .aria-selected\:inset-ring[aria-selected="true"] {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-active\:bg-gray-950\/7\.5[data-active] {
    background-color: rgba(3, 7, 18, .075);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-active\:bg-gray-950\/7\.5[data-active] {
      background-color: color-mix(in oklab, var(--color-gray-950) 7.5%, transparent);
    }
  }

  .data-checked\:bg-white[data-checked] {
    background-color: var(--color-white);
  }

  .data-checked\:ring[data-checked] {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-checked\:inset-ring[data-checked] {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-checked\:ring-gray-950\/10[data-checked] {
    --tw-ring-color: rgba(3, 7, 18, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-checked\:ring-gray-950\/10[data-checked] {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-950) 10%, transparent);
    }
  }

  .data-checked\:inset-ring-white\/10[data-checked] {
    --tw-inset-ring-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-checked\:inset-ring-white\/10[data-checked] {
      --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .data-selected\:bg-indigo-500\/5[data-selected] {
    background-color: rgba(98, 95, 255, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-selected\:bg-indigo-500\/5[data-selected] {
      background-color: color-mix(in oklab, var(--color-indigo-500) 5%, transparent);
    }
  }

  .data-selected\:bg-pink-500\/5[data-selected] {
    background-color: rgba(246, 51, 154, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-selected\:bg-pink-500\/5[data-selected] {
      background-color: color-mix(in oklab, var(--color-pink-500) 5%, transparent);
    }
  }

  .data-selected\:bg-sky-500\/5[data-selected] {
    background-color: rgba(0, 165, 239, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-selected\:bg-sky-500\/5[data-selected] {
      background-color: color-mix(in oklab, var(--color-sky-500) 5%, transparent);
    }
  }

  .data-selected\:text-indigo-600[data-selected] {
    color: var(--color-indigo-600);
  }

  .data-selected\:text-pink-600[data-selected] {
    color: var(--color-pink-600);
  }

  .data-selected\:text-sky-600[data-selected] {
    color: var(--color-sky-600);
  }

  .data-show\:opacity-100[data-show] {
    opacity: 1;
  }

  .data-show\:transition-opacity[data-show] {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .data-show\:delay-100[data-show] {
    transition-delay: .1s;
  }

  .data-show\:duration-200[data-show] {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  @media not (min-width: 96rem) {
    .max-2xl\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }

  @media not (min-width: 96rem) {
    .max-2xl\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media not (min-width: 80rem) {
    .max-xl\:hidden {
      display: none;
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:bottom-8 {
      bottom: calc(var(--spacing) * 8);
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:hidden {
      display: none;
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:h-66 {
      height: calc(var(--spacing) * 66);
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:max-h-76 {
      max-height: calc(var(--spacing) * 76);
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:flex-col {
      flex-direction: column;
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:border-t {
      border-top-style: var(--tw-border-style);
      border-top-width: 1px;
    }
  }

  @media not (min-width: 64rem) {
    .max-lg\:font-medium {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:hidden {
      display: none;
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:translate-x-1\/2 {
      --tw-translate-x: calc(1 / 2 * 100%);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media not (min-width: 48rem) {
    .max-md\:gap-5 {
      gap: calc(var(--spacing) * 5);
    }
  }

  @media not (min-width: 40rem) {
    .max-sm\:mr-0\! {
      margin-right: calc(var(--spacing) * 0) !important;
    }
  }

  @media not (min-width: 40rem) {
    .max-sm\:hidden {
      display: none;
    }
  }

  @media not (min-width: 40rem) {
    .max-sm\:inline {
      display: inline;
    }
  }

  @media not (min-width: 40rem) {
    .max-sm\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 500px) {
    .min-\[500px\]\:-left-\[250\%\] {
      left: -250%;
    }
  }

  @media (min-width: 40rem) {
    .sm\:visible {
      visibility: visible;
    }
  }

  @media (min-width: 40rem) {
    .sm\:-left-\[200\%\] {
      left: -200%;
    }
  }

  @media (min-width: 40rem) {
    .sm\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (min-width: 40rem) {
    .sm\:row-span-2 {
      grid-row: span 2 / span 2;
    }
  }

  @media (min-width: 40rem) {
    .sm\:my-0 {
      margin-block: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:-mt-26 {
      margin-top: calc(var(--spacing) * -26);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-10 {
      margin-top: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mr-6 {
      margin-right: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex {
      display: flex;
    }
  }

  @media (min-width: 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (min-width: 40rem) {
    .sm\:size-10 {
      width: calc(var(--spacing) * 10);
      height: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:size-18 {
      width: calc(var(--spacing) * 18);
      height: calc(var(--spacing) * 18);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-10 {
      height: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-24 {
      height: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-80 {
      width: calc(var(--spacing) * 80);
    }
  }

  @media (min-width: 40rem) {
    .sm\:translate-x-8 {
      --tw-translate-x: calc(var(--spacing) * 8);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 40rem) {
    .sm\:translate-y-5 {
      --tw-translate-y: calc(var(--spacing) * 5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 40rem) {
    .sm\:grid-cols-\[repeat\(3\,var\(--size\)\)\] {
      grid-template-columns: repeat(3, var(--size));
    }
  }

  @media (min-width: 40rem) {
    .sm\:grid-rows-\[repeat\(2\,var\(--size\)\)\] {
      grid-template-rows: repeat(2, var(--size));
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (min-width: 40rem) {
    .sm\:justify-between {
      justify-content: space-between;
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-11 {
      gap: calc(var(--spacing) * 11);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-40 {
      gap: calc(var(--spacing) * 40);
    }
  }

  @media (min-width: 40rem) {
    .sm\:rounded-none {
      border-radius: 0;
    }
  }

  @media (min-width: 40rem) {
    .sm\:rounded-l-2xl {
      border-top-left-radius: var(--radius-2xl);
      border-bottom-left-radius: var(--radius-2xl);
    }
  }

  @media (min-width: 40rem) {
    .sm\:rounded-tr-2xl {
      border-top-right-radius: var(--radius-2xl);
    }
  }

  @media (min-width: 40rem) {
    .sm\:rounded-bl-none {
      border-bottom-left-radius: 0;
    }
  }

  @media (min-width: 40rem) {
    .sm\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:border-b-0 {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0;
    }
  }

  @media (min-width: 40rem) {
    .sm\:border-gray-900\/10 {
      border-color: rgba(16, 24, 40, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .sm\:border-gray-900\/10 {
        border-color: color-mix(in oklab, var(--color-gray-900) 10%, transparent);
      }
    }
  }

  @media (min-width: 40rem) {
    .sm\:p-0 {
      padding: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 40rem) {
    .sm\:p-10 {
      padding: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:p-16 {
      padding: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-5 {
      padding-inline: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pr-4 {
      padding-right: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pr-6 {
      padding-right: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-xs {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:transition-\[width\] {
      transition-property: width;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }

  @media (min-width: 40rem) {
    .sm\:duration-300 {
      --tw-duration: .3s;
      transition-duration: .3s;
    }
  }

  @media (min-width: 40rem) {
    .sm\:\[--height\:--spacing\(10\)\] {
      --height: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:\[--width\:--spacing\(16\)\] {
      --width: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 40rem) {
    @media (prefers-reduced-motion: reduce) {
      .sm\:motion-reduce\:transition-none {
        transition-property: none;
      }
    }
  }

  @media (min-width: 40rem) {
    @media not (min-width: 48rem) {
      .sm\:max-md\:inline {
        display: inline;
      }
    }
  }

  @media (min-width: 48rem) {
    .md\:right-16 {
      right: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    .md\:-left-\[150\%\] {
      left: -150%;
    }
  }

  @media (min-width: 48rem) {
    .md\:col-span-15 {
      grid-column: span 15 / span 15;
    }
  }

  @media (min-width: 48rem) {
    .md\:col-start-2 {
      grid-column-start: 2;
    }
  }

  @media (min-width: 48rem) {
    .md\:col-start-3 {
      grid-column-start: 3;
    }
  }

  @media (min-width: 48rem) {
    .md\:-mx-4 {
      margin-inline: calc(var(--spacing) * -4);
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (min-width: 48rem) {
    .md\:grid {
      display: grid;
    }
  }

  @media (min-width: 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (min-width: 48rem) {
    .md\:w-\[var\(--booker-timeslots-width\)\] {
      width: var(--booker-timeslots-width);
    }
  }

  @media (min-width: 48rem) {
    .md\:w-auto {
      width: auto;
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-\[var\(--gutter-width\)_minmax\(0\,var\(--breakpoint-2xl\)\)_var\(--gutter-width\)\] {
      grid-template-columns: var(--gutter-width) minmax(0, var(--breakpoint-2xl)) var(--gutter-width);
    }
  }

  @media (min-width: 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 48rem) {
    .md\:items-center {
      align-items: center;
    }
  }

  @media (min-width: 48rem) {
    .md\:justify-center {
      justify-content: center;
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-10 {
      gap: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 48rem) {
    :where(.md\:space-y-0 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-top: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-bottom: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-x-4 {
      column-gap: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 48rem) {
    :where(.md\:space-x-4 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
    }

    :where(.md\:space-x-4 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
      margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.md\:space-x-4 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
      margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.md\:space-x-4 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
      margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.md\:space-x-4 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.md\:space-x-4 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.md\:space-x-4 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (min-width: 48rem) {
    .md\:rounded-full {
      border-radius: 3.40282e38px;
    }
  }

  @media (min-width: 48rem) {
    .md\:border-0 {
      border-style: var(--tw-border-style);
      border-width: 0;
    }
  }

  @media (min-width: 48rem) {
    .md\:border-b-0 {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0;
    }
  }

  @media (min-width: 48rem) {
    .md\:border-l {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }

  @media (min-width: 48rem) {
    .md\:p-12 {
      padding: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-16 {
      padding-inline: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-4 {
      padding-block: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 48rem) {
    .md\:pb-40 {
      padding-bottom: calc(var(--spacing) * 40);
    }
  }

  @media (min-width: 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:top-1\/2 {
      top: 50%;
    }
  }

  @media (min-width: 64rem) {
    .lg\:-left-\[100\%\] {
      left: -100%;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-start-3 {
      grid-column-start: 3;
    }
  }

  @media (min-width: 64rem) {
    .lg\:-mx-px {
      margin-left: -1px;
      margin-right: -1px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid {
      display: grid;
    }
  }

  @media (min-width: 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-46 {
      height: calc(var(--spacing) * 46);
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-132\.5 {
      height: calc(var(--spacing) * 132.5);
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-auto {
      height: auto;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-1\/2 {
      width: 50%;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-\[var\(--booker-main-width\)\] {
      width: var(--booker-main-width);
    }
  }

  @media (min-width: 64rem) {
    .lg\:-translate-y-1\/2 {
      --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-\[auto_1fr\] {
      grid-template-columns: auto 1fr;
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-\[var\(--gutter-width\)_minmax\(0\,var\(--breakpoint-2xl\)\)_var\(--gutter-width\)\] {
      grid-template-columns: var(--gutter-width) minmax(0, var(--breakpoint-2xl)) var(--gutter-width);
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 64rem) {
    .lg\:justify-center {
      justify-content: center;
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-x {
      border-inline-style: var(--tw-border-style);
      border-left-width: 1px;
      border-right-width: 1px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-l {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    @media not (min-width: 80rem) {
      .lg\:max-xl\:inline {
        display: inline;
      }
    }
  }

  @media (min-width: 80rem) {
    .xl\:-left-\[80\%\] {
      left: -80%;
    }
  }

  @media (min-width: 80rem) {
    .xl\:col-span-10 {
      grid-column: span 10 / span 10;
    }
  }

  @media (min-width: 80rem) {
    .xl\:col-span-12 {
      grid-column: span 12 / span 12;
    }
  }

  @media (min-width: 80rem) {
    .xl\:col-span-15 {
      grid-column: span 15 / span 15;
    }
  }

  @media (min-width: 80rem) {
    .xl\:col-span-18 {
      grid-column: span 18 / span 18;
    }
  }

  @media (min-width: 80rem) {
    .xl\:-mr-26 {
      margin-right: calc(var(--spacing) * -26);
    }
  }

  @media (min-width: 80rem) {
    .xl\:ml-\[3rem\] {
      margin-left: 3rem;
    }
  }

  @media (min-width: 80rem) {
    .xl\:block {
      display: block;
    }
  }

  @media (min-width: 80rem) {
    .xl\:flex {
      display: flex;
    }
  }

  @media (min-width: 80rem) {
    .xl\:inline {
      display: inline;
    }
  }

  @media (min-width: 80rem) {
    .xl\:w-3\/8 {
      width: 37.5%;
    }
  }

  @media (min-width: 80rem) {
    .xl\:w-5\/8 {
      width: 62.5%;
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:border-x {
      border-inline-style: var(--tw-border-style);
      border-left-width: 1px;
      border-right-width: 1px;
    }
  }

  @media (min-width: 80rem) {
    .xl\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 80rem) {
    .xl\:text-8xl {
      font-size: var(--text-8xl);
      line-height: var(--tw-leading, var(--text-8xl--line-height));
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:visible {
      visibility: visible;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:absolute {
      position: absolute;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:right-1\/2 {
      right: 50%;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:-left-\[65\%\] {
      left: -65%;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:flex {
      display: flex;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:-translate-x-full {
      --tw-translate-x: -100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:translate-x-\[calc\(50\%-3rem\)\] {
      --tw-translate-x: calc(50% - 3rem);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:-translate-y-full {
      --tw-translate-y: -100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:-rotate-90 {
      rotate: -90deg;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:text-right {
      text-align: right;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:before\:hidden:before {
      content: var(--tw-content);
      display: none;
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:after\:hidden:after {
      content: var(--tw-content);
      display: none;
    }
  }

  @container (width < 64rem) {
    .\@max-\[theme\(--breakpoint-lg\)\]\:hidden {
      display: none;
    }
  }

  @container (width < 48rem) {
    .\@max-\[theme\(--breakpoint-md\)\]\:hidden {
      display: none;
    }
  }

  @container (width < 40rem) {
    .\@max-\[theme\(--breakpoint-sm\)\]\:hidden {
      display: none;
    }
  }

  @media (min-width: 40rem) {
    @container (width < 40rem) {
      .sm\:\@max-\[theme\(--breakpoint-sm\)\]\:inline-block {
        display: inline-block;
      }
    }
  }

  @container (width < 80rem) {
    .\@max-\[theme\(--breakpoint-xl\)\]\:-mx-4 {
      margin-inline: calc(var(--spacing) * -4);
    }
  }

  @container (width < 80rem) {
    .\@max-\[theme\(--breakpoint-xl\)\]\:-mt-4 {
      margin-top: calc(var(--spacing) * -4);
    }
  }

  @container (width < 80rem) {
    .\@max-\[theme\(--breakpoint-xl\)\]\:hidden {
      display: none;
    }
  }

  @container (width < 80rem) {
    .\@max-\[theme\(--breakpoint-xl\)\]\:aspect-square {
      aspect-ratio: 1;
    }
  }

  @container (width >= 40rem) {
    .\@min-\[--theme\(--breakpoint-sm\)\]\:-mx-8 {
      margin-inline: calc(var(--spacing) * -8);
    }
  }

  @container (width >= 24rem) {
    .\@sm\:aspect-3\/2 {
      aspect-ratio: 3 / 2;
    }
  }

  @container (width >= 24rem) {
    .\@sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @container (width >= 28rem) {
    .\@md\:flex-row {
      flex-direction: row;
    }
  }

  @container (width >= 28rem) {
    .\@md\:gap-x-8 {
      column-gap: calc(var(--spacing) * 8);
    }
  }

  @container (width >= 28rem) {
    .\@md\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @container (width >= 28rem) {
    .\@md\:text-2xl\/10 {
      font-size: var(--text-2xl);
      line-height: calc(var(--spacing) * 10);
    }
  }

  @container (width >= 64rem) {
    .\@min-\[theme\(--breakpoint-lg\)\]\:hidden {
      display: none;
    }
  }

  @container (width >= 64rem) {
    .\@min-\[theme\(--breakpoint-lg\)\]\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @container (width >= 64rem) {
    .\@min-\[theme\(--breakpoint-lg\)\]\:px-20 {
      padding-inline: calc(var(--spacing) * 20);
    }
  }

  @container (width >= 64rem) {
    .\@min-\[theme\(--breakpoint-lg\)\]\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }
  }

  @container (width >= 64rem) {
    .\@min-\[theme\(--breakpoint-lg\)\]\:pb-10 {
      padding-bottom: calc(var(--spacing) * 10);
    }
  }

  @container (width >= 48rem) {
    .\@min-\[theme\(--breakpoint-md\)\]\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @container (width >= 40rem) {
    .\@min-\[theme\(--breakpoint-sm\)\]\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @container (width >= 40rem) {
    .\@min-\[theme\(--breakpoint-sm\)\]\:hidden {
      display: none;
    }
  }

  @container (width >= 40rem) {
    .\@min-\[theme\(--breakpoint-sm\)\]\:h-40 {
      height: calc(var(--spacing) * 40);
    }
  }

  @container (width >= 40rem) {
    .\@min-\[theme\(--breakpoint-sm\)\]\:w-auto {
      width: auto;
    }
  }

  @container (width >= 40rem) {
    .\@min-\[theme\(--breakpoint-sm\)\]\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @container (width >= 40rem) {
    .\@min-\[theme\(--breakpoint-sm\)\]\:grid-cols-\[1fr_auto\] {
      grid-template-columns: 1fr auto;
    }
  }

  @container (width >= 80rem) {
    .\@min-\[theme\(--breakpoint-xl\)\]\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @container (width >= 80rem) {
    .\@min-\[theme\(--breakpoint-xl\)\]\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @container (width >= 80rem) {
    .\@min-\[theme\(--breakpoint-xl\)\]\:row-span-2 {
      grid-row: span 2 / span 2;
    }
  }

  @container (width >= 80rem) {
    .\@min-\[theme\(--breakpoint-xl\)\]\:aspect-square {
      aspect-ratio: 1;
    }
  }

  @container (width >= 80rem) {
    .\@min-\[theme\(--breakpoint-xl\)\]\:h-\[308px\] {
      height: 308px;
    }
  }

  @container (width >= 80rem) {
    .\@min-\[theme\(--breakpoint-xl\)\]\:max-w-md {
      max-width: var(--container-md);
    }
  }

  @container (width >= 80rem) {
    .\@min-\[theme\(--breakpoint-xl\)\]\:grid-cols-1 {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .ltr\:md\:border-l:where(:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))), [dir="ltr"], [dir="ltr"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }

  @media (min-width: 48rem) {
    .ltr\:md\:border-l:where(:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))), [dir="ltr"], [dir="ltr"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }

  @media (min-width: 48rem) {
    .ltr\:md\:border-l:where(:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))), [dir="ltr"], [dir="ltr"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }

  .rtl\:border-r:where(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .rtl\:border-r:where(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .rtl\:border-r:where(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  @media (prefers-color-scheme: dark) {
    .dark\:hidden {
      display: none;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:inline {
      display: inline;
    }
  }

  @media (prefers-color-scheme: dark) {
    :where(.dark\:divide-white\/5 > :not(:last-child)) {
      border-color: rgba(255, 255, 255, .05);
    }

    @supports (color: color-mix(in lab, red, red)) {
      :where(.dark\:divide-white\/5 > :not(:last-child)) {
        border-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    :where(.dark\:divide-white\/10 > :not(:last-child)) {
      border-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      :where(.dark\:divide-white\/10 > :not(:last-child)) {
        border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-700 {
      border-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-sky-300\/30 {
      border-color: rgba(119, 212, 255, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-sky-300\/30 {
        border-color: color-mix(in oklab, var(--color-sky-300) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-white\/10 {
      border-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-white\/10 {
        border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-700 {
      background-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-800 {
      background-color: var(--color-gray-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-900 {
      background-color: var(--color-gray-900);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-950 {
      background-color: var(--color-gray-950);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-950\! {
      background-color: var(--color-gray-950) !important;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-slate-500 {
      background-color: var(--color-slate-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white\/5 {
      background-color: rgba(255, 255, 255, .05);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/5 {
        background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white\/10 {
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/10 {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-white\/30 {
      background-color: rgba(255, 255, 255, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-white\/30 {
        background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-emerald-400 {
      --tw-gradient-from: var(--color-emerald-400);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-pink-500 {
      --tw-gradient-from: var(--color-pink-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-purple-500 {
      --tw-gradient-from: var(--color-purple-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-amber-200 {
      --tw-gradient-to: var(--color-amber-200);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:fill-gray-400 {
      fill: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:fill-gray-500 {
      fill: var(--color-gray-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:fill-gray-950 {
      fill: var(--color-gray-950);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:fill-sky-300\/50 {
      fill: rgba(119, 212, 255, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:fill-sky-300\/50 {
        fill: color-mix(in oklab, var(--color-sky-300) 50%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-fuchsia-400 {
      color: var(--color-fuchsia-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-200 {
      color: var(--color-gray-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-400 {
      color: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-500 {
      color: var(--color-gray-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-600 {
      color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-pink-400 {
      color: var(--color-pink-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-pink-500 {
      color: var(--color-pink-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-sky-300 {
      color: var(--color-sky-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-sky-400 {
      color: var(--color-sky-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white {
      color: var(--color-white);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/25 {
      color: rgba(255, 255, 255, .25);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/25 {
        color: color-mix(in oklab, var(--color-white) 25%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/50 {
      color: rgba(255, 255, 255, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/50 {
        color: color-mix(in oklab, var(--color-white) 50%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/75 {
      color: rgba(255, 255, 255, .75);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/75 {
        color: color-mix(in oklab, var(--color-white) 75%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:\[color-scheme\:dark\] {
      --lightningcss-light: ;
      --lightningcss-dark: initial;
      color-scheme: dark;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:opacity-40 {
      opacity: .4;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:ring {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:inset-ring {
      --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:ring-white\/10 {
      --tw-ring-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:ring-white\/10 {
        --tw-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:inset-ring-white\/2 {
      --tw-inset-ring-color: rgba(255, 255, 255, .02);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:inset-ring-white\/2 {
        --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 2%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:inset-ring-white\/5 {
      --tw-inset-ring-color: rgba(255, 255, 255, .05);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:inset-ring-white\/5 {
        --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:inset-ring-white\/10 {
      --tw-inset-ring-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:inset-ring-white\/10 {
        --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:inset-ring-white\/15 {
      --tw-inset-ring-color: rgba(255, 255, 255, .15);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:inset-ring-white\/15 {
        --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 15%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:outline {
      outline-style: var(--tw-outline-style);
      outline-width: 1px;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:outline-1 {
      outline-style: var(--tw-outline-style);
      outline-width: 1px;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:-outline-offset-1 {
      outline-offset: calc(1px * -1);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:outline-white\/10 {
      outline-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:outline-white\/10 {
        outline-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:\[--pattern-fg\:var\(--color-white\)\]\/10 {
      --pattern-fg: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:\[--pattern-fg\:var\(--color-white\)\]\/10 {
        --pattern-fg: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:\[--site-background\:var\(--color-gray-950\)\] {
      --site-background: var(--color-gray-950);
    }
  }

  @media (prefers-color-scheme: dark) {
    :is(.dark\:\*\:bg-white\/5\! > *) {
      background-color: rgba(255, 255, 255, .05) !important;
    }

    @supports (color: color-mix(in lab, red, red)) {
      :is(.dark\:\*\:bg-white\/5\! > *) {
        background-color: color-mix(in oklab, var(--color-white) 5%, transparent) !important;
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    :is(.dark\:\*\:inset-ring-white\/5 > *) {
      --tw-inset-ring-color: rgba(255, 255, 255, .05);
    }

    @supports (color: color-mix(in lab, red, red)) {
      :is(.dark\:\*\:inset-ring-white\/5 > *) {
        --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 5%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:not-group-has-data-lg\:bg-white\/10:not(:is(:where(.group):has([data-lg]) *)) {
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:not-group-has-data-lg\:bg-white\/10:not(:is(:where(.group):has([data-lg]) *)) {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:not-group-has-data-md\:bg-white\/10:not(:is(:where(.group):has([data-md]) *)) {
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:not-group-has-data-md\:bg-white\/10:not(:is(:where(.group):has([data-md]) *)) {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:not-group-has-data-sm\:bg-white\/10:not(:is(:where(.group):has([data-sm]) *)) {
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:not-group-has-data-sm\:bg-white\/10:not(:is(:where(.group):has([data-sm]) *)) {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:not-group-has-data-xl\:bg-white\/10:not(:is(:where(.group):has([data-xl]) *)) {
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:not-group-has-data-xl\:bg-white\/10:not(:is(:where(.group):has([data-xl]) *)) {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:group-data-dragging\:bg-slate-300:is(:where(.group)[data-dragging] *) {
      background-color: var(--color-slate-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:before\:bg-white\/10:before {
      content: var(--tw-content);
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:before\:bg-white\/10:before {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:after\:bg-white\/10:after {
      content: var(--tw-content);
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:after\:bg-white\/10:after {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:after\:inset-ring-white\/10:after {
      content: var(--tw-content);
      --tw-inset-ring-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:after\:inset-ring-white\/10:after {
        --tw-inset-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-600:hover {
        background-color: var(--color-gray-600);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-slate-300:hover {
        background-color: var(--color-slate-300);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/2\.5:hover {
        background-color: rgba(255, 255, 255, .024);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/2\.5:hover {
          background-color: color-mix(in oklab, var(--color-white) 2.5%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/10:hover {
        background-color: rgba(255, 255, 255, .1);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/10:hover {
          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/12\.5:hover {
        background-color: rgba(255, 255, 255, .125);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/12\.5:hover {
          background-color: color-mix(in oklab, var(--color-white) 12.5%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-active\:bg-white\/12\.5[data-active] {
      background-color: rgba(255, 255, 255, .125);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:data-active\:bg-white\/12\.5[data-active] {
        background-color: color-mix(in oklab, var(--color-white) 12.5%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-checked\:bg-gray-700[data-checked] {
      background-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-checked\:text-white[data-checked] {
      color: var(--color-white);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-checked\:ring-transparent[data-checked] {
      --tw-ring-color: transparent;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-selected\:text-indigo-500[data-selected] {
      color: var(--color-indigo-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-selected\:text-pink-500[data-selected] {
      color: var(--color-pink-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:data-selected\:text-sky-500[data-selected] {
      color: var(--color-sky-500);
    }
  }

  @media (min-width: 40rem) {
    @media (prefers-color-scheme: dark) {
      .sm\:dark\:border-gray-300\/10 {
        border-color: rgba(209, 213, 220, .1);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .sm\:dark\:border-gray-300\/10 {
          border-color: color-mix(in oklab, var(--color-gray-300) 10%, transparent);
        }
      }
    }
  }

  @media (pointer: fine) {
    .pointer-fine\:hidden {
      display: none;
    }
  }

  :is(.\*\*\:\[\.line\]\:isolate *).line {
    isolation: isolate;
  }

  :is(.\*\*\:\[\.line\]\:block *).line {
    display: block;
  }

  :is(.\*\*\:\[\.line\]\:not-last\:min-h-\[1lh\] *).line:not(:last-child) {
    min-height: 1lh;
  }

  :-webkit-any(.\*\*\:\[code\]\:w-full *):-webkit-any(code) {
    width: 100%;
  }

  :-moz-any(.\*\*\:\[code\]\:w-full *):-moz-any(code) {
    width: 100%;
  }

  :is(.\*\*\:\[code\]\:w-full *):is(code) {
    width: 100%;
  }

  :-webkit-any(.\*\*\:\[code\]\:pr-4 *):-webkit-any(code) {
    padding-right: calc(var(--spacing) * 4);
  }

  :-moz-any(.\*\*\:\[code\]\:pr-4 *):-moz-any(code) {
    padding-right: calc(var(--spacing) * 4);
  }

  :is(.\*\*\:\[code\]\:pr-4 *):is(code) {
    padding-right: calc(var(--spacing) * 4);
  }

  :-webkit-any(.\*\*\:\[pre\]\:w-full *):-webkit-any(pre) {
    width: 100%;
  }

  :-moz-any(.\*\*\:\[pre\]\:w-full *):-moz-any(pre) {
    width: 100%;
  }

  :is(.\*\*\:\[pre\]\:w-full *):is(pre) {
    width: 100%;
  }

  :-webkit-any(.\*\*\:\[pre\]\:whitespace-pre-wrap *):-webkit-any(pre) {
    white-space: pre-wrap;
  }

  :-moz-any(.\*\*\:\[pre\]\:whitespace-pre-wrap *):-moz-any(pre) {
    white-space: pre-wrap;
  }

  :is(.\*\*\:\[pre\]\:whitespace-pre-wrap *):is(pre) {
    white-space: pre-wrap;
  }
}

:root {
  --background: #fff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  background: #f97316;
  border: 2px solid #1e293b;
  border-radius: 50%;
  width: 20px;
  height: 20px;
}

.slider::-moz-range-thumb {
  cursor: pointer;
  background: #f97316;
  border: 2px solid #1e293b;
  border-radius: 50%;
  width: 20px;
  height: 20px;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-scroll-snap-strictness {
  syntax: "*";
  inherits: false;
  initial-value: proximity;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

/*# sourceMappingURL=src_app_globals_css_bad6b30c._.single.css.map*/