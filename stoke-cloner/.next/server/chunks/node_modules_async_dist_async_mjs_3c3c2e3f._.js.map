{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/node_modules/async/dist/async.mjs"], "sourcesContent": ["/**\n * Creates a continuation function with some arguments already applied.\n *\n * Useful as a shorthand when combined with other control flow functions. Any\n * arguments passed to the returned function are added to the arguments\n * originally passed to apply.\n *\n * @name apply\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {Function} fn - The function you want to eventually apply all\n * arguments to. Invokes with (arguments...).\n * @param {...*} arguments... - Any number of arguments to automatically apply\n * when the continuation is called.\n * @returns {Function} the partially-applied function\n * @example\n *\n * // using apply\n * async.parallel([\n *     async.apply(fs.writeFile, 'testfile1', 'test1'),\n *     async.apply(fs.writeFile, 'testfile2', 'test2')\n * ]);\n *\n *\n * // the same process without using apply\n * async.parallel([\n *     function(callback) {\n *         fs.writeFile('testfile1', 'test1', callback);\n *     },\n *     function(callback) {\n *         fs.writeFile('testfile2', 'test2', callback);\n *     }\n * ]);\n *\n * // It's possible to pass any number of additional arguments when calling the\n * // continuation:\n *\n * node> var fn = async.apply(sys.puts, 'one');\n * node> fn('two', 'three');\n * one\n * two\n * three\n */\nfunction apply(fn, ...args) {\n    return (...callArgs) => fn(...args,...callArgs);\n}\n\nfunction initialParams (fn) {\n    return function (...args/*, callback*/) {\n        var callback = args.pop();\n        return fn.call(this, args, callback);\n    };\n}\n\n/* istanbul ignore file */\n\nvar hasQueueMicrotask = typeof queueMicrotask === 'function' && queueMicrotask;\nvar hasSetImmediate = typeof setImmediate === 'function' && setImmediate;\nvar hasNextTick = typeof process === 'object' && typeof process.nextTick === 'function';\n\nfunction fallback(fn) {\n    setTimeout(fn, 0);\n}\n\nfunction wrap(defer) {\n    return (fn, ...args) => defer(() => fn(...args));\n}\n\nvar _defer$1;\n\nif (hasQueueMicrotask) {\n    _defer$1 = queueMicrotask;\n} else if (hasSetImmediate) {\n    _defer$1 = setImmediate;\n} else if (hasNextTick) {\n    _defer$1 = process.nextTick;\n} else {\n    _defer$1 = fallback;\n}\n\nvar setImmediate$1 = wrap(_defer$1);\n\n/**\n * Take a sync function and make it async, passing its return value to a\n * callback. This is useful for plugging sync functions into a waterfall,\n * series, or other async functions. Any arguments passed to the generated\n * function will be passed to the wrapped function (except for the final\n * callback argument). Errors thrown will be passed to the callback.\n *\n * If the function passed to `asyncify` returns a Promise, that promises's\n * resolved/rejected state will be used to call the callback, rather than simply\n * the synchronous return value.\n *\n * This also means you can asyncify ES2017 `async` functions.\n *\n * @name asyncify\n * @static\n * @memberOf module:Utils\n * @method\n * @alias wrapSync\n * @category Util\n * @param {Function} func - The synchronous function, or Promise-returning\n * function to convert to an {@link AsyncFunction}.\n * @returns {AsyncFunction} An asynchronous wrapper of the `func`. To be\n * invoked with `(args..., callback)`.\n * @example\n *\n * // passing a regular synchronous function\n * async.waterfall([\n *     async.apply(fs.readFile, filename, \"utf8\"),\n *     async.asyncify(JSON.parse),\n *     function (data, next) {\n *         // data is the result of parsing the text.\n *         // If there was a parsing error, it would have been caught.\n *     }\n * ], callback);\n *\n * // passing a function returning a promise\n * async.waterfall([\n *     async.apply(fs.readFile, filename, \"utf8\"),\n *     async.asyncify(function (contents) {\n *         return db.model.create(contents);\n *     }),\n *     function (model, next) {\n *         // `model` is the instantiated model object.\n *         // If there was an error, this function would be skipped.\n *     }\n * ], callback);\n *\n * // es2017 example, though `asyncify` is not needed if your JS environment\n * // supports async functions out of the box\n * var q = async.queue(async.asyncify(async function(file) {\n *     var intermediateStep = await processFile(file);\n *     return await somePromise(intermediateStep)\n * }));\n *\n * q.push(files);\n */\nfunction asyncify(func) {\n    if (isAsync(func)) {\n        return function (...args/*, callback*/) {\n            const callback = args.pop();\n            const promise = func.apply(this, args);\n            return handlePromise(promise, callback)\n        }\n    }\n\n    return initialParams(function (args, callback) {\n        var result;\n        try {\n            result = func.apply(this, args);\n        } catch (e) {\n            return callback(e);\n        }\n        // if result is Promise object\n        if (result && typeof result.then === 'function') {\n            return handlePromise(result, callback)\n        } else {\n            callback(null, result);\n        }\n    });\n}\n\nfunction handlePromise(promise, callback) {\n    return promise.then(value => {\n        invokeCallback(callback, null, value);\n    }, err => {\n        invokeCallback(callback, err && (err instanceof Error || err.message) ? err : new Error(err));\n    });\n}\n\nfunction invokeCallback(callback, error, value) {\n    try {\n        callback(error, value);\n    } catch (err) {\n        setImmediate$1(e => { throw e }, err);\n    }\n}\n\nfunction isAsync(fn) {\n    return fn[Symbol.toStringTag] === 'AsyncFunction';\n}\n\nfunction isAsyncGenerator(fn) {\n    return fn[Symbol.toStringTag] === 'AsyncGenerator';\n}\n\nfunction isAsyncIterable(obj) {\n    return typeof obj[Symbol.asyncIterator] === 'function';\n}\n\nfunction wrapAsync(asyncFn) {\n    if (typeof asyncFn !== 'function') throw new Error('expected a function')\n    return isAsync(asyncFn) ? asyncify(asyncFn) : asyncFn;\n}\n\n// conditionally promisify a function.\n// only return a promise if a callback is omitted\nfunction awaitify (asyncFn, arity) {\n    if (!arity) arity = asyncFn.length;\n    if (!arity) throw new Error('arity is undefined')\n    function awaitable (...args) {\n        if (typeof args[arity - 1] === 'function') {\n            return asyncFn.apply(this, args)\n        }\n\n        return new Promise((resolve, reject) => {\n            args[arity - 1] = (err, ...cbArgs) => {\n                if (err) return reject(err)\n                resolve(cbArgs.length > 1 ? cbArgs : cbArgs[0]);\n            };\n            asyncFn.apply(this, args);\n        })\n    }\n\n    return awaitable\n}\n\nfunction applyEach$1 (eachfn) {\n    return function applyEach(fns, ...callArgs) {\n        const go = awaitify(function (callback) {\n            var that = this;\n            return eachfn(fns, (fn, cb) => {\n                wrapAsync(fn).apply(that, callArgs.concat(cb));\n            }, callback);\n        });\n        return go;\n    };\n}\n\nfunction _asyncMap(eachfn, arr, iteratee, callback) {\n    arr = arr || [];\n    var results = [];\n    var counter = 0;\n    var _iteratee = wrapAsync(iteratee);\n\n    return eachfn(arr, (value, _, iterCb) => {\n        var index = counter++;\n        _iteratee(value, (err, v) => {\n            results[index] = v;\n            iterCb(err);\n        });\n    }, err => {\n        callback(err, results);\n    });\n}\n\nfunction isArrayLike(value) {\n    return value &&\n        typeof value.length === 'number' &&\n        value.length >= 0 &&\n        value.length % 1 === 0;\n}\n\n// A temporary value used to identify if the loop should be broken.\n// See #1064, #1293\nconst breakLoop = {};\n\nfunction once(fn) {\n    function wrapper (...args) {\n        if (fn === null) return;\n        var callFn = fn;\n        fn = null;\n        callFn.apply(this, args);\n    }\n    Object.assign(wrapper, fn);\n    return wrapper\n}\n\nfunction getIterator (coll) {\n    return coll[Symbol.iterator] && coll[Symbol.iterator]();\n}\n\nfunction createArrayIterator(coll) {\n    var i = -1;\n    var len = coll.length;\n    return function next() {\n        return ++i < len ? {value: coll[i], key: i} : null;\n    }\n}\n\nfunction createES2015Iterator(iterator) {\n    var i = -1;\n    return function next() {\n        var item = iterator.next();\n        if (item.done)\n            return null;\n        i++;\n        return {value: item.value, key: i};\n    }\n}\n\nfunction createObjectIterator(obj) {\n    var okeys = obj ? Object.keys(obj) : [];\n    var i = -1;\n    var len = okeys.length;\n    return function next() {\n        var key = okeys[++i];\n        if (key === '__proto__') {\n            return next();\n        }\n        return i < len ? {value: obj[key], key} : null;\n    };\n}\n\nfunction createIterator(coll) {\n    if (isArrayLike(coll)) {\n        return createArrayIterator(coll);\n    }\n\n    var iterator = getIterator(coll);\n    return iterator ? createES2015Iterator(iterator) : createObjectIterator(coll);\n}\n\nfunction onlyOnce(fn) {\n    return function (...args) {\n        if (fn === null) throw new Error(\"Callback was already called.\");\n        var callFn = fn;\n        fn = null;\n        callFn.apply(this, args);\n    };\n}\n\n// for async generators\nfunction asyncEachOfLimit(generator, limit, iteratee, callback) {\n    let done = false;\n    let canceled = false;\n    let awaiting = false;\n    let running = 0;\n    let idx = 0;\n\n    function replenish() {\n        //console.log('replenish')\n        if (running >= limit || awaiting || done) return\n        //console.log('replenish awaiting')\n        awaiting = true;\n        generator.next().then(({value, done: iterDone}) => {\n            //console.log('got value', value)\n            if (canceled || done) return\n            awaiting = false;\n            if (iterDone) {\n                done = true;\n                if (running <= 0) {\n                    //console.log('done nextCb')\n                    callback(null);\n                }\n                return;\n            }\n            running++;\n            iteratee(value, idx, iterateeCallback);\n            idx++;\n            replenish();\n        }).catch(handleError);\n    }\n\n    function iterateeCallback(err, result) {\n        //console.log('iterateeCallback')\n        running -= 1;\n        if (canceled) return\n        if (err) return handleError(err)\n\n        if (err === false) {\n            done = true;\n            canceled = true;\n            return\n        }\n\n        if (result === breakLoop || (done && running <= 0)) {\n            done = true;\n            //console.log('done iterCb')\n            return callback(null);\n        }\n        replenish();\n    }\n\n    function handleError(err) {\n        if (canceled) return\n        awaiting = false;\n        done = true;\n        callback(err);\n    }\n\n    replenish();\n}\n\nvar eachOfLimit$2 = (limit) => {\n    return (obj, iteratee, callback) => {\n        callback = once(callback);\n        if (limit <= 0) {\n            throw new RangeError('concurrency limit cannot be less than 1')\n        }\n        if (!obj) {\n            return callback(null);\n        }\n        if (isAsyncGenerator(obj)) {\n            return asyncEachOfLimit(obj, limit, iteratee, callback)\n        }\n        if (isAsyncIterable(obj)) {\n            return asyncEachOfLimit(obj[Symbol.asyncIterator](), limit, iteratee, callback)\n        }\n        var nextElem = createIterator(obj);\n        var done = false;\n        var canceled = false;\n        var running = 0;\n        var looping = false;\n\n        function iterateeCallback(err, value) {\n            if (canceled) return\n            running -= 1;\n            if (err) {\n                done = true;\n                callback(err);\n            }\n            else if (err === false) {\n                done = true;\n                canceled = true;\n            }\n            else if (value === breakLoop || (done && running <= 0)) {\n                done = true;\n                return callback(null);\n            }\n            else if (!looping) {\n                replenish();\n            }\n        }\n\n        function replenish () {\n            looping = true;\n            while (running < limit && !done) {\n                var elem = nextElem();\n                if (elem === null) {\n                    done = true;\n                    if (running <= 0) {\n                        callback(null);\n                    }\n                    return;\n                }\n                running += 1;\n                iteratee(elem.value, elem.key, onlyOnce(iterateeCallback));\n            }\n            looping = false;\n        }\n\n        replenish();\n    };\n};\n\n/**\n * The same as [`eachOf`]{@link module:Collections.eachOf} but runs a maximum of `limit` async operations at a\n * time.\n *\n * @name eachOfLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.eachOf]{@link module:Collections.eachOf}\n * @alias forEachOfLimit\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - An async function to apply to each\n * item in `coll`. The `key` is the item's key, or index in the case of an\n * array.\n * Invoked with (item, key, callback).\n * @param {Function} [callback] - A callback which is called when all\n * `iteratee` functions have finished, or an error occurs. Invoked with (err).\n * @returns {Promise} a promise, if a callback is omitted\n */\nfunction eachOfLimit(coll, limit, iteratee, callback) {\n    return eachOfLimit$2(limit)(coll, wrapAsync(iteratee), callback);\n}\n\nvar eachOfLimit$1 = awaitify(eachOfLimit, 4);\n\n// eachOf implementation optimized for array-likes\nfunction eachOfArrayLike(coll, iteratee, callback) {\n    callback = once(callback);\n    var index = 0,\n        completed = 0,\n        {length} = coll,\n        canceled = false;\n    if (length === 0) {\n        callback(null);\n    }\n\n    function iteratorCallback(err, value) {\n        if (err === false) {\n            canceled = true;\n        }\n        if (canceled === true) return\n        if (err) {\n            callback(err);\n        } else if ((++completed === length) || value === breakLoop) {\n            callback(null);\n        }\n    }\n\n    for (; index < length; index++) {\n        iteratee(coll[index], index, onlyOnce(iteratorCallback));\n    }\n}\n\n// a generic version of eachOf which can handle array, object, and iterator cases.\nfunction eachOfGeneric (coll, iteratee, callback) {\n    return eachOfLimit$1(coll, Infinity, iteratee, callback);\n}\n\n/**\n * Like [`each`]{@link module:Collections.each}, except that it passes the key (or index) as the second argument\n * to the iteratee.\n *\n * @name eachOf\n * @static\n * @memberOf module:Collections\n * @method\n * @alias forEachOf\n * @category Collection\n * @see [async.each]{@link module:Collections.each}\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - A function to apply to each\n * item in `coll`.\n * The `key` is the item's key, or index in the case of an array.\n * Invoked with (item, key, callback).\n * @param {Function} [callback] - A callback which is called when all\n * `iteratee` functions have finished, or an error occurs. Invoked with (err).\n * @returns {Promise} a promise, if a callback is omitted\n * @example\n *\n * // dev.json is a file containing a valid json object config for dev environment\n * // dev.json is a file containing a valid json object config for test environment\n * // prod.json is a file containing a valid json object config for prod environment\n * // invalid.json is a file with a malformed json object\n *\n * let configs = {}; //global variable\n * let validConfigFileMap = {dev: 'dev.json', test: 'test.json', prod: 'prod.json'};\n * let invalidConfigFileMap = {dev: 'dev.json', test: 'test.json', invalid: 'invalid.json'};\n *\n * // asynchronous function that reads a json file and parses the contents as json object\n * function parseFile(file, key, callback) {\n *     fs.readFile(file, \"utf8\", function(err, data) {\n *         if (err) return calback(err);\n *         try {\n *             configs[key] = JSON.parse(data);\n *         } catch (e) {\n *             return callback(e);\n *         }\n *         callback();\n *     });\n * }\n *\n * // Using callbacks\n * async.forEachOf(validConfigFileMap, parseFile, function (err) {\n *     if (err) {\n *         console.error(err);\n *     } else {\n *         console.log(configs);\n *         // configs is now a map of JSON data, e.g.\n *         // { dev: //parsed dev.json, test: //parsed test.json, prod: //parsed prod.json}\n *     }\n * });\n *\n * //Error handing\n * async.forEachOf(invalidConfigFileMap, parseFile, function (err) {\n *     if (err) {\n *         console.error(err);\n *         // JSON parse error exception\n *     } else {\n *         console.log(configs);\n *     }\n * });\n *\n * // Using Promises\n * async.forEachOf(validConfigFileMap, parseFile)\n * .then( () => {\n *     console.log(configs);\n *     // configs is now a map of JSON data, e.g.\n *     // { dev: //parsed dev.json, test: //parsed test.json, prod: //parsed prod.json}\n * }).catch( err => {\n *     console.error(err);\n * });\n *\n * //Error handing\n * async.forEachOf(invalidConfigFileMap, parseFile)\n * .then( () => {\n *     console.log(configs);\n * }).catch( err => {\n *     console.error(err);\n *     // JSON parse error exception\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.forEachOf(validConfigFileMap, parseFile);\n *         console.log(configs);\n *         // configs is now a map of JSON data, e.g.\n *         // { dev: //parsed dev.json, test: //parsed test.json, prod: //parsed prod.json}\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * //Error handing\n * async () => {\n *     try {\n *         let result = await async.forEachOf(invalidConfigFileMap, parseFile);\n *         console.log(configs);\n *     }\n *     catch (err) {\n *         console.log(err);\n *         // JSON parse error exception\n *     }\n * }\n *\n */\nfunction eachOf(coll, iteratee, callback) {\n    var eachOfImplementation = isArrayLike(coll) ? eachOfArrayLike : eachOfGeneric;\n    return eachOfImplementation(coll, wrapAsync(iteratee), callback);\n}\n\nvar eachOf$1 = awaitify(eachOf, 3);\n\n/**\n * Produces a new collection of values by mapping each value in `coll` through\n * the `iteratee` function. The `iteratee` is called with an item from `coll`\n * and a callback for when it has finished processing. Each of these callbacks\n * takes 2 arguments: an `error`, and the transformed item from `coll`. If\n * `iteratee` passes an error to its callback, the main `callback` (for the\n * `map` function) is immediately called with the error.\n *\n * Note, that since this function applies the `iteratee` to each item in\n * parallel, there is no guarantee that the `iteratee` functions will complete\n * in order. However, the results array will be in the same order as the\n * original `coll`.\n *\n * If `map` is passed an Object, the results will be an Array.  The results\n * will roughly be in the order of the original Objects' keys (but this can\n * vary across JavaScript engines).\n *\n * @name map\n * @static\n * @memberOf module:Collections\n * @method\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The iteratee should complete with the transformed item.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. Results is an Array of the\n * transformed items from the `coll`. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback is passed\n * @example\n *\n * // file1.txt is a file that is 1000 bytes in size\n * // file2.txt is a file that is 2000 bytes in size\n * // file3.txt is a file that is 3000 bytes in size\n * // file4.txt does not exist\n *\n * const fileList = ['file1.txt','file2.txt','file3.txt'];\n * const withMissingFileList = ['file1.txt','file2.txt','file4.txt'];\n *\n * // asynchronous function that returns the file size in bytes\n * function getFileSizeInBytes(file, callback) {\n *     fs.stat(file, function(err, stat) {\n *         if (err) {\n *             return callback(err);\n *         }\n *         callback(null, stat.size);\n *     });\n * }\n *\n * // Using callbacks\n * async.map(fileList, getFileSizeInBytes, function(err, results) {\n *     if (err) {\n *         console.log(err);\n *     } else {\n *         console.log(results);\n *         // results is now an array of the file size in bytes for each file, e.g.\n *         // [ 1000, 2000, 3000]\n *     }\n * });\n *\n * // Error Handling\n * async.map(withMissingFileList, getFileSizeInBytes, function(err, results) {\n *     if (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *     } else {\n *         console.log(results);\n *     }\n * });\n *\n * // Using Promises\n * async.map(fileList, getFileSizeInBytes)\n * .then( results => {\n *     console.log(results);\n *     // results is now an array of the file size in bytes for each file, e.g.\n *     // [ 1000, 2000, 3000]\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Error Handling\n * async.map(withMissingFileList, getFileSizeInBytes)\n * .then( results => {\n *     console.log(results);\n * }).catch( err => {\n *     console.log(err);\n *     // [ Error: ENOENT: no such file or directory ]\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let results = await async.map(fileList, getFileSizeInBytes);\n *         console.log(results);\n *         // results is now an array of the file size in bytes for each file, e.g.\n *         // [ 1000, 2000, 3000]\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * // Error Handling\n * async () => {\n *     try {\n *         let results = await async.map(withMissingFileList, getFileSizeInBytes);\n *         console.log(results);\n *     }\n *     catch (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *     }\n * }\n *\n */\nfunction map (coll, iteratee, callback) {\n    return _asyncMap(eachOf$1, coll, iteratee, callback)\n}\nvar map$1 = awaitify(map, 3);\n\n/**\n * Applies the provided arguments to each function in the array, calling\n * `callback` after all functions have completed. If you only provide the first\n * argument, `fns`, then it will return a function which lets you pass in the\n * arguments as if it were a single function call. If more arguments are\n * provided, `callback` is required while `args` is still optional. The results\n * for each of the applied async functions are passed to the final callback\n * as an array.\n *\n * @name applyEach\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {Array|Iterable|AsyncIterable|Object} fns - A collection of {@link AsyncFunction}s\n * to all call with the same arguments\n * @param {...*} [args] - any number of separate arguments to pass to the\n * function.\n * @param {Function} [callback] - the final argument should be the callback,\n * called when all functions have completed processing.\n * @returns {AsyncFunction} - Returns a function that takes no args other than\n * an optional callback, that is the result of applying the `args` to each\n * of the functions.\n * @example\n *\n * const appliedFn = async.applyEach([enableSearch, updateSchema], 'bucket')\n *\n * appliedFn((err, results) => {\n *     // results[0] is the results for `enableSearch`\n *     // results[1] is the results for `updateSchema`\n * });\n *\n * // partial application example:\n * async.each(\n *     buckets,\n *     async (bucket) => async.applyEach([enableSearch, updateSchema], bucket)(),\n *     callback\n * );\n */\nvar applyEach = applyEach$1(map$1);\n\n/**\n * The same as [`eachOf`]{@link module:Collections.eachOf} but runs only a single async operation at a time.\n *\n * @name eachOfSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.eachOf]{@link module:Collections.eachOf}\n * @alias forEachOfSeries\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * Invoked with (item, key, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. Invoked with (err).\n * @returns {Promise} a promise, if a callback is omitted\n */\nfunction eachOfSeries(coll, iteratee, callback) {\n    return eachOfLimit$1(coll, 1, iteratee, callback)\n}\nvar eachOfSeries$1 = awaitify(eachOfSeries, 3);\n\n/**\n * The same as [`map`]{@link module:Collections.map} but runs only a single async operation at a time.\n *\n * @name mapSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.map]{@link module:Collections.map}\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The iteratee should complete with the transformed item.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. Results is an array of the\n * transformed items from the `coll`. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction mapSeries (coll, iteratee, callback) {\n    return _asyncMap(eachOfSeries$1, coll, iteratee, callback)\n}\nvar mapSeries$1 = awaitify(mapSeries, 3);\n\n/**\n * The same as [`applyEach`]{@link module:ControlFlow.applyEach} but runs only a single async operation at a time.\n *\n * @name applyEachSeries\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.applyEach]{@link module:ControlFlow.applyEach}\n * @category Control Flow\n * @param {Array|Iterable|AsyncIterable|Object} fns - A collection of {@link AsyncFunction}s to all\n * call with the same arguments\n * @param {...*} [args] - any number of separate arguments to pass to the\n * function.\n * @param {Function} [callback] - the final argument should be the callback,\n * called when all functions have completed processing.\n * @returns {AsyncFunction} - A function, that when called, is the result of\n * appling the `args` to the list of functions.  It takes no args, other than\n * a callback.\n */\nvar applyEachSeries = applyEach$1(mapSeries$1);\n\nconst PROMISE_SYMBOL = Symbol('promiseCallback');\n\nfunction promiseCallback () {\n    let resolve, reject;\n    function callback (err, ...args) {\n        if (err) return reject(err)\n        resolve(args.length > 1 ? args : args[0]);\n    }\n\n    callback[PROMISE_SYMBOL] = new Promise((res, rej) => {\n        resolve = res,\n        reject = rej;\n    });\n\n    return callback\n}\n\n/**\n * Determines the best order for running the {@link AsyncFunction}s in `tasks`, based on\n * their requirements. Each function can optionally depend on other functions\n * being completed first, and each function is run as soon as its requirements\n * are satisfied.\n *\n * If any of the {@link AsyncFunction}s pass an error to their callback, the `auto` sequence\n * will stop. Further tasks will not execute (so any other functions depending\n * on it will not run), and the main `callback` is immediately called with the\n * error.\n *\n * {@link AsyncFunction}s also receive an object containing the results of functions which\n * have completed so far as the first argument, if they have dependencies. If a\n * task function has no dependencies, it will only be passed a callback.\n *\n * @name auto\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {Object} tasks - An object. Each of its properties is either a\n * function or an array of requirements, with the {@link AsyncFunction} itself the last item\n * in the array. The object's key of a property serves as the name of the task\n * defined by that property, i.e. can be used when specifying requirements for\n * other tasks. The function receives one or two arguments:\n * * a `results` object, containing the results of the previously executed\n *   functions, only passed if the task has any dependencies,\n * * a `callback(err, result)` function, which must be called when finished,\n *   passing an `error` (which can be `null`) and the result of the function's\n *   execution.\n * @param {number} [concurrency=Infinity] - An optional `integer` for\n * determining the maximum number of tasks that can be run in parallel. By\n * default, as many as possible.\n * @param {Function} [callback] - An optional callback which is called when all\n * the tasks have been completed. It receives the `err` argument if any `tasks`\n * pass an error to their callback. Results are always returned; however, if an\n * error occurs, no further `tasks` will be performed, and the results object\n * will only contain partial results. Invoked with (err, results).\n * @returns {Promise} a promise, if a callback is not passed\n * @example\n *\n * //Using Callbacks\n * async.auto({\n *     get_data: function(callback) {\n *         // async code to get some data\n *         callback(null, 'data', 'converted to array');\n *     },\n *     make_folder: function(callback) {\n *         // async code to create a directory to store a file in\n *         // this is run at the same time as getting the data\n *         callback(null, 'folder');\n *     },\n *     write_file: ['get_data', 'make_folder', function(results, callback) {\n *         // once there is some data and the directory exists,\n *         // write the data to a file in the directory\n *         callback(null, 'filename');\n *     }],\n *     email_link: ['write_file', function(results, callback) {\n *         // once the file is written let's email a link to it...\n *         callback(null, {'file':results.write_file, 'email':'<EMAIL>'});\n *     }]\n * }, function(err, results) {\n *     if (err) {\n *         console.log('err = ', err);\n *     }\n *     console.log('results = ', results);\n *     // results = {\n *     //     get_data: ['data', 'converted to array']\n *     //     make_folder; 'folder',\n *     //     write_file: 'filename'\n *     //     email_link: { file: 'filename', email: '<EMAIL>' }\n *     // }\n * });\n *\n * //Using Promises\n * async.auto({\n *     get_data: function(callback) {\n *         console.log('in get_data');\n *         // async code to get some data\n *         callback(null, 'data', 'converted to array');\n *     },\n *     make_folder: function(callback) {\n *         console.log('in make_folder');\n *         // async code to create a directory to store a file in\n *         // this is run at the same time as getting the data\n *         callback(null, 'folder');\n *     },\n *     write_file: ['get_data', 'make_folder', function(results, callback) {\n *         // once there is some data and the directory exists,\n *         // write the data to a file in the directory\n *         callback(null, 'filename');\n *     }],\n *     email_link: ['write_file', function(results, callback) {\n *         // once the file is written let's email a link to it...\n *         callback(null, {'file':results.write_file, 'email':'<EMAIL>'});\n *     }]\n * }).then(results => {\n *     console.log('results = ', results);\n *     // results = {\n *     //     get_data: ['data', 'converted to array']\n *     //     make_folder; 'folder',\n *     //     write_file: 'filename'\n *     //     email_link: { file: 'filename', email: '<EMAIL>' }\n *     // }\n * }).catch(err => {\n *     console.log('err = ', err);\n * });\n *\n * //Using async/await\n * async () => {\n *     try {\n *         let results = await async.auto({\n *             get_data: function(callback) {\n *                 // async code to get some data\n *                 callback(null, 'data', 'converted to array');\n *             },\n *             make_folder: function(callback) {\n *                 // async code to create a directory to store a file in\n *                 // this is run at the same time as getting the data\n *                 callback(null, 'folder');\n *             },\n *             write_file: ['get_data', 'make_folder', function(results, callback) {\n *                 // once there is some data and the directory exists,\n *                 // write the data to a file in the directory\n *                 callback(null, 'filename');\n *             }],\n *             email_link: ['write_file', function(results, callback) {\n *                 // once the file is written let's email a link to it...\n *                 callback(null, {'file':results.write_file, 'email':'<EMAIL>'});\n *             }]\n *         });\n *         console.log('results = ', results);\n *         // results = {\n *         //     get_data: ['data', 'converted to array']\n *         //     make_folder; 'folder',\n *         //     write_file: 'filename'\n *         //     email_link: { file: 'filename', email: '<EMAIL>' }\n *         // }\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction auto(tasks, concurrency, callback) {\n    if (typeof concurrency !== 'number') {\n        // concurrency is optional, shift the args.\n        callback = concurrency;\n        concurrency = null;\n    }\n    callback = once(callback || promiseCallback());\n    var numTasks = Object.keys(tasks).length;\n    if (!numTasks) {\n        return callback(null);\n    }\n    if (!concurrency) {\n        concurrency = numTasks;\n    }\n\n    var results = {};\n    var runningTasks = 0;\n    var canceled = false;\n    var hasError = false;\n\n    var listeners = Object.create(null);\n\n    var readyTasks = [];\n\n    // for cycle detection:\n    var readyToCheck = []; // tasks that have been identified as reachable\n    // without the possibility of returning to an ancestor task\n    var uncheckedDependencies = {};\n\n    Object.keys(tasks).forEach(key => {\n        var task = tasks[key];\n        if (!Array.isArray(task)) {\n            // no dependencies\n            enqueueTask(key, [task]);\n            readyToCheck.push(key);\n            return;\n        }\n\n        var dependencies = task.slice(0, task.length - 1);\n        var remainingDependencies = dependencies.length;\n        if (remainingDependencies === 0) {\n            enqueueTask(key, task);\n            readyToCheck.push(key);\n            return;\n        }\n        uncheckedDependencies[key] = remainingDependencies;\n\n        dependencies.forEach(dependencyName => {\n            if (!tasks[dependencyName]) {\n                throw new Error('async.auto task `' + key +\n                    '` has a non-existent dependency `' +\n                    dependencyName + '` in ' +\n                    dependencies.join(', '));\n            }\n            addListener(dependencyName, () => {\n                remainingDependencies--;\n                if (remainingDependencies === 0) {\n                    enqueueTask(key, task);\n                }\n            });\n        });\n    });\n\n    checkForDeadlocks();\n    processQueue();\n\n    function enqueueTask(key, task) {\n        readyTasks.push(() => runTask(key, task));\n    }\n\n    function processQueue() {\n        if (canceled) return\n        if (readyTasks.length === 0 && runningTasks === 0) {\n            return callback(null, results);\n        }\n        while(readyTasks.length && runningTasks < concurrency) {\n            var run = readyTasks.shift();\n            run();\n        }\n\n    }\n\n    function addListener(taskName, fn) {\n        var taskListeners = listeners[taskName];\n        if (!taskListeners) {\n            taskListeners = listeners[taskName] = [];\n        }\n\n        taskListeners.push(fn);\n    }\n\n    function taskComplete(taskName) {\n        var taskListeners = listeners[taskName] || [];\n        taskListeners.forEach(fn => fn());\n        processQueue();\n    }\n\n\n    function runTask(key, task) {\n        if (hasError) return;\n\n        var taskCallback = onlyOnce((err, ...result) => {\n            runningTasks--;\n            if (err === false) {\n                canceled = true;\n                return\n            }\n            if (result.length < 2) {\n                [result] = result;\n            }\n            if (err) {\n                var safeResults = {};\n                Object.keys(results).forEach(rkey => {\n                    safeResults[rkey] = results[rkey];\n                });\n                safeResults[key] = result;\n                hasError = true;\n                listeners = Object.create(null);\n                if (canceled) return\n                callback(err, safeResults);\n            } else {\n                results[key] = result;\n                taskComplete(key);\n            }\n        });\n\n        runningTasks++;\n        var taskFn = wrapAsync(task[task.length - 1]);\n        if (task.length > 1) {\n            taskFn(results, taskCallback);\n        } else {\n            taskFn(taskCallback);\n        }\n    }\n\n    function checkForDeadlocks() {\n        // Kahn's algorithm\n        // https://en.wikipedia.org/wiki/Topological_sorting#Kahn.27s_algorithm\n        // http://connalle.blogspot.com/2013/10/topological-sortingkahn-algorithm.html\n        var currentTask;\n        var counter = 0;\n        while (readyToCheck.length) {\n            currentTask = readyToCheck.pop();\n            counter++;\n            getDependents(currentTask).forEach(dependent => {\n                if (--uncheckedDependencies[dependent] === 0) {\n                    readyToCheck.push(dependent);\n                }\n            });\n        }\n\n        if (counter !== numTasks) {\n            throw new Error(\n                'async.auto cannot execute tasks due to a recursive dependency'\n            );\n        }\n    }\n\n    function getDependents(taskName) {\n        var result = [];\n        Object.keys(tasks).forEach(key => {\n            const task = tasks[key];\n            if (Array.isArray(task) && task.indexOf(taskName) >= 0) {\n                result.push(key);\n            }\n        });\n        return result;\n    }\n\n    return callback[PROMISE_SYMBOL]\n}\n\nvar FN_ARGS = /^(?:async\\s)?(?:function)?\\s*(?:\\w+\\s*)?\\(([^)]+)\\)(?:\\s*{)/;\nvar ARROW_FN_ARGS = /^(?:async\\s)?\\s*(?:\\(\\s*)?((?:[^)=\\s]\\s*)*)(?:\\)\\s*)?=>/;\nvar FN_ARG_SPLIT = /,/;\nvar FN_ARG = /(=.+)?(\\s*)$/;\n\nfunction stripComments(string) {\n    let stripped = '';\n    let index = 0;\n    let endBlockComment = string.indexOf('*/');\n    while (index < string.length) {\n        if (string[index] === '/' && string[index+1] === '/') {\n            // inline comment\n            let endIndex = string.indexOf('\\n', index);\n            index = (endIndex === -1) ? string.length : endIndex;\n        } else if ((endBlockComment !== -1) && (string[index] === '/') && (string[index+1] === '*')) {\n            // block comment\n            let endIndex = string.indexOf('*/', index);\n            if (endIndex !== -1) {\n                index = endIndex + 2;\n                endBlockComment = string.indexOf('*/', index);\n            } else {\n                stripped += string[index];\n                index++;\n            }\n        } else {\n            stripped += string[index];\n            index++;\n        }\n    }\n    return stripped;\n}\n\nfunction parseParams(func) {\n    const src = stripComments(func.toString());\n    let match = src.match(FN_ARGS);\n    if (!match) {\n        match = src.match(ARROW_FN_ARGS);\n    }\n    if (!match) throw new Error('could not parse args in autoInject\\nSource:\\n' + src)\n    let [, args] = match;\n    return args\n        .replace(/\\s/g, '')\n        .split(FN_ARG_SPLIT)\n        .map((arg) => arg.replace(FN_ARG, '').trim());\n}\n\n/**\n * A dependency-injected version of the [async.auto]{@link module:ControlFlow.auto} function. Dependent\n * tasks are specified as parameters to the function, after the usual callback\n * parameter, with the parameter names matching the names of the tasks it\n * depends on. This can provide even more readable task graphs which can be\n * easier to maintain.\n *\n * If a final callback is specified, the task results are similarly injected,\n * specified as named parameters after the initial error parameter.\n *\n * The autoInject function is purely syntactic sugar and its semantics are\n * otherwise equivalent to [async.auto]{@link module:ControlFlow.auto}.\n *\n * @name autoInject\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.auto]{@link module:ControlFlow.auto}\n * @category Control Flow\n * @param {Object} tasks - An object, each of whose properties is an {@link AsyncFunction} of\n * the form 'func([dependencies...], callback). The object's key of a property\n * serves as the name of the task defined by that property, i.e. can be used\n * when specifying requirements for other tasks.\n * * The `callback` parameter is a `callback(err, result)` which must be called\n *   when finished, passing an `error` (which can be `null`) and the result of\n *   the function's execution. The remaining parameters name other tasks on\n *   which the task is dependent, and the results from those tasks are the\n *   arguments of those parameters.\n * @param {Function} [callback] - An optional callback which is called when all\n * the tasks have been completed. It receives the `err` argument if any `tasks`\n * pass an error to their callback, and a `results` object with any completed\n * task results, similar to `auto`.\n * @returns {Promise} a promise, if no callback is passed\n * @example\n *\n * //  The example from `auto` can be rewritten as follows:\n * async.autoInject({\n *     get_data: function(callback) {\n *         // async code to get some data\n *         callback(null, 'data', 'converted to array');\n *     },\n *     make_folder: function(callback) {\n *         // async code to create a directory to store a file in\n *         // this is run at the same time as getting the data\n *         callback(null, 'folder');\n *     },\n *     write_file: function(get_data, make_folder, callback) {\n *         // once there is some data and the directory exists,\n *         // write the data to a file in the directory\n *         callback(null, 'filename');\n *     },\n *     email_link: function(write_file, callback) {\n *         // once the file is written let's email a link to it...\n *         // write_file contains the filename returned by write_file.\n *         callback(null, {'file':write_file, 'email':'<EMAIL>'});\n *     }\n * }, function(err, results) {\n *     console.log('err = ', err);\n *     console.log('email_link = ', results.email_link);\n * });\n *\n * // If you are using a JS minifier that mangles parameter names, `autoInject`\n * // will not work with plain functions, since the parameter names will be\n * // collapsed to a single letter identifier.  To work around this, you can\n * // explicitly specify the names of the parameters your task function needs\n * // in an array, similar to Angular.js dependency injection.\n *\n * // This still has an advantage over plain `auto`, since the results a task\n * // depends on are still spread into arguments.\n * async.autoInject({\n *     //...\n *     write_file: ['get_data', 'make_folder', function(get_data, make_folder, callback) {\n *         callback(null, 'filename');\n *     }],\n *     email_link: ['write_file', function(write_file, callback) {\n *         callback(null, {'file':write_file, 'email':'<EMAIL>'});\n *     }]\n *     //...\n * }, function(err, results) {\n *     console.log('err = ', err);\n *     console.log('email_link = ', results.email_link);\n * });\n */\nfunction autoInject(tasks, callback) {\n    var newTasks = {};\n\n    Object.keys(tasks).forEach(key => {\n        var taskFn = tasks[key];\n        var params;\n        var fnIsAsync = isAsync(taskFn);\n        var hasNoDeps =\n            (!fnIsAsync && taskFn.length === 1) ||\n            (fnIsAsync && taskFn.length === 0);\n\n        if (Array.isArray(taskFn)) {\n            params = [...taskFn];\n            taskFn = params.pop();\n\n            newTasks[key] = params.concat(params.length > 0 ? newTask : taskFn);\n        } else if (hasNoDeps) {\n            // no dependencies, use the function as-is\n            newTasks[key] = taskFn;\n        } else {\n            params = parseParams(taskFn);\n            if ((taskFn.length === 0 && !fnIsAsync) && params.length === 0) {\n                throw new Error(\"autoInject task functions require explicit parameters.\");\n            }\n\n            // remove callback param\n            if (!fnIsAsync) params.pop();\n\n            newTasks[key] = params.concat(newTask);\n        }\n\n        function newTask(results, taskCb) {\n            var newArgs = params.map(name => results[name]);\n            newArgs.push(taskCb);\n            wrapAsync(taskFn)(...newArgs);\n        }\n    });\n\n    return auto(newTasks, callback);\n}\n\n// Simple doubly linked list (https://en.wikipedia.org/wiki/Doubly_linked_list) implementation\n// used for queues. This implementation assumes that the node provided by the user can be modified\n// to adjust the next and last properties. We implement only the minimal functionality\n// for queue support.\nclass DLL {\n    constructor() {\n        this.head = this.tail = null;\n        this.length = 0;\n    }\n\n    removeLink(node) {\n        if (node.prev) node.prev.next = node.next;\n        else this.head = node.next;\n        if (node.next) node.next.prev = node.prev;\n        else this.tail = node.prev;\n\n        node.prev = node.next = null;\n        this.length -= 1;\n        return node;\n    }\n\n    empty () {\n        while(this.head) this.shift();\n        return this;\n    }\n\n    insertAfter(node, newNode) {\n        newNode.prev = node;\n        newNode.next = node.next;\n        if (node.next) node.next.prev = newNode;\n        else this.tail = newNode;\n        node.next = newNode;\n        this.length += 1;\n    }\n\n    insertBefore(node, newNode) {\n        newNode.prev = node.prev;\n        newNode.next = node;\n        if (node.prev) node.prev.next = newNode;\n        else this.head = newNode;\n        node.prev = newNode;\n        this.length += 1;\n    }\n\n    unshift(node) {\n        if (this.head) this.insertBefore(this.head, node);\n        else setInitial(this, node);\n    }\n\n    push(node) {\n        if (this.tail) this.insertAfter(this.tail, node);\n        else setInitial(this, node);\n    }\n\n    shift() {\n        return this.head && this.removeLink(this.head);\n    }\n\n    pop() {\n        return this.tail && this.removeLink(this.tail);\n    }\n\n    toArray() {\n        return [...this]\n    }\n\n    *[Symbol.iterator] () {\n        var cur = this.head;\n        while (cur) {\n            yield cur.data;\n            cur = cur.next;\n        }\n    }\n\n    remove (testFn) {\n        var curr = this.head;\n        while(curr) {\n            var {next} = curr;\n            if (testFn(curr)) {\n                this.removeLink(curr);\n            }\n            curr = next;\n        }\n        return this;\n    }\n}\n\nfunction setInitial(dll, node) {\n    dll.length = 1;\n    dll.head = dll.tail = node;\n}\n\nfunction queue$1(worker, concurrency, payload) {\n    if (concurrency == null) {\n        concurrency = 1;\n    }\n    else if(concurrency === 0) {\n        throw new RangeError('Concurrency must not be zero');\n    }\n\n    var _worker = wrapAsync(worker);\n    var numRunning = 0;\n    var workersList = [];\n    const events = {\n        error: [],\n        drain: [],\n        saturated: [],\n        unsaturated: [],\n        empty: []\n    };\n\n    function on (event, handler) {\n        events[event].push(handler);\n    }\n\n    function once (event, handler) {\n        const handleAndRemove = (...args) => {\n            off(event, handleAndRemove);\n            handler(...args);\n        };\n        events[event].push(handleAndRemove);\n    }\n\n    function off (event, handler) {\n        if (!event) return Object.keys(events).forEach(ev => events[ev] = [])\n        if (!handler) return events[event] = []\n        events[event] = events[event].filter(ev => ev !== handler);\n    }\n\n    function trigger (event, ...args) {\n        events[event].forEach(handler => handler(...args));\n    }\n\n    var processingScheduled = false;\n    function _insert(data, insertAtFront, rejectOnError, callback) {\n        if (callback != null && typeof callback !== 'function') {\n            throw new Error('task callback must be a function');\n        }\n        q.started = true;\n\n        var res, rej;\n        function promiseCallback (err, ...args) {\n            // we don't care about the error, let the global error handler\n            // deal with it\n            if (err) return rejectOnError ? rej(err) : res()\n            if (args.length <= 1) return res(args[0])\n            res(args);\n        }\n\n        var item = q._createTaskItem(\n            data,\n            rejectOnError ? promiseCallback :\n                (callback || promiseCallback)\n        );\n\n        if (insertAtFront) {\n            q._tasks.unshift(item);\n        } else {\n            q._tasks.push(item);\n        }\n\n        if (!processingScheduled) {\n            processingScheduled = true;\n            setImmediate$1(() => {\n                processingScheduled = false;\n                q.process();\n            });\n        }\n\n        if (rejectOnError || !callback) {\n            return new Promise((resolve, reject) => {\n                res = resolve;\n                rej = reject;\n            })\n        }\n    }\n\n    function _createCB(tasks) {\n        return function (err, ...args) {\n            numRunning -= 1;\n\n            for (var i = 0, l = tasks.length; i < l; i++) {\n                var task = tasks[i];\n\n                var index = workersList.indexOf(task);\n                if (index === 0) {\n                    workersList.shift();\n                } else if (index > 0) {\n                    workersList.splice(index, 1);\n                }\n\n                task.callback(err, ...args);\n\n                if (err != null) {\n                    trigger('error', err, task.data);\n                }\n            }\n\n            if (numRunning <= (q.concurrency - q.buffer) ) {\n                trigger('unsaturated');\n            }\n\n            if (q.idle()) {\n                trigger('drain');\n            }\n            q.process();\n        };\n    }\n\n    function _maybeDrain(data) {\n        if (data.length === 0 && q.idle()) {\n            // call drain immediately if there are no tasks\n            setImmediate$1(() => trigger('drain'));\n            return true\n        }\n        return false\n    }\n\n    const eventMethod = (name) => (handler) => {\n        if (!handler) {\n            return new Promise((resolve, reject) => {\n                once(name, (err, data) => {\n                    if (err) return reject(err)\n                    resolve(data);\n                });\n            })\n        }\n        off(name);\n        on(name, handler);\n\n    };\n\n    var isProcessing = false;\n    var q = {\n        _tasks: new DLL(),\n        _createTaskItem (data, callback) {\n            return {\n                data,\n                callback\n            };\n        },\n        *[Symbol.iterator] () {\n            yield* q._tasks[Symbol.iterator]();\n        },\n        concurrency,\n        payload,\n        buffer: concurrency / 4,\n        started: false,\n        paused: false,\n        push (data, callback) {\n            if (Array.isArray(data)) {\n                if (_maybeDrain(data)) return\n                return data.map(datum => _insert(datum, false, false, callback))\n            }\n            return _insert(data, false, false, callback);\n        },\n        pushAsync (data, callback) {\n            if (Array.isArray(data)) {\n                if (_maybeDrain(data)) return\n                return data.map(datum => _insert(datum, false, true, callback))\n            }\n            return _insert(data, false, true, callback);\n        },\n        kill () {\n            off();\n            q._tasks.empty();\n        },\n        unshift (data, callback) {\n            if (Array.isArray(data)) {\n                if (_maybeDrain(data)) return\n                return data.map(datum => _insert(datum, true, false, callback))\n            }\n            return _insert(data, true, false, callback);\n        },\n        unshiftAsync (data, callback) {\n            if (Array.isArray(data)) {\n                if (_maybeDrain(data)) return\n                return data.map(datum => _insert(datum, true, true, callback))\n            }\n            return _insert(data, true, true, callback);\n        },\n        remove (testFn) {\n            q._tasks.remove(testFn);\n        },\n        process () {\n            // Avoid trying to start too many processing operations. This can occur\n            // when callbacks resolve synchronously (#1267).\n            if (isProcessing) {\n                return;\n            }\n            isProcessing = true;\n            while(!q.paused && numRunning < q.concurrency && q._tasks.length){\n                var tasks = [], data = [];\n                var l = q._tasks.length;\n                if (q.payload) l = Math.min(l, q.payload);\n                for (var i = 0; i < l; i++) {\n                    var node = q._tasks.shift();\n                    tasks.push(node);\n                    workersList.push(node);\n                    data.push(node.data);\n                }\n\n                numRunning += 1;\n\n                if (q._tasks.length === 0) {\n                    trigger('empty');\n                }\n\n                if (numRunning === q.concurrency) {\n                    trigger('saturated');\n                }\n\n                var cb = onlyOnce(_createCB(tasks));\n                _worker(data, cb);\n            }\n            isProcessing = false;\n        },\n        length () {\n            return q._tasks.length;\n        },\n        running () {\n            return numRunning;\n        },\n        workersList () {\n            return workersList;\n        },\n        idle() {\n            return q._tasks.length + numRunning === 0;\n        },\n        pause () {\n            q.paused = true;\n        },\n        resume () {\n            if (q.paused === false) { return; }\n            q.paused = false;\n            setImmediate$1(q.process);\n        }\n    };\n    // define these as fixed properties, so people get useful errors when updating\n    Object.defineProperties(q, {\n        saturated: {\n            writable: false,\n            value: eventMethod('saturated')\n        },\n        unsaturated: {\n            writable: false,\n            value: eventMethod('unsaturated')\n        },\n        empty: {\n            writable: false,\n            value: eventMethod('empty')\n        },\n        drain: {\n            writable: false,\n            value: eventMethod('drain')\n        },\n        error: {\n            writable: false,\n            value: eventMethod('error')\n        },\n    });\n    return q;\n}\n\n/**\n * Creates a `cargo` object with the specified payload. Tasks added to the\n * cargo will be processed altogether (up to the `payload` limit). If the\n * `worker` is in progress, the task is queued until it becomes available. Once\n * the `worker` has completed some tasks, each callback of those tasks is\n * called. Check out [these](https://camo.githubusercontent.com/6bbd36f4cf5b35a0f11a96dcd2e97711ffc2fb37/68747470733a2f2f662e636c6f75642e6769746875622e636f6d2f6173736574732f313637363837312f36383130382f62626330636662302d356632392d313165322d393734662d3333393763363464633835382e676966) [animations](https://camo.githubusercontent.com/f4810e00e1c5f5f8addbe3e9f49064fd5d102699/68747470733a2f2f662e636c6f75642e6769746875622e636f6d2f6173736574732f313637363837312f36383130312f38346339323036362d356632392d313165322d383134662d3964336430323431336266642e676966)\n * for how `cargo` and `queue` work.\n *\n * While [`queue`]{@link module:ControlFlow.queue} passes only one task to one of a group of workers\n * at a time, cargo passes an array of tasks to a single worker, repeating\n * when the worker is finished.\n *\n * @name cargo\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.queue]{@link module:ControlFlow.queue}\n * @category Control Flow\n * @param {AsyncFunction} worker - An asynchronous function for processing an array\n * of queued tasks. Invoked with `(tasks, callback)`.\n * @param {number} [payload=Infinity] - An optional `integer` for determining\n * how many tasks should be processed per round; if omitted, the default is\n * unlimited.\n * @returns {module:ControlFlow.QueueObject} A cargo object to manage the tasks. Callbacks can\n * attached as certain properties to listen for specific events during the\n * lifecycle of the cargo and inner queue.\n * @example\n *\n * // create a cargo object with payload 2\n * var cargo = async.cargo(function(tasks, callback) {\n *     for (var i=0; i<tasks.length; i++) {\n *         console.log('hello ' + tasks[i].name);\n *     }\n *     callback();\n * }, 2);\n *\n * // add some items\n * cargo.push({name: 'foo'}, function(err) {\n *     console.log('finished processing foo');\n * });\n * cargo.push({name: 'bar'}, function(err) {\n *     console.log('finished processing bar');\n * });\n * await cargo.push({name: 'baz'});\n * console.log('finished processing baz');\n */\nfunction cargo$1(worker, payload) {\n    return queue$1(worker, 1, payload);\n}\n\n/**\n * Creates a `cargoQueue` object with the specified payload. Tasks added to the\n * cargoQueue will be processed together (up to the `payload` limit) in `concurrency` parallel workers.\n * If the all `workers` are in progress, the task is queued until one becomes available. Once\n * a `worker` has completed some tasks, each callback of those tasks is\n * called. Check out [these](https://camo.githubusercontent.com/6bbd36f4cf5b35a0f11a96dcd2e97711ffc2fb37/68747470733a2f2f662e636c6f75642e6769746875622e636f6d2f6173736574732f313637363837312f36383130382f62626330636662302d356632392d313165322d393734662d3333393763363464633835382e676966) [animations](https://camo.githubusercontent.com/f4810e00e1c5f5f8addbe3e9f49064fd5d102699/68747470733a2f2f662e636c6f75642e6769746875622e636f6d2f6173736574732f313637363837312f36383130312f38346339323036362d356632392d313165322d383134662d3964336430323431336266642e676966)\n * for how `cargo` and `queue` work.\n *\n * While [`queue`]{@link module:ControlFlow.queue} passes only one task to one of a group of workers\n * at a time, and [`cargo`]{@link module:ControlFlow.cargo} passes an array of tasks to a single worker,\n * the cargoQueue passes an array of tasks to multiple parallel workers.\n *\n * @name cargoQueue\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.queue]{@link module:ControlFlow.queue}\n * @see [async.cargo]{@link module:ControlFLow.cargo}\n * @category Control Flow\n * @param {AsyncFunction} worker - An asynchronous function for processing an array\n * of queued tasks. Invoked with `(tasks, callback)`.\n * @param {number} [concurrency=1] - An `integer` for determining how many\n * `worker` functions should be run in parallel.  If omitted, the concurrency\n * defaults to `1`.  If the concurrency is `0`, an error is thrown.\n * @param {number} [payload=Infinity] - An optional `integer` for determining\n * how many tasks should be processed per round; if omitted, the default is\n * unlimited.\n * @returns {module:ControlFlow.QueueObject} A cargoQueue object to manage the tasks. Callbacks can\n * attached as certain properties to listen for specific events during the\n * lifecycle of the cargoQueue and inner queue.\n * @example\n *\n * // create a cargoQueue object with payload 2 and concurrency 2\n * var cargoQueue = async.cargoQueue(function(tasks, callback) {\n *     for (var i=0; i<tasks.length; i++) {\n *         console.log('hello ' + tasks[i].name);\n *     }\n *     callback();\n * }, 2, 2);\n *\n * // add some items\n * cargoQueue.push({name: 'foo'}, function(err) {\n *     console.log('finished processing foo');\n * });\n * cargoQueue.push({name: 'bar'}, function(err) {\n *     console.log('finished processing bar');\n * });\n * cargoQueue.push({name: 'baz'}, function(err) {\n *     console.log('finished processing baz');\n * });\n * cargoQueue.push({name: 'boo'}, function(err) {\n *     console.log('finished processing boo');\n * });\n */\nfunction cargo(worker, concurrency, payload) {\n    return queue$1(worker, concurrency, payload);\n}\n\n/**\n * Reduces `coll` into a single value using an async `iteratee` to return each\n * successive step. `memo` is the initial state of the reduction. This function\n * only operates in series.\n *\n * For performance reasons, it may make sense to split a call to this function\n * into a parallel map, and then use the normal `Array.prototype.reduce` on the\n * results. This function is for situations where each step in the reduction\n * needs to be async; if you can get the data before reducing it, then it's\n * probably a good idea to do so.\n *\n * @name reduce\n * @static\n * @memberOf module:Collections\n * @method\n * @alias inject\n * @alias foldl\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {*} memo - The initial state of the reduction.\n * @param {AsyncFunction} iteratee - A function applied to each item in the\n * array to produce the next step in the reduction.\n * The `iteratee` should complete with the next state of the reduction.\n * If the iteratee completes with an error, the reduction is stopped and the\n * main `callback` is immediately called with the error.\n * Invoked with (memo, item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Result is the reduced value. Invoked with\n * (err, result).\n * @returns {Promise} a promise, if no callback is passed\n * @example\n *\n * // file1.txt is a file that is 1000 bytes in size\n * // file2.txt is a file that is 2000 bytes in size\n * // file3.txt is a file that is 3000 bytes in size\n * // file4.txt does not exist\n *\n * const fileList = ['file1.txt','file2.txt','file3.txt'];\n * const withMissingFileList = ['file1.txt','file2.txt','file3.txt', 'file4.txt'];\n *\n * // asynchronous function that computes the file size in bytes\n * // file size is added to the memoized value, then returned\n * function getFileSizeInBytes(memo, file, callback) {\n *     fs.stat(file, function(err, stat) {\n *         if (err) {\n *             return callback(err);\n *         }\n *         callback(null, memo + stat.size);\n *     });\n * }\n *\n * // Using callbacks\n * async.reduce(fileList, 0, getFileSizeInBytes, function(err, result) {\n *     if (err) {\n *         console.log(err);\n *     } else {\n *         console.log(result);\n *         // 6000\n *         // which is the sum of the file sizes of the three files\n *     }\n * });\n *\n * // Error Handling\n * async.reduce(withMissingFileList, 0, getFileSizeInBytes, function(err, result) {\n *     if (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *     } else {\n *         console.log(result);\n *     }\n * });\n *\n * // Using Promises\n * async.reduce(fileList, 0, getFileSizeInBytes)\n * .then( result => {\n *     console.log(result);\n *     // 6000\n *     // which is the sum of the file sizes of the three files\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Error Handling\n * async.reduce(withMissingFileList, 0, getFileSizeInBytes)\n * .then( result => {\n *     console.log(result);\n * }).catch( err => {\n *     console.log(err);\n *     // [ Error: ENOENT: no such file or directory ]\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.reduce(fileList, 0, getFileSizeInBytes);\n *         console.log(result);\n *         // 6000\n *         // which is the sum of the file sizes of the three files\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * // Error Handling\n * async () => {\n *     try {\n *         let result = await async.reduce(withMissingFileList, 0, getFileSizeInBytes);\n *         console.log(result);\n *     }\n *     catch (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *     }\n * }\n *\n */\nfunction reduce(coll, memo, iteratee, callback) {\n    callback = once(callback);\n    var _iteratee = wrapAsync(iteratee);\n    return eachOfSeries$1(coll, (x, i, iterCb) => {\n        _iteratee(memo, x, (err, v) => {\n            memo = v;\n            iterCb(err);\n        });\n    }, err => callback(err, memo));\n}\nvar reduce$1 = awaitify(reduce, 4);\n\n/**\n * Version of the compose function that is more natural to read. Each function\n * consumes the return value of the previous function. It is the equivalent of\n * [compose]{@link module:ControlFlow.compose} with the arguments reversed.\n *\n * Each function is executed with the `this` binding of the composed function.\n *\n * @name seq\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.compose]{@link module:ControlFlow.compose}\n * @category Control Flow\n * @param {...AsyncFunction} functions - the asynchronous functions to compose\n * @returns {Function} a function that composes the `functions` in order\n * @example\n *\n * // Requires lodash (or underscore), express3 and dresende's orm2.\n * // Part of an app, that fetches cats of the logged user.\n * // This example uses `seq` function to avoid overnesting and error\n * // handling clutter.\n * app.get('/cats', function(request, response) {\n *     var User = request.models.User;\n *     async.seq(\n *         User.get.bind(User),  // 'User.get' has signature (id, callback(err, data))\n *         function(user, fn) {\n *             user.getCats(fn);      // 'getCats' has signature (callback(err, data))\n *         }\n *     )(req.session.user_id, function (err, cats) {\n *         if (err) {\n *             console.error(err);\n *             response.json({ status: 'error', message: err.message });\n *         } else {\n *             response.json({ status: 'ok', message: 'Cats found', data: cats });\n *         }\n *     });\n * });\n */\nfunction seq(...functions) {\n    var _functions = functions.map(wrapAsync);\n    return function (...args) {\n        var that = this;\n\n        var cb = args[args.length - 1];\n        if (typeof cb == 'function') {\n            args.pop();\n        } else {\n            cb = promiseCallback();\n        }\n\n        reduce$1(_functions, args, (newargs, fn, iterCb) => {\n            fn.apply(that, newargs.concat((err, ...nextargs) => {\n                iterCb(err, nextargs);\n            }));\n        },\n        (err, results) => cb(err, ...results));\n\n        return cb[PROMISE_SYMBOL]\n    };\n}\n\n/**\n * Creates a function which is a composition of the passed asynchronous\n * functions. Each function consumes the return value of the function that\n * follows. Composing functions `f()`, `g()`, and `h()` would produce the result\n * of `f(g(h()))`, only this version uses callbacks to obtain the return values.\n *\n * If the last argument to the composed function is not a function, a promise\n * is returned when you call it.\n *\n * Each function is executed with the `this` binding of the composed function.\n *\n * @name compose\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {...AsyncFunction} functions - the asynchronous functions to compose\n * @returns {Function} an asynchronous function that is the composed\n * asynchronous `functions`\n * @example\n *\n * function add1(n, callback) {\n *     setTimeout(function () {\n *         callback(null, n + 1);\n *     }, 10);\n * }\n *\n * function mul3(n, callback) {\n *     setTimeout(function () {\n *         callback(null, n * 3);\n *     }, 10);\n * }\n *\n * var add1mul3 = async.compose(mul3, add1);\n * add1mul3(4, function (err, result) {\n *     // result now equals 15\n * });\n */\nfunction compose(...args) {\n    return seq(...args.reverse());\n}\n\n/**\n * The same as [`map`]{@link module:Collections.map} but runs a maximum of `limit` async operations at a time.\n *\n * @name mapLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.map]{@link module:Collections.map}\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The iteratee should complete with the transformed item.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. Results is an array of the\n * transformed items from the `coll`. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction mapLimit (coll, limit, iteratee, callback) {\n    return _asyncMap(eachOfLimit$2(limit), coll, iteratee, callback)\n}\nvar mapLimit$1 = awaitify(mapLimit, 4);\n\n/**\n * The same as [`concat`]{@link module:Collections.concat} but runs a maximum of `limit` async operations at a time.\n *\n * @name concatLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.concat]{@link module:Collections.concat}\n * @category Collection\n * @alias flatMapLimit\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - A function to apply to each item in `coll`,\n * which should use an array as its result. Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished, or an error occurs. Results is an array\n * containing the concatenated results of the `iteratee` function. Invoked with\n * (err, results).\n * @returns A Promise, if no callback is passed\n */\nfunction concatLimit(coll, limit, iteratee, callback) {\n    var _iteratee = wrapAsync(iteratee);\n    return mapLimit$1(coll, limit, (val, iterCb) => {\n        _iteratee(val, (err, ...args) => {\n            if (err) return iterCb(err);\n            return iterCb(err, args);\n        });\n    }, (err, mapResults) => {\n        var result = [];\n        for (var i = 0; i < mapResults.length; i++) {\n            if (mapResults[i]) {\n                result = result.concat(...mapResults[i]);\n            }\n        }\n\n        return callback(err, result);\n    });\n}\nvar concatLimit$1 = awaitify(concatLimit, 4);\n\n/**\n * Applies `iteratee` to each item in `coll`, concatenating the results. Returns\n * the concatenated list. The `iteratee`s are called in parallel, and the\n * results are concatenated as they return. The results array will be returned in\n * the original order of `coll` passed to the `iteratee` function.\n *\n * @name concat\n * @static\n * @memberOf module:Collections\n * @method\n * @category Collection\n * @alias flatMap\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - A function to apply to each item in `coll`,\n * which should use an array as its result. Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished, or an error occurs. Results is an array\n * containing the concatenated results of the `iteratee` function. Invoked with\n * (err, results).\n * @returns A Promise, if no callback is passed\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n * // dir4 does not exist\n *\n * let directoryList = ['dir1','dir2','dir3'];\n * let withMissingDirectoryList = ['dir1','dir2','dir3', 'dir4'];\n *\n * // Using callbacks\n * async.concat(directoryList, fs.readdir, function(err, results) {\n *    if (err) {\n *        console.log(err);\n *    } else {\n *        console.log(results);\n *        // [ 'file1.txt', 'file2.txt', 'file3.txt', 'file4.txt', file5.txt ]\n *    }\n * });\n *\n * // Error Handling\n * async.concat(withMissingDirectoryList, fs.readdir, function(err, results) {\n *    if (err) {\n *        console.log(err);\n *        // [ Error: ENOENT: no such file or directory ]\n *        // since dir4 does not exist\n *    } else {\n *        console.log(results);\n *    }\n * });\n *\n * // Using Promises\n * async.concat(directoryList, fs.readdir)\n * .then(results => {\n *     console.log(results);\n *     // [ 'file1.txt', 'file2.txt', 'file3.txt', 'file4.txt', file5.txt ]\n * }).catch(err => {\n *      console.log(err);\n * });\n *\n * // Error Handling\n * async.concat(withMissingDirectoryList, fs.readdir)\n * .then(results => {\n *     console.log(results);\n * }).catch(err => {\n *     console.log(err);\n *     // [ Error: ENOENT: no such file or directory ]\n *     // since dir4 does not exist\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let results = await async.concat(directoryList, fs.readdir);\n *         console.log(results);\n *         // [ 'file1.txt', 'file2.txt', 'file3.txt', 'file4.txt', file5.txt ]\n *     } catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * // Error Handling\n * async () => {\n *     try {\n *         let results = await async.concat(withMissingDirectoryList, fs.readdir);\n *         console.log(results);\n *     } catch (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *         // since dir4 does not exist\n *     }\n * }\n *\n */\nfunction concat(coll, iteratee, callback) {\n    return concatLimit$1(coll, Infinity, iteratee, callback)\n}\nvar concat$1 = awaitify(concat, 3);\n\n/**\n * The same as [`concat`]{@link module:Collections.concat} but runs only a single async operation at a time.\n *\n * @name concatSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.concat]{@link module:Collections.concat}\n * @category Collection\n * @alias flatMapSeries\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - A function to apply to each item in `coll`.\n * The iteratee should complete with an array an array of results.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished, or an error occurs. Results is an array\n * containing the concatenated results of the `iteratee` function. Invoked with\n * (err, results).\n * @returns A Promise, if no callback is passed\n */\nfunction concatSeries(coll, iteratee, callback) {\n    return concatLimit$1(coll, 1, iteratee, callback)\n}\nvar concatSeries$1 = awaitify(concatSeries, 3);\n\n/**\n * Returns a function that when called, calls-back with the values provided.\n * Useful as the first function in a [`waterfall`]{@link module:ControlFlow.waterfall}, or for plugging values in to\n * [`auto`]{@link module:ControlFlow.auto}.\n *\n * @name constant\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {...*} arguments... - Any number of arguments to automatically invoke\n * callback with.\n * @returns {AsyncFunction} Returns a function that when invoked, automatically\n * invokes the callback with the previous given arguments.\n * @example\n *\n * async.waterfall([\n *     async.constant(42),\n *     function (value, next) {\n *         // value === 42\n *     },\n *     //...\n * ], callback);\n *\n * async.waterfall([\n *     async.constant(filename, \"utf8\"),\n *     fs.readFile,\n *     function (fileData, next) {\n *         //...\n *     }\n *     //...\n * ], callback);\n *\n * async.auto({\n *     hostname: async.constant(\"https://server.net/\"),\n *     port: findFreePort,\n *     launchServer: [\"hostname\", \"port\", function (options, cb) {\n *         startServer(options, cb);\n *     }],\n *     //...\n * }, callback);\n */\nfunction constant$1(...args) {\n    return function (...ignoredArgs/*, callback*/) {\n        var callback = ignoredArgs.pop();\n        return callback(null, ...args);\n    };\n}\n\nfunction _createTester(check, getResult) {\n    return (eachfn, arr, _iteratee, cb) => {\n        var testPassed = false;\n        var testResult;\n        const iteratee = wrapAsync(_iteratee);\n        eachfn(arr, (value, _, callback) => {\n            iteratee(value, (err, result) => {\n                if (err || err === false) return callback(err);\n\n                if (check(result) && !testResult) {\n                    testPassed = true;\n                    testResult = getResult(true, value);\n                    return callback(null, breakLoop);\n                }\n                callback();\n            });\n        }, err => {\n            if (err) return cb(err);\n            cb(null, testPassed ? testResult : getResult(false));\n        });\n    };\n}\n\n/**\n * Returns the first value in `coll` that passes an async truth test. The\n * `iteratee` is applied in parallel, meaning the first iteratee to return\n * `true` will fire the detect `callback` with that result. That means the\n * result might not be the first item in the original `coll` (in terms of order)\n * that passes the test.\n\n * If order within the original `coll` is important, then look at\n * [`detectSeries`]{@link module:Collections.detectSeries}.\n *\n * @name detect\n * @static\n * @memberOf module:Collections\n * @method\n * @alias find\n * @category Collections\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - A truth test to apply to each item in `coll`.\n * The iteratee must complete with a boolean value as its result.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called as soon as any\n * iteratee returns `true`, or after all the `iteratee` functions have finished.\n * Result will be the first item in the array that passes the truth test\n * (iteratee) or the value `undefined` if none passed. Invoked with\n * (err, result).\n * @returns {Promise} a promise, if a callback is omitted\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n *\n * // asynchronous function that checks if a file exists\n * function fileExists(file, callback) {\n *    fs.access(file, fs.constants.F_OK, (err) => {\n *        callback(null, !err);\n *    });\n * }\n *\n * async.detect(['file3.txt','file2.txt','dir1/file1.txt'], fileExists,\n *    function(err, result) {\n *        console.log(result);\n *        // dir1/file1.txt\n *        // result now equals the first file in the list that exists\n *    }\n *);\n *\n * // Using Promises\n * async.detect(['file3.txt','file2.txt','dir1/file1.txt'], fileExists)\n * .then(result => {\n *     console.log(result);\n *     // dir1/file1.txt\n *     // result now equals the first file in the list that exists\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.detect(['file3.txt','file2.txt','dir1/file1.txt'], fileExists);\n *         console.log(result);\n *         // dir1/file1.txt\n *         // result now equals the file in the list that exists\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction detect(coll, iteratee, callback) {\n    return _createTester(bool => bool, (res, item) => item)(eachOf$1, coll, iteratee, callback)\n}\nvar detect$1 = awaitify(detect, 3);\n\n/**\n * The same as [`detect`]{@link module:Collections.detect} but runs a maximum of `limit` async operations at a\n * time.\n *\n * @name detectLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.detect]{@link module:Collections.detect}\n * @alias findLimit\n * @category Collections\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - A truth test to apply to each item in `coll`.\n * The iteratee must complete with a boolean value as its result.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called as soon as any\n * iteratee returns `true`, or after all the `iteratee` functions have finished.\n * Result will be the first item in the array that passes the truth test\n * (iteratee) or the value `undefined` if none passed. Invoked with\n * (err, result).\n * @returns {Promise} a promise, if a callback is omitted\n */\nfunction detectLimit(coll, limit, iteratee, callback) {\n    return _createTester(bool => bool, (res, item) => item)(eachOfLimit$2(limit), coll, iteratee, callback)\n}\nvar detectLimit$1 = awaitify(detectLimit, 4);\n\n/**\n * The same as [`detect`]{@link module:Collections.detect} but runs only a single async operation at a time.\n *\n * @name detectSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.detect]{@link module:Collections.detect}\n * @alias findSeries\n * @category Collections\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - A truth test to apply to each item in `coll`.\n * The iteratee must complete with a boolean value as its result.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called as soon as any\n * iteratee returns `true`, or after all the `iteratee` functions have finished.\n * Result will be the first item in the array that passes the truth test\n * (iteratee) or the value `undefined` if none passed. Invoked with\n * (err, result).\n * @returns {Promise} a promise, if a callback is omitted\n */\nfunction detectSeries(coll, iteratee, callback) {\n    return _createTester(bool => bool, (res, item) => item)(eachOfLimit$2(1), coll, iteratee, callback)\n}\n\nvar detectSeries$1 = awaitify(detectSeries, 3);\n\nfunction consoleFunc(name) {\n    return (fn, ...args) => wrapAsync(fn)(...args, (err, ...resultArgs) => {\n        /* istanbul ignore else */\n        if (typeof console === 'object') {\n            /* istanbul ignore else */\n            if (err) {\n                /* istanbul ignore else */\n                if (console.error) {\n                    console.error(err);\n                }\n            } else if (console[name]) { /* istanbul ignore else */\n                resultArgs.forEach(x => console[name](x));\n            }\n        }\n    })\n}\n\n/**\n * Logs the result of an [`async` function]{@link AsyncFunction} to the\n * `console` using `console.dir` to display the properties of the resulting object.\n * Only works in Node.js or in browsers that support `console.dir` and\n * `console.error` (such as FF and Chrome).\n * If multiple arguments are returned from the async function,\n * `console.dir` is called on each argument in order.\n *\n * @name dir\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {AsyncFunction} function - The function you want to eventually apply\n * all arguments to.\n * @param {...*} arguments... - Any number of arguments to apply to the function.\n * @example\n *\n * // in a module\n * var hello = function(name, callback) {\n *     setTimeout(function() {\n *         callback(null, {hello: name});\n *     }, 1000);\n * };\n *\n * // in the node repl\n * node> async.dir(hello, 'world');\n * {hello: 'world'}\n */\nvar dir = consoleFunc('dir');\n\n/**\n * The post-check version of [`whilst`]{@link module:ControlFlow.whilst}. To reflect the difference in\n * the order of operations, the arguments `test` and `iteratee` are switched.\n *\n * `doWhilst` is to `whilst` as `do while` is to `while` in plain JavaScript.\n *\n * @name doWhilst\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.whilst]{@link module:ControlFlow.whilst}\n * @category Control Flow\n * @param {AsyncFunction} iteratee - A function which is called each time `test`\n * passes. Invoked with (callback).\n * @param {AsyncFunction} test - asynchronous truth test to perform after each\n * execution of `iteratee`. Invoked with (...args, callback), where `...args` are the\n * non-error args from the previous callback of `iteratee`.\n * @param {Function} [callback] - A callback which is called after the test\n * function has failed and repeated execution of `iteratee` has stopped.\n * `callback` will be passed an error and any arguments passed to the final\n * `iteratee`'s callback. Invoked with (err, [results]);\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction doWhilst(iteratee, test, callback) {\n    callback = onlyOnce(callback);\n    var _fn = wrapAsync(iteratee);\n    var _test = wrapAsync(test);\n    var results;\n\n    function next(err, ...args) {\n        if (err) return callback(err);\n        if (err === false) return;\n        results = args;\n        _test(...args, check);\n    }\n\n    function check(err, truth) {\n        if (err) return callback(err);\n        if (err === false) return;\n        if (!truth) return callback(null, ...results);\n        _fn(next);\n    }\n\n    return check(null, true);\n}\n\nvar doWhilst$1 = awaitify(doWhilst, 3);\n\n/**\n * Like ['doWhilst']{@link module:ControlFlow.doWhilst}, except the `test` is inverted. Note the\n * argument ordering differs from `until`.\n *\n * @name doUntil\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.doWhilst]{@link module:ControlFlow.doWhilst}\n * @category Control Flow\n * @param {AsyncFunction} iteratee - An async function which is called each time\n * `test` fails. Invoked with (callback).\n * @param {AsyncFunction} test - asynchronous truth test to perform after each\n * execution of `iteratee`. Invoked with (...args, callback), where `...args` are the\n * non-error args from the previous callback of `iteratee`\n * @param {Function} [callback] - A callback which is called after the test\n * function has passed and repeated execution of `iteratee` has stopped. `callback`\n * will be passed an error and any arguments passed to the final `iteratee`'s\n * callback. Invoked with (err, [results]);\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction doUntil(iteratee, test, callback) {\n    const _test = wrapAsync(test);\n    return doWhilst$1(iteratee, (...args) => {\n        const cb = args.pop();\n        _test(...args, (err, truth) => cb (err, !truth));\n    }, callback);\n}\n\nfunction _withoutIndex(iteratee) {\n    return (value, index, callback) => iteratee(value, callback);\n}\n\n/**\n * Applies the function `iteratee` to each item in `coll`, in parallel.\n * The `iteratee` is called with an item from the list, and a callback for when\n * it has finished. If the `iteratee` passes an error to its `callback`, the\n * main `callback` (for the `each` function) is immediately called with the\n * error.\n *\n * Note, that since this function applies `iteratee` to each item in parallel,\n * there is no guarantee that the iteratee functions will complete in order.\n *\n * @name each\n * @static\n * @memberOf module:Collections\n * @method\n * @alias forEach\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to\n * each item in `coll`. Invoked with (item, callback).\n * The array index is not passed to the iteratee.\n * If you need the index, use `eachOf`.\n * @param {Function} [callback] - A callback which is called when all\n * `iteratee` functions have finished, or an error occurs. Invoked with (err).\n * @returns {Promise} a promise, if a callback is omitted\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n * // dir4 does not exist\n *\n * const fileList = [ 'dir1/file2.txt', 'dir2/file3.txt', 'dir/file5.txt'];\n * const withMissingFileList = ['dir1/file1.txt', 'dir4/file2.txt'];\n *\n * // asynchronous function that deletes a file\n * const deleteFile = function(file, callback) {\n *     fs.unlink(file, callback);\n * };\n *\n * // Using callbacks\n * async.each(fileList, deleteFile, function(err) {\n *     if( err ) {\n *         console.log(err);\n *     } else {\n *         console.log('All files have been deleted successfully');\n *     }\n * });\n *\n * // Error Handling\n * async.each(withMissingFileList, deleteFile, function(err){\n *     console.log(err);\n *     // [ Error: ENOENT: no such file or directory ]\n *     // since dir4/file2.txt does not exist\n *     // dir1/file1.txt could have been deleted\n * });\n *\n * // Using Promises\n * async.each(fileList, deleteFile)\n * .then( () => {\n *     console.log('All files have been deleted successfully');\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Error Handling\n * async.each(fileList, deleteFile)\n * .then( () => {\n *     console.log('All files have been deleted successfully');\n * }).catch( err => {\n *     console.log(err);\n *     // [ Error: ENOENT: no such file or directory ]\n *     // since dir4/file2.txt does not exist\n *     // dir1/file1.txt could have been deleted\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         await async.each(files, deleteFile);\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * // Error Handling\n * async () => {\n *     try {\n *         await async.each(withMissingFileList, deleteFile);\n *     }\n *     catch (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *         // since dir4/file2.txt does not exist\n *         // dir1/file1.txt could have been deleted\n *     }\n * }\n *\n */\nfunction eachLimit$2(coll, iteratee, callback) {\n    return eachOf$1(coll, _withoutIndex(wrapAsync(iteratee)), callback);\n}\n\nvar each = awaitify(eachLimit$2, 3);\n\n/**\n * The same as [`each`]{@link module:Collections.each} but runs a maximum of `limit` async operations at a time.\n *\n * @name eachLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.each]{@link module:Collections.each}\n * @alias forEachLimit\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The array index is not passed to the iteratee.\n * If you need the index, use `eachOfLimit`.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called when all\n * `iteratee` functions have finished, or an error occurs. Invoked with (err).\n * @returns {Promise} a promise, if a callback is omitted\n */\nfunction eachLimit(coll, limit, iteratee, callback) {\n    return eachOfLimit$2(limit)(coll, _withoutIndex(wrapAsync(iteratee)), callback);\n}\nvar eachLimit$1 = awaitify(eachLimit, 4);\n\n/**\n * The same as [`each`]{@link module:Collections.each} but runs only a single async operation at a time.\n *\n * Note, that unlike [`each`]{@link module:Collections.each}, this function applies iteratee to each item\n * in series and therefore the iteratee functions will complete in order.\n\n * @name eachSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.each]{@link module:Collections.each}\n * @alias forEachSeries\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to each\n * item in `coll`.\n * The array index is not passed to the iteratee.\n * If you need the index, use `eachOfSeries`.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called when all\n * `iteratee` functions have finished, or an error occurs. Invoked with (err).\n * @returns {Promise} a promise, if a callback is omitted\n */\nfunction eachSeries(coll, iteratee, callback) {\n    return eachLimit$1(coll, 1, iteratee, callback)\n}\nvar eachSeries$1 = awaitify(eachSeries, 3);\n\n/**\n * Wrap an async function and ensure it calls its callback on a later tick of\n * the event loop.  If the function already calls its callback on a next tick,\n * no extra deferral is added. This is useful for preventing stack overflows\n * (`RangeError: Maximum call stack size exceeded`) and generally keeping\n * [Zalgo](http://blog.izs.me/post/59142742143/designing-apis-for-asynchrony)\n * contained. ES2017 `async` functions are returned as-is -- they are immune\n * to Zalgo's corrupting influences, as they always resolve on a later tick.\n *\n * @name ensureAsync\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {AsyncFunction} fn - an async function, one that expects a node-style\n * callback as its last argument.\n * @returns {AsyncFunction} Returns a wrapped function with the exact same call\n * signature as the function passed in.\n * @example\n *\n * function sometimesAsync(arg, callback) {\n *     if (cache[arg]) {\n *         return callback(null, cache[arg]); // this would be synchronous!!\n *     } else {\n *         doSomeIO(arg, callback); // this IO would be asynchronous\n *     }\n * }\n *\n * // this has a risk of stack overflows if many results are cached in a row\n * async.mapSeries(args, sometimesAsync, done);\n *\n * // this will defer sometimesAsync's callback if necessary,\n * // preventing stack overflows\n * async.mapSeries(args, async.ensureAsync(sometimesAsync), done);\n */\nfunction ensureAsync(fn) {\n    if (isAsync(fn)) return fn;\n    return function (...args/*, callback*/) {\n        var callback = args.pop();\n        var sync = true;\n        args.push((...innerArgs) => {\n            if (sync) {\n                setImmediate$1(() => callback(...innerArgs));\n            } else {\n                callback(...innerArgs);\n            }\n        });\n        fn.apply(this, args);\n        sync = false;\n    };\n}\n\n/**\n * Returns `true` if every element in `coll` satisfies an async test. If any\n * iteratee call returns `false`, the main `callback` is immediately called.\n *\n * @name every\n * @static\n * @memberOf module:Collections\n * @method\n * @alias all\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async truth test to apply to each item\n * in the collection in parallel.\n * The iteratee must complete with a boolean result value.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Result will be either `true` or `false`\n * depending on the values of the async tests. Invoked with (err, result).\n * @returns {Promise} a promise, if no callback provided\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n * // dir4 does not exist\n *\n * const fileList = ['dir1/file1.txt','dir2/file3.txt','dir3/file5.txt'];\n * const withMissingFileList = ['file1.txt','file2.txt','file4.txt'];\n *\n * // asynchronous function that checks if a file exists\n * function fileExists(file, callback) {\n *    fs.access(file, fs.constants.F_OK, (err) => {\n *        callback(null, !err);\n *    });\n * }\n *\n * // Using callbacks\n * async.every(fileList, fileExists, function(err, result) {\n *     console.log(result);\n *     // true\n *     // result is true since every file exists\n * });\n *\n * async.every(withMissingFileList, fileExists, function(err, result) {\n *     console.log(result);\n *     // false\n *     // result is false since NOT every file exists\n * });\n *\n * // Using Promises\n * async.every(fileList, fileExists)\n * .then( result => {\n *     console.log(result);\n *     // true\n *     // result is true since every file exists\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * async.every(withMissingFileList, fileExists)\n * .then( result => {\n *     console.log(result);\n *     // false\n *     // result is false since NOT every file exists\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.every(fileList, fileExists);\n *         console.log(result);\n *         // true\n *         // result is true since every file exists\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * async () => {\n *     try {\n *         let result = await async.every(withMissingFileList, fileExists);\n *         console.log(result);\n *         // false\n *         // result is false since NOT every file exists\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction every(coll, iteratee, callback) {\n    return _createTester(bool => !bool, res => !res)(eachOf$1, coll, iteratee, callback)\n}\nvar every$1 = awaitify(every, 3);\n\n/**\n * The same as [`every`]{@link module:Collections.every} but runs a maximum of `limit` async operations at a time.\n *\n * @name everyLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.every]{@link module:Collections.every}\n * @alias allLimit\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - An async truth test to apply to each item\n * in the collection in parallel.\n * The iteratee must complete with a boolean result value.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Result will be either `true` or `false`\n * depending on the values of the async tests. Invoked with (err, result).\n * @returns {Promise} a promise, if no callback provided\n */\nfunction everyLimit(coll, limit, iteratee, callback) {\n    return _createTester(bool => !bool, res => !res)(eachOfLimit$2(limit), coll, iteratee, callback)\n}\nvar everyLimit$1 = awaitify(everyLimit, 4);\n\n/**\n * The same as [`every`]{@link module:Collections.every} but runs only a single async operation at a time.\n *\n * @name everySeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.every]{@link module:Collections.every}\n * @alias allSeries\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async truth test to apply to each item\n * in the collection in series.\n * The iteratee must complete with a boolean result value.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Result will be either `true` or `false`\n * depending on the values of the async tests. Invoked with (err, result).\n * @returns {Promise} a promise, if no callback provided\n */\nfunction everySeries(coll, iteratee, callback) {\n    return _createTester(bool => !bool, res => !res)(eachOfSeries$1, coll, iteratee, callback)\n}\nvar everySeries$1 = awaitify(everySeries, 3);\n\nfunction filterArray(eachfn, arr, iteratee, callback) {\n    var truthValues = new Array(arr.length);\n    eachfn(arr, (x, index, iterCb) => {\n        iteratee(x, (err, v) => {\n            truthValues[index] = !!v;\n            iterCb(err);\n        });\n    }, err => {\n        if (err) return callback(err);\n        var results = [];\n        for (var i = 0; i < arr.length; i++) {\n            if (truthValues[i]) results.push(arr[i]);\n        }\n        callback(null, results);\n    });\n}\n\nfunction filterGeneric(eachfn, coll, iteratee, callback) {\n    var results = [];\n    eachfn(coll, (x, index, iterCb) => {\n        iteratee(x, (err, v) => {\n            if (err) return iterCb(err);\n            if (v) {\n                results.push({index, value: x});\n            }\n            iterCb(err);\n        });\n    }, err => {\n        if (err) return callback(err);\n        callback(null, results\n            .sort((a, b) => a.index - b.index)\n            .map(v => v.value));\n    });\n}\n\nfunction _filter(eachfn, coll, iteratee, callback) {\n    var filter = isArrayLike(coll) ? filterArray : filterGeneric;\n    return filter(eachfn, coll, wrapAsync(iteratee), callback);\n}\n\n/**\n * Returns a new array of all the values in `coll` which pass an async truth\n * test. This operation is performed in parallel, but the results array will be\n * in the same order as the original.\n *\n * @name filter\n * @static\n * @memberOf module:Collections\n * @method\n * @alias select\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {Function} iteratee - A truth test to apply to each item in `coll`.\n * The `iteratee` is passed a `callback(err, truthValue)`, which must be called\n * with a boolean argument once it has completed. Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback provided\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n *\n * const files = ['dir1/file1.txt','dir2/file3.txt','dir3/file6.txt'];\n *\n * // asynchronous function that checks if a file exists\n * function fileExists(file, callback) {\n *    fs.access(file, fs.constants.F_OK, (err) => {\n *        callback(null, !err);\n *    });\n * }\n *\n * // Using callbacks\n * async.filter(files, fileExists, function(err, results) {\n *    if(err) {\n *        console.log(err);\n *    } else {\n *        console.log(results);\n *        // [ 'dir1/file1.txt', 'dir2/file3.txt' ]\n *        // results is now an array of the existing files\n *    }\n * });\n *\n * // Using Promises\n * async.filter(files, fileExists)\n * .then(results => {\n *     console.log(results);\n *     // [ 'dir1/file1.txt', 'dir2/file3.txt' ]\n *     // results is now an array of the existing files\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let results = await async.filter(files, fileExists);\n *         console.log(results);\n *         // [ 'dir1/file1.txt', 'dir2/file3.txt' ]\n *         // results is now an array of the existing files\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction filter (coll, iteratee, callback) {\n    return _filter(eachOf$1, coll, iteratee, callback)\n}\nvar filter$1 = awaitify(filter, 3);\n\n/**\n * The same as [`filter`]{@link module:Collections.filter} but runs a maximum of `limit` async operations at a\n * time.\n *\n * @name filterLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.filter]{@link module:Collections.filter}\n * @alias selectLimit\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {Function} iteratee - A truth test to apply to each item in `coll`.\n * The `iteratee` is passed a `callback(err, truthValue)`, which must be called\n * with a boolean argument once it has completed. Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback provided\n */\nfunction filterLimit (coll, limit, iteratee, callback) {\n    return _filter(eachOfLimit$2(limit), coll, iteratee, callback)\n}\nvar filterLimit$1 = awaitify(filterLimit, 4);\n\n/**\n * The same as [`filter`]{@link module:Collections.filter} but runs only a single async operation at a time.\n *\n * @name filterSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.filter]{@link module:Collections.filter}\n * @alias selectSeries\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {Function} iteratee - A truth test to apply to each item in `coll`.\n * The `iteratee` is passed a `callback(err, truthValue)`, which must be called\n * with a boolean argument once it has completed. Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Invoked with (err, results)\n * @returns {Promise} a promise, if no callback provided\n */\nfunction filterSeries (coll, iteratee, callback) {\n    return _filter(eachOfSeries$1, coll, iteratee, callback)\n}\nvar filterSeries$1 = awaitify(filterSeries, 3);\n\n/**\n * Calls the asynchronous function `fn` with a callback parameter that allows it\n * to call itself again, in series, indefinitely.\n\n * If an error is passed to the callback then `errback` is called with the\n * error, and execution stops, otherwise it will never be called.\n *\n * @name forever\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {AsyncFunction} fn - an async function to call repeatedly.\n * Invoked with (next).\n * @param {Function} [errback] - when `fn` passes an error to it's callback,\n * this function will be called, and execution stops. Invoked with (err).\n * @returns {Promise} a promise that rejects if an error occurs and an errback\n * is not passed\n * @example\n *\n * async.forever(\n *     function(next) {\n *         // next is suitable for passing to things that need a callback(err [, whatever]);\n *         // it will result in this function being called again.\n *     },\n *     function(err) {\n *         // if next is called with a value in its first parameter, it will appear\n *         // in here as 'err', and execution will stop.\n *     }\n * );\n */\nfunction forever(fn, errback) {\n    var done = onlyOnce(errback);\n    var task = wrapAsync(ensureAsync(fn));\n\n    function next(err) {\n        if (err) return done(err);\n        if (err === false) return;\n        task(next);\n    }\n    return next();\n}\nvar forever$1 = awaitify(forever, 2);\n\n/**\n * The same as [`groupBy`]{@link module:Collections.groupBy} but runs a maximum of `limit` async operations at a time.\n *\n * @name groupByLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.groupBy]{@link module:Collections.groupBy}\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The iteratee should complete with a `key` to group the value under.\n * Invoked with (value, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. Result is an `Object` whoses\n * properties are arrays of values which returned the corresponding key.\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction groupByLimit(coll, limit, iteratee, callback) {\n    var _iteratee = wrapAsync(iteratee);\n    return mapLimit$1(coll, limit, (val, iterCb) => {\n        _iteratee(val, (err, key) => {\n            if (err) return iterCb(err);\n            return iterCb(err, {key, val});\n        });\n    }, (err, mapResults) => {\n        var result = {};\n        // from MDN, handle object having an `hasOwnProperty` prop\n        var {hasOwnProperty} = Object.prototype;\n\n        for (var i = 0; i < mapResults.length; i++) {\n            if (mapResults[i]) {\n                var {key} = mapResults[i];\n                var {val} = mapResults[i];\n\n                if (hasOwnProperty.call(result, key)) {\n                    result[key].push(val);\n                } else {\n                    result[key] = [val];\n                }\n            }\n        }\n\n        return callback(err, result);\n    });\n}\n\nvar groupByLimit$1 = awaitify(groupByLimit, 4);\n\n/**\n * Returns a new object, where each value corresponds to an array of items, from\n * `coll`, that returned the corresponding key. That is, the keys of the object\n * correspond to the values passed to the `iteratee` callback.\n *\n * Note: Since this function applies the `iteratee` to each item in parallel,\n * there is no guarantee that the `iteratee` functions will complete in order.\n * However, the values for each key in the `result` will be in the same order as\n * the original `coll`. For Objects, the values will roughly be in the order of\n * the original Objects' keys (but this can vary across JavaScript engines).\n *\n * @name groupBy\n * @static\n * @memberOf module:Collections\n * @method\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The iteratee should complete with a `key` to group the value under.\n * Invoked with (value, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. Result is an `Object` whoses\n * properties are arrays of values which returned the corresponding key.\n * @returns {Promise} a promise, if no callback is passed\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n * // dir4 does not exist\n *\n * const files = ['dir1/file1.txt','dir2','dir4']\n *\n * // asynchronous function that detects file type as none, file, or directory\n * function detectFile(file, callback) {\n *     fs.stat(file, function(err, stat) {\n *         if (err) {\n *             return callback(null, 'none');\n *         }\n *         callback(null, stat.isDirectory() ? 'directory' : 'file');\n *     });\n * }\n *\n * //Using callbacks\n * async.groupBy(files, detectFile, function(err, result) {\n *     if(err) {\n *         console.log(err);\n *     } else {\n *\t       console.log(result);\n *         // {\n *         //     file: [ 'dir1/file1.txt' ],\n *         //     none: [ 'dir4' ],\n *         //     directory: [ 'dir2']\n *         // }\n *         // result is object containing the files grouped by type\n *     }\n * });\n *\n * // Using Promises\n * async.groupBy(files, detectFile)\n * .then( result => {\n *     console.log(result);\n *     // {\n *     //     file: [ 'dir1/file1.txt' ],\n *     //     none: [ 'dir4' ],\n *     //     directory: [ 'dir2']\n *     // }\n *     // result is object containing the files grouped by type\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.groupBy(files, detectFile);\n *         console.log(result);\n *         // {\n *         //     file: [ 'dir1/file1.txt' ],\n *         //     none: [ 'dir4' ],\n *         //     directory: [ 'dir2']\n *         // }\n *         // result is object containing the files grouped by type\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction groupBy (coll, iteratee, callback) {\n    return groupByLimit$1(coll, Infinity, iteratee, callback)\n}\n\n/**\n * The same as [`groupBy`]{@link module:Collections.groupBy} but runs only a single async operation at a time.\n *\n * @name groupBySeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.groupBy]{@link module:Collections.groupBy}\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The iteratee should complete with a `key` to group the value under.\n * Invoked with (value, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. Result is an `Object` whose\n * properties are arrays of values which returned the corresponding key.\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction groupBySeries (coll, iteratee, callback) {\n    return groupByLimit$1(coll, 1, iteratee, callback)\n}\n\n/**\n * Logs the result of an `async` function to the `console`. Only works in\n * Node.js or in browsers that support `console.log` and `console.error` (such\n * as FF and Chrome). If multiple arguments are returned from the async\n * function, `console.log` is called on each argument in order.\n *\n * @name log\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {AsyncFunction} function - The function you want to eventually apply\n * all arguments to.\n * @param {...*} arguments... - Any number of arguments to apply to the function.\n * @example\n *\n * // in a module\n * var hello = function(name, callback) {\n *     setTimeout(function() {\n *         callback(null, 'hello ' + name);\n *     }, 1000);\n * };\n *\n * // in the node repl\n * node> async.log(hello, 'world');\n * 'hello world'\n */\nvar log = consoleFunc('log');\n\n/**\n * The same as [`mapValues`]{@link module:Collections.mapValues} but runs a maximum of `limit` async operations at a\n * time.\n *\n * @name mapValuesLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.mapValues]{@link module:Collections.mapValues}\n * @category Collection\n * @param {Object} obj - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - A function to apply to each value and key\n * in `coll`.\n * The iteratee should complete with the transformed value as its result.\n * Invoked with (value, key, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. `result` is a new object consisting\n * of each key from `obj`, with each transformed value on the right-hand side.\n * Invoked with (err, result).\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction mapValuesLimit(obj, limit, iteratee, callback) {\n    callback = once(callback);\n    var newObj = {};\n    var _iteratee = wrapAsync(iteratee);\n    return eachOfLimit$2(limit)(obj, (val, key, next) => {\n        _iteratee(val, key, (err, result) => {\n            if (err) return next(err);\n            newObj[key] = result;\n            next(err);\n        });\n    }, err => callback(err, newObj));\n}\n\nvar mapValuesLimit$1 = awaitify(mapValuesLimit, 4);\n\n/**\n * A relative of [`map`]{@link module:Collections.map}, designed for use with objects.\n *\n * Produces a new Object by mapping each value of `obj` through the `iteratee`\n * function. The `iteratee` is called each `value` and `key` from `obj` and a\n * callback for when it has finished processing. Each of these callbacks takes\n * two arguments: an `error`, and the transformed item from `obj`. If `iteratee`\n * passes an error to its callback, the main `callback` (for the `mapValues`\n * function) is immediately called with the error.\n *\n * Note, the order of the keys in the result is not guaranteed.  The keys will\n * be roughly in the order they complete, (but this is very engine-specific)\n *\n * @name mapValues\n * @static\n * @memberOf module:Collections\n * @method\n * @category Collection\n * @param {Object} obj - A collection to iterate over.\n * @param {AsyncFunction} iteratee - A function to apply to each value and key\n * in `coll`.\n * The iteratee should complete with the transformed value as its result.\n * Invoked with (value, key, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. `result` is a new object consisting\n * of each key from `obj`, with each transformed value on the right-hand side.\n * Invoked with (err, result).\n * @returns {Promise} a promise, if no callback is passed\n * @example\n *\n * // file1.txt is a file that is 1000 bytes in size\n * // file2.txt is a file that is 2000 bytes in size\n * // file3.txt is a file that is 3000 bytes in size\n * // file4.txt does not exist\n *\n * const fileMap = {\n *     f1: 'file1.txt',\n *     f2: 'file2.txt',\n *     f3: 'file3.txt'\n * };\n *\n * const withMissingFileMap = {\n *     f1: 'file1.txt',\n *     f2: 'file2.txt',\n *     f3: 'file4.txt'\n * };\n *\n * // asynchronous function that returns the file size in bytes\n * function getFileSizeInBytes(file, key, callback) {\n *     fs.stat(file, function(err, stat) {\n *         if (err) {\n *             return callback(err);\n *         }\n *         callback(null, stat.size);\n *     });\n * }\n *\n * // Using callbacks\n * async.mapValues(fileMap, getFileSizeInBytes, function(err, result) {\n *     if (err) {\n *         console.log(err);\n *     } else {\n *         console.log(result);\n *         // result is now a map of file size in bytes for each file, e.g.\n *         // {\n *         //     f1: 1000,\n *         //     f2: 2000,\n *         //     f3: 3000\n *         // }\n *     }\n * });\n *\n * // Error handling\n * async.mapValues(withMissingFileMap, getFileSizeInBytes, function(err, result) {\n *     if (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *     } else {\n *         console.log(result);\n *     }\n * });\n *\n * // Using Promises\n * async.mapValues(fileMap, getFileSizeInBytes)\n * .then( result => {\n *     console.log(result);\n *     // result is now a map of file size in bytes for each file, e.g.\n *     // {\n *     //     f1: 1000,\n *     //     f2: 2000,\n *     //     f3: 3000\n *     // }\n * }).catch (err => {\n *     console.log(err);\n * });\n *\n * // Error Handling\n * async.mapValues(withMissingFileMap, getFileSizeInBytes)\n * .then( result => {\n *     console.log(result);\n * }).catch (err => {\n *     console.log(err);\n *     // [ Error: ENOENT: no such file or directory ]\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.mapValues(fileMap, getFileSizeInBytes);\n *         console.log(result);\n *         // result is now a map of file size in bytes for each file, e.g.\n *         // {\n *         //     f1: 1000,\n *         //     f2: 2000,\n *         //     f3: 3000\n *         // }\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * // Error Handling\n * async () => {\n *     try {\n *         let result = await async.mapValues(withMissingFileMap, getFileSizeInBytes);\n *         console.log(result);\n *     }\n *     catch (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *     }\n * }\n *\n */\nfunction mapValues(obj, iteratee, callback) {\n    return mapValuesLimit$1(obj, Infinity, iteratee, callback)\n}\n\n/**\n * The same as [`mapValues`]{@link module:Collections.mapValues} but runs only a single async operation at a time.\n *\n * @name mapValuesSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.mapValues]{@link module:Collections.mapValues}\n * @category Collection\n * @param {Object} obj - A collection to iterate over.\n * @param {AsyncFunction} iteratee - A function to apply to each value and key\n * in `coll`.\n * The iteratee should complete with the transformed value as its result.\n * Invoked with (value, key, callback).\n * @param {Function} [callback] - A callback which is called when all `iteratee`\n * functions have finished, or an error occurs. `result` is a new object consisting\n * of each key from `obj`, with each transformed value on the right-hand side.\n * Invoked with (err, result).\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction mapValuesSeries(obj, iteratee, callback) {\n    return mapValuesLimit$1(obj, 1, iteratee, callback)\n}\n\n/**\n * Caches the results of an async function. When creating a hash to store\n * function results against, the callback is omitted from the hash and an\n * optional hash function can be used.\n *\n * **Note: if the async function errs, the result will not be cached and\n * subsequent calls will call the wrapped function.**\n *\n * If no hash function is specified, the first argument is used as a hash key,\n * which may work reasonably if it is a string or a data type that converts to a\n * distinct string. Note that objects and arrays will not behave reasonably.\n * Neither will cases where the other arguments are significant. In such cases,\n * specify your own hash function.\n *\n * The cache of results is exposed as the `memo` property of the function\n * returned by `memoize`.\n *\n * @name memoize\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {AsyncFunction} fn - The async function to proxy and cache results from.\n * @param {Function} hasher - An optional function for generating a custom hash\n * for storing results. It has all the arguments applied to it apart from the\n * callback, and must be synchronous.\n * @returns {AsyncFunction} a memoized version of `fn`\n * @example\n *\n * var slow_fn = function(name, callback) {\n *     // do something\n *     callback(null, result);\n * };\n * var fn = async.memoize(slow_fn);\n *\n * // fn can now be used as if it were slow_fn\n * fn('some name', function() {\n *     // callback\n * });\n */\nfunction memoize(fn, hasher = v => v) {\n    var memo = Object.create(null);\n    var queues = Object.create(null);\n    var _fn = wrapAsync(fn);\n    var memoized = initialParams((args, callback) => {\n        var key = hasher(...args);\n        if (key in memo) {\n            setImmediate$1(() => callback(null, ...memo[key]));\n        } else if (key in queues) {\n            queues[key].push(callback);\n        } else {\n            queues[key] = [callback];\n            _fn(...args, (err, ...resultArgs) => {\n                // #1465 don't memoize if an error occurred\n                if (!err) {\n                    memo[key] = resultArgs;\n                }\n                var q = queues[key];\n                delete queues[key];\n                for (var i = 0, l = q.length; i < l; i++) {\n                    q[i](err, ...resultArgs);\n                }\n            });\n        }\n    });\n    memoized.memo = memo;\n    memoized.unmemoized = fn;\n    return memoized;\n}\n\n/* istanbul ignore file */\n\n/**\n * Calls `callback` on a later loop around the event loop. In Node.js this just\n * calls `process.nextTick`.  In the browser it will use `setImmediate` if\n * available, otherwise `setTimeout(callback, 0)`, which means other higher\n * priority events may precede the execution of `callback`.\n *\n * This is used internally for browser-compatibility purposes.\n *\n * @name nextTick\n * @static\n * @memberOf module:Utils\n * @method\n * @see [async.setImmediate]{@link module:Utils.setImmediate}\n * @category Util\n * @param {Function} callback - The function to call on a later loop around\n * the event loop. Invoked with (args...).\n * @param {...*} args... - any number of additional arguments to pass to the\n * callback on the next tick.\n * @example\n *\n * var call_order = [];\n * async.nextTick(function() {\n *     call_order.push('two');\n *     // call_order now equals ['one','two']\n * });\n * call_order.push('one');\n *\n * async.setImmediate(function (a, b, c) {\n *     // a, b, and c equal 1, 2, and 3\n * }, 1, 2, 3);\n */\nvar _defer;\n\nif (hasNextTick) {\n    _defer = process.nextTick;\n} else if (hasSetImmediate) {\n    _defer = setImmediate;\n} else {\n    _defer = fallback;\n}\n\nvar nextTick = wrap(_defer);\n\nvar _parallel = awaitify((eachfn, tasks, callback) => {\n    var results = isArrayLike(tasks) ? [] : {};\n\n    eachfn(tasks, (task, key, taskCb) => {\n        wrapAsync(task)((err, ...result) => {\n            if (result.length < 2) {\n                [result] = result;\n            }\n            results[key] = result;\n            taskCb(err);\n        });\n    }, err => callback(err, results));\n}, 3);\n\n/**\n * Run the `tasks` collection of functions in parallel, without waiting until\n * the previous function has completed. If any of the functions pass an error to\n * its callback, the main `callback` is immediately called with the value of the\n * error. Once the `tasks` have completed, the results are passed to the final\n * `callback` as an array.\n *\n * **Note:** `parallel` is about kicking-off I/O tasks in parallel, not about\n * parallel execution of code.  If your tasks do not use any timers or perform\n * any I/O, they will actually be executed in series.  Any synchronous setup\n * sections for each task will happen one after the other.  JavaScript remains\n * single-threaded.\n *\n * **Hint:** Use [`reflect`]{@link module:Utils.reflect} to continue the\n * execution of other tasks when a task fails.\n *\n * It is also possible to use an object instead of an array. Each property will\n * be run as a function and the results will be passed to the final `callback`\n * as an object instead of an array. This can be a more readable way of handling\n * results from {@link async.parallel}.\n *\n * @name parallel\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {Array|Iterable|AsyncIterable|Object} tasks - A collection of\n * [async functions]{@link AsyncFunction} to run.\n * Each async function can complete with any number of optional `result` values.\n * @param {Function} [callback] - An optional callback to run once all the\n * functions have completed successfully. This function gets a results array\n * (or object) containing all the result arguments passed to the task callbacks.\n * Invoked with (err, results).\n * @returns {Promise} a promise, if a callback is not passed\n *\n * @example\n *\n * //Using Callbacks\n * async.parallel([\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'one');\n *         }, 200);\n *     },\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'two');\n *         }, 100);\n *     }\n * ], function(err, results) {\n *     console.log(results);\n *     // results is equal to ['one','two'] even though\n *     // the second function had a shorter timeout.\n * });\n *\n * // an example using an object instead of an array\n * async.parallel({\n *     one: function(callback) {\n *         setTimeout(function() {\n *             callback(null, 1);\n *         }, 200);\n *     },\n *     two: function(callback) {\n *         setTimeout(function() {\n *             callback(null, 2);\n *         }, 100);\n *     }\n * }, function(err, results) {\n *     console.log(results);\n *     // results is equal to: { one: 1, two: 2 }\n * });\n *\n * //Using Promises\n * async.parallel([\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'one');\n *         }, 200);\n *     },\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'two');\n *         }, 100);\n *     }\n * ]).then(results => {\n *     console.log(results);\n *     // results is equal to ['one','two'] even though\n *     // the second function had a shorter timeout.\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * // an example using an object instead of an array\n * async.parallel({\n *     one: function(callback) {\n *         setTimeout(function() {\n *             callback(null, 1);\n *         }, 200);\n *     },\n *     two: function(callback) {\n *         setTimeout(function() {\n *             callback(null, 2);\n *         }, 100);\n *     }\n * }).then(results => {\n *     console.log(results);\n *     // results is equal to: { one: 1, two: 2 }\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * //Using async/await\n * async () => {\n *     try {\n *         let results = await async.parallel([\n *             function(callback) {\n *                 setTimeout(function() {\n *                     callback(null, 'one');\n *                 }, 200);\n *             },\n *             function(callback) {\n *                 setTimeout(function() {\n *                     callback(null, 'two');\n *                 }, 100);\n *             }\n *         ]);\n *         console.log(results);\n *         // results is equal to ['one','two'] even though\n *         // the second function had a shorter timeout.\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * // an example using an object instead of an array\n * async () => {\n *     try {\n *         let results = await async.parallel({\n *             one: function(callback) {\n *                 setTimeout(function() {\n *                     callback(null, 1);\n *                 }, 200);\n *             },\n *            two: function(callback) {\n *                 setTimeout(function() {\n *                     callback(null, 2);\n *                 }, 100);\n *            }\n *         });\n *         console.log(results);\n *         // results is equal to: { one: 1, two: 2 }\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction parallel(tasks, callback) {\n    return _parallel(eachOf$1, tasks, callback);\n}\n\n/**\n * The same as [`parallel`]{@link module:ControlFlow.parallel} but runs a maximum of `limit` async operations at a\n * time.\n *\n * @name parallelLimit\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.parallel]{@link module:ControlFlow.parallel}\n * @category Control Flow\n * @param {Array|Iterable|AsyncIterable|Object} tasks - A collection of\n * [async functions]{@link AsyncFunction} to run.\n * Each async function can complete with any number of optional `result` values.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {Function} [callback] - An optional callback to run once all the\n * functions have completed successfully. This function gets a results array\n * (or object) containing all the result arguments passed to the task callbacks.\n * Invoked with (err, results).\n * @returns {Promise} a promise, if a callback is not passed\n */\nfunction parallelLimit(tasks, limit, callback) {\n    return _parallel(eachOfLimit$2(limit), tasks, callback);\n}\n\n/**\n * A queue of tasks for the worker function to complete.\n * @typedef {Iterable} QueueObject\n * @memberOf module:ControlFlow\n * @property {Function} length - a function returning the number of items\n * waiting to be processed. Invoke with `queue.length()`.\n * @property {boolean} started - a boolean indicating whether or not any\n * items have been pushed and processed by the queue.\n * @property {Function} running - a function returning the number of items\n * currently being processed. Invoke with `queue.running()`.\n * @property {Function} workersList - a function returning the array of items\n * currently being processed. Invoke with `queue.workersList()`.\n * @property {Function} idle - a function returning false if there are items\n * waiting or being processed, or true if not. Invoke with `queue.idle()`.\n * @property {number} concurrency - an integer for determining how many `worker`\n * functions should be run in parallel. This property can be changed after a\n * `queue` is created to alter the concurrency on-the-fly.\n * @property {number} payload - an integer that specifies how many items are\n * passed to the worker function at a time. only applies if this is a\n * [cargo]{@link module:ControlFlow.cargo} object\n * @property {AsyncFunction} push - add a new task to the `queue`. Calls `callback`\n * once the `worker` has finished processing the task. Instead of a single task,\n * a `tasks` array can be submitted. The respective callback is used for every\n * task in the list. Invoke with `queue.push(task, [callback])`,\n * @property {AsyncFunction} unshift - add a new task to the front of the `queue`.\n * Invoke with `queue.unshift(task, [callback])`.\n * @property {AsyncFunction} pushAsync - the same as `q.push`, except this returns\n * a promise that rejects if an error occurs.\n * @property {AsyncFunction} unshiftAsync - the same as `q.unshift`, except this returns\n * a promise that rejects if an error occurs.\n * @property {Function} remove - remove items from the queue that match a test\n * function.  The test function will be passed an object with a `data` property,\n * and a `priority` property, if this is a\n * [priorityQueue]{@link module:ControlFlow.priorityQueue} object.\n * Invoked with `queue.remove(testFn)`, where `testFn` is of the form\n * `function ({data, priority}) {}` and returns a Boolean.\n * @property {Function} saturated - a function that sets a callback that is\n * called when the number of running workers hits the `concurrency` limit, and\n * further tasks will be queued.  If the callback is omitted, `q.saturated()`\n * returns a promise for the next occurrence.\n * @property {Function} unsaturated - a function that sets a callback that is\n * called when the number of running workers is less than the `concurrency` &\n * `buffer` limits, and further tasks will not be queued. If the callback is\n * omitted, `q.unsaturated()` returns a promise for the next occurrence.\n * @property {number} buffer - A minimum threshold buffer in order to say that\n * the `queue` is `unsaturated`.\n * @property {Function} empty - a function that sets a callback that is called\n * when the last item from the `queue` is given to a `worker`. If the callback\n * is omitted, `q.empty()` returns a promise for the next occurrence.\n * @property {Function} drain - a function that sets a callback that is called\n * when the last item from the `queue` has returned from the `worker`. If the\n * callback is omitted, `q.drain()` returns a promise for the next occurrence.\n * @property {Function} error - a function that sets a callback that is called\n * when a task errors. Has the signature `function(error, task)`. If the\n * callback is omitted, `error()` returns a promise that rejects on the next\n * error.\n * @property {boolean} paused - a boolean for determining whether the queue is\n * in a paused state.\n * @property {Function} pause - a function that pauses the processing of tasks\n * until `resume()` is called. Invoke with `queue.pause()`.\n * @property {Function} resume - a function that resumes the processing of\n * queued tasks when the queue is paused. Invoke with `queue.resume()`.\n * @property {Function} kill - a function that removes the `drain` callback and\n * empties remaining tasks from the queue forcing it to go idle. No more tasks\n * should be pushed to the queue after calling this function. Invoke with `queue.kill()`.\n *\n * @example\n * const q = async.queue(worker, 2)\n * q.push(item1)\n * q.push(item2)\n * q.push(item3)\n * // queues are iterable, spread into an array to inspect\n * const items = [...q] // [item1, item2, item3]\n * // or use for of\n * for (let item of q) {\n *     console.log(item)\n * }\n *\n * q.drain(() => {\n *     console.log('all done')\n * })\n * // or\n * await q.drain()\n */\n\n/**\n * Creates a `queue` object with the specified `concurrency`. Tasks added to the\n * `queue` are processed in parallel (up to the `concurrency` limit). If all\n * `worker`s are in progress, the task is queued until one becomes available.\n * Once a `worker` completes a `task`, that `task`'s callback is called.\n *\n * @name queue\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {AsyncFunction} worker - An async function for processing a queued task.\n * If you want to handle errors from an individual task, pass a callback to\n * `q.push()`. Invoked with (task, callback).\n * @param {number} [concurrency=1] - An `integer` for determining how many\n * `worker` functions should be run in parallel.  If omitted, the concurrency\n * defaults to `1`.  If the concurrency is `0`, an error is thrown.\n * @returns {module:ControlFlow.QueueObject} A queue object to manage the tasks. Callbacks can be\n * attached as certain properties to listen for specific events during the\n * lifecycle of the queue.\n * @example\n *\n * // create a queue object with concurrency 2\n * var q = async.queue(function(task, callback) {\n *     console.log('hello ' + task.name);\n *     callback();\n * }, 2);\n *\n * // assign a callback\n * q.drain(function() {\n *     console.log('all items have been processed');\n * });\n * // or await the end\n * await q.drain()\n *\n * // assign an error callback\n * q.error(function(err, task) {\n *     console.error('task experienced an error');\n * });\n *\n * // add some items to the queue\n * q.push({name: 'foo'}, function(err) {\n *     console.log('finished processing foo');\n * });\n * // callback is optional\n * q.push({name: 'bar'});\n *\n * // add some items to the queue (batch-wise)\n * q.push([{name: 'baz'},{name: 'bay'},{name: 'bax'}], function(err) {\n *     console.log('finished processing item');\n * });\n *\n * // add some items to the front of the queue\n * q.unshift({name: 'bar'}, function (err) {\n *     console.log('finished processing bar');\n * });\n */\nfunction queue (worker, concurrency) {\n    var _worker = wrapAsync(worker);\n    return queue$1((items, cb) => {\n        _worker(items[0], cb);\n    }, concurrency, 1);\n}\n\n// Binary min-heap implementation used for priority queue.\n// Implementation is stable, i.e. push time is considered for equal priorities\nclass Heap {\n    constructor() {\n        this.heap = [];\n        this.pushCount = Number.MIN_SAFE_INTEGER;\n    }\n\n    get length() {\n        return this.heap.length;\n    }\n\n    empty () {\n        this.heap = [];\n        return this;\n    }\n\n    percUp(index) {\n        let p;\n\n        while (index > 0 && smaller(this.heap[index], this.heap[p=parent(index)])) {\n            let t = this.heap[index];\n            this.heap[index] = this.heap[p];\n            this.heap[p] = t;\n\n            index = p;\n        }\n    }\n\n    percDown(index) {\n        let l;\n\n        while ((l=leftChi(index)) < this.heap.length) {\n            if (l+1 < this.heap.length && smaller(this.heap[l+1], this.heap[l])) {\n                l = l+1;\n            }\n\n            if (smaller(this.heap[index], this.heap[l])) {\n                break;\n            }\n\n            let t = this.heap[index];\n            this.heap[index] = this.heap[l];\n            this.heap[l] = t;\n\n            index = l;\n        }\n    }\n\n    push(node) {\n        node.pushCount = ++this.pushCount;\n        this.heap.push(node);\n        this.percUp(this.heap.length-1);\n    }\n\n    unshift(node) {\n        return this.heap.push(node);\n    }\n\n    shift() {\n        let [top] = this.heap;\n\n        this.heap[0] = this.heap[this.heap.length-1];\n        this.heap.pop();\n        this.percDown(0);\n\n        return top;\n    }\n\n    toArray() {\n        return [...this];\n    }\n\n    *[Symbol.iterator] () {\n        for (let i = 0; i < this.heap.length; i++) {\n            yield this.heap[i].data;\n        }\n    }\n\n    remove (testFn) {\n        let j = 0;\n        for (let i = 0; i < this.heap.length; i++) {\n            if (!testFn(this.heap[i])) {\n                this.heap[j] = this.heap[i];\n                j++;\n            }\n        }\n\n        this.heap.splice(j);\n\n        for (let i = parent(this.heap.length-1); i >= 0; i--) {\n            this.percDown(i);\n        }\n\n        return this;\n    }\n}\n\nfunction leftChi(i) {\n    return (i<<1)+1;\n}\n\nfunction parent(i) {\n    return ((i+1)>>1)-1;\n}\n\nfunction smaller(x, y) {\n    if (x.priority !== y.priority) {\n        return x.priority < y.priority;\n    }\n    else {\n        return x.pushCount < y.pushCount;\n    }\n}\n\n/**\n * The same as [async.queue]{@link module:ControlFlow.queue} only tasks are assigned a priority and\n * completed in ascending priority order.\n *\n * @name priorityQueue\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.queue]{@link module:ControlFlow.queue}\n * @category Control Flow\n * @param {AsyncFunction} worker - An async function for processing a queued task.\n * If you want to handle errors from an individual task, pass a callback to\n * `q.push()`.\n * Invoked with (task, callback).\n * @param {number} concurrency - An `integer` for determining how many `worker`\n * functions should be run in parallel.  If omitted, the concurrency defaults to\n * `1`.  If the concurrency is `0`, an error is thrown.\n * @returns {module:ControlFlow.QueueObject} A priorityQueue object to manage the tasks. There are three\n * differences between `queue` and `priorityQueue` objects:\n * * `push(task, priority, [callback])` - `priority` should be a number. If an\n *   array of `tasks` is given, all tasks will be assigned the same priority.\n * * `pushAsync(task, priority, [callback])` - the same as `priorityQueue.push`,\n *   except this returns a promise that rejects if an error occurs.\n * * The `unshift` and `unshiftAsync` methods were removed.\n */\nfunction priorityQueue(worker, concurrency) {\n    // Start with a normal queue\n    var q = queue(worker, concurrency);\n\n    var {\n        push,\n        pushAsync\n    } = q;\n\n    q._tasks = new Heap();\n    q._createTaskItem = ({data, priority}, callback) => {\n        return {\n            data,\n            priority,\n            callback\n        };\n    };\n\n    function createDataItems(tasks, priority) {\n        if (!Array.isArray(tasks)) {\n            return {data: tasks, priority};\n        }\n        return tasks.map(data => { return {data, priority}; });\n    }\n\n    // Override push to accept second parameter representing priority\n    q.push = function(data, priority = 0, callback) {\n        return push(createDataItems(data, priority), callback);\n    };\n\n    q.pushAsync = function(data, priority = 0, callback) {\n        return pushAsync(createDataItems(data, priority), callback);\n    };\n\n    // Remove unshift functions\n    delete q.unshift;\n    delete q.unshiftAsync;\n\n    return q;\n}\n\n/**\n * Runs the `tasks` array of functions in parallel, without waiting until the\n * previous function has completed. Once any of the `tasks` complete or pass an\n * error to its callback, the main `callback` is immediately called. It's\n * equivalent to `Promise.race()`.\n *\n * @name race\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {Array} tasks - An array containing [async functions]{@link AsyncFunction}\n * to run. Each function can complete with an optional `result` value.\n * @param {Function} callback - A callback to run once any of the functions have\n * completed. This function gets an error or result from the first function that\n * completed. Invoked with (err, result).\n * @returns {Promise} a promise, if a callback is omitted\n * @example\n *\n * async.race([\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'one');\n *         }, 200);\n *     },\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'two');\n *         }, 100);\n *     }\n * ],\n * // main callback\n * function(err, result) {\n *     // the result will be equal to 'two' as it finishes earlier\n * });\n */\nfunction race(tasks, callback) {\n    callback = once(callback);\n    if (!Array.isArray(tasks)) return callback(new TypeError('First argument to race must be an array of functions'));\n    if (!tasks.length) return callback();\n    for (var i = 0, l = tasks.length; i < l; i++) {\n        wrapAsync(tasks[i])(callback);\n    }\n}\n\nvar race$1 = awaitify(race, 2);\n\n/**\n * Same as [`reduce`]{@link module:Collections.reduce}, only operates on `array` in reverse order.\n *\n * @name reduceRight\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.reduce]{@link module:Collections.reduce}\n * @alias foldr\n * @category Collection\n * @param {Array} array - A collection to iterate over.\n * @param {*} memo - The initial state of the reduction.\n * @param {AsyncFunction} iteratee - A function applied to each item in the\n * array to produce the next step in the reduction.\n * The `iteratee` should complete with the next state of the reduction.\n * If the iteratee completes with an error, the reduction is stopped and the\n * main `callback` is immediately called with the error.\n * Invoked with (memo, item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Result is the reduced value. Invoked with\n * (err, result).\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction reduceRight (array, memo, iteratee, callback) {\n    var reversed = [...array].reverse();\n    return reduce$1(reversed, memo, iteratee, callback);\n}\n\n/**\n * Wraps the async function in another function that always completes with a\n * result object, even when it errors.\n *\n * The result object has either the property `error` or `value`.\n *\n * @name reflect\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {AsyncFunction} fn - The async function you want to wrap\n * @returns {Function} - A function that always passes null to it's callback as\n * the error. The second argument to the callback will be an `object` with\n * either an `error` or a `value` property.\n * @example\n *\n * async.parallel([\n *     async.reflect(function(callback) {\n *         // do some stuff ...\n *         callback(null, 'one');\n *     }),\n *     async.reflect(function(callback) {\n *         // do some more stuff but error ...\n *         callback('bad stuff happened');\n *     }),\n *     async.reflect(function(callback) {\n *         // do some more stuff ...\n *         callback(null, 'two');\n *     })\n * ],\n * // optional callback\n * function(err, results) {\n *     // values\n *     // results[0].value = 'one'\n *     // results[1].error = 'bad stuff happened'\n *     // results[2].value = 'two'\n * });\n */\nfunction reflect(fn) {\n    var _fn = wrapAsync(fn);\n    return initialParams(function reflectOn(args, reflectCallback) {\n        args.push((error, ...cbArgs) => {\n            let retVal = {};\n            if (error) {\n                retVal.error = error;\n            }\n            if (cbArgs.length > 0){\n                var value = cbArgs;\n                if (cbArgs.length <= 1) {\n                    [value] = cbArgs;\n                }\n                retVal.value = value;\n            }\n            reflectCallback(null, retVal);\n        });\n\n        return _fn.apply(this, args);\n    });\n}\n\n/**\n * A helper function that wraps an array or an object of functions with `reflect`.\n *\n * @name reflectAll\n * @static\n * @memberOf module:Utils\n * @method\n * @see [async.reflect]{@link module:Utils.reflect}\n * @category Util\n * @param {Array|Object|Iterable} tasks - The collection of\n * [async functions]{@link AsyncFunction} to wrap in `async.reflect`.\n * @returns {Array} Returns an array of async functions, each wrapped in\n * `async.reflect`\n * @example\n *\n * let tasks = [\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'one');\n *         }, 200);\n *     },\n *     function(callback) {\n *         // do some more stuff but error ...\n *         callback(new Error('bad stuff happened'));\n *     },\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'two');\n *         }, 100);\n *     }\n * ];\n *\n * async.parallel(async.reflectAll(tasks),\n * // optional callback\n * function(err, results) {\n *     // values\n *     // results[0].value = 'one'\n *     // results[1].error = Error('bad stuff happened')\n *     // results[2].value = 'two'\n * });\n *\n * // an example using an object instead of an array\n * let tasks = {\n *     one: function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'one');\n *         }, 200);\n *     },\n *     two: function(callback) {\n *         callback('two');\n *     },\n *     three: function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'three');\n *         }, 100);\n *     }\n * };\n *\n * async.parallel(async.reflectAll(tasks),\n * // optional callback\n * function(err, results) {\n *     // values\n *     // results.one.value = 'one'\n *     // results.two.error = 'two'\n *     // results.three.value = 'three'\n * });\n */\nfunction reflectAll(tasks) {\n    var results;\n    if (Array.isArray(tasks)) {\n        results = tasks.map(reflect);\n    } else {\n        results = {};\n        Object.keys(tasks).forEach(key => {\n            results[key] = reflect.call(this, tasks[key]);\n        });\n    }\n    return results;\n}\n\nfunction reject$2(eachfn, arr, _iteratee, callback) {\n    const iteratee = wrapAsync(_iteratee);\n    return _filter(eachfn, arr, (value, cb) => {\n        iteratee(value, (err, v) => {\n            cb(err, !v);\n        });\n    }, callback);\n}\n\n/**\n * The opposite of [`filter`]{@link module:Collections.filter}. Removes values that pass an `async` truth test.\n *\n * @name reject\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.filter]{@link module:Collections.filter}\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {Function} iteratee - An async truth test to apply to each item in\n * `coll`.\n * The should complete with a boolean value as its `result`.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback is passed\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n *\n * const fileList = ['dir1/file1.txt','dir2/file3.txt','dir3/file6.txt'];\n *\n * // asynchronous function that checks if a file exists\n * function fileExists(file, callback) {\n *    fs.access(file, fs.constants.F_OK, (err) => {\n *        callback(null, !err);\n *    });\n * }\n *\n * // Using callbacks\n * async.reject(fileList, fileExists, function(err, results) {\n *    // [ 'dir3/file6.txt' ]\n *    // results now equals an array of the non-existing files\n * });\n *\n * // Using Promises\n * async.reject(fileList, fileExists)\n * .then( results => {\n *     console.log(results);\n *     // [ 'dir3/file6.txt' ]\n *     // results now equals an array of the non-existing files\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let results = await async.reject(fileList, fileExists);\n *         console.log(results);\n *         // [ 'dir3/file6.txt' ]\n *         // results now equals an array of the non-existing files\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction reject (coll, iteratee, callback) {\n    return reject$2(eachOf$1, coll, iteratee, callback)\n}\nvar reject$1 = awaitify(reject, 3);\n\n/**\n * The same as [`reject`]{@link module:Collections.reject} but runs a maximum of `limit` async operations at a\n * time.\n *\n * @name rejectLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.reject]{@link module:Collections.reject}\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {Function} iteratee - An async truth test to apply to each item in\n * `coll`.\n * The should complete with a boolean value as its `result`.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction rejectLimit (coll, limit, iteratee, callback) {\n    return reject$2(eachOfLimit$2(limit), coll, iteratee, callback)\n}\nvar rejectLimit$1 = awaitify(rejectLimit, 4);\n\n/**\n * The same as [`reject`]{@link module:Collections.reject} but runs only a single async operation at a time.\n *\n * @name rejectSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.reject]{@link module:Collections.reject}\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {Function} iteratee - An async truth test to apply to each item in\n * `coll`.\n * The should complete with a boolean value as its `result`.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback is passed\n */\nfunction rejectSeries (coll, iteratee, callback) {\n    return reject$2(eachOfSeries$1, coll, iteratee, callback)\n}\nvar rejectSeries$1 = awaitify(rejectSeries, 3);\n\nfunction constant(value) {\n    return function () {\n        return value;\n    }\n}\n\n/**\n * Attempts to get a successful response from `task` no more than `times` times\n * before returning an error. If the task is successful, the `callback` will be\n * passed the result of the successful task. If all attempts fail, the callback\n * will be passed the error and result (if any) of the final attempt.\n *\n * @name retry\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @see [async.retryable]{@link module:ControlFlow.retryable}\n * @param {Object|number} [opts = {times: 5, interval: 0}| 5] - Can be either an\n * object with `times` and `interval` or a number.\n * * `times` - The number of attempts to make before giving up.  The default\n *   is `5`.\n * * `interval` - The time to wait between retries, in milliseconds.  The\n *   default is `0`. The interval may also be specified as a function of the\n *   retry count (see example).\n * * `errorFilter` - An optional synchronous function that is invoked on\n *   erroneous result. If it returns `true` the retry attempts will continue;\n *   if the function returns `false` the retry flow is aborted with the current\n *   attempt's error and result being returned to the final callback.\n *   Invoked with (err).\n * * If `opts` is a number, the number specifies the number of times to retry,\n *   with the default interval of `0`.\n * @param {AsyncFunction} task - An async function to retry.\n * Invoked with (callback).\n * @param {Function} [callback] - An optional callback which is called when the\n * task has succeeded, or after the final failed attempt. It receives the `err`\n * and `result` arguments of the last attempt at completing the `task`. Invoked\n * with (err, results).\n * @returns {Promise} a promise if no callback provided\n *\n * @example\n *\n * // The `retry` function can be used as a stand-alone control flow by passing\n * // a callback, as shown below:\n *\n * // try calling apiMethod 3 times\n * async.retry(3, apiMethod, function(err, result) {\n *     // do something with the result\n * });\n *\n * // try calling apiMethod 3 times, waiting 200 ms between each retry\n * async.retry({times: 3, interval: 200}, apiMethod, function(err, result) {\n *     // do something with the result\n * });\n *\n * // try calling apiMethod 10 times with exponential backoff\n * // (i.e. intervals of 100, 200, 400, 800, 1600, ... milliseconds)\n * async.retry({\n *   times: 10,\n *   interval: function(retryCount) {\n *     return 50 * Math.pow(2, retryCount);\n *   }\n * }, apiMethod, function(err, result) {\n *     // do something with the result\n * });\n *\n * // try calling apiMethod the default 5 times no delay between each retry\n * async.retry(apiMethod, function(err, result) {\n *     // do something with the result\n * });\n *\n * // try calling apiMethod only when error condition satisfies, all other\n * // errors will abort the retry control flow and return to final callback\n * async.retry({\n *   errorFilter: function(err) {\n *     return err.message === 'Temporary error'; // only retry on a specific error\n *   }\n * }, apiMethod, function(err, result) {\n *     // do something with the result\n * });\n *\n * // to retry individual methods that are not as reliable within other\n * // control flow functions, use the `retryable` wrapper:\n * async.auto({\n *     users: api.getUsers.bind(api),\n *     payments: async.retryable(3, api.getPayments.bind(api))\n * }, function(err, results) {\n *     // do something with the results\n * });\n *\n */\nconst DEFAULT_TIMES = 5;\nconst DEFAULT_INTERVAL = 0;\n\nfunction retry(opts, task, callback) {\n    var options = {\n        times: DEFAULT_TIMES,\n        intervalFunc: constant(DEFAULT_INTERVAL)\n    };\n\n    if (arguments.length < 3 && typeof opts === 'function') {\n        callback = task || promiseCallback();\n        task = opts;\n    } else {\n        parseTimes(options, opts);\n        callback = callback || promiseCallback();\n    }\n\n    if (typeof task !== 'function') {\n        throw new Error(\"Invalid arguments for async.retry\");\n    }\n\n    var _task = wrapAsync(task);\n\n    var attempt = 1;\n    function retryAttempt() {\n        _task((err, ...args) => {\n            if (err === false) return\n            if (err && attempt++ < options.times &&\n                (typeof options.errorFilter != 'function' ||\n                    options.errorFilter(err))) {\n                setTimeout(retryAttempt, options.intervalFunc(attempt - 1));\n            } else {\n                callback(err, ...args);\n            }\n        });\n    }\n\n    retryAttempt();\n    return callback[PROMISE_SYMBOL]\n}\n\nfunction parseTimes(acc, t) {\n    if (typeof t === 'object') {\n        acc.times = +t.times || DEFAULT_TIMES;\n\n        acc.intervalFunc = typeof t.interval === 'function' ?\n            t.interval :\n            constant(+t.interval || DEFAULT_INTERVAL);\n\n        acc.errorFilter = t.errorFilter;\n    } else if (typeof t === 'number' || typeof t === 'string') {\n        acc.times = +t || DEFAULT_TIMES;\n    } else {\n        throw new Error(\"Invalid arguments for async.retry\");\n    }\n}\n\n/**\n * A close relative of [`retry`]{@link module:ControlFlow.retry}.  This method\n * wraps a task and makes it retryable, rather than immediately calling it\n * with retries.\n *\n * @name retryable\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.retry]{@link module:ControlFlow.retry}\n * @category Control Flow\n * @param {Object|number} [opts = {times: 5, interval: 0}| 5] - optional\n * options, exactly the same as from `retry`, except for a `opts.arity` that\n * is the arity of the `task` function, defaulting to `task.length`\n * @param {AsyncFunction} task - the asynchronous function to wrap.\n * This function will be passed any arguments passed to the returned wrapper.\n * Invoked with (...args, callback).\n * @returns {AsyncFunction} The wrapped function, which when invoked, will\n * retry on an error, based on the parameters specified in `opts`.\n * This function will accept the same parameters as `task`.\n * @example\n *\n * async.auto({\n *     dep1: async.retryable(3, getFromFlakyService),\n *     process: [\"dep1\", async.retryable(3, function (results, cb) {\n *         maybeProcessData(results.dep1, cb);\n *     })]\n * }, callback);\n */\nfunction retryable (opts, task) {\n    if (!task) {\n        task = opts;\n        opts = null;\n    }\n    let arity = (opts && opts.arity) || task.length;\n    if (isAsync(task)) {\n        arity += 1;\n    }\n    var _task = wrapAsync(task);\n    return initialParams((args, callback) => {\n        if (args.length < arity - 1 || callback == null) {\n            args.push(callback);\n            callback = promiseCallback();\n        }\n        function taskFn(cb) {\n            _task(...args, cb);\n        }\n\n        if (opts) retry(opts, taskFn, callback);\n        else retry(taskFn, callback);\n\n        return callback[PROMISE_SYMBOL]\n    });\n}\n\n/**\n * Run the functions in the `tasks` collection in series, each one running once\n * the previous function has completed. If any functions in the series pass an\n * error to its callback, no more functions are run, and `callback` is\n * immediately called with the value of the error. Otherwise, `callback`\n * receives an array of results when `tasks` have completed.\n *\n * It is also possible to use an object instead of an array. Each property will\n * be run as a function, and the results will be passed to the final `callback`\n * as an object instead of an array. This can be a more readable way of handling\n *  results from {@link async.series}.\n *\n * **Note** that while many implementations preserve the order of object\n * properties, the [ECMAScript Language Specification](http://www.ecma-international.org/ecma-262/5.1/#sec-8.6)\n * explicitly states that\n *\n * > The mechanics and order of enumerating the properties is not specified.\n *\n * So if you rely on the order in which your series of functions are executed,\n * and want this to work on all platforms, consider using an array.\n *\n * @name series\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {Array|Iterable|AsyncIterable|Object} tasks - A collection containing\n * [async functions]{@link AsyncFunction} to run in series.\n * Each function can complete with any number of optional `result` values.\n * @param {Function} [callback] - An optional callback to run once all the\n * functions have completed. This function gets a results array (or object)\n * containing all the result arguments passed to the `task` callbacks. Invoked\n * with (err, result).\n * @return {Promise} a promise, if no callback is passed\n * @example\n *\n * //Using Callbacks\n * async.series([\n *     function(callback) {\n *         setTimeout(function() {\n *             // do some async task\n *             callback(null, 'one');\n *         }, 200);\n *     },\n *     function(callback) {\n *         setTimeout(function() {\n *             // then do another async task\n *             callback(null, 'two');\n *         }, 100);\n *     }\n * ], function(err, results) {\n *     console.log(results);\n *     // results is equal to ['one','two']\n * });\n *\n * // an example using objects instead of arrays\n * async.series({\n *     one: function(callback) {\n *         setTimeout(function() {\n *             // do some async task\n *             callback(null, 1);\n *         }, 200);\n *     },\n *     two: function(callback) {\n *         setTimeout(function() {\n *             // then do another async task\n *             callback(null, 2);\n *         }, 100);\n *     }\n * }, function(err, results) {\n *     console.log(results);\n *     // results is equal to: { one: 1, two: 2 }\n * });\n *\n * //Using Promises\n * async.series([\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'one');\n *         }, 200);\n *     },\n *     function(callback) {\n *         setTimeout(function() {\n *             callback(null, 'two');\n *         }, 100);\n *     }\n * ]).then(results => {\n *     console.log(results);\n *     // results is equal to ['one','two']\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * // an example using an object instead of an array\n * async.series({\n *     one: function(callback) {\n *         setTimeout(function() {\n *             // do some async task\n *             callback(null, 1);\n *         }, 200);\n *     },\n *     two: function(callback) {\n *         setTimeout(function() {\n *             // then do another async task\n *             callback(null, 2);\n *         }, 100);\n *     }\n * }).then(results => {\n *     console.log(results);\n *     // results is equal to: { one: 1, two: 2 }\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * //Using async/await\n * async () => {\n *     try {\n *         let results = await async.series([\n *             function(callback) {\n *                 setTimeout(function() {\n *                     // do some async task\n *                     callback(null, 'one');\n *                 }, 200);\n *             },\n *             function(callback) {\n *                 setTimeout(function() {\n *                     // then do another async task\n *                     callback(null, 'two');\n *                 }, 100);\n *             }\n *         ]);\n *         console.log(results);\n *         // results is equal to ['one','two']\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * // an example using an object instead of an array\n * async () => {\n *     try {\n *         let results = await async.parallel({\n *             one: function(callback) {\n *                 setTimeout(function() {\n *                     // do some async task\n *                     callback(null, 1);\n *                 }, 200);\n *             },\n *            two: function(callback) {\n *                 setTimeout(function() {\n *                     // then do another async task\n *                     callback(null, 2);\n *                 }, 100);\n *            }\n *         });\n *         console.log(results);\n *         // results is equal to: { one: 1, two: 2 }\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction series(tasks, callback) {\n    return _parallel(eachOfSeries$1, tasks, callback);\n}\n\n/**\n * Returns `true` if at least one element in the `coll` satisfies an async test.\n * If any iteratee call returns `true`, the main `callback` is immediately\n * called.\n *\n * @name some\n * @static\n * @memberOf module:Collections\n * @method\n * @alias any\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async truth test to apply to each item\n * in the collections in parallel.\n * The iteratee should complete with a boolean `result` value.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called as soon as any\n * iteratee returns `true`, or after all the iteratee functions have finished.\n * Result will be either `true` or `false` depending on the values of the async\n * tests. Invoked with (err, result).\n * @returns {Promise} a promise, if no callback provided\n * @example\n *\n * // dir1 is a directory that contains file1.txt, file2.txt\n * // dir2 is a directory that contains file3.txt, file4.txt\n * // dir3 is a directory that contains file5.txt\n * // dir4 does not exist\n *\n * // asynchronous function that checks if a file exists\n * function fileExists(file, callback) {\n *    fs.access(file, fs.constants.F_OK, (err) => {\n *        callback(null, !err);\n *    });\n * }\n *\n * // Using callbacks\n * async.some(['dir1/missing.txt','dir2/missing.txt','dir3/file5.txt'], fileExists,\n *    function(err, result) {\n *        console.log(result);\n *        // true\n *        // result is true since some file in the list exists\n *    }\n *);\n *\n * async.some(['dir1/missing.txt','dir2/missing.txt','dir4/missing.txt'], fileExists,\n *    function(err, result) {\n *        console.log(result);\n *        // false\n *        // result is false since none of the files exists\n *    }\n *);\n *\n * // Using Promises\n * async.some(['dir1/missing.txt','dir2/missing.txt','dir3/file5.txt'], fileExists)\n * .then( result => {\n *     console.log(result);\n *     // true\n *     // result is true since some file in the list exists\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * async.some(['dir1/missing.txt','dir2/missing.txt','dir4/missing.txt'], fileExists)\n * .then( result => {\n *     console.log(result);\n *     // false\n *     // result is false since none of the files exists\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.some(['dir1/missing.txt','dir2/missing.txt','dir3/file5.txt'], fileExists);\n *         console.log(result);\n *         // true\n *         // result is true since some file in the list exists\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n * async () => {\n *     try {\n *         let result = await async.some(['dir1/missing.txt','dir2/missing.txt','dir4/missing.txt'], fileExists);\n *         console.log(result);\n *         // false\n *         // result is false since none of the files exists\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction some(coll, iteratee, callback) {\n    return _createTester(Boolean, res => res)(eachOf$1, coll, iteratee, callback)\n}\nvar some$1 = awaitify(some, 3);\n\n/**\n * The same as [`some`]{@link module:Collections.some} but runs a maximum of `limit` async operations at a time.\n *\n * @name someLimit\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.some]{@link module:Collections.some}\n * @alias anyLimit\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - An async truth test to apply to each item\n * in the collections in parallel.\n * The iteratee should complete with a boolean `result` value.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called as soon as any\n * iteratee returns `true`, or after all the iteratee functions have finished.\n * Result will be either `true` or `false` depending on the values of the async\n * tests. Invoked with (err, result).\n * @returns {Promise} a promise, if no callback provided\n */\nfunction someLimit(coll, limit, iteratee, callback) {\n    return _createTester(Boolean, res => res)(eachOfLimit$2(limit), coll, iteratee, callback)\n}\nvar someLimit$1 = awaitify(someLimit, 4);\n\n/**\n * The same as [`some`]{@link module:Collections.some} but runs only a single async operation at a time.\n *\n * @name someSeries\n * @static\n * @memberOf module:Collections\n * @method\n * @see [async.some]{@link module:Collections.some}\n * @alias anySeries\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async truth test to apply to each item\n * in the collections in series.\n * The iteratee should complete with a boolean `result` value.\n * Invoked with (item, callback).\n * @param {Function} [callback] - A callback which is called as soon as any\n * iteratee returns `true`, or after all the iteratee functions have finished.\n * Result will be either `true` or `false` depending on the values of the async\n * tests. Invoked with (err, result).\n * @returns {Promise} a promise, if no callback provided\n */\nfunction someSeries(coll, iteratee, callback) {\n    return _createTester(Boolean, res => res)(eachOfSeries$1, coll, iteratee, callback)\n}\nvar someSeries$1 = awaitify(someSeries, 3);\n\n/**\n * Sorts a list by the results of running each `coll` value through an async\n * `iteratee`.\n *\n * @name sortBy\n * @static\n * @memberOf module:Collections\n * @method\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {AsyncFunction} iteratee - An async function to apply to each item in\n * `coll`.\n * The iteratee should complete with a value to use as the sort criteria as\n * its `result`.\n * Invoked with (item, callback).\n * @param {Function} callback - A callback which is called after all the\n * `iteratee` functions have finished, or an error occurs. Results is the items\n * from the original `coll` sorted by the values returned by the `iteratee`\n * calls. Invoked with (err, results).\n * @returns {Promise} a promise, if no callback passed\n * @example\n *\n * // bigfile.txt is a file that is 251100 bytes in size\n * // mediumfile.txt is a file that is 11000 bytes in size\n * // smallfile.txt is a file that is 121 bytes in size\n *\n * // asynchronous function that returns the file size in bytes\n * function getFileSizeInBytes(file, callback) {\n *     fs.stat(file, function(err, stat) {\n *         if (err) {\n *             return callback(err);\n *         }\n *         callback(null, stat.size);\n *     });\n * }\n *\n * // Using callbacks\n * async.sortBy(['mediumfile.txt','smallfile.txt','bigfile.txt'], getFileSizeInBytes,\n *     function(err, results) {\n *         if (err) {\n *             console.log(err);\n *         } else {\n *             console.log(results);\n *             // results is now the original array of files sorted by\n *             // file size (ascending by default), e.g.\n *             // [ 'smallfile.txt', 'mediumfile.txt', 'bigfile.txt']\n *         }\n *     }\n * );\n *\n * // By modifying the callback parameter the\n * // sorting order can be influenced:\n *\n * // ascending order\n * async.sortBy(['mediumfile.txt','smallfile.txt','bigfile.txt'], function(file, callback) {\n *     getFileSizeInBytes(file, function(getFileSizeErr, fileSize) {\n *         if (getFileSizeErr) return callback(getFileSizeErr);\n *         callback(null, fileSize);\n *     });\n * }, function(err, results) {\n *         if (err) {\n *             console.log(err);\n *         } else {\n *             console.log(results);\n *             // results is now the original array of files sorted by\n *             // file size (ascending by default), e.g.\n *             // [ 'smallfile.txt', 'mediumfile.txt', 'bigfile.txt']\n *         }\n *     }\n * );\n *\n * // descending order\n * async.sortBy(['bigfile.txt','mediumfile.txt','smallfile.txt'], function(file, callback) {\n *     getFileSizeInBytes(file, function(getFileSizeErr, fileSize) {\n *         if (getFileSizeErr) {\n *             return callback(getFileSizeErr);\n *         }\n *         callback(null, fileSize * -1);\n *     });\n * }, function(err, results) {\n *         if (err) {\n *             console.log(err);\n *         } else {\n *             console.log(results);\n *             // results is now the original array of files sorted by\n *             // file size (ascending by default), e.g.\n *             // [ 'bigfile.txt', 'mediumfile.txt', 'smallfile.txt']\n *         }\n *     }\n * );\n *\n * // Error handling\n * async.sortBy(['mediumfile.txt','smallfile.txt','missingfile.txt'], getFileSizeInBytes,\n *     function(err, results) {\n *         if (err) {\n *             console.log(err);\n *             // [ Error: ENOENT: no such file or directory ]\n *         } else {\n *             console.log(results);\n *         }\n *     }\n * );\n *\n * // Using Promises\n * async.sortBy(['mediumfile.txt','smallfile.txt','bigfile.txt'], getFileSizeInBytes)\n * .then( results => {\n *     console.log(results);\n *     // results is now the original array of files sorted by\n *     // file size (ascending by default), e.g.\n *     // [ 'smallfile.txt', 'mediumfile.txt', 'bigfile.txt']\n * }).catch( err => {\n *     console.log(err);\n * });\n *\n * // Error handling\n * async.sortBy(['mediumfile.txt','smallfile.txt','missingfile.txt'], getFileSizeInBytes)\n * .then( results => {\n *     console.log(results);\n * }).catch( err => {\n *     console.log(err);\n *     // [ Error: ENOENT: no such file or directory ]\n * });\n *\n * // Using async/await\n * (async () => {\n *     try {\n *         let results = await async.sortBy(['bigfile.txt','mediumfile.txt','smallfile.txt'], getFileSizeInBytes);\n *         console.log(results);\n *         // results is now the original array of files sorted by\n *         // file size (ascending by default), e.g.\n *         // [ 'smallfile.txt', 'mediumfile.txt', 'bigfile.txt']\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * })();\n *\n * // Error handling\n * async () => {\n *     try {\n *         let results = await async.sortBy(['missingfile.txt','mediumfile.txt','smallfile.txt'], getFileSizeInBytes);\n *         console.log(results);\n *     }\n *     catch (err) {\n *         console.log(err);\n *         // [ Error: ENOENT: no such file or directory ]\n *     }\n * }\n *\n */\nfunction sortBy (coll, iteratee, callback) {\n    var _iteratee = wrapAsync(iteratee);\n    return map$1(coll, (x, iterCb) => {\n        _iteratee(x, (err, criteria) => {\n            if (err) return iterCb(err);\n            iterCb(err, {value: x, criteria});\n        });\n    }, (err, results) => {\n        if (err) return callback(err);\n        callback(null, results.sort(comparator).map(v => v.value));\n    });\n\n    function comparator(left, right) {\n        var a = left.criteria, b = right.criteria;\n        return a < b ? -1 : a > b ? 1 : 0;\n    }\n}\nvar sortBy$1 = awaitify(sortBy, 3);\n\n/**\n * Sets a time limit on an asynchronous function. If the function does not call\n * its callback within the specified milliseconds, it will be called with a\n * timeout error. The code property for the error object will be `'ETIMEDOUT'`.\n *\n * @name timeout\n * @static\n * @memberOf module:Utils\n * @method\n * @category Util\n * @param {AsyncFunction} asyncFn - The async function to limit in time.\n * @param {number} milliseconds - The specified time limit.\n * @param {*} [info] - Any variable you want attached (`string`, `object`, etc)\n * to timeout Error for more information..\n * @returns {AsyncFunction} Returns a wrapped function that can be used with any\n * of the control flow functions.\n * Invoke this function with the same parameters as you would `asyncFunc`.\n * @example\n *\n * function myFunction(foo, callback) {\n *     doAsyncTask(foo, function(err, data) {\n *         // handle errors\n *         if (err) return callback(err);\n *\n *         // do some stuff ...\n *\n *         // return processed data\n *         return callback(null, data);\n *     });\n * }\n *\n * var wrapped = async.timeout(myFunction, 1000);\n *\n * // call `wrapped` as you would `myFunction`\n * wrapped({ bar: 'bar' }, function(err, data) {\n *     // if `myFunction` takes < 1000 ms to execute, `err`\n *     // and `data` will have their expected values\n *\n *     // else `err` will be an Error with the code 'ETIMEDOUT'\n * });\n */\nfunction timeout(asyncFn, milliseconds, info) {\n    var fn = wrapAsync(asyncFn);\n\n    return initialParams((args, callback) => {\n        var timedOut = false;\n        var timer;\n\n        function timeoutCallback() {\n            var name = asyncFn.name || 'anonymous';\n            var error  = new Error('Callback function \"' + name + '\" timed out.');\n            error.code = 'ETIMEDOUT';\n            if (info) {\n                error.info = info;\n            }\n            timedOut = true;\n            callback(error);\n        }\n\n        args.push((...cbArgs) => {\n            if (!timedOut) {\n                callback(...cbArgs);\n                clearTimeout(timer);\n            }\n        });\n\n        // setup timer and call original function\n        timer = setTimeout(timeoutCallback, milliseconds);\n        fn(...args);\n    });\n}\n\nfunction range(size) {\n    var result = Array(size);\n    while (size--) {\n        result[size] = size;\n    }\n    return result;\n}\n\n/**\n * The same as [times]{@link module:ControlFlow.times} but runs a maximum of `limit` async operations at a\n * time.\n *\n * @name timesLimit\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.times]{@link module:ControlFlow.times}\n * @category Control Flow\n * @param {number} count - The number of times to run the function.\n * @param {number} limit - The maximum number of async operations at a time.\n * @param {AsyncFunction} iteratee - The async function to call `n` times.\n * Invoked with the iteration index and a callback: (n, next).\n * @param {Function} callback - see [async.map]{@link module:Collections.map}.\n * @returns {Promise} a promise, if no callback is provided\n */\nfunction timesLimit(count, limit, iteratee, callback) {\n    var _iteratee = wrapAsync(iteratee);\n    return mapLimit$1(range(count), limit, _iteratee, callback);\n}\n\n/**\n * Calls the `iteratee` function `n` times, and accumulates results in the same\n * manner you would use with [map]{@link module:Collections.map}.\n *\n * @name times\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.map]{@link module:Collections.map}\n * @category Control Flow\n * @param {number} n - The number of times to run the function.\n * @param {AsyncFunction} iteratee - The async function to call `n` times.\n * Invoked with the iteration index and a callback: (n, next).\n * @param {Function} callback - see {@link module:Collections.map}.\n * @returns {Promise} a promise, if no callback is provided\n * @example\n *\n * // Pretend this is some complicated async factory\n * var createUser = function(id, callback) {\n *     callback(null, {\n *         id: 'user' + id\n *     });\n * };\n *\n * // generate 5 users\n * async.times(5, function(n, next) {\n *     createUser(n, function(err, user) {\n *         next(err, user);\n *     });\n * }, function(err, users) {\n *     // we should now have 5 users\n * });\n */\nfunction times (n, iteratee, callback) {\n    return timesLimit(n, Infinity, iteratee, callback)\n}\n\n/**\n * The same as [times]{@link module:ControlFlow.times} but runs only a single async operation at a time.\n *\n * @name timesSeries\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.times]{@link module:ControlFlow.times}\n * @category Control Flow\n * @param {number} n - The number of times to run the function.\n * @param {AsyncFunction} iteratee - The async function to call `n` times.\n * Invoked with the iteration index and a callback: (n, next).\n * @param {Function} callback - see {@link module:Collections.map}.\n * @returns {Promise} a promise, if no callback is provided\n */\nfunction timesSeries (n, iteratee, callback) {\n    return timesLimit(n, 1, iteratee, callback)\n}\n\n/**\n * A relative of `reduce`.  Takes an Object or Array, and iterates over each\n * element in parallel, each step potentially mutating an `accumulator` value.\n * The type of the accumulator defaults to the type of collection passed in.\n *\n * @name transform\n * @static\n * @memberOf module:Collections\n * @method\n * @category Collection\n * @param {Array|Iterable|AsyncIterable|Object} coll - A collection to iterate over.\n * @param {*} [accumulator] - The initial state of the transform.  If omitted,\n * it will default to an empty Object or Array, depending on the type of `coll`\n * @param {AsyncFunction} iteratee - A function applied to each item in the\n * collection that potentially modifies the accumulator.\n * Invoked with (accumulator, item, key, callback).\n * @param {Function} [callback] - A callback which is called after all the\n * `iteratee` functions have finished. Result is the transformed accumulator.\n * Invoked with (err, result).\n * @returns {Promise} a promise, if no callback provided\n * @example\n *\n * // file1.txt is a file that is 1000 bytes in size\n * // file2.txt is a file that is 2000 bytes in size\n * // file3.txt is a file that is 3000 bytes in size\n *\n * // helper function that returns human-readable size format from bytes\n * function formatBytes(bytes, decimals = 2) {\n *   // implementation not included for brevity\n *   return humanReadbleFilesize;\n * }\n *\n * const fileList = ['file1.txt','file2.txt','file3.txt'];\n *\n * // asynchronous function that returns the file size, transformed to human-readable format\n * // e.g. 1024 bytes = 1KB, 1234 bytes = 1.21 KB, 1048576 bytes = 1MB, etc.\n * function transformFileSize(acc, value, key, callback) {\n *     fs.stat(value, function(err, stat) {\n *         if (err) {\n *             return callback(err);\n *         }\n *         acc[key] = formatBytes(stat.size);\n *         callback(null);\n *     });\n * }\n *\n * // Using callbacks\n * async.transform(fileList, transformFileSize, function(err, result) {\n *     if(err) {\n *         console.log(err);\n *     } else {\n *         console.log(result);\n *         // [ '1000 Bytes', '1.95 KB', '2.93 KB' ]\n *     }\n * });\n *\n * // Using Promises\n * async.transform(fileList, transformFileSize)\n * .then(result => {\n *     console.log(result);\n *     // [ '1000 Bytes', '1.95 KB', '2.93 KB' ]\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * (async () => {\n *     try {\n *         let result = await async.transform(fileList, transformFileSize);\n *         console.log(result);\n *         // [ '1000 Bytes', '1.95 KB', '2.93 KB' ]\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * })();\n *\n * @example\n *\n * // file1.txt is a file that is 1000 bytes in size\n * // file2.txt is a file that is 2000 bytes in size\n * // file3.txt is a file that is 3000 bytes in size\n *\n * // helper function that returns human-readable size format from bytes\n * function formatBytes(bytes, decimals = 2) {\n *   // implementation not included for brevity\n *   return humanReadbleFilesize;\n * }\n *\n * const fileMap = { f1: 'file1.txt', f2: 'file2.txt', f3: 'file3.txt' };\n *\n * // asynchronous function that returns the file size, transformed to human-readable format\n * // e.g. 1024 bytes = 1KB, 1234 bytes = 1.21 KB, 1048576 bytes = 1MB, etc.\n * function transformFileSize(acc, value, key, callback) {\n *     fs.stat(value, function(err, stat) {\n *         if (err) {\n *             return callback(err);\n *         }\n *         acc[key] = formatBytes(stat.size);\n *         callback(null);\n *     });\n * }\n *\n * // Using callbacks\n * async.transform(fileMap, transformFileSize, function(err, result) {\n *     if(err) {\n *         console.log(err);\n *     } else {\n *         console.log(result);\n *         // { f1: '1000 Bytes', f2: '1.95 KB', f3: '2.93 KB' }\n *     }\n * });\n *\n * // Using Promises\n * async.transform(fileMap, transformFileSize)\n * .then(result => {\n *     console.log(result);\n *     // { f1: '1000 Bytes', f2: '1.95 KB', f3: '2.93 KB' }\n * }).catch(err => {\n *     console.log(err);\n * });\n *\n * // Using async/await\n * async () => {\n *     try {\n *         let result = await async.transform(fileMap, transformFileSize);\n *         console.log(result);\n *         // { f1: '1000 Bytes', f2: '1.95 KB', f3: '2.93 KB' }\n *     }\n *     catch (err) {\n *         console.log(err);\n *     }\n * }\n *\n */\nfunction transform (coll, accumulator, iteratee, callback) {\n    if (arguments.length <= 3 && typeof accumulator === 'function') {\n        callback = iteratee;\n        iteratee = accumulator;\n        accumulator = Array.isArray(coll) ? [] : {};\n    }\n    callback = once(callback || promiseCallback());\n    var _iteratee = wrapAsync(iteratee);\n\n    eachOf$1(coll, (v, k, cb) => {\n        _iteratee(accumulator, v, k, cb);\n    }, err => callback(err, accumulator));\n    return callback[PROMISE_SYMBOL]\n}\n\n/**\n * It runs each task in series but stops whenever any of the functions were\n * successful. If one of the tasks were successful, the `callback` will be\n * passed the result of the successful task. If all tasks fail, the callback\n * will be passed the error and result (if any) of the final attempt.\n *\n * @name tryEach\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {Array|Iterable|AsyncIterable|Object} tasks - A collection containing functions to\n * run, each function is passed a `callback(err, result)` it must call on\n * completion with an error `err` (which can be `null`) and an optional `result`\n * value.\n * @param {Function} [callback] - An optional callback which is called when one\n * of the tasks has succeeded, or all have failed. It receives the `err` and\n * `result` arguments of the last attempt at completing the `task`. Invoked with\n * (err, results).\n * @returns {Promise} a promise, if no callback is passed\n * @example\n * async.tryEach([\n *     function getDataFromFirstWebsite(callback) {\n *         // Try getting the data from the first website\n *         callback(err, data);\n *     },\n *     function getDataFromSecondWebsite(callback) {\n *         // First website failed,\n *         // Try getting the data from the backup website\n *         callback(err, data);\n *     }\n * ],\n * // optional callback\n * function(err, results) {\n *     Now do something with the data.\n * });\n *\n */\nfunction tryEach(tasks, callback) {\n    var error = null;\n    var result;\n    return eachSeries$1(tasks, (task, taskCb) => {\n        wrapAsync(task)((err, ...args) => {\n            if (err === false) return taskCb(err);\n\n            if (args.length < 2) {\n                [result] = args;\n            } else {\n                result = args;\n            }\n            error = err;\n            taskCb(err ? null : {});\n        });\n    }, () => callback(error, result));\n}\n\nvar tryEach$1 = awaitify(tryEach);\n\n/**\n * Undoes a [memoize]{@link module:Utils.memoize}d function, reverting it to the original,\n * unmemoized form. Handy for testing.\n *\n * @name unmemoize\n * @static\n * @memberOf module:Utils\n * @method\n * @see [async.memoize]{@link module:Utils.memoize}\n * @category Util\n * @param {AsyncFunction} fn - the memoized function\n * @returns {AsyncFunction} a function that calls the original unmemoized function\n */\nfunction unmemoize(fn) {\n    return (...args) => {\n        return (fn.unmemoized || fn)(...args);\n    };\n}\n\n/**\n * Repeatedly call `iteratee`, while `test` returns `true`. Calls `callback` when\n * stopped, or an error occurs.\n *\n * @name whilst\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {AsyncFunction} test - asynchronous truth test to perform before each\n * execution of `iteratee`. Invoked with (callback).\n * @param {AsyncFunction} iteratee - An async function which is called each time\n * `test` passes. Invoked with (callback).\n * @param {Function} [callback] - A callback which is called after the test\n * function has failed and repeated execution of `iteratee` has stopped. `callback`\n * will be passed an error and any arguments passed to the final `iteratee`'s\n * callback. Invoked with (err, [results]);\n * @returns {Promise} a promise, if no callback is passed\n * @example\n *\n * var count = 0;\n * async.whilst(\n *     function test(cb) { cb(null, count < 5); },\n *     function iter(callback) {\n *         count++;\n *         setTimeout(function() {\n *             callback(null, count);\n *         }, 1000);\n *     },\n *     function (err, n) {\n *         // 5 seconds have passed, n = 5\n *     }\n * );\n */\nfunction whilst(test, iteratee, callback) {\n    callback = onlyOnce(callback);\n    var _fn = wrapAsync(iteratee);\n    var _test = wrapAsync(test);\n    var results = [];\n\n    function next(err, ...rest) {\n        if (err) return callback(err);\n        results = rest;\n        if (err === false) return;\n        _test(check);\n    }\n\n    function check(err, truth) {\n        if (err) return callback(err);\n        if (err === false) return;\n        if (!truth) return callback(null, ...results);\n        _fn(next);\n    }\n\n    return _test(check);\n}\nvar whilst$1 = awaitify(whilst, 3);\n\n/**\n * Repeatedly call `iteratee` until `test` returns `true`. Calls `callback` when\n * stopped, or an error occurs. `callback` will be passed an error and any\n * arguments passed to the final `iteratee`'s callback.\n *\n * The inverse of [whilst]{@link module:ControlFlow.whilst}.\n *\n * @name until\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @see [async.whilst]{@link module:ControlFlow.whilst}\n * @category Control Flow\n * @param {AsyncFunction} test - asynchronous truth test to perform before each\n * execution of `iteratee`. Invoked with (callback).\n * @param {AsyncFunction} iteratee - An async function which is called each time\n * `test` fails. Invoked with (callback).\n * @param {Function} [callback] - A callback which is called after the test\n * function has passed and repeated execution of `iteratee` has stopped. `callback`\n * will be passed an error and any arguments passed to the final `iteratee`'s\n * callback. Invoked with (err, [results]);\n * @returns {Promise} a promise, if a callback is not passed\n *\n * @example\n * const results = []\n * let finished = false\n * async.until(function test(cb) {\n *     cb(null, finished)\n * }, function iter(next) {\n *     fetchPage(url, (err, body) => {\n *         if (err) return next(err)\n *         results = results.concat(body.objects)\n *         finished = !!body.next\n *         next(err)\n *     })\n * }, function done (err) {\n *     // all pages have been fetched\n * })\n */\nfunction until(test, iteratee, callback) {\n    const _test = wrapAsync(test);\n    return whilst$1((cb) => _test((err, truth) => cb (err, !truth)), iteratee, callback);\n}\n\n/**\n * Runs the `tasks` array of functions in series, each passing their results to\n * the next in the array. However, if any of the `tasks` pass an error to their\n * own callback, the next function is not executed, and the main `callback` is\n * immediately called with the error.\n *\n * @name waterfall\n * @static\n * @memberOf module:ControlFlow\n * @method\n * @category Control Flow\n * @param {Array} tasks - An array of [async functions]{@link AsyncFunction}\n * to run.\n * Each function should complete with any number of `result` values.\n * The `result` values will be passed as arguments, in order, to the next task.\n * @param {Function} [callback] - An optional callback to run once all the\n * functions have completed. This will be passed the results of the last task's\n * callback. Invoked with (err, [results]).\n * @returns {Promise} a promise, if a callback is omitted\n * @example\n *\n * async.waterfall([\n *     function(callback) {\n *         callback(null, 'one', 'two');\n *     },\n *     function(arg1, arg2, callback) {\n *         // arg1 now equals 'one' and arg2 now equals 'two'\n *         callback(null, 'three');\n *     },\n *     function(arg1, callback) {\n *         // arg1 now equals 'three'\n *         callback(null, 'done');\n *     }\n * ], function (err, result) {\n *     // result now equals 'done'\n * });\n *\n * // Or, with named functions:\n * async.waterfall([\n *     myFirstFunction,\n *     mySecondFunction,\n *     myLastFunction,\n * ], function (err, result) {\n *     // result now equals 'done'\n * });\n * function myFirstFunction(callback) {\n *     callback(null, 'one', 'two');\n * }\n * function mySecondFunction(arg1, arg2, callback) {\n *     // arg1 now equals 'one' and arg2 now equals 'two'\n *     callback(null, 'three');\n * }\n * function myLastFunction(arg1, callback) {\n *     // arg1 now equals 'three'\n *     callback(null, 'done');\n * }\n */\nfunction waterfall (tasks, callback) {\n    callback = once(callback);\n    if (!Array.isArray(tasks)) return callback(new Error('First argument to waterfall must be an array of functions'));\n    if (!tasks.length) return callback();\n    var taskIndex = 0;\n\n    function nextTask(args) {\n        var task = wrapAsync(tasks[taskIndex++]);\n        task(...args, onlyOnce(next));\n    }\n\n    function next(err, ...args) {\n        if (err === false) return\n        if (err || taskIndex === tasks.length) {\n            return callback(err, ...args);\n        }\n        nextTask(args);\n    }\n\n    nextTask([]);\n}\n\nvar waterfall$1 = awaitify(waterfall);\n\n/**\n * An \"async function\" in the context of Async is an asynchronous function with\n * a variable number of parameters, with the final parameter being a callback.\n * (`function (arg1, arg2, ..., callback) {}`)\n * The final callback is of the form `callback(err, results...)`, which must be\n * called once the function is completed.  The callback should be called with a\n * Error as its first argument to signal that an error occurred.\n * Otherwise, if no error occurred, it should be called with `null` as the first\n * argument, and any additional `result` arguments that may apply, to signal\n * successful completion.\n * The callback must be called exactly once, ideally on a later tick of the\n * JavaScript event loop.\n *\n * This type of function is also referred to as a \"Node-style async function\",\n * or a \"continuation passing-style function\" (CPS). Most of the methods of this\n * library are themselves CPS/Node-style async functions, or functions that\n * return CPS/Node-style async functions.\n *\n * Wherever we accept a Node-style async function, we also directly accept an\n * [ES2017 `async` function]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/async_function}.\n * In this case, the `async` function will not be passed a final callback\n * argument, and any thrown error will be used as the `err` argument of the\n * implicit callback, and the return value will be used as the `result` value.\n * (i.e. a `rejected` of the returned Promise becomes the `err` callback\n * argument, and a `resolved` value becomes the `result`.)\n *\n * Note, due to JavaScript limitations, we can only detect native `async`\n * functions and not transpilied implementations.\n * Your environment must have `async`/`await` support for this to work.\n * (e.g. Node > v7.6, or a recent version of a modern browser).\n * If you are using `async` functions through a transpiler (e.g. Babel), you\n * must still wrap the function with [asyncify]{@link module:Utils.asyncify},\n * because the `async function` will be compiled to an ordinary function that\n * returns a promise.\n *\n * @typedef {Function} AsyncFunction\n * @static\n */\n\n\nvar index = {\n    apply,\n    applyEach,\n    applyEachSeries,\n    asyncify,\n    auto,\n    autoInject,\n    cargo: cargo$1,\n    cargoQueue: cargo,\n    compose,\n    concat: concat$1,\n    concatLimit: concatLimit$1,\n    concatSeries: concatSeries$1,\n    constant: constant$1,\n    detect: detect$1,\n    detectLimit: detectLimit$1,\n    detectSeries: detectSeries$1,\n    dir,\n    doUntil,\n    doWhilst: doWhilst$1,\n    each,\n    eachLimit: eachLimit$1,\n    eachOf: eachOf$1,\n    eachOfLimit: eachOfLimit$1,\n    eachOfSeries: eachOfSeries$1,\n    eachSeries: eachSeries$1,\n    ensureAsync,\n    every: every$1,\n    everyLimit: everyLimit$1,\n    everySeries: everySeries$1,\n    filter: filter$1,\n    filterLimit: filterLimit$1,\n    filterSeries: filterSeries$1,\n    forever: forever$1,\n    groupBy,\n    groupByLimit: groupByLimit$1,\n    groupBySeries,\n    log,\n    map: map$1,\n    mapLimit: mapLimit$1,\n    mapSeries: mapSeries$1,\n    mapValues,\n    mapValuesLimit: mapValuesLimit$1,\n    mapValuesSeries,\n    memoize,\n    nextTick,\n    parallel,\n    parallelLimit,\n    priorityQueue,\n    queue,\n    race: race$1,\n    reduce: reduce$1,\n    reduceRight,\n    reflect,\n    reflectAll,\n    reject: reject$1,\n    rejectLimit: rejectLimit$1,\n    rejectSeries: rejectSeries$1,\n    retry,\n    retryable,\n    seq,\n    series,\n    setImmediate: setImmediate$1,\n    some: some$1,\n    someLimit: someLimit$1,\n    someSeries: someSeries$1,\n    sortBy: sortBy$1,\n    timeout,\n    times,\n    timesLimit,\n    timesSeries,\n    transform,\n    tryEach: tryEach$1,\n    unmemoize,\n    until,\n    waterfall: waterfall$1,\n    whilst: whilst$1,\n\n    // aliases\n    all: every$1,\n    allLimit: everyLimit$1,\n    allSeries: everySeries$1,\n    any: some$1,\n    anyLimit: someLimit$1,\n    anySeries: someSeries$1,\n    find: detect$1,\n    findLimit: detectLimit$1,\n    findSeries: detectSeries$1,\n    flatMap: concat$1,\n    flatMapLimit: concatLimit$1,\n    flatMapSeries: concatSeries$1,\n    forEach: each,\n    forEachSeries: eachSeries$1,\n    forEachLimit: eachLimit$1,\n    forEachOf: eachOf$1,\n    forEachOfSeries: eachOfSeries$1,\n    forEachOfLimit: eachOfLimit$1,\n    inject: reduce$1,\n    foldl: reduce$1,\n    foldr: reduceRight,\n    select: filter$1,\n    selectLimit: filterLimit$1,\n    selectSeries: filterSeries$1,\n    wrapSync: asyncify,\n    during: whilst$1,\n    doDuring: doWhilst$1\n};\n\nexport { every$1 as all, everyLimit$1 as allLimit, everySeries$1 as allSeries, some$1 as any, someLimit$1 as anyLimit, someSeries$1 as anySeries, apply, applyEach, applyEachSeries, asyncify, auto, autoInject, cargo$1 as cargo, cargo as cargoQueue, compose, concat$1 as concat, concatLimit$1 as concatLimit, concatSeries$1 as concatSeries, constant$1 as constant, index as default, detect$1 as detect, detectLimit$1 as detectLimit, detectSeries$1 as detectSeries, dir, doWhilst$1 as doDuring, doUntil, doWhilst$1 as doWhilst, whilst$1 as during, each, eachLimit$1 as eachLimit, eachOf$1 as eachOf, eachOfLimit$1 as eachOfLimit, eachOfSeries$1 as eachOfSeries, eachSeries$1 as eachSeries, ensureAsync, every$1 as every, everyLimit$1 as everyLimit, everySeries$1 as everySeries, filter$1 as filter, filterLimit$1 as filterLimit, filterSeries$1 as filterSeries, detect$1 as find, detectLimit$1 as findLimit, detectSeries$1 as findSeries, concat$1 as flatMap, concatLimit$1 as flatMapLimit, concatSeries$1 as flatMapSeries, reduce$1 as foldl, reduceRight as foldr, each as forEach, eachLimit$1 as forEachLimit, eachOf$1 as forEachOf, eachOfLimit$1 as forEachOfLimit, eachOfSeries$1 as forEachOfSeries, eachSeries$1 as forEachSeries, forever$1 as forever, groupBy, groupByLimit$1 as groupByLimit, groupBySeries, reduce$1 as inject, log, map$1 as map, mapLimit$1 as mapLimit, mapSeries$1 as mapSeries, mapValues, mapValuesLimit$1 as mapValuesLimit, mapValuesSeries, memoize, nextTick, parallel, parallelLimit, priorityQueue, queue, race$1 as race, reduce$1 as reduce, reduceRight, reflect, reflectAll, reject$1 as reject, rejectLimit$1 as rejectLimit, rejectSeries$1 as rejectSeries, retry, retryable, filter$1 as select, filterLimit$1 as selectLimit, filterSeries$1 as selectSeries, seq, series, setImmediate$1 as setImmediate, some$1 as some, someLimit$1 as someLimit, someSeries$1 as someSeries, sortBy$1 as sortBy, timeout, times, timesLimit, timesSeries, transform, tryEach$1 as tryEach, unmemoize, until, waterfall$1 as waterfall, whilst$1 as whilst, asyncify as wrapSync };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4CC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD,SAAS,MAAM,EAAE,EAAE,GAAG,IAAI;IACtB,OAAO,CAAC,GAAG,WAAa,MAAM,SAAQ;AAC1C;AAEA,SAAS,cAAe,EAAE;IACtB,OAAO,SAAU,GAAG,KAAI,YAAY,GAAZ;QACpB,IAAI,WAAW,KAAK,GAAG;QACvB,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM;IAC/B;AACJ;AAEA,wBAAwB,GAExB,IAAI,oBAAoB,OAAO,mBAAmB,cAAc;AAChE,IAAI,kBAAkB,OAAO,iBAAiB,cAAc;AAC5D,IAAI,cAAc,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK;AAE7E,SAAS,SAAS,EAAE;IAChB,WAAW,IAAI;AACnB;AAEA,SAAS,KAAK,KAAK;IACf,OAAO,CAAC,IAAI,GAAG,OAAS,MAAM,IAAM,MAAM;AAC9C;AAEA,IAAI;AAEJ,IAAI,mBAAmB;IACnB,WAAW;AACf,OAAO,IAAI,iBAAiB;IACxB,WAAW;AACf,OAAO,IAAI,aAAa;IACpB,WAAW,QAAQ,QAAQ;AAC/B,OAAO;IACH,WAAW;AACf;AAEA,IAAI,iBAAiB,KAAK;AAE1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAuDC,GACD,SAAS,SAAS,IAAI;IAClB,IAAI,QAAQ,OAAO;QACf,OAAO,SAAU,GAAG,KAAI,YAAY,GAAZ;YACpB,MAAM,WAAW,KAAK,GAAG;YACzB,MAAM,UAAU,KAAK,KAAK,CAAC,IAAI,EAAE;YACjC,OAAO,cAAc,SAAS;QAClC;IACJ;IAEA,OAAO,cAAc,SAAU,IAAI,EAAE,QAAQ;QACzC,IAAI;QACJ,IAAI;YACA,SAAS,KAAK,KAAK,CAAC,IAAI,EAAE;QAC9B,EAAE,OAAO,GAAG;YACR,OAAO,SAAS;QACpB;QACA,8BAA8B;QAC9B,IAAI,UAAU,OAAO,OAAO,IAAI,KAAK,YAAY;YAC7C,OAAO,cAAc,QAAQ;QACjC,OAAO;YACH,SAAS,MAAM;QACnB;IACJ;AACJ;AAEA,SAAS,cAAc,OAAO,EAAE,QAAQ;IACpC,OAAO,QAAQ,IAAI,CAAC,CAAA;QAChB,eAAe,UAAU,MAAM;IACnC,GAAG,CAAA;QACC,eAAe,UAAU,OAAO,CAAC,eAAe,SAAS,IAAI,OAAO,IAAI,MAAM,IAAI,MAAM;IAC5F;AACJ;AAEA,SAAS,eAAe,QAAQ,EAAE,KAAK,EAAE,KAAK;IAC1C,IAAI;QACA,SAAS,OAAO;IACpB,EAAE,OAAO,KAAK;QACV,eAAe,CAAA;YAAO,MAAM;QAAE,GAAG;IACrC;AACJ;AAEA,SAAS,QAAQ,EAAE;IACf,OAAO,EAAE,CAAC,OAAO,WAAW,CAAC,KAAK;AACtC;AAEA,SAAS,iBAAiB,EAAE;IACxB,OAAO,EAAE,CAAC,OAAO,WAAW,CAAC,KAAK;AACtC;AAEA,SAAS,gBAAgB,GAAG;IACxB,OAAO,OAAO,GAAG,CAAC,OAAO,aAAa,CAAC,KAAK;AAChD;AAEA,SAAS,UAAU,OAAO;IACtB,IAAI,OAAO,YAAY,YAAY,MAAM,IAAI,MAAM;IACnD,OAAO,QAAQ,WAAW,SAAS,WAAW;AAClD;AAEA,sCAAsC;AACtC,iDAAiD;AACjD,SAAS,SAAU,OAAO,EAAE,KAAK;IAC7B,IAAI,CAAC,OAAO,QAAQ,QAAQ,MAAM;IAClC,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM;IAC5B,SAAS,UAAW,GAAG,IAAI;QACvB,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,YAAY;YACvC,OAAO,QAAQ,KAAK,CAAC,IAAI,EAAE;QAC/B;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,GAAG;gBACvB,IAAI,KAAK,OAAO,OAAO;gBACvB,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS,MAAM,CAAC,EAAE;YAClD;YACA,QAAQ,KAAK,CAAC,IAAI,EAAE;QACxB;IACJ;IAEA,OAAO;AACX;AAEA,SAAS,YAAa,MAAM;IACxB,OAAO,SAAS,UAAU,GAAG,EAAE,GAAG,QAAQ;QACtC,MAAM,KAAK,SAAS,SAAU,QAAQ;YAClC,IAAI,OAAO,IAAI;YACf,OAAO,OAAO,KAAK,CAAC,IAAI;gBACpB,UAAU,IAAI,KAAK,CAAC,MAAM,SAAS,MAAM,CAAC;YAC9C,GAAG;QACP;QACA,OAAO;IACX;AACJ;AAEA,SAAS,UAAU,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ;IAC9C,MAAM,OAAO,EAAE;IACf,IAAI,UAAU,EAAE;IAChB,IAAI,UAAU;IACd,IAAI,YAAY,UAAU;IAE1B,OAAO,OAAO,KAAK,CAAC,OAAO,GAAG;QAC1B,IAAI,QAAQ;QACZ,UAAU,OAAO,CAAC,KAAK;YACnB,OAAO,CAAC,MAAM,GAAG;YACjB,OAAO;QACX;IACJ,GAAG,CAAA;QACC,SAAS,KAAK;IAClB;AACJ;AAEA,SAAS,YAAY,KAAK;IACtB,OAAO,SACH,OAAO,MAAM,MAAM,KAAK,YACxB,MAAM,MAAM,IAAI,KAChB,MAAM,MAAM,GAAG,MAAM;AAC7B;AAEA,mEAAmE;AACnE,mBAAmB;AACnB,MAAM,YAAY,CAAC;AAEnB,SAAS,KAAK,EAAE;IACZ,SAAS,QAAS,GAAG,IAAI;QACrB,IAAI,OAAO,MAAM;QACjB,IAAI,SAAS;QACb,KAAK;QACL,OAAO,KAAK,CAAC,IAAI,EAAE;IACvB;IACA,OAAO,MAAM,CAAC,SAAS;IACvB,OAAO;AACX;AAEA,SAAS,YAAa,IAAI;IACtB,OAAO,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,OAAO,QAAQ,CAAC;AACzD;AAEA,SAAS,oBAAoB,IAAI;IAC7B,IAAI,IAAI,CAAC;IACT,IAAI,MAAM,KAAK,MAAM;IACrB,OAAO,SAAS;QACZ,OAAO,EAAE,IAAI,MAAM;YAAC,OAAO,IAAI,CAAC,EAAE;YAAE,KAAK;QAAC,IAAI;IAClD;AACJ;AAEA,SAAS,qBAAqB,QAAQ;IAClC,IAAI,IAAI,CAAC;IACT,OAAO,SAAS;QACZ,IAAI,OAAO,SAAS,IAAI;QACxB,IAAI,KAAK,IAAI,EACT,OAAO;QACX;QACA,OAAO;YAAC,OAAO,KAAK,KAAK;YAAE,KAAK;QAAC;IACrC;AACJ;AAEA,SAAS,qBAAqB,GAAG;IAC7B,IAAI,QAAQ,MAAM,OAAO,IAAI,CAAC,OAAO,EAAE;IACvC,IAAI,IAAI,CAAC;IACT,IAAI,MAAM,MAAM,MAAM;IACtB,OAAO,SAAS;QACZ,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE;QACpB,IAAI,QAAQ,aAAa;YACrB,OAAO;QACX;QACA,OAAO,IAAI,MAAM;YAAC,OAAO,GAAG,CAAC,IAAI;YAAE;QAAG,IAAI;IAC9C;AACJ;AAEA,SAAS,eAAe,IAAI;IACxB,IAAI,YAAY,OAAO;QACnB,OAAO,oBAAoB;IAC/B;IAEA,IAAI,WAAW,YAAY;IAC3B,OAAO,WAAW,qBAAqB,YAAY,qBAAqB;AAC5E;AAEA,SAAS,SAAS,EAAE;IAChB,OAAO,SAAU,GAAG,IAAI;QACpB,IAAI,OAAO,MAAM,MAAM,IAAI,MAAM;QACjC,IAAI,SAAS;QACb,KAAK;QACL,OAAO,KAAK,CAAC,IAAI,EAAE;IACvB;AACJ;AAEA,uBAAuB;AACvB,SAAS,iBAAiB,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAC1D,IAAI,OAAO;IACX,IAAI,WAAW;IACf,IAAI,WAAW;IACf,IAAI,UAAU;IACd,IAAI,MAAM;IAEV,SAAS;QACL,0BAA0B;QAC1B,IAAI,WAAW,SAAS,YAAY,MAAM;QAC1C,mCAAmC;QACnC,WAAW;QACX,UAAU,IAAI,GAAG,IAAI,CAAC,CAAC,EAAC,KAAK,EAAE,MAAM,QAAQ,EAAC;YAC1C,iCAAiC;YACjC,IAAI,YAAY,MAAM;YACtB,WAAW;YACX,IAAI,UAAU;gBACV,OAAO;gBACP,IAAI,WAAW,GAAG;oBACd,4BAA4B;oBAC5B,SAAS;gBACb;gBACA;YACJ;YACA;YACA,SAAS,OAAO,KAAK;YACrB;YACA;QACJ,GAAG,KAAK,CAAC;IACb;IAEA,SAAS,iBAAiB,GAAG,EAAE,MAAM;QACjC,iCAAiC;QACjC,WAAW;QACX,IAAI,UAAU;QACd,IAAI,KAAK,OAAO,YAAY;QAE5B,IAAI,QAAQ,OAAO;YACf,OAAO;YACP,WAAW;YACX;QACJ;QAEA,IAAI,WAAW,aAAc,QAAQ,WAAW,GAAI;YAChD,OAAO;YACP,4BAA4B;YAC5B,OAAO,SAAS;QACpB;QACA;IACJ;IAEA,SAAS,YAAY,GAAG;QACpB,IAAI,UAAU;QACd,WAAW;QACX,OAAO;QACP,SAAS;IACb;IAEA;AACJ;AAEA,IAAI,gBAAgB,CAAC;IACjB,OAAO,CAAC,KAAK,UAAU;QACnB,WAAW,KAAK;QAChB,IAAI,SAAS,GAAG;YACZ,MAAM,IAAI,WAAW;QACzB;QACA,IAAI,CAAC,KAAK;YACN,OAAO,SAAS;QACpB;QACA,IAAI,iBAAiB,MAAM;YACvB,OAAO,iBAAiB,KAAK,OAAO,UAAU;QAClD;QACA,IAAI,gBAAgB,MAAM;YACtB,OAAO,iBAAiB,GAAG,CAAC,OAAO,aAAa,CAAC,IAAI,OAAO,UAAU;QAC1E;QACA,IAAI,WAAW,eAAe;QAC9B,IAAI,OAAO;QACX,IAAI,WAAW;QACf,IAAI,UAAU;QACd,IAAI,UAAU;QAEd,SAAS,iBAAiB,GAAG,EAAE,KAAK;YAChC,IAAI,UAAU;YACd,WAAW;YACX,IAAI,KAAK;gBACL,OAAO;gBACP,SAAS;YACb,OACK,IAAI,QAAQ,OAAO;gBACpB,OAAO;gBACP,WAAW;YACf,OACK,IAAI,UAAU,aAAc,QAAQ,WAAW,GAAI;gBACpD,OAAO;gBACP,OAAO,SAAS;YACpB,OACK,IAAI,CAAC,SAAS;gBACf;YACJ;QACJ;QAEA,SAAS;YACL,UAAU;YACV,MAAO,UAAU,SAAS,CAAC,KAAM;gBAC7B,IAAI,OAAO;gBACX,IAAI,SAAS,MAAM;oBACf,OAAO;oBACP,IAAI,WAAW,GAAG;wBACd,SAAS;oBACb;oBACA;gBACJ;gBACA,WAAW;gBACX,SAAS,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE,SAAS;YAC5C;YACA,UAAU;QACd;QAEA;IACJ;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,YAAY,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAChD,OAAO,cAAc,OAAO,MAAM,UAAU,WAAW;AAC3D;AAEA,IAAI,gBAAgB,SAAS,aAAa;AAE1C,kDAAkD;AAClD,SAAS,gBAAgB,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC7C,WAAW,KAAK;IAChB,IAAI,QAAQ,GACR,YAAY,GACZ,EAAC,MAAM,EAAC,GAAG,MACX,WAAW;IACf,IAAI,WAAW,GAAG;QACd,SAAS;IACb;IAEA,SAAS,iBAAiB,GAAG,EAAE,KAAK;QAChC,IAAI,QAAQ,OAAO;YACf,WAAW;QACf;QACA,IAAI,aAAa,MAAM;QACvB,IAAI,KAAK;YACL,SAAS;QACb,OAAO,IAAI,AAAC,EAAE,cAAc,UAAW,UAAU,WAAW;YACxD,SAAS;QACb;IACJ;IAEA,MAAO,QAAQ,QAAQ,QAAS;QAC5B,SAAS,IAAI,CAAC,MAAM,EAAE,OAAO,SAAS;IAC1C;AACJ;AAEA,kFAAkF;AAClF,SAAS,cAAe,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC5C,OAAO,cAAc,MAAM,UAAU,UAAU;AACnD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4GC,GACD,SAAS,OAAO,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACpC,IAAI,uBAAuB,YAAY,QAAQ,kBAAkB;IACjE,OAAO,qBAAqB,MAAM,UAAU,WAAW;AAC3D;AAEA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoHC,GACD,SAAS,IAAK,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAClC,OAAO,UAAU,UAAU,MAAM,UAAU;AAC/C;AACA,IAAI,QAAQ,SAAS,KAAK;AAE1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsCC,GACD,IAAI,YAAY,YAAY;AAE5B;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS,aAAa,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC1C,OAAO,cAAc,MAAM,GAAG,UAAU;AAC5C;AACA,IAAI,iBAAiB,SAAS,cAAc;AAE5C;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,UAAW,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACxC,OAAO,UAAU,gBAAgB,MAAM,UAAU;AACrD;AACA,IAAI,cAAc,SAAS,WAAW;AAEtC;;;;;;;;;;;;;;;;;;CAkBC,GACD,IAAI,kBAAkB,YAAY;AAElC,MAAM,iBAAiB,OAAO;AAE9B,SAAS;IACL,IAAI,SAAS;IACb,SAAS,SAAU,GAAG,EAAE,GAAG,IAAI;QAC3B,IAAI,KAAK,OAAO,OAAO;QACvB,QAAQ,KAAK,MAAM,GAAG,IAAI,OAAO,IAAI,CAAC,EAAE;IAC5C;IAEA,QAAQ,CAAC,eAAe,GAAG,IAAI,QAAQ,CAAC,KAAK;QACzC,UAAU,KACV,SAAS;IACb;IAEA,OAAO;AACX;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgJC,GACD,SAAS,KAAK,KAAK,EAAE,WAAW,EAAE,QAAQ;IACtC,IAAI,OAAO,gBAAgB,UAAU;QACjC,2CAA2C;QAC3C,WAAW;QACX,cAAc;IAClB;IACA,WAAW,KAAK,YAAY;IAC5B,IAAI,WAAW,OAAO,IAAI,CAAC,OAAO,MAAM;IACxC,IAAI,CAAC,UAAU;QACX,OAAO,SAAS;IACpB;IACA,IAAI,CAAC,aAAa;QACd,cAAc;IAClB;IAEA,IAAI,UAAU,CAAC;IACf,IAAI,eAAe;IACnB,IAAI,WAAW;IACf,IAAI,WAAW;IAEf,IAAI,YAAY,OAAO,MAAM,CAAC;IAE9B,IAAI,aAAa,EAAE;IAEnB,uBAAuB;IACvB,IAAI,eAAe,EAAE,EAAE,+CAA+C;IACtE,2DAA2D;IAC3D,IAAI,wBAAwB,CAAC;IAE7B,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACvB,IAAI,OAAO,KAAK,CAAC,IAAI;QACrB,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;YACtB,kBAAkB;YAClB,YAAY,KAAK;gBAAC;aAAK;YACvB,aAAa,IAAI,CAAC;YAClB;QACJ;QAEA,IAAI,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;QAC/C,IAAI,wBAAwB,aAAa,MAAM;QAC/C,IAAI,0BAA0B,GAAG;YAC7B,YAAY,KAAK;YACjB,aAAa,IAAI,CAAC;YAClB;QACJ;QACA,qBAAqB,CAAC,IAAI,GAAG;QAE7B,aAAa,OAAO,CAAC,CAAA;YACjB,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;gBACxB,MAAM,IAAI,MAAM,sBAAsB,MAClC,sCACA,iBAAiB,UACjB,aAAa,IAAI,CAAC;YAC1B;YACA,YAAY,gBAAgB;gBACxB;gBACA,IAAI,0BAA0B,GAAG;oBAC7B,YAAY,KAAK;gBACrB;YACJ;QACJ;IACJ;IAEA;IACA;IAEA,SAAS,YAAY,GAAG,EAAE,IAAI;QAC1B,WAAW,IAAI,CAAC,IAAM,QAAQ,KAAK;IACvC;IAEA,SAAS;QACL,IAAI,UAAU;QACd,IAAI,WAAW,MAAM,KAAK,KAAK,iBAAiB,GAAG;YAC/C,OAAO,SAAS,MAAM;QAC1B;QACA,MAAM,WAAW,MAAM,IAAI,eAAe,YAAa;YACnD,IAAI,MAAM,WAAW,KAAK;YAC1B;QACJ;IAEJ;IAEA,SAAS,YAAY,QAAQ,EAAE,EAAE;QAC7B,IAAI,gBAAgB,SAAS,CAAC,SAAS;QACvC,IAAI,CAAC,eAAe;YAChB,gBAAgB,SAAS,CAAC,SAAS,GAAG,EAAE;QAC5C;QAEA,cAAc,IAAI,CAAC;IACvB;IAEA,SAAS,aAAa,QAAQ;QAC1B,IAAI,gBAAgB,SAAS,CAAC,SAAS,IAAI,EAAE;QAC7C,cAAc,OAAO,CAAC,CAAA,KAAM;QAC5B;IACJ;IAGA,SAAS,QAAQ,GAAG,EAAE,IAAI;QACtB,IAAI,UAAU;QAEd,IAAI,eAAe,SAAS,CAAC,KAAK,GAAG;YACjC;YACA,IAAI,QAAQ,OAAO;gBACf,WAAW;gBACX;YACJ;YACA,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,CAAC,OAAO,GAAG;YACf;YACA,IAAI,KAAK;gBACL,IAAI,cAAc,CAAC;gBACnB,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,CAAA;oBACzB,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;gBACrC;gBACA,WAAW,CAAC,IAAI,GAAG;gBACnB,WAAW;gBACX,YAAY,OAAO,MAAM,CAAC;gBAC1B,IAAI,UAAU;gBACd,SAAS,KAAK;YAClB,OAAO;gBACH,OAAO,CAAC,IAAI,GAAG;gBACf,aAAa;YACjB;QACJ;QAEA;QACA,IAAI,SAAS,UAAU,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC5C,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,OAAO,SAAS;QACpB,OAAO;YACH,OAAO;QACX;IACJ;IAEA,SAAS;QACL,mBAAmB;QACnB,uEAAuE;QACvE,8EAA8E;QAC9E,IAAI;QACJ,IAAI,UAAU;QACd,MAAO,aAAa,MAAM,CAAE;YACxB,cAAc,aAAa,GAAG;YAC9B;YACA,cAAc,aAAa,OAAO,CAAC,CAAA;gBAC/B,IAAI,EAAE,qBAAqB,CAAC,UAAU,KAAK,GAAG;oBAC1C,aAAa,IAAI,CAAC;gBACtB;YACJ;QACJ;QAEA,IAAI,YAAY,UAAU;YACtB,MAAM,IAAI,MACN;QAER;IACJ;IAEA,SAAS,cAAc,QAAQ;QAC3B,IAAI,SAAS,EAAE;QACf,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;YACvB,MAAM,OAAO,KAAK,CAAC,IAAI;YACvB,IAAI,MAAM,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,aAAa,GAAG;gBACpD,OAAO,IAAI,CAAC;YAChB;QACJ;QACA,OAAO;IACX;IAEA,OAAO,QAAQ,CAAC,eAAe;AACnC;AAEA,IAAI,UAAU;AACd,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,SAAS;AAEb,SAAS,cAAc,MAAM;IACzB,IAAI,WAAW;IACf,IAAI,QAAQ;IACZ,IAAI,kBAAkB,OAAO,OAAO,CAAC;IACrC,MAAO,QAAQ,OAAO,MAAM,CAAE;QAC1B,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,MAAM,CAAC,QAAM,EAAE,KAAK,KAAK;YAClD,iBAAiB;YACjB,IAAI,WAAW,OAAO,OAAO,CAAC,MAAM;YACpC,QAAQ,AAAC,aAAa,CAAC,IAAK,OAAO,MAAM,GAAG;QAChD,OAAO,IAAI,AAAC,oBAAoB,CAAC,KAAO,MAAM,CAAC,MAAM,KAAK,OAAS,MAAM,CAAC,QAAM,EAAE,KAAK,KAAM;YACzF,gBAAgB;YAChB,IAAI,WAAW,OAAO,OAAO,CAAC,MAAM;YACpC,IAAI,aAAa,CAAC,GAAG;gBACjB,QAAQ,WAAW;gBACnB,kBAAkB,OAAO,OAAO,CAAC,MAAM;YAC3C,OAAO;gBACH,YAAY,MAAM,CAAC,MAAM;gBACzB;YACJ;QACJ,OAAO;YACH,YAAY,MAAM,CAAC,MAAM;YACzB;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,YAAY,IAAI;IACrB,MAAM,MAAM,cAAc,KAAK,QAAQ;IACvC,IAAI,QAAQ,IAAI,KAAK,CAAC;IACtB,IAAI,CAAC,OAAO;QACR,QAAQ,IAAI,KAAK,CAAC;IACtB;IACA,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM,kDAAkD;IAC9E,IAAI,GAAG,KAAK,GAAG;IACf,OAAO,KACF,OAAO,CAAC,OAAO,IACf,KAAK,CAAC,cACN,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI;AAClD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkFC,GACD,SAAS,WAAW,KAAK,EAAE,QAAQ;IAC/B,IAAI,WAAW,CAAC;IAEhB,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACvB,IAAI,SAAS,KAAK,CAAC,IAAI;QACvB,IAAI;QACJ,IAAI,YAAY,QAAQ;QACxB,IAAI,YACA,AAAC,CAAC,aAAa,OAAO,MAAM,KAAK,KAChC,aAAa,OAAO,MAAM,KAAK;QAEpC,IAAI,MAAM,OAAO,CAAC,SAAS;YACvB,SAAS;mBAAI;aAAO;YACpB,SAAS,OAAO,GAAG;YAEnB,QAAQ,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,UAAU;QAChE,OAAO,IAAI,WAAW;YAClB,0CAA0C;YAC1C,QAAQ,CAAC,IAAI,GAAG;QACpB,OAAO;YACH,SAAS,YAAY;YACrB,IAAI,AAAC,OAAO,MAAM,KAAK,KAAK,CAAC,aAAc,OAAO,MAAM,KAAK,GAAG;gBAC5D,MAAM,IAAI,MAAM;YACpB;YAEA,wBAAwB;YACxB,IAAI,CAAC,WAAW,OAAO,GAAG;YAE1B,QAAQ,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC;QAClC;QAEA,SAAS,QAAQ,OAAO,EAAE,MAAM;YAC5B,IAAI,UAAU,OAAO,GAAG,CAAC,CAAA,OAAQ,OAAO,CAAC,KAAK;YAC9C,QAAQ,IAAI,CAAC;YACb,UAAU,WAAW;QACzB;IACJ;IAEA,OAAO,KAAK,UAAU;AAC1B;AAEA,8FAA8F;AAC9F,kGAAkG;AAClG,sFAAsF;AACtF,qBAAqB;AACrB,MAAM;IACF,aAAc;QACV,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACxB,IAAI,CAAC,MAAM,GAAG;IAClB;IAEA,WAAW,IAAI,EAAE;QACb,IAAI,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;aACpC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QAC1B,IAAI,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;aACpC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QAE1B,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;QACxB,IAAI,CAAC,MAAM,IAAI;QACf,OAAO;IACX;IAEA,QAAS;QACL,MAAM,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC,KAAK;QAC3B,OAAO,IAAI;IACf;IAEA,YAAY,IAAI,EAAE,OAAO,EAAE;QACvB,QAAQ,IAAI,GAAG;QACf,QAAQ,IAAI,GAAG,KAAK,IAAI;QACxB,IAAI,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG;aAC3B,IAAI,CAAC,IAAI,GAAG;QACjB,KAAK,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,IAAI;IACnB;IAEA,aAAa,IAAI,EAAE,OAAO,EAAE;QACxB,QAAQ,IAAI,GAAG,KAAK,IAAI;QACxB,QAAQ,IAAI,GAAG;QACf,IAAI,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG;aAC3B,IAAI,CAAC,IAAI,GAAG;QACjB,KAAK,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,IAAI;IACnB;IAEA,QAAQ,IAAI,EAAE;QACV,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;aACvC,WAAW,IAAI,EAAE;IAC1B;IAEA,KAAK,IAAI,EAAE;QACP,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;aACtC,WAAW,IAAI,EAAE;IAC1B;IAEA,QAAQ;QACJ,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;IACjD;IAEA,MAAM;QACF,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;IACjD;IAEA,UAAU;QACN,OAAO;eAAI,IAAI;SAAC;IACpB;IAEA,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAI;QAClB,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,MAAO,IAAK;YACR,MAAM,IAAI,IAAI;YACd,MAAM,IAAI,IAAI;QAClB;IACJ;IAEA,OAAQ,MAAM,EAAE;QACZ,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,MAAM,KAAM;YACR,IAAI,EAAC,IAAI,EAAC,GAAG;YACb,IAAI,OAAO,OAAO;gBACd,IAAI,CAAC,UAAU,CAAC;YACpB;YACA,OAAO;QACX;QACA,OAAO,IAAI;IACf;AACJ;AAEA,SAAS,WAAW,GAAG,EAAE,IAAI;IACzB,IAAI,MAAM,GAAG;IACb,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG;AAC1B;AAEA,SAAS,QAAQ,MAAM,EAAE,WAAW,EAAE,OAAO;IACzC,IAAI,eAAe,MAAM;QACrB,cAAc;IAClB,OACK,IAAG,gBAAgB,GAAG;QACvB,MAAM,IAAI,WAAW;IACzB;IAEA,IAAI,UAAU,UAAU;IACxB,IAAI,aAAa;IACjB,IAAI,cAAc,EAAE;IACpB,MAAM,SAAS;QACX,OAAO,EAAE;QACT,OAAO,EAAE;QACT,WAAW,EAAE;QACb,aAAa,EAAE;QACf,OAAO,EAAE;IACb;IAEA,SAAS,GAAI,KAAK,EAAE,OAAO;QACvB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IACvB;IAEA,SAAS,KAAM,KAAK,EAAE,OAAO;QACzB,MAAM,kBAAkB,CAAC,GAAG;YACxB,IAAI,OAAO;YACX,WAAW;QACf;QACA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IACvB;IAEA,SAAS,IAAK,KAAK,EAAE,OAAO;QACxB,IAAI,CAAC,OAAO,OAAO,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA,KAAM,MAAM,CAAC,GAAG,GAAG,EAAE;QACpE,IAAI,CAAC,SAAS,OAAO,MAAM,CAAC,MAAM,GAAG,EAAE;QACvC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IACtD;IAEA,SAAS,QAAS,KAAK,EAAE,GAAG,IAAI;QAC5B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA,UAAW,WAAW;IAChD;IAEA,IAAI,sBAAsB;IAC1B,SAAS,QAAQ,IAAI,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ;QACzD,IAAI,YAAY,QAAQ,OAAO,aAAa,YAAY;YACpD,MAAM,IAAI,MAAM;QACpB;QACA,EAAE,OAAO,GAAG;QAEZ,IAAI,KAAK;QACT,SAAS,gBAAiB,GAAG,EAAE,GAAG,IAAI;YAClC,8DAA8D;YAC9D,eAAe;YACf,IAAI,KAAK,OAAO,gBAAgB,IAAI,OAAO;YAC3C,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO,IAAI,IAAI,CAAC,EAAE;YACxC,IAAI;QACR;QAEA,IAAI,OAAO,EAAE,eAAe,CACxB,MACA,gBAAgB,kBACX,YAAY;QAGrB,IAAI,eAAe;YACf,EAAE,MAAM,CAAC,OAAO,CAAC;QACrB,OAAO;YACH,EAAE,MAAM,CAAC,IAAI,CAAC;QAClB;QAEA,IAAI,CAAC,qBAAqB;YACtB,sBAAsB;YACtB,eAAe;gBACX,sBAAsB;gBACtB,EAAE,OAAO;YACb;QACJ;QAEA,IAAI,iBAAiB,CAAC,UAAU;YAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;gBACzB,MAAM;gBACN,MAAM;YACV;QACJ;IACJ;IAEA,SAAS,UAAU,KAAK;QACpB,OAAO,SAAU,GAAG,EAAE,GAAG,IAAI;YACzB,cAAc;YAEd,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;gBAC1C,IAAI,OAAO,KAAK,CAAC,EAAE;gBAEnB,IAAI,QAAQ,YAAY,OAAO,CAAC;gBAChC,IAAI,UAAU,GAAG;oBACb,YAAY,KAAK;gBACrB,OAAO,IAAI,QAAQ,GAAG;oBAClB,YAAY,MAAM,CAAC,OAAO;gBAC9B;gBAEA,KAAK,QAAQ,CAAC,QAAQ;gBAEtB,IAAI,OAAO,MAAM;oBACb,QAAQ,SAAS,KAAK,KAAK,IAAI;gBACnC;YACJ;YAEA,IAAI,cAAe,EAAE,WAAW,GAAG,EAAE,MAAM,EAAI;gBAC3C,QAAQ;YACZ;YAEA,IAAI,EAAE,IAAI,IAAI;gBACV,QAAQ;YACZ;YACA,EAAE,OAAO;QACb;IACJ;IAEA,SAAS,YAAY,IAAI;QACrB,IAAI,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI,IAAI;YAC/B,+CAA+C;YAC/C,eAAe,IAAM,QAAQ;YAC7B,OAAO;QACX;QACA,OAAO;IACX;IAEA,MAAM,cAAc,CAAC,OAAS,CAAC;YAC3B,IAAI,CAAC,SAAS;gBACV,OAAO,IAAI,QAAQ,CAAC,SAAS;oBACzB,KAAK,MAAM,CAAC,KAAK;wBACb,IAAI,KAAK,OAAO,OAAO;wBACvB,QAAQ;oBACZ;gBACJ;YACJ;YACA,IAAI;YACJ,GAAG,MAAM;QAEb;IAEA,IAAI,eAAe;IACnB,IAAI,IAAI;QACJ,QAAQ,IAAI;QACZ,iBAAiB,IAAI,EAAE,QAAQ;YAC3B,OAAO;gBACH;gBACA;YACJ;QACJ;QACA,CAAC,CAAC,OAAO,QAAQ,CAAC;YACd,OAAO,EAAE,MAAM,CAAC,OAAO,QAAQ,CAAC;QACpC;QACA;QACA;QACA,QAAQ,cAAc;QACtB,SAAS;QACT,QAAQ;QACR,MAAM,IAAI,EAAE,QAAQ;YAChB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACrB,IAAI,YAAY,OAAO;gBACvB,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,QAAQ,OAAO,OAAO,OAAO;YAC1D;YACA,OAAO,QAAQ,MAAM,OAAO,OAAO;QACvC;QACA,WAAW,IAAI,EAAE,QAAQ;YACrB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACrB,IAAI,YAAY,OAAO;gBACvB,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,QAAQ,OAAO,OAAO,MAAM;YACzD;YACA,OAAO,QAAQ,MAAM,OAAO,MAAM;QACtC;QACA;YACI;YACA,EAAE,MAAM,CAAC,KAAK;QAClB;QACA,SAAS,IAAI,EAAE,QAAQ;YACnB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACrB,IAAI,YAAY,OAAO;gBACvB,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,QAAQ,OAAO,MAAM,OAAO;YACzD;YACA,OAAO,QAAQ,MAAM,MAAM,OAAO;QACtC;QACA,cAAc,IAAI,EAAE,QAAQ;YACxB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACrB,IAAI,YAAY,OAAO;gBACvB,OAAO,KAAK,GAAG,CAAC,CAAA,QAAS,QAAQ,OAAO,MAAM,MAAM;YACxD;YACA,OAAO,QAAQ,MAAM,MAAM,MAAM;QACrC;QACA,QAAQ,MAAM;YACV,EAAE,MAAM,CAAC,MAAM,CAAC;QACpB;QACA;YACI,uEAAuE;YACvE,gDAAgD;YAChD,IAAI,cAAc;gBACd;YACJ;YACA,eAAe;YACf,MAAM,CAAC,EAAE,MAAM,IAAI,aAAa,EAAE,WAAW,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC;gBAC7D,IAAI,QAAQ,EAAE,EAAE,OAAO,EAAE;gBACzB,IAAI,IAAI,EAAE,MAAM,CAAC,MAAM;gBACvB,IAAI,EAAE,OAAO,EAAE,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,OAAO;gBACxC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBACxB,IAAI,OAAO,EAAE,MAAM,CAAC,KAAK;oBACzB,MAAM,IAAI,CAAC;oBACX,YAAY,IAAI,CAAC;oBACjB,KAAK,IAAI,CAAC,KAAK,IAAI;gBACvB;gBAEA,cAAc;gBAEd,IAAI,EAAE,MAAM,CAAC,MAAM,KAAK,GAAG;oBACvB,QAAQ;gBACZ;gBAEA,IAAI,eAAe,EAAE,WAAW,EAAE;oBAC9B,QAAQ;gBACZ;gBAEA,IAAI,KAAK,SAAS,UAAU;gBAC5B,QAAQ,MAAM;YAClB;YACA,eAAe;QACnB;QACA;YACI,OAAO,EAAE,MAAM,CAAC,MAAM;QAC1B;QACA;YACI,OAAO;QACX;QACA;YACI,OAAO;QACX;QACA;YACI,OAAO,EAAE,MAAM,CAAC,MAAM,GAAG,eAAe;QAC5C;QACA;YACI,EAAE,MAAM,GAAG;QACf;QACA;YACI,IAAI,EAAE,MAAM,KAAK,OAAO;gBAAE;YAAQ;YAClC,EAAE,MAAM,GAAG;YACX,eAAe,EAAE,OAAO;QAC5B;IACJ;IACA,8EAA8E;IAC9E,OAAO,gBAAgB,CAAC,GAAG;QACvB,WAAW;YACP,UAAU;YACV,OAAO,YAAY;QACvB;QACA,aAAa;YACT,UAAU;YACV,OAAO,YAAY;QACvB;QACA,OAAO;YACH,UAAU;YACV,OAAO,YAAY;QACvB;QACA,OAAO;YACH,UAAU;YACV,OAAO,YAAY;QACvB;QACA,OAAO;YACH,UAAU;YACV,OAAO,YAAY;QACvB;IACJ;IACA,OAAO;AACX;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6CC,GACD,SAAS,QAAQ,MAAM,EAAE,OAAO;IAC5B,OAAO,QAAQ,QAAQ,GAAG;AAC9B;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqDC,GACD,SAAS,MAAM,MAAM,EAAE,WAAW,EAAE,OAAO;IACvC,OAAO,QAAQ,QAAQ,aAAa;AACxC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoHC,GACD,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC1C,WAAW,KAAK;IAChB,IAAI,YAAY,UAAU;IAC1B,OAAO,eAAe,MAAM,CAAC,GAAG,GAAG;QAC/B,UAAU,MAAM,GAAG,CAAC,KAAK;YACrB,OAAO;YACP,OAAO;QACX;IACJ,GAAG,CAAA,MAAO,SAAS,KAAK;AAC5B;AACA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GACD,SAAS,IAAI,GAAG,SAAS;IACrB,IAAI,aAAa,UAAU,GAAG,CAAC;IAC/B,OAAO,SAAU,GAAG,IAAI;QACpB,IAAI,OAAO,IAAI;QAEf,IAAI,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC9B,IAAI,OAAO,MAAM,YAAY;YACzB,KAAK,GAAG;QACZ,OAAO;YACH,KAAK;QACT;QAEA,SAAS,YAAY,MAAM,CAAC,SAAS,IAAI;YACrC,GAAG,KAAK,CAAC,MAAM,QAAQ,MAAM,CAAC,CAAC,KAAK,GAAG;gBACnC,OAAO,KAAK;YAChB;QACJ,GACA,CAAC,KAAK,UAAY,GAAG,QAAQ;QAE7B,OAAO,EAAE,CAAC,eAAe;IAC7B;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GACD,SAAS,QAAQ,GAAG,IAAI;IACpB,OAAO,OAAO,KAAK,OAAO;AAC9B;AAEA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,SAAU,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAC9C,OAAO,UAAU,cAAc,QAAQ,MAAM,UAAU;AAC3D;AACA,IAAI,aAAa,SAAS,UAAU;AAEpC;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,YAAY,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAChD,IAAI,YAAY,UAAU;IAC1B,OAAO,WAAW,MAAM,OAAO,CAAC,KAAK;QACjC,UAAU,KAAK,CAAC,KAAK,GAAG;YACpB,IAAI,KAAK,OAAO,OAAO;YACvB,OAAO,OAAO,KAAK;QACvB;IACJ,GAAG,CAAC,KAAK;QACL,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,IAAI,UAAU,CAAC,EAAE,EAAE;gBACf,SAAS,OAAO,MAAM,IAAI,UAAU,CAAC,EAAE;YAC3C;QACJ;QAEA,OAAO,SAAS,KAAK;IACzB;AACJ;AACA,IAAI,gBAAgB,SAAS,aAAa;AAE1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6FC,GACD,SAAS,OAAO,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACpC,OAAO,cAAc,MAAM,UAAU,UAAU;AACnD;AACA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,aAAa,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC1C,OAAO,cAAc,MAAM,GAAG,UAAU;AAC5C;AACA,IAAI,iBAAiB,SAAS,cAAc;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAyCC,GACD,SAAS,WAAW,GAAG,IAAI;IACvB,OAAO,SAAU,GAAG,YAAW,YAAY,GAAZ;QAC3B,IAAI,WAAW,YAAY,GAAG;QAC9B,OAAO,SAAS,SAAS;IAC7B;AACJ;AAEA,SAAS,cAAc,KAAK,EAAE,SAAS;IACnC,OAAO,CAAC,QAAQ,KAAK,WAAW;QAC5B,IAAI,aAAa;QACjB,IAAI;QACJ,MAAM,WAAW,UAAU;QAC3B,OAAO,KAAK,CAAC,OAAO,GAAG;YACnB,SAAS,OAAO,CAAC,KAAK;gBAClB,IAAI,OAAO,QAAQ,OAAO,OAAO,SAAS;gBAE1C,IAAI,MAAM,WAAW,CAAC,YAAY;oBAC9B,aAAa;oBACb,aAAa,UAAU,MAAM;oBAC7B,OAAO,SAAS,MAAM;gBAC1B;gBACA;YACJ;QACJ,GAAG,CAAA;YACC,IAAI,KAAK,OAAO,GAAG;YACnB,GAAG,MAAM,aAAa,aAAa,UAAU;QACjD;IACJ;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsEC,GACD,SAAS,OAAO,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACpC,OAAO,cAAc,CAAA,OAAQ,MAAM,CAAC,KAAK,OAAS,MAAM,UAAU,MAAM,UAAU;AACtF;AACA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,YAAY,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAChD,OAAO,cAAc,CAAA,OAAQ,MAAM,CAAC,KAAK,OAAS,MAAM,cAAc,QAAQ,MAAM,UAAU;AAClG;AACA,IAAI,gBAAgB,SAAS,aAAa;AAE1C;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,aAAa,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC1C,OAAO,cAAc,CAAA,OAAQ,MAAM,CAAC,KAAK,OAAS,MAAM,cAAc,IAAI,MAAM,UAAU;AAC9F;AAEA,IAAI,iBAAiB,SAAS,cAAc;AAE5C,SAAS,YAAY,IAAI;IACrB,OAAO,CAAC,IAAI,GAAG,OAAS,UAAU,OAAO,MAAM,CAAC,KAAK,GAAG;YACpD,wBAAwB,GACxB,IAAI,OAAO,YAAY,UAAU;gBAC7B,wBAAwB,GACxB,IAAI,KAAK;oBACL,wBAAwB,GACxB,IAAI,QAAQ,KAAK,EAAE;wBACf,QAAQ,KAAK,CAAC;oBAClB;gBACJ,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;oBACtB,WAAW,OAAO,CAAC,CAAA,IAAK,OAAO,CAAC,KAAK,CAAC;gBAC1C;YACJ;QACJ;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,IAAI,MAAM,YAAY;AAEtB;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ;IACtC,WAAW,SAAS;IACpB,IAAI,MAAM,UAAU;IACpB,IAAI,QAAQ,UAAU;IACtB,IAAI;IAEJ,SAAS,KAAK,GAAG,EAAE,GAAG,IAAI;QACtB,IAAI,KAAK,OAAO,SAAS;QACzB,IAAI,QAAQ,OAAO;QACnB,UAAU;QACV,SAAS,MAAM;IACnB;IAEA,SAAS,MAAM,GAAG,EAAE,KAAK;QACrB,IAAI,KAAK,OAAO,SAAS;QACzB,IAAI,QAAQ,OAAO;QACnB,IAAI,CAAC,OAAO,OAAO,SAAS,SAAS;QACrC,IAAI;IACR;IAEA,OAAO,MAAM,MAAM;AACvB;AAEA,IAAI,aAAa,SAAS,UAAU;AAEpC;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,QAAQ,QAAQ,EAAE,IAAI,EAAE,QAAQ;IACrC,MAAM,QAAQ,UAAU;IACxB,OAAO,WAAW,UAAU,CAAC,GAAG;QAC5B,MAAM,KAAK,KAAK,GAAG;QACnB,SAAS,MAAM,CAAC,KAAK,QAAU,GAAI,KAAK,CAAC;IAC7C,GAAG;AACP;AAEA,SAAS,cAAc,QAAQ;IAC3B,OAAO,CAAC,OAAO,OAAO,WAAa,SAAS,OAAO;AACvD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkGC,GACD,SAAS,YAAY,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACzC,OAAO,SAAS,MAAM,cAAc,UAAU,YAAY;AAC9D;AAEA,IAAI,OAAO,SAAS,aAAa;AAEjC;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAC9C,OAAO,cAAc,OAAO,MAAM,cAAc,UAAU,YAAY;AAC1E;AACA,IAAI,cAAc,SAAS,WAAW;AAEtC;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,WAAW,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACxC,OAAO,YAAY,MAAM,GAAG,UAAU;AAC1C;AACA,IAAI,eAAe,SAAS,YAAY;AAExC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCC,GACD,SAAS,YAAY,EAAE;IACnB,IAAI,QAAQ,KAAK,OAAO;IACxB,OAAO,SAAU,GAAG,KAAI,YAAY,GAAZ;QACpB,IAAI,WAAW,KAAK,GAAG;QACvB,IAAI,OAAO;QACX,KAAK,IAAI,CAAC,CAAC,GAAG;YACV,IAAI,MAAM;gBACN,eAAe,IAAM,YAAY;YACrC,OAAO;gBACH,YAAY;YAChB;QACJ;QACA,GAAG,KAAK,CAAC,IAAI,EAAE;QACf,OAAO;IACX;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6FC,GACD,SAAS,MAAM,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACnC,OAAO,cAAc,CAAA,OAAQ,CAAC,MAAM,CAAA,MAAO,CAAC,KAAK,UAAU,MAAM,UAAU;AAC/E;AACA,IAAI,UAAU,SAAS,OAAO;AAE9B;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,WAAW,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAC/C,OAAO,cAAc,CAAA,OAAQ,CAAC,MAAM,CAAA,MAAO,CAAC,KAAK,cAAc,QAAQ,MAAM,UAAU;AAC3F;AACA,IAAI,eAAe,SAAS,YAAY;AAExC;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,YAAY,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACzC,OAAO,cAAc,CAAA,OAAQ,CAAC,MAAM,CAAA,MAAO,CAAC,KAAK,gBAAgB,MAAM,UAAU;AACrF;AACA,IAAI,gBAAgB,SAAS,aAAa;AAE1C,SAAS,YAAY,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ;IAChD,IAAI,cAAc,IAAI,MAAM,IAAI,MAAM;IACtC,OAAO,KAAK,CAAC,GAAG,OAAO;QACnB,SAAS,GAAG,CAAC,KAAK;YACd,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;YACvB,OAAO;QACX;IACJ,GAAG,CAAA;QACC,IAAI,KAAK,OAAO,SAAS;QACzB,IAAI,UAAU,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,IAAI,WAAW,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE;QAC3C;QACA,SAAS,MAAM;IACnB;AACJ;AAEA,SAAS,cAAc,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACnD,IAAI,UAAU,EAAE;IAChB,OAAO,MAAM,CAAC,GAAG,OAAO;QACpB,SAAS,GAAG,CAAC,KAAK;YACd,IAAI,KAAK,OAAO,OAAO;YACvB,IAAI,GAAG;gBACH,QAAQ,IAAI,CAAC;oBAAC;oBAAO,OAAO;gBAAC;YACjC;YACA,OAAO;QACX;IACJ,GAAG,CAAA;QACC,IAAI,KAAK,OAAO,SAAS;QACzB,SAAS,MAAM,QACV,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IACzB;AACJ;AAEA,SAAS,QAAQ,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC7C,IAAI,SAAS,YAAY,QAAQ,cAAc;IAC/C,OAAO,OAAO,QAAQ,MAAM,UAAU,WAAW;AACrD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmEC,GACD,SAAS,OAAQ,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACrC,OAAO,QAAQ,UAAU,MAAM,UAAU;AAC7C;AACA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,YAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IACjD,OAAO,QAAQ,cAAc,QAAQ,MAAM,UAAU;AACzD;AACA,IAAI,gBAAgB,SAAS,aAAa;AAE1C;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS,aAAc,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC3C,OAAO,QAAQ,gBAAgB,MAAM,UAAU;AACnD;AACA,IAAI,iBAAiB,SAAS,cAAc;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,SAAS,QAAQ,EAAE,EAAE,OAAO;IACxB,IAAI,OAAO,SAAS;IACpB,IAAI,OAAO,UAAU,YAAY;IAEjC,SAAS,KAAK,GAAG;QACb,IAAI,KAAK,OAAO,KAAK;QACrB,IAAI,QAAQ,OAAO;QACnB,KAAK;IACT;IACA,OAAO;AACX;AACA,IAAI,YAAY,SAAS,SAAS;AAElC;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IACjD,IAAI,YAAY,UAAU;IAC1B,OAAO,WAAW,MAAM,OAAO,CAAC,KAAK;QACjC,UAAU,KAAK,CAAC,KAAK;YACjB,IAAI,KAAK,OAAO,OAAO;YACvB,OAAO,OAAO,KAAK;gBAAC;gBAAK;YAAG;QAChC;IACJ,GAAG,CAAC,KAAK;QACL,IAAI,SAAS,CAAC;QACd,0DAA0D;QAC1D,IAAI,EAAC,cAAc,EAAC,GAAG,OAAO,SAAS;QAEvC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,IAAI,UAAU,CAAC,EAAE,EAAE;gBACf,IAAI,EAAC,GAAG,EAAC,GAAG,UAAU,CAAC,EAAE;gBACzB,IAAI,EAAC,GAAG,EAAC,GAAG,UAAU,CAAC,EAAE;gBAEzB,IAAI,eAAe,IAAI,CAAC,QAAQ,MAAM;oBAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;gBACrB,OAAO;oBACH,MAAM,CAAC,IAAI,GAAG;wBAAC;qBAAI;gBACvB;YACJ;QACJ;QAEA,OAAO,SAAS,KAAK;IACzB;AACJ;AAEA,IAAI,iBAAiB,SAAS,cAAc;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0FC,GACD,SAAS,QAAS,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACtC,OAAO,eAAe,MAAM,UAAU,UAAU;AACpD;AAEA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,cAAe,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC5C,OAAO,eAAe,MAAM,GAAG,UAAU;AAC7C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,IAAI,MAAM,YAAY;AAEtB;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,SAAS,eAAe,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAClD,WAAW,KAAK;IAChB,IAAI,SAAS,CAAC;IACd,IAAI,YAAY,UAAU;IAC1B,OAAO,cAAc,OAAO,KAAK,CAAC,KAAK,KAAK;QACxC,UAAU,KAAK,KAAK,CAAC,KAAK;YACtB,IAAI,KAAK,OAAO,KAAK;YACrB,MAAM,CAAC,IAAI,GAAG;YACd,KAAK;QACT;IACJ,GAAG,CAAA,MAAO,SAAS,KAAK;AAC5B;AAEA,IAAI,mBAAmB,SAAS,gBAAgB;AAEhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsIC,GACD,SAAS,UAAU,GAAG,EAAE,QAAQ,EAAE,QAAQ;IACtC,OAAO,iBAAiB,KAAK,UAAU,UAAU;AACrD;AAEA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,gBAAgB,GAAG,EAAE,QAAQ,EAAE,QAAQ;IAC5C,OAAO,iBAAiB,KAAK,GAAG,UAAU;AAC9C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAuCC,GACD,SAAS,QAAQ,EAAE,EAAE,SAAS,CAAA,IAAK,CAAC;IAChC,IAAI,OAAO,OAAO,MAAM,CAAC;IACzB,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,IAAI,MAAM,UAAU;IACpB,IAAI,WAAW,cAAc,CAAC,MAAM;QAChC,IAAI,MAAM,UAAU;QACpB,IAAI,OAAO,MAAM;YACb,eAAe,IAAM,SAAS,SAAS,IAAI,CAAC,IAAI;QACpD,OAAO,IAAI,OAAO,QAAQ;YACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACrB,OAAO;YACH,MAAM,CAAC,IAAI,GAAG;gBAAC;aAAS;YACxB,OAAO,MAAM,CAAC,KAAK,GAAG;gBAClB,2CAA2C;gBAC3C,IAAI,CAAC,KAAK;oBACN,IAAI,CAAC,IAAI,GAAG;gBAChB;gBACA,IAAI,IAAI,MAAM,CAAC,IAAI;gBACnB,OAAO,MAAM,CAAC,IAAI;gBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAI,GAAG,IAAK;oBACtC,CAAC,CAAC,EAAE,CAAC,QAAQ;gBACjB;YACJ;QACJ;IACJ;IACA,SAAS,IAAI,GAAG;IAChB,SAAS,UAAU,GAAG;IACtB,OAAO;AACX;AAEA,wBAAwB,GAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,IAAI;AAEJ,IAAI,aAAa;IACb,SAAS,QAAQ,QAAQ;AAC7B,OAAO,IAAI,iBAAiB;IACxB,SAAS;AACb,OAAO;IACH,SAAS;AACb;AAEA,IAAI,WAAW,KAAK;AAEpB,IAAI,YAAY,SAAS,CAAC,QAAQ,OAAO;IACrC,IAAI,UAAU,YAAY,SAAS,EAAE,GAAG,CAAC;IAEzC,OAAO,OAAO,CAAC,MAAM,KAAK;QACtB,UAAU,MAAM,CAAC,KAAK,GAAG;YACrB,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,CAAC,OAAO,GAAG;YACf;YACA,OAAO,CAAC,IAAI,GAAG;YACf,OAAO;QACX;IACJ,GAAG,CAAA,MAAO,SAAS,KAAK;AAC5B,GAAG;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8JC,GACD,SAAS,SAAS,KAAK,EAAE,QAAQ;IAC7B,OAAO,UAAU,UAAU,OAAO;AACtC;AAEA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,cAAc,KAAK,EAAE,KAAK,EAAE,QAAQ;IACzC,OAAO,UAAU,cAAc,QAAQ,OAAO;AAClD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmFC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwDC,GACD,SAAS,MAAO,MAAM,EAAE,WAAW;IAC/B,IAAI,UAAU,UAAU;IACxB,OAAO,QAAQ,CAAC,OAAO;QACnB,QAAQ,KAAK,CAAC,EAAE,EAAE;IACtB,GAAG,aAAa;AACpB;AAEA,0DAA0D;AAC1D,8EAA8E;AAC9E,MAAM;IACF,aAAc;QACV,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,SAAS,GAAG,OAAO,gBAAgB;IAC5C;IAEA,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;IAC3B;IAEA,QAAS;QACL,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,OAAO,IAAI;IACf;IAEA,OAAO,KAAK,EAAE;QACV,IAAI;QAEJ,MAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAE,OAAO,OAAO,EAAG;YACvE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;YAEf,QAAQ;QACZ;IACJ;IAEA,SAAS,KAAK,EAAE;QACZ,IAAI;QAEJ,MAAO,CAAC,IAAE,QAAQ,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAE;YAC1C,IAAI,IAAE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAE,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;gBACjE,IAAI,IAAE;YACV;YAEA,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;gBACzC;YACJ;YAEA,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;YAEf,QAAQ;QACZ;IACJ;IAEA,KAAK,IAAI,EAAE;QACP,KAAK,SAAS,GAAG,EAAE,IAAI,CAAC,SAAS;QACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAC;IACjC;IAEA,QAAQ,IAAI,EAAE;QACV,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1B;IAEA,QAAQ;QACJ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QAErB,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAC,EAAE;QAC5C,IAAI,CAAC,IAAI,CAAC,GAAG;QACb,IAAI,CAAC,QAAQ,CAAC;QAEd,OAAO;IACX;IAEA,UAAU;QACN,OAAO;eAAI,IAAI;SAAC;IACpB;IAEA,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAI;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YACvC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;QAC3B;IACJ;IAEA,OAAQ,MAAM,EAAE;QACZ,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YACvC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;gBACvB,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC3B;YACJ;QACJ;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAEjB,IAAK,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAC,IAAI,KAAK,GAAG,IAAK;YAClD,IAAI,CAAC,QAAQ,CAAC;QAClB;QAEA,OAAO,IAAI;IACf;AACJ;AAEA,SAAS,QAAQ,CAAC;IACd,OAAO,CAAC,KAAG,CAAC,IAAE;AAClB;AAEA,SAAS,OAAO,CAAC;IACb,OAAO,CAAC,AAAC,IAAE,KAAI,CAAC,IAAE;AACtB;AAEA,SAAS,QAAQ,CAAC,EAAE,CAAC;IACjB,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;QAC3B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAClC,OACK;QACD,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;IACpC;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GACD,SAAS,cAAc,MAAM,EAAE,WAAW;IACtC,4BAA4B;IAC5B,IAAI,IAAI,MAAM,QAAQ;IAEtB,IAAI,EACA,IAAI,EACJ,SAAS,EACZ,GAAG;IAEJ,EAAE,MAAM,GAAG,IAAI;IACf,EAAE,eAAe,GAAG,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAC,EAAE;QACnC,OAAO;YACH;YACA;YACA;QACJ;IACJ;IAEA,SAAS,gBAAgB,KAAK,EAAE,QAAQ;QACpC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;YACvB,OAAO;gBAAC,MAAM;gBAAO;YAAQ;QACjC;QACA,OAAO,MAAM,GAAG,CAAC,CAAA;YAAU,OAAO;gBAAC;gBAAM;YAAQ;QAAG;IACxD;IAEA,iEAAiE;IACjE,EAAE,IAAI,GAAG,SAAS,IAAI,EAAE,WAAW,CAAC,EAAE,QAAQ;QAC1C,OAAO,KAAK,gBAAgB,MAAM,WAAW;IACjD;IAEA,EAAE,SAAS,GAAG,SAAS,IAAI,EAAE,WAAW,CAAC,EAAE,QAAQ;QAC/C,OAAO,UAAU,gBAAgB,MAAM,WAAW;IACtD;IAEA,2BAA2B;IAC3B,OAAO,EAAE,OAAO;IAChB,OAAO,EAAE,YAAY;IAErB,OAAO;AACX;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GACD,SAAS,KAAK,KAAK,EAAE,QAAQ;IACzB,WAAW,KAAK;IAChB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO,SAAS,IAAI,UAAU;IACzD,IAAI,CAAC,MAAM,MAAM,EAAE,OAAO;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;QAC1C,UAAU,KAAK,CAAC,EAAE,EAAE;IACxB;AACJ;AAEA,IAAI,SAAS,SAAS,MAAM;AAE5B;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,YAAa,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACjD,IAAI,WAAW;WAAI;KAAM,CAAC,OAAO;IACjC,OAAO,SAAS,UAAU,MAAM,UAAU;AAC9C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsCC,GACD,SAAS,QAAQ,EAAE;IACf,IAAI,MAAM,UAAU;IACpB,OAAO,cAAc,SAAS,UAAU,IAAI,EAAE,eAAe;QACzD,KAAK,IAAI,CAAC,CAAC,OAAO,GAAG;YACjB,IAAI,SAAS,CAAC;YACd,IAAI,OAAO;gBACP,OAAO,KAAK,GAAG;YACnB;YACA,IAAI,OAAO,MAAM,GAAG,GAAE;gBAClB,IAAI,QAAQ;gBACZ,IAAI,OAAO,MAAM,IAAI,GAAG;oBACpB,CAAC,MAAM,GAAG;gBACd;gBACA,OAAO,KAAK,GAAG;YACnB;YACA,gBAAgB,MAAM;QAC1B;QAEA,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;IAC3B;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkEC,GACD,SAAS,WAAW,KAAK;IACrB,IAAI;IACJ,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,UAAU,MAAM,GAAG,CAAC;IACxB,OAAO;QACH,UAAU,CAAC;QACX,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;YACvB,OAAO,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI;QAChD;IACJ;IACA,OAAO;AACX;AAEA,SAAS,SAAS,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ;IAC9C,MAAM,WAAW,UAAU;IAC3B,OAAO,QAAQ,QAAQ,KAAK,CAAC,OAAO;QAChC,SAAS,OAAO,CAAC,KAAK;YAClB,GAAG,KAAK,CAAC;QACb;IACJ,GAAG;AACP;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6DC,GACD,SAAS,OAAQ,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACrC,OAAO,SAAS,UAAU,MAAM,UAAU;AAC9C;AACA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS,YAAa,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IACjD,OAAO,SAAS,cAAc,QAAQ,MAAM,UAAU;AAC1D;AACA,IAAI,gBAAgB,SAAS,aAAa;AAE1C;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS,aAAc,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAC3C,OAAO,SAAS,gBAAgB,MAAM,UAAU;AACpD;AACA,IAAI,iBAAiB,SAAS,cAAc;AAE5C,SAAS,SAAS,KAAK;IACnB,OAAO;QACH,OAAO;IACX;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoFC,GACD,MAAM,gBAAgB;AACtB,MAAM,mBAAmB;AAEzB,SAAS,MAAM,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC/B,IAAI,UAAU;QACV,OAAO;QACP,cAAc,SAAS;IAC3B;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK,OAAO,SAAS,YAAY;QACpD,WAAW,QAAQ;QACnB,OAAO;IACX,OAAO;QACH,WAAW,SAAS;QACpB,WAAW,YAAY;IAC3B;IAEA,IAAI,OAAO,SAAS,YAAY;QAC5B,MAAM,IAAI,MAAM;IACpB;IAEA,IAAI,QAAQ,UAAU;IAEtB,IAAI,UAAU;IACd,SAAS;QACL,MAAM,CAAC,KAAK,GAAG;YACX,IAAI,QAAQ,OAAO;YACnB,IAAI,OAAO,YAAY,QAAQ,KAAK,IAChC,CAAC,OAAO,QAAQ,WAAW,IAAI,cAC3B,QAAQ,WAAW,CAAC,IAAI,GAAG;gBAC/B,WAAW,cAAc,QAAQ,YAAY,CAAC,UAAU;YAC5D,OAAO;gBACH,SAAS,QAAQ;YACrB;QACJ;IACJ;IAEA;IACA,OAAO,QAAQ,CAAC,eAAe;AACnC;AAEA,SAAS,WAAW,GAAG,EAAE,CAAC;IACtB,IAAI,OAAO,MAAM,UAAU;QACvB,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI;QAExB,IAAI,YAAY,GAAG,OAAO,EAAE,QAAQ,KAAK,aACrC,EAAE,QAAQ,GACV,SAAS,CAAC,EAAE,QAAQ,IAAI;QAE5B,IAAI,WAAW,GAAG,EAAE,WAAW;IACnC,OAAO,IAAI,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;QACvD,IAAI,KAAK,GAAG,CAAC,KAAK;IACtB,OAAO;QACH,MAAM,IAAI,MAAM;IACpB;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,SAAS,UAAW,IAAI,EAAE,IAAI;IAC1B,IAAI,CAAC,MAAM;QACP,OAAO;QACP,OAAO;IACX;IACA,IAAI,QAAQ,AAAC,QAAQ,KAAK,KAAK,IAAK,KAAK,MAAM;IAC/C,IAAI,QAAQ,OAAO;QACf,SAAS;IACb;IACA,IAAI,QAAQ,UAAU;IACtB,OAAO,cAAc,CAAC,MAAM;QACxB,IAAI,KAAK,MAAM,GAAG,QAAQ,KAAK,YAAY,MAAM;YAC7C,KAAK,IAAI,CAAC;YACV,WAAW;QACf;QACA,SAAS,OAAO,EAAE;YACd,SAAS,MAAM;QACnB;QAEA,IAAI,MAAM,MAAM,MAAM,QAAQ;aACzB,MAAM,QAAQ;QAEnB,OAAO,QAAQ,CAAC,eAAe;IACnC;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoKC,GACD,SAAS,OAAO,KAAK,EAAE,QAAQ;IAC3B,OAAO,UAAU,gBAAgB,OAAO;AAC5C;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgGC,GACD,SAAS,KAAK,IAAI,EAAE,QAAQ,EAAE,QAAQ;IAClC,OAAO,cAAc,SAAS,CAAA,MAAO,KAAK,UAAU,MAAM,UAAU;AACxE;AACA,IAAI,SAAS,SAAS,MAAM;AAE5B;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAC9C,OAAO,cAAc,SAAS,CAAA,MAAO,KAAK,cAAc,QAAQ,MAAM,UAAU;AACpF;AACA,IAAI,cAAc,SAAS,WAAW;AAEtC;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,WAAW,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACxC,OAAO,cAAc,SAAS,CAAA,MAAO,KAAK,gBAAgB,MAAM,UAAU;AAC9E;AACA,IAAI,eAAe,SAAS,YAAY;AAExC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqJC,GACD,SAAS,OAAQ,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACrC,IAAI,YAAY,UAAU;IAC1B,OAAO,MAAM,MAAM,CAAC,GAAG;QACnB,UAAU,GAAG,CAAC,KAAK;YACf,IAAI,KAAK,OAAO,OAAO;YACvB,OAAO,KAAK;gBAAC,OAAO;gBAAG;YAAQ;QACnC;IACJ,GAAG,CAAC,KAAK;QACL,IAAI,KAAK,OAAO,SAAS;QACzB,SAAS,MAAM,QAAQ,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;IAC5D;;;IAEA,SAAS,WAAW,IAAI,EAAE,KAAK;QAC3B,IAAI,IAAI,KAAK,QAAQ,EAAE,IAAI,MAAM,QAAQ;QACzC,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI;IACpC;AACJ;AACA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwCC,GACD,SAAS,QAAQ,OAAO,EAAE,YAAY,EAAE,IAAI;IACxC,IAAI,KAAK,UAAU;IAEnB,OAAO,cAAc,CAAC,MAAM;QACxB,IAAI,WAAW;QACf,IAAI;QAEJ,SAAS;YACL,IAAI,OAAO,QAAQ,IAAI,IAAI;YAC3B,IAAI,QAAS,IAAI,MAAM,wBAAwB,OAAO;YACtD,MAAM,IAAI,GAAG;YACb,IAAI,MAAM;gBACN,MAAM,IAAI,GAAG;YACjB;YACA,WAAW;YACX,SAAS;QACb;QAEA,KAAK,IAAI,CAAC,CAAC,GAAG;YACV,IAAI,CAAC,UAAU;gBACX,YAAY;gBACZ,aAAa;YACjB;QACJ;QAEA,yCAAyC;QACzC,QAAQ,WAAW,iBAAiB;QACpC,MAAM;IACV;AACJ;AAEA,SAAS,MAAM,IAAI;IACf,IAAI,SAAS,MAAM;IACnB,MAAO,OAAQ;QACX,MAAM,CAAC,KAAK,GAAG;IACnB;IACA,OAAO;AACX;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,WAAW,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;IAChD,IAAI,YAAY,UAAU;IAC1B,OAAO,WAAW,MAAM,QAAQ,OAAO,WAAW;AACtD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCC,GACD,SAAS,MAAO,CAAC,EAAE,QAAQ,EAAE,QAAQ;IACjC,OAAO,WAAW,GAAG,UAAU,UAAU;AAC7C;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,YAAa,CAAC,EAAE,QAAQ,EAAE,QAAQ;IACvC,OAAO,WAAW,GAAG,GAAG,UAAU;AACtC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsIC,GACD,SAAS,UAAW,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ;IACrD,IAAI,UAAU,MAAM,IAAI,KAAK,OAAO,gBAAgB,YAAY;QAC5D,WAAW;QACX,WAAW;QACX,cAAc,MAAM,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC9C;IACA,WAAW,KAAK,YAAY;IAC5B,IAAI,YAAY,UAAU;IAE1B,SAAS,MAAM,CAAC,GAAG,GAAG;QAClB,UAAU,aAAa,GAAG,GAAG;IACjC,GAAG,CAAA,MAAO,SAAS,KAAK;IACxB,OAAO,QAAQ,CAAC,eAAe;AACnC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GACD,SAAS,QAAQ,KAAK,EAAE,QAAQ;IAC5B,IAAI,QAAQ;IACZ,IAAI;IACJ,OAAO,aAAa,OAAO,CAAC,MAAM;QAC9B,UAAU,MAAM,CAAC,KAAK,GAAG;YACrB,IAAI,QAAQ,OAAO,OAAO,OAAO;YAEjC,IAAI,KAAK,MAAM,GAAG,GAAG;gBACjB,CAAC,OAAO,GAAG;YACf,OAAO;gBACH,SAAS;YACb;YACA,QAAQ;YACR,OAAO,MAAM,OAAO,CAAC;QACzB;IACJ,GAAG,IAAM,SAAS,OAAO;AAC7B;AAEA,IAAI,YAAY,SAAS;AAEzB;;;;;;;;;;;;CAYC,GACD,SAAS,UAAU,EAAE;IACjB,OAAO,CAAC,GAAG;QACP,OAAO,CAAC,GAAG,UAAU,IAAI,EAAE,KAAK;IACpC;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GACD,SAAS,OAAO,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACpC,WAAW,SAAS;IACpB,IAAI,MAAM,UAAU;IACpB,IAAI,QAAQ,UAAU;IACtB,IAAI,UAAU,EAAE;IAEhB,SAAS,KAAK,GAAG,EAAE,GAAG,IAAI;QACtB,IAAI,KAAK,OAAO,SAAS;QACzB,UAAU;QACV,IAAI,QAAQ,OAAO;QACnB,MAAM;IACV;IAEA,SAAS,MAAM,GAAG,EAAE,KAAK;QACrB,IAAI,KAAK,OAAO,SAAS;QACzB,IAAI,QAAQ,OAAO;QACnB,IAAI,CAAC,OAAO,OAAO,SAAS,SAAS;QACrC,IAAI;IACR;IAEA,OAAO,MAAM;AACjB;AACA,IAAI,WAAW,SAAS,QAAQ;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsCC,GACD,SAAS,MAAM,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACnC,MAAM,QAAQ,UAAU;IACxB,OAAO,SAAS,CAAC,KAAO,MAAM,CAAC,KAAK,QAAU,GAAI,KAAK,CAAC,SAAS,UAAU;AAC/E;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwDC,GACD,SAAS,UAAW,KAAK,EAAE,QAAQ;IAC/B,WAAW,KAAK;IAChB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO,SAAS,IAAI,MAAM;IACrD,IAAI,CAAC,MAAM,MAAM,EAAE,OAAO;IAC1B,IAAI,YAAY;IAEhB,SAAS,SAAS,IAAI;QAClB,IAAI,OAAO,UAAU,KAAK,CAAC,YAAY;QACvC,QAAQ,MAAM,SAAS;IAC3B;IAEA,SAAS,KAAK,GAAG,EAAE,GAAG,IAAI;QACtB,IAAI,QAAQ,OAAO;QACnB,IAAI,OAAO,cAAc,MAAM,MAAM,EAAE;YACnC,OAAO,SAAS,QAAQ;QAC5B;QACA,SAAS;IACb;IAEA,SAAS,EAAE;AACf;AAEA,IAAI,cAAc,SAAS;AAE3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GAGD,IAAI,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA,OAAO;IACP,YAAY;IACZ;IACA,QAAQ;IACR,aAAa;IACb,cAAc;IACd,UAAU;IACV,QAAQ;IACR,aAAa;IACb,cAAc;IACd;IACA;IACA,UAAU;IACV;IACA,WAAW;IACX,QAAQ;IACR,aAAa;IACb,cAAc;IACd,YAAY;IACZ;IACA,OAAO;IACP,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,aAAa;IACb,cAAc;IACd,SAAS;IACT;IACA,cAAc;IACd;IACA;IACA,KAAK;IACL,UAAU;IACV,WAAW;IACX;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM;IACN,QAAQ;IACR;IACA;IACA;IACA,QAAQ;IACR,aAAa;IACb,cAAc;IACd;IACA;IACA;IACA;IACA,cAAc;IACd,MAAM;IACN,WAAW;IACX,YAAY;IACZ,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA,SAAS;IACT;IACA;IACA,WAAW;IACX,QAAQ;IAER,UAAU;IACV,KAAK;IACL,UAAU;IACV,WAAW;IACX,KAAK;IACL,UAAU;IACV,WAAW;IACX,MAAM;IACN,WAAW;IACX,YAAY;IACZ,SAAS;IACT,cAAc;IACd,eAAe;IACf,SAAS;IACT,eAAe;IACf,cAAc;IACd,WAAW;IACX,iBAAiB;IACjB,gBAAgB;IAChB,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,aAAa;IACb,cAAc;IACd,UAAU;IACV,QAAQ;IACR,UAAU;AACd", "ignoreList": [0], "debugId": null}}]}