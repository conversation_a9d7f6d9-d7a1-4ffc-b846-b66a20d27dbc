#!/usr/bin/env python3
"""
Generate Stoke Cloner extension icons
Creates 16x16, 32x32, 48x48, and 128x128 PNG icons
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
except ImportError:
    print("PIL (Pillow) not found. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
    from PIL import Image, ImageDraw, ImageFont
    import os

def create_icon(size):
    # Create image with orange background (#f97316)
    img = Image.new('RGBA', (size, size), (249, 115, 22, 255))
    draw = ImageDraw.Draw(img)
    
    # Calculate font size (roughly 60% of icon size)
    font_size = int(size * 0.6)
    
    try:
        # Try to use a system font
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
    
    # Draw white "S" in center
    text = "S"
    
    # Get text bounding box for centering
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Calculate position to center text
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - bbox[1]  # Adjust for baseline
    
    # Draw the text
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    return img

def main():
    sizes = [16, 32, 48, 128]
    
    print("Generating Stoke Cloner extension icons...")
    
    for size in sizes:
        print(f"Creating icon{size}.png...")
        icon = create_icon(size)
        filename = f"icon{size}.png"
        icon.save(filename, "PNG")
        print(f"✓ Saved {filename}")
    
    print("\n✅ All icons generated successfully!")
    print("Icons created:")
    for size in sizes:
        print(f"  - icon{size}.png ({size}x{size})")

if __name__ == "__main__":
    main()
