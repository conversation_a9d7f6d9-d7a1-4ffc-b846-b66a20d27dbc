var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/clone/route.js")
R.c("server/chunks/node_modules_next_2cc057cf._.js")
R.c("server/chunks/node_modules_async_dist_async_mjs_3c3c2e3f._.js")
R.c("server/chunks/node_modules_4ce94685._.js")
R.c("server/chunks/node_modules_526a9a34._.js")
R.c("server/chunks/[root-of-the-server]__add885bf._.js")
R.m("[project]/.next-internal/server/app/api/clone/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/clone/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/clone/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
