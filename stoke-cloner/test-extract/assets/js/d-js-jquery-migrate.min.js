/*!
 * jQuery Migrate - v3.5.2 - 2024-07-17T22:31Z
 * Copyright OpenJS Foundation and other contributors
 */
!function(){"use strict";!function(e,t){function compareVersions(e,t){var r,a=/^(\d+)\.(\d+)\.(\d+)/,n=a.exec(e)||[],i=a.exec(t)||[];for(r=1;r<=3;r++){if(+n[r]>+i[r])return 1;if(+n[r]<+i[r])return-1}return 0}function jQueryVersionSince(t){return compareVersions(e.fn.jquery,t)>=0}e.migrateVersion="3.5.2";var r=Object.create(null);e.migrateDisablePatches=function(){var e;for(e=0;e<arguments.length;e++)r[arguments[e]]=!0},e.migrateEnablePatches=function(){var e;for(e=0;e<arguments.length;e++)delete r[arguments[e]]},e.migrateIsPatchEnabled=function(e){return!r[e]},t.console&&t.console.log&&(e&&jQueryVersionSince("3.0.0")&&!jQueryVersionSince("5.0.0")||t.console.log("JQMIGRATE: jQuery 3.x-4.x REQUIRED"),e.migrateWarnings&&t.console.log("JQMIGRATE: Migrate plugin loaded multiple times"),t.console.log("JQMIGRATE: Migrate is installed"+(e.migrateMute?"":" with logging active")+", version "+e.migrateVersion));var a={};e.migrateDeduplicateWarnings=!0,e.migrateWarnings=[],void 0===e.migrateTrace&&(e.migrateTrace=!0);function migrateWarn(r,n){var i=t.console;!e.migrateIsPatchEnabled(r)||e.migrateDeduplicateWarnings&&a[n]||(a[n]=!0,e.migrateWarnings.push(n+" ["+r+"]"),i&&i.warn&&!e.migrateMute&&(i.warn("JQMIGRATE: "+n),e.migrateTrace&&i.trace&&i.trace()))}function migrateWarnProp(e,t,r,a,n){Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return migrateWarn(a,n),r},set:function(e){migrateWarn(a,n),r=e}})}function migrateWarnFuncInternal(t,r,a,n,i){var o=t[r];t[r]=function(){return i&&migrateWarn(n,i),(e.migrateIsPatchEnabled(n)?a:o||e.noop).apply(this,arguments)}}function migratePatchAndWarnFunc(e,t,r,a,n){if(!n)throw new Error("No warning message provided");return migrateWarnFuncInternal(e,t,r,a,n)}function migratePatchFunc(e,t,r,a){return migrateWarnFuncInternal(e,t,r,a)}e.migrateReset=function(){a={},e.migrateWarnings.length=0},"BackCompat"===t.document.compatMode&&migrateWarn("quirks","jQuery is not compatible with Quirks Mode");var n,i={},o=e.fn.init,s=e.find,c=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/,u=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/g,d=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;for(n in migratePatchFunc(e.fn,"init",(function(t){var r=Array.prototype.slice.call(arguments);return e.migrateIsPatchEnabled("selector-empty-id")&&"string"==typeof t&&"#"===t&&(migrateWarn("selector-empty-id","jQuery( '#' ) is not a valid selector"),r[0]=[]),o.apply(this,r)}),"selector-empty-id"),e.fn.init.prototype=e.fn,migratePatchFunc(e,"find",(function(e){var r=Array.prototype.slice.call(arguments);if("string"==typeof e&&c.test(e))try{t.document.querySelector(e)}catch(a){e=e.replace(u,(function(e,t,r,a){return"["+t+r+'"'+a+'"]'}));try{t.document.querySelector(e),migrateWarn("selector-hash","Attribute selector with '#' must be quoted: "+r[0]),r[0]=e}catch(e){migrateWarn("selector-hash","Attribute selector with '#' was not fixed: "+r[0])}}return s.apply(this,r)}),"selector-hash"),s)Object.prototype.hasOwnProperty.call(s,n)&&(e.find[n]=s[n]);migratePatchAndWarnFunc(e.fn,"size",(function(){return this.length}),"size","jQuery.fn.size() is deprecated and removed; use the .length property"),migratePatchAndWarnFunc(e,"parseJSON",(function(){return JSON.parse.apply(null,arguments)}),"parseJSON","jQuery.parseJSON is deprecated; use JSON.parse"),migratePatchAndWarnFunc(e,"holdReady",e.holdReady,"holdReady","jQuery.holdReady is deprecated"),migratePatchAndWarnFunc(e,"unique",e.uniqueSort,"unique","jQuery.unique is deprecated; use jQuery.uniqueSort"),migrateWarnProp(e.expr,"filters",e.expr.pseudos,"expr-pre-pseudos","jQuery.expr.filters is deprecated; use jQuery.expr.pseudos"),migrateWarnProp(e.expr,":",e.expr.pseudos,"expr-pre-pseudos","jQuery.expr[':'] is deprecated; use jQuery.expr.pseudos"),jQueryVersionSince("3.1.1")&&migratePatchAndWarnFunc(e,"trim",(function(e){return null==e?"":(e+"").replace(d,"$1")}),"trim","jQuery.trim is deprecated; use String.prototype.trim");jQueryVersionSince("3.2.0")&&(migratePatchAndWarnFunc(e,"nodeName",(function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}),"nodeName","jQuery.nodeName is deprecated"),migratePatchAndWarnFunc(e,"isArray",Array.isArray,"isArray","jQuery.isArray is deprecated; use Array.isArray"));jQueryVersionSince("3.3.0")&&(migratePatchAndWarnFunc(e,"isNumeric",(function(e){var t=typeof e;return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))}),"isNumeric","jQuery.isNumeric() is deprecated"),e.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){i["[object "+t+"]"]=t.toLowerCase()})),migratePatchAndWarnFunc(e,"type",(function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?i[Object.prototype.toString.call(e)]||"object":typeof e}),"type","jQuery.type is deprecated"),migratePatchAndWarnFunc(e,"isFunction",(function(e){return"function"==typeof e}),"isFunction","jQuery.isFunction() is deprecated"),migratePatchAndWarnFunc(e,"isWindow",(function(e){return null!=e&&e===e.window}),"isWindow","jQuery.isWindow() is deprecated"));if(e.ajax){var l=e.ajax,p=/(=)\?(?=&|$)|\?\?/;migratePatchFunc(e,"ajax",(function(){var e=l.apply(this,arguments);return e.promise&&(migratePatchAndWarnFunc(e,"success",e.done,"jqXHR-methods","jQXHR.success is deprecated and removed"),migratePatchAndWarnFunc(e,"error",e.fail,"jqXHR-methods","jQXHR.error is deprecated and removed"),migratePatchAndWarnFunc(e,"complete",e.always,"jqXHR-methods","jQXHR.complete is deprecated and removed")),e}),"jqXHR-methods"),jQueryVersionSince("4.0.0")||e.ajaxPrefilter("+json",(function(e){!1!==e.jsonp&&(p.test(e.url)||"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&p.test(e.data))&&migrateWarn("jsonp-promotion","JSON-to-JSONP auto-promotion is deprecated")}))}var m=e.fn.removeAttr,f=e.fn.toggleClass,g=/^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$/i,h=/\S+/g;function camelCase(e){return e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))}migratePatchFunc(e.fn,"removeAttr",(function(t){var r=this,a=!1;return e.each(t.match(h),(function(t,n){g.test(n)&&r.each((function(){if(!1!==e(this).prop(n))return a=!0,!1})),a&&(migrateWarn("removeAttr-bool","jQuery.fn.removeAttr no longer sets boolean properties: "+n),r.prop(n,!1))})),m.apply(this,arguments)}),"removeAttr-bool"),migratePatchFunc(e.fn,"toggleClass",(function(t){return void 0!==t&&"boolean"!=typeof t?f.apply(this,arguments):(migrateWarn("toggleClass-bool","jQuery.fn.toggleClass( boolean ) is deprecated"),this.each((function(){var r=this.getAttribute&&this.getAttribute("class")||"";r&&e.data(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===t?"":e.data(this,"__className__")||"")})))}),"toggleClass-bool");var y,v,j=!1,P=/^[a-z]/,b=/^(?:Border(?:Top|Right|Bottom|Left)?(?:Width|)|(?:Margin|Padding)?(?:Top|Right|Bottom|Left)?|(?:Min|Max)?(?:Width|Height))$/;e.swap&&e.each(["height","width","reliableMarginRight"],(function(t,r){var a=e.cssHooks[r]&&e.cssHooks[r].get;a&&(e.cssHooks[r].get=function(){var e;return j=!0,e=a.apply(this,arguments),j=!1,e})}));migratePatchFunc(e,"swap",(function(e,t,r,a){var n,i,o={};for(i in j||migrateWarn("swap","jQuery.swap() is undocumented and deprecated"),t)o[i]=e.style[i],e.style[i]=t[i];for(i in n=r.apply(e,a||[]),t)e.style[i]=o[i];return n}),"swap"),jQueryVersionSince("3.4.0")&&"undefined"!=typeof Proxy&&(e.cssProps=new Proxy(e.cssProps||{},{set:function(){return migrateWarn("cssProps","jQuery.cssProps is deprecated"),Reflect.set.apply(this,arguments)}}));jQueryVersionSince("4.0.0")?(v={animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},"undefined"!=typeof Proxy?e.cssNumber=new Proxy(v,{get:function(){return migrateWarn("css-number","jQuery.cssNumber is deprecated"),Reflect.get.apply(this,arguments)},set:function(){return migrateWarn("css-number","jQuery.cssNumber is deprecated"),Reflect.set.apply(this,arguments)}}):e.cssNumber=v):v=e.cssNumber;function isAutoPx(e){return P.test(e)&&b.test(e[0].toUpperCase()+e.slice(1))}y=e.fn.css,migratePatchFunc(e.fn,"css",(function(t,r){var a,n=this;return t&&"object"==typeof t&&!Array.isArray(t)?(e.each(t,(function(t,r){e.fn.css.call(n,t,r)})),this):("number"==typeof r&&(isAutoPx(a=camelCase(t))||v[a]||migrateWarn("css-number",'Number-typed values are deprecated for jQuery.fn.css( "'+t+'", value )')),y.apply(this,arguments))}),"css-number");var W=e.data;migratePatchFunc(e,"data",(function(t,r,a){var n,i,o;if(r&&"object"==typeof r&&2===arguments.length){for(o in n=e.hasData(t)&&W.call(this,t),i={},r)o!==camelCase(o)?(migrateWarn("data-camelCase","jQuery.data() always sets/gets camelCased names: "+o),n[o]=r[o]):i[o]=r[o];return W.call(this,t,i),r}return r&&"string"==typeof r&&r!==camelCase(r)&&(n=e.hasData(t)&&W.call(this,t))&&r in n?(migrateWarn("data-camelCase","jQuery.data() always sets/gets camelCased names: "+r),arguments.length>2&&(n[r]=a),n[r]):W.apply(this,arguments)}),"data-camelCase");var Q=e.fn.data;if(migratePatchFunc(e.fn,"data",(function(e,t){if(0===arguments.length){var r=Q.call(this);return r?new Proxy(r,{get:function(e,t){return t!==camelCase(t)&&void 0===e[t]?(migrateWarn("data-camelCase","jQuery.data() always sets/gets camelCased names: "+t),e[camelCase(t)]):e[t]}}):r}if("string"==typeof e&&e!==camelCase(e)){migrateWarn("data-camelCase","jQuery.data() always sets/gets camelCased names: "+e);var a=arguments.length>1?[camelCase(e),t]:[camelCase(e)];return Q.apply(this,a)}return Q.apply(this,arguments)}),"data-camelCase"),e.fx){var F,A,w=e.Tween.prototype.run,linearEasing=function(e){return e};migratePatchFunc(e.Tween.prototype,"run",(function(){e.easing[this.easing].length>1&&(migrateWarn("easing-one-arg","'jQuery.easing."+this.easing.toString()+"' should use only one argument"),e.easing[this.easing]=linearEasing),w.apply(this,arguments)}),"easing-one-arg"),F=e.fx.interval,A="jQuery.fx.interval is deprecated",t.requestAnimationFrame&&Object.defineProperty(e.fx,"interval",{configurable:!0,enumerable:!0,get:function(){return t.document.hidden||migrateWarn("fx-interval",A),e.migrateIsPatchEnabled("fx-interval")&&void 0===F?13:F},set:function(e){migrateWarn("fx-interval",A),F=e}})}var x=e.fn.load,C=e.event.add,S=e.event.fix;e.event.props=[],e.event.fixHooks={},migrateWarnProp(e.event.props,"concat",e.event.props.concat,"event-old-patch","jQuery.event.props.concat() is deprecated and removed"),migratePatchFunc(e.event,"fix",(function(t){var r,a=t.type,n=this.fixHooks[a],i=e.event.props;if(i.length)for(migrateWarn("event-old-patch","jQuery.event.props are deprecated and removed: "+i.join());i.length;)e.event.addProp(i.pop());if(n&&!n._migrated_&&(n._migrated_=!0,migrateWarn("event-old-patch","jQuery.event.fixHooks are deprecated and removed: "+a),(i=n.props)&&i.length))for(;i.length;)e.event.addProp(i.pop());return r=S.call(this,t),n&&n.filter?n.filter(r,t):r}),"event-old-patch"),migratePatchFunc(e.event,"add",(function(e,r){return e===t&&"load"===r&&"complete"===t.document.readyState&&migrateWarn("load-after-event","jQuery(window).on('load'...) called after load event occurred"),C.apply(this,arguments)}),"load-after-event"),e.each(["load","unload","error"],(function(t,r){migratePatchFunc(e.fn,r,(function(){var e=Array.prototype.slice.call(arguments,0);return"load"===r&&"string"==typeof e[0]?x.apply(this,e):(migrateWarn("shorthand-removed-v3","jQuery.fn."+r+"() is deprecated"),e.splice(0,0,r),arguments.length?this.on.apply(this,e):(this.triggerHandler.apply(this,e),this))}),"shorthand-removed-v3")})),e.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,r){migratePatchAndWarnFunc(e.fn,r,(function(e,t){return arguments.length>0?this.on(r,null,e,t):this.trigger(r)}),"shorthand-deprecated-v3","jQuery.fn."+r+"() event shorthand is deprecated")})),e((function(){e(t.document).triggerHandler("ready")})),e.event.special.ready={setup:function(){this===t.document&&migrateWarn("ready-event","'ready' event is deprecated")}},migratePatchAndWarnFunc(e.fn,"bind",(function(e,t,r){return this.on(e,null,t,r)}),"pre-on-methods","jQuery.fn.bind() is deprecated"),migratePatchAndWarnFunc(e.fn,"unbind",(function(e,t){return this.off(e,null,t)}),"pre-on-methods","jQuery.fn.unbind() is deprecated"),migratePatchAndWarnFunc(e.fn,"delegate",(function(e,t,r,a){return this.on(t,e,r,a)}),"pre-on-methods","jQuery.fn.delegate() is deprecated"),migratePatchAndWarnFunc(e.fn,"undelegate",(function(e,t,r){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",r)}),"pre-on-methods","jQuery.fn.undelegate() is deprecated"),migratePatchAndWarnFunc(e.fn,"hover",(function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}),"pre-on-methods","jQuery.fn.hover() is deprecated");var R=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,makeMarkup=function(e){var r=t.document.implementation.createHTMLDocument("");return r.body.innerHTML=e,r.body&&r.body.innerHTML},warnIfChanged=function(e){var t=e.replace(R,"<$1></$2>");t!==e&&makeMarkup(e)!==makeMarkup(t)&&migrateWarn("self-closed-tags","HTML tags must be properly nested and closed: "+e)};migratePatchAndWarnFunc(e,"UNSAFE_restoreLegacyHtmlPrefilter",(function(){e.migrateEnablePatches("self-closed-tags")}),"legacy-self-closed-tags",'jQuery.UNSAFE_restoreLegacyHtmlPrefilter deprecated; use `jQuery.migrateEnablePatches( "self-closed-tags" )`'),migratePatchFunc(e,"htmlPrefilter",(function(e){return warnIfChanged(e),e.replace(R,"<$1></$2>")}),"self-closed-tags"),e.migrateDisablePatches("self-closed-tags");var k=e.fn.offset;if(migratePatchFunc(e.fn,"offset",(function(){var e=this[0];return!e||e.nodeType&&e.getBoundingClientRect?k.apply(this,arguments):(migrateWarn("offset-valid-elem","jQuery.fn.offset() requires a valid DOM element"),arguments.length?this:void 0)}),"offset-valid-elem"),e.ajax){var N=e.param;migratePatchFunc(e,"param",(function(t,r){var a=e.ajaxSettings&&e.ajaxSettings.traditional;return void 0===r&&a&&(migrateWarn("param-ajax-traditional","jQuery.param() no longer uses jQuery.ajaxSettings.traditional"),r=a),N.call(this,t,r)}),"param-ajax-traditional")}if(migratePatchAndWarnFunc(e.fn,"andSelf",e.fn.addBack,"andSelf","jQuery.fn.andSelf() is deprecated and removed, use jQuery.fn.addBack()"),e.Deferred){var H=e.Deferred,E=[["resolve","done",e.Callbacks("once memory"),e.Callbacks("once memory"),"resolved"],["reject","fail",e.Callbacks("once memory"),e.Callbacks("once memory"),"rejected"],["notify","progress",e.Callbacks("memory"),e.Callbacks("memory")]];migratePatchFunc(e,"Deferred",(function(t){var r=H(),a=r.promise();function newDeferredPipe(){var t=arguments;return e.Deferred((function(n){e.each(E,(function(e,i){var o="function"==typeof t[e]&&t[e];r[i[1]]((function(){var e=o&&o.apply(this,arguments);e&&"function"==typeof e.promise?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[i[0]+"With"](this===a?n.promise():this,o?[e]:arguments)}))})),t=null})).promise()}return migratePatchAndWarnFunc(r,"pipe",newDeferredPipe,"deferred-pipe","deferred.pipe() is deprecated"),migratePatchAndWarnFunc(a,"pipe",newDeferredPipe,"deferred-pipe","deferred.pipe() is deprecated"),t&&t.call(r,r),r}),"deferred-pipe"),e.Deferred.exceptionHook=H.exceptionHook}}(jQuery,window)}();
//# sourceMappingURL=https://sourcemaps-lambda.dwhitelabel.com/mnlt/production/5810/_dm/s/rt/dist/scripts/d-js-jquery-migrate.min.js.map
