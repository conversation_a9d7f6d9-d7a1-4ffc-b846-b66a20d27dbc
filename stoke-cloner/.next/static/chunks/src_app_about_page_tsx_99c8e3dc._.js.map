{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/backup/stoke-cloner/src/app/about/page.tsx"], "sourcesContent": ["'use client';\n\nimport { ArrowLeft, Code, Database, Download, Globe, Layers, Shield, Zap } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-6 py-8 pb-24\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Header */}\n          <div className=\"mb-12\">\n            <Link\n              href=\"/\"\n              className=\"inline-flex items-center text-orange-500 hover:text-orange-400 mb-6 transition-colors\"\n            >\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Back to Stoke Cloner\n            </Link>\n\n            <div className=\"text-center\">\n              <h1 className=\"text-3xl font-bold mb-4\">\n                Technical Details\n              </h1>\n              <p className=\"text-xl text-slate-400\">\n                How Stoke Cloner works under the hood\n              </p>\n            </div>\n          </div>\n\n          {/* Architecture Overview */}\n          <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8\">\n            <div className=\"flex items-center mb-6\">\n              <Layers className=\"w-8 h-8 text-orange-500 mr-3\" />\n              <h2 className=\"text-2xl font-bold text-white\">\n                System Architecture\n              </h2>\n            </div>\n\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-3\">Frontend Stack</h3>\n                <ul className=\"space-y-2 text-slate-300\">\n                  <li><strong className=\"text-orange-400\">Next.js 15:</strong> React framework with App Router</li>\n                  <li><strong className=\"text-orange-400\">TypeScript:</strong> Type-safe development</li>\n                  <li><strong className=\"text-orange-400\">Tailwind CSS:</strong> Utility-first styling</li>\n                  <li><strong className=\"text-orange-400\">Lucide React:</strong> Beautiful SVG icons</li>\n                  <li><strong className=\"text-orange-400\">Turbopack:</strong> Fast development builds</li>\n                </ul>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-3\">Backend Stack</h3>\n                <ul className=\"space-y-2 text-slate-300\">\n                  <li><strong className=\"text-orange-400\">Next.js API Routes:</strong> Serverless functions</li>\n                  <li><strong className=\"text-orange-400\">Puppeteer:</strong> Headless Chrome automation</li>\n                  <li><strong className=\"text-orange-400\">Archiver:</strong> ZIP file generation</li>\n                  <li><strong className=\"text-orange-400\">Node.js Streams:</strong> Memory-efficient processing</li>\n                  <li><strong className=\"text-orange-400\">File System API:</strong> Temporary file management</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Core Features */}\n          <div className=\"grid md:grid-cols-2 gap-6 mb-8\">\n            {/* Web Scraping Engine */}\n            <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-6\">\n              <div className=\"flex items-center mb-4\">\n                <Globe className=\"w-6 h-6 text-orange-500 mr-3\" />\n                <h3 className=\"text-xl font-semibold text-white\">\n                  Web Scraping Engine\n                </h3>\n              </div>\n              <ul className=\"space-y-2 text-sm text-slate-300\">\n                <li>• <strong className=\"text-orange-400\">Puppeteer-based:</strong> Full browser rendering</li>\n                <li>• <strong className=\"text-orange-400\">Depth-first crawling:</strong> Configurable 1-3 levels</li>\n                <li>• <strong className=\"text-orange-400\">Smart asset discovery:</strong> CSS, JS, images, fonts</li>\n                <li>• <strong className=\"text-orange-400\">Background image extraction:</strong> From computed styles</li>\n                <li>• <strong className=\"text-orange-400\">Font detection:</strong> @font-face rule parsing</li>\n                <li>• <strong>Assets-only mode:</strong> Extract only media and documents</li>\n                <li>• <strong>Retry logic:</strong> 3 attempts with exponential backoff</li>\n              </ul>\n            </div>\n\n            {/* Asset Processing */}\n            <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-6\">\n              <div className=\"flex items-center mb-4\">\n                <Code className=\"w-6 h-6 text-orange-500 mr-3\" />\n                <h3 className=\"text-xl font-semibold text-white\">\n                  Asset Localization\n                </h3>\n              </div>\n              <ul className=\"space-y-2 text-sm text-slate-300\">\n                <li>• <strong className=\"text-orange-400\">URL rewriting:</strong> Absolute to relative paths</li>\n                <li>• <strong className=\"text-orange-400\">CSS processing:</strong> url() and @import statements</li>\n                <li>• <strong className=\"text-orange-400\">Inline style handling:</strong> &lt;style&gt; tag processing</li>\n                <li>• <strong className=\"text-orange-400\">CDN localization:</strong> External asset downloading</li>\n                <li>• <strong className=\"text-orange-400\">Organized structure:</strong> assets/css/, js/, images/</li>\n                <li>• <strong className=\"text-orange-400\">Duplicate prevention:</strong> Asset deduplication</li>\n                <li>• <strong className=\"text-orange-400\">Smart navigation:</strong> Internal links converted for offline use</li>\n                <li>• <strong className=\"text-orange-400\">Markdown extraction:</strong> Clean text content for AI analysis</li>\n              </ul>\n            </div>\n\n            {/* Performance & Safety */}\n            <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-6\">\n              <div className=\"flex items-center mb-4\">\n                <Zap className=\"w-6 h-6 text-orange-500 mr-3\" />\n                <h3 className=\"text-xl font-semibold text-white\">\n                  Performance Optimizations\n                </h3>\n              </div>\n              <ul className=\"space-y-2 text-sm text-slate-300\">\n                <li>• <strong className=\"text-orange-400\">Parallel downloads:</strong> Concurrent asset fetching</li>\n                <li>• <strong className=\"text-orange-400\">Memory streaming:</strong> Buffer-based processing</li>\n                <li>• <strong className=\"text-orange-400\">Temporary files:</strong> OS temp directory usage</li>\n                <li>• <strong className=\"text-orange-400\">Progress tracking:</strong> Real-time status updates</li>\n                <li>• <strong className=\"text-orange-400\">Timeout handling:</strong> 30s page, 10s asset limits</li>\n                <li>• <strong className=\"text-orange-400\">Resource cleanup:</strong> Automatic temp file removal</li>\n              </ul>\n            </div>\n\n            {/* Security & Limits */}\n            <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-6\">\n              <div className=\"flex items-center mb-4\">\n                <Shield className=\"w-6 h-6 text-orange-500 mr-3\" />\n                <h3 className=\"text-xl font-semibold text-white\">\n                  Security & Limits\n                </h3>\n              </div>\n              <ul className=\"space-y-2 text-sm text-slate-300\">\n                <li>• <strong className=\"text-orange-400\">2GB size limit:</strong> Prevents resource exhaustion</li>\n                <li>• <strong className=\"text-orange-400\">100 page limit:</strong> Reasonable crawl boundaries</li>\n                <li>• <strong className=\"text-orange-400\">Same-origin policy:</strong> Domain-restricted crawling</li>\n                <li>• <strong className=\"text-orange-400\">User-Agent spoofing:</strong> Bypass basic bot detection</li>\n                <li>• <strong className=\"text-orange-400\">Input validation:</strong> URL and depth sanitization</li>\n                <li>• <strong className=\"text-orange-400\">Error isolation:</strong> Graceful failure handling</li>\n              </ul>\n            </div>\n          </div>\n\n          {/* Technical Implementation */}\n          <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8\">\n            <div className=\"flex items-center mb-6\">\n              <Database className=\"w-8 h-8 text-orange-500 mr-3\" />\n              <h2 className=\"text-2xl font-bold text-white\">\n                Implementation Details\n              </h2>\n            </div>\n            \n            <div className=\"grid md:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-4\">\n                  Crawling Algorithm\n                </h3>\n                <div className=\"bg-slate-700 rounded-lg p-4 font-mono text-sm\">\n                  <pre className=\"text-slate-200\">\n{`1. Initialize queue with start URL\n2. While queue not empty:\n   a. Dequeue URL\n   b. Check visited set\n   c. Launch Puppeteer page\n   d. Extract HTML content\n   e. Discover assets via DOM\n   f. Download assets in parallel\n   g. Extract internal links\n   h. Add links to queue\n   i. Update progress\n3. Generate ZIP archive`}\n                  </pre>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-4\">\n                  Asset Discovery Methods\n                </h3>\n                <div className=\"space-y-3 text-sm text-slate-300\">\n                  <div>\n                    <strong className=\"text-orange-400\">CSS Files:</strong>\n                    <code className=\"block bg-slate-700 p-2 mt-1 rounded text-slate-200\">\n                      document.querySelectorAll('link[rel=\"stylesheet\"]')\n                    </code>\n                  </div>\n                  <div>\n                    <strong className=\"text-orange-400\">JavaScript:</strong>\n                    <code className=\"block bg-slate-700 p-2 mt-1 rounded text-slate-200\">\n                      document.querySelectorAll('script[src]')\n                    </code>\n                  </div>\n                  <div>\n                    <strong className=\"text-orange-400\">Images:</strong>\n                    <code className=\"block bg-slate-700 p-2 mt-1 rounded text-slate-200\">\n                      document.querySelectorAll('img[src]')\n                    </code>\n                  </div>\n                  <div>\n                    <strong className=\"text-orange-400\">Background Images:</strong>\n                    <code className=\"block bg-slate-700 p-2 mt-1 rounded text-slate-200\">\n                      window.getComputedStyle(el).backgroundImage\n                    </code>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* File Structure */}\n          <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8\">\n            <div className=\"flex items-center mb-6\">\n              <Download className=\"w-8 h-8 text-orange-500 mr-3\" />\n              <h2 className=\"text-2xl font-bold text-white\">\n                Generated ZIP Structure\n              </h2>\n            </div>\n\n            <div className=\"bg-slate-700 rounded-lg p-6 font-mono text-sm\">\n              <pre className=\"text-slate-200\">\n{`website-clone-[timestamp].zip\n├── README.md                    # Usage instructions\n├── index.html                   # Main page\n├── page2.html                   # Additional pages\n├── assets/\n│   ├── css/\n│   │   ├── main.css            # Stylesheets\n│   │   └── bootstrap.min.css\n│   ├── js/\n│   │   ├── app.js              # JavaScript files\n│   │   └── jquery.min.js\n│   ├── images/\n│   │   ├── logo.png            # Images & graphics\n│   │   └── background.jpg\n│   └── fonts/\n│       ├── font.woff2          # Web fonts\n│       └── icons.ttf`}\n              </pre>\n            </div>\n          </div>\n\n          {/* Performance Metrics */}\n          <div className=\"bg-slate-800 border border-orange-500/20 rounded-xl p-8 mb-8\">\n            <h2 className=\"text-2xl font-bold text-white mb-6\">\n              Performance Characteristics\n            </h2>\n\n            <div className=\"grid md:grid-cols-3 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-orange-500 mb-2\">~15s</div>\n                <div className=\"text-sm text-slate-300\">\n                  Average clone time<br />\n                  <span className=\"text-xs text-slate-400\">(single page with assets)</span>\n                </div>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-orange-500 mb-2\">2GB</div>\n                <div className=\"text-sm text-slate-300\">\n                  Maximum output size<br />\n                  <span className=\"text-xs text-slate-400\">(safety limit)</span>\n                </div>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-orange-500 mb-2\">100</div>\n                <div className=\"text-sm text-slate-300\">\n                  Maximum pages<br />\n                  <span className=\"text-xs text-slate-400\">(per crawl session)</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Development & Deployment */}\n          <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-8 mb-8\">\n            <h2 className=\"text-2xl font-bold text-white mb-6\">\n              Development & Deployment\n            </h2>\n\n            <div className=\"grid md:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-4\">\n                  Project Structure\n                </h3>\n                <div className=\"bg-slate-700 rounded-lg p-4 font-mono text-sm\">\n                  <pre className=\"text-slate-200\">\n{`stoke-cloner/\n├── src/app/\n│   ├── api/clone/\n│   │   └── route.ts      # API endpoint\n│   ├── about/\n│   │   └── page.tsx      # This page\n│   ├── page.tsx          # Main UI\n│   ├── layout.tsx        # App layout\n│   └── globals.css       # Global styles\n├── package.json          # Dependencies\n├── next.config.ts        # Next.js config\n├── tailwind.config.ts    # Tailwind config\n└── tsconfig.json         # TypeScript config`}\n                  </pre>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-4\">\n                  Key Dependencies\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between items-center py-2 border-b border-slate-600\">\n                    <span className=\"font-medium text-white\">next</span>\n                    <span className=\"text-sm text-slate-400\">^15.5.3</span>\n                  </div>\n                  <div className=\"flex justify-between items-center py-2 border-b border-slate-600\">\n                    <span className=\"font-medium text-white\">puppeteer</span>\n                    <span className=\"text-sm text-slate-400\">^23.x</span>\n                  </div>\n                  <div className=\"flex justify-between items-center py-2 border-b border-slate-600\">\n                    <span className=\"font-medium text-white\">archiver</span>\n                    <span className=\"text-sm text-slate-400\">^7.x</span>\n                  </div>\n                  <div className=\"flex justify-between items-center py-2 border-b border-slate-600\">\n                    <span className=\"font-medium text-white\">tailwindcss</span>\n                    <span className=\"text-sm text-slate-400\">^3.x</span>\n                  </div>\n                  <div className=\"flex justify-between items-center py-2 border-b border-slate-600\">\n                    <span className=\"font-medium text-white\">lucide-react</span>\n                    <span className=\"text-sm text-slate-400\">^0.x</span>\n                  </div>\n                  <div className=\"flex justify-between items-center py-2\">\n                    <span className=\"font-medium text-white\">typescript</span>\n                    <span className=\"text-sm text-slate-400\">^5.x</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* API Specification */}\n          <div className=\"bg-slate-800 rounded-xl border border-slate-700 p-8\">\n            <h2 className=\"text-2xl font-bold text-white mb-6\">\n              API Specification\n            </h2>\n\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-white mb-3\">\n                  POST /api/clone\n                </h3>\n                <p className=\"text-slate-400 mb-4\">\n                  Clones a website and returns a ZIP file containing the offline copy.\n                </p>\n\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div>\n                    <h4 className=\"font-semibold text-white mb-2\">Request Body</h4>\n                    <div className=\"bg-slate-700 rounded-lg p-4 font-mono text-sm\">\n                      <pre className=\"text-slate-200\">\n{`{\n  \"url\": \"https://example.com\",\n  \"depth\": 1,\n  \"assetsOnly\": false\n}`}\n                      </pre>\n                    </div>\n                    <ul className=\"mt-2 text-sm text-slate-400\">\n                      <li><strong className=\"text-orange-400\">url:</strong> Target website URL (required)</li>\n                      <li><strong className=\"text-orange-400\">depth:</strong> Crawl depth 1-3 (required)</li>\n                      <li><strong className=\"text-orange-400\">assetsOnly:</strong> Extract only media/documents (optional, default: false)</li>\n                    </ul>\n                  </div>\n\n                  <div>\n                    <h4 className=\"font-semibold text-white mb-2\">Response</h4>\n                    <div className=\"bg-slate-700 rounded-lg p-4 font-mono text-sm\">\n                      <pre className=\"text-slate-200\">\n{`Content-Type: application/zip\nContent-Disposition: attachment;\n  filename=\"website-clone-[timestamp].zip\"\nContent-Length: [size]\n\n[Binary ZIP data]`}\n                      </pre>\n                    </div>\n                    <ul className=\"mt-2 text-sm text-slate-400\">\n                      <li><strong className=\"text-orange-400\">200:</strong> Success - ZIP file returned</li>\n                      <li><strong className=\"text-orange-400\">400:</strong> Invalid URL or depth</li>\n                      <li><strong className=\"text-orange-400\">500:</strong> Server error</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,gOAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDAGxC,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mNAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAKhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAoB;;;;;;;kEAC5D,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAoB;;;;;;;kEAC5D,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAsB;;;;;;;kEAC9D,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAsB;;;;;;;kEAC9D,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAmB;;;;;;;;;;;;;;;;;;;kDAI/D,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAA4B;;;;;;;kEACpE,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAmB;;;;;;;kEAC3D,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAkB;;;;;;;kEAC1D,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAyB;;;;;;;kEACjE,6LAAC;;0EAAG,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;4DAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gNAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAInD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAyB;;;;;;;0DACnE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA8B;;;;;;;0DACxE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA+B;;;;;;;0DACzE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAqC;;;;;;;0DAC/E,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAwB;;;;;;;0DAClE,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAA0B;;;;;;;0DACxC,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAqB;;;;;;;;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAInD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAuB;;;;;;;0DACjE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAwB;;;;;;;0DAClE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA+B;;;;;;;0DACzE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA0B;;;;;;;0DACpE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA6B;;;;;;;0DACvE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA8B;;;;;;;0DACxE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA0B;;;;;;;0DACpE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA6B;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,0MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAInD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA4B;;;;;;;0DACtE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA0B;;;;;;;0DACpE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAyB;;;;;;;0DACnE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA2B;;;;;;;0DACrE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA0B;;;;;;;0DACpE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA0B;;;;;;;;;;;;;;;;;;;0CAKxE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAInD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAwB;;;;;;;0DAClE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAwB;;;;;;;0DAClE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA4B;;;;;;;0DACtE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA6B;;;;;;;0DACvE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAA0B;;;;;;;0DACpE,6LAAC;;oDAAG;kEAAE,6LAAC;wDAAO,WAAU;kEAAkB;;;;;;oDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yNAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAKhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAC/B;;;;;;;;;;;;;;;;;kDAgBY,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;0EACpC,6LAAC;gEAAK,WAAU;0EAAqD;;;;;;;;;;;;kEAIvE,6LAAC;;0EACC,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;0EACpC,6LAAC;gEAAK,WAAU;0EAAqD;;;;;;;;;;;;kEAIvE,6LAAC;;0EACC,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;0EACpC,6LAAC;gEAAK,WAAU;0EAAqD;;;;;;;;;;;;kEAIvE,6LAAC;;0EACC,6LAAC;gEAAO,WAAU;0EAAkB;;;;;;0EACpC,6LAAC;gEAAK,WAAU;0EAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yNAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;;0CAKhD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAC3B;;;;;;;;;;;;;;;;;kCAsBQ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAInD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;oDAAyB;kEACpB,6LAAC;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;kDAI7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;oDAAyB;kEACnB,6LAAC;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;kDAI7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;oDAAyB;kEACzB,6LAAC;;;;;kEACd,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAInD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAC/B;;;;;;;;;;;;;;;;;kDAiBY,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,6LAAC;gEAAK,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAInD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;sDAInC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACnC;;;;;;;;;;;sEAOkB,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;;sFAAG,6LAAC;4EAAO,WAAU;sFAAkB;;;;;;wEAAa;;;;;;;8EACrD,6LAAC;;sFAAG,6LAAC;4EAAO,WAAU;sFAAkB;;;;;;wEAAe;;;;;;;8EACvD,6LAAC;;sFAAG,6LAAC;4EAAO,WAAU;sFAAkB;;;;;;wEAAoB;;;;;;;;;;;;;;;;;;;8DAIhE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACnC;;;;;;;;;;;sEAQkB,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;;sFAAG,6LAAC;4EAAO,WAAU;sFAAkB;;;;;;wEAAa;;;;;;;8EACrD,6LAAC;;sFAAG,6LAAC;4EAAO,WAAU;sFAAkB;;;;;;wEAAa;;;;;;;8EACrD,6LAAC;;sFAAG,6LAAC;4EAAO,WAAU;sFAAkB;;;;;;wEAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3E;KAxYwB", "debugId": null}}]}